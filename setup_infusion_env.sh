#!/bin/bash

# 获取当前激活的 micromamba 环境路径
if [ -n "$CONDA_PREFIX" ]; then
    MAMBA_ENV_PATH="$CONDA_PREFIX"
else
    echo "错误：未检测到激活的 micromamba 环境"
    return 1
fi

echo "使用环境路径: $MAMBA_ENV_PATH"

# 设置 CUDA_HOME
export CUDA_HOME="$MAMBA_ENV_PATH"

# 优先添加环境路径到 PATH
export PATH="$MAMBA_ENV_PATH/bin:$PATH"

# 设置 LD_LIBRARY_PATH
PYTORCH_LIB_PATH=$(python -c "import site; print(site.getsitepackages()[0])")/torch/lib
export LD_LIBRARY_PATH="$MAMBA_ENV_PATH/lib64:$MAMBA_ENV_PATH/lib:$PYTORCH_LIB_PATH:$LD_LIBRARY_PATH"

# 显式设置编译器
export CC="$MAMBA_ENV_PATH/bin/x86_64-conda-linux-gnu-gcc"
export CXX="$MAMBA_ENV_PATH/bin/x86_64-conda-linux-gnu-g++"

echo "环境变量设置完成："
echo "  CUDA_HOME: $CUDA_HOME"
echo "  CC: $CC"
echo "  CXX: $CXX"
echo "  nvcc: $(which nvcc)"
