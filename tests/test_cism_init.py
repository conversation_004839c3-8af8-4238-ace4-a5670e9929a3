#!/usr/bin/env python3

import sys
import os
import torch

# 添加路径
sys.path.append('./stage2_results')

def test_cism_initialization():
    print("🔧 测试 CISM 初始化...")
    
    try:
        # 测试导入
        print("1. 测试模块导入...")
        from cism_core.diffusion_engine import DiffusionEngine
        print("   ✅ DiffusionEngine 导入成功")
        
        from cism_core.concept_guidance import ConceptGuidance
        print("   ✅ ConceptGuidance 导入成功")
        
        from cism_core.sds_loss import SDSLoss
        print("   ✅ SDSLoss 导入成功")
        
        from cism_core.cism_trainer import CISMTrainer
        print("   ✅ CISMTrainer 导入成功")
        
        # 测试 DiffusionEngine 初始化
        print("\n2. 测试 DiffusionEngine 初始化...")
        diffusion_engine = DiffusionEngine(
            model_id='runwayml/stable-diffusion-v1-5',
            device='cuda',
            half_precision=True,
            enable_attention_slicing=True,
            enable_memory_efficient_attention=True
        )
        print("   ✅ DiffusionEngine 初始化成功")
        
        # 测试 ConceptGuidance 初始化
        print("\n3. 测试 ConceptGuidance 初始化...")
        concept_guidance = ConceptGuidance(
            diffusion_engine,
            'configs/concept_prompts.yaml',
            'cuda'
        )
        print("   ✅ ConceptGuidance 初始化成功")
        
        # 测试 SDSLoss 初始化
        print("\n4. 测试 SDSLoss 初始化...")
        sds_loss = SDSLoss(
            diffusion_engine,
            concept_guidance,
            min_step=20,
            max_step=980,
            device='cuda'
        )
        print("   ✅ SDSLoss 初始化成功")
        
        # 测试 CISMTrainer 初始化
        print("\n5. 测试 CISMTrainer 初始化...")

        # 创建一个模拟的 gaussians 对象
        class MockGaussians:
            def __init__(self):
                self.device = 'cuda'

        mock_gaussians = MockGaussians()

        cism_config = {
            'model_id': 'runwayml/stable-diffusion-v1-5',
            'half_precision': True,
            'attention_slicing': True,
            'memory_efficient_attention': True,
            'concept_config_path': 'configs/concept_prompts.yaml',
            'cism_start_iter': 100,
            'cism_end_iter': 200,
            'cism_interval': 20,
            'cism_weight': 0.05,
            'guidance_scale': 7.5,
            'timestep_range': [0.02, 0.98],
            'device': 'cuda'
        }

        cism_trainer = CISMTrainer(mock_gaussians, cism_config)
        print("   ✅ CISMTrainer 初始化成功")
        
        print("\n🎉 所有 CISM 组件初始化成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ CISM 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_cism_initialization()
    if success:
        print("\n✅ CISM 测试通过，可以继续训练")
    else:
        print("\n❌ CISM 测试失败，需要修复问题")
