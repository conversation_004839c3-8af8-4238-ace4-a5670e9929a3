# -*- coding: utf-8 -*-
"""
InFusion 阶段4: RCA损失函数设计
多目标损失函数平衡分类精度和空间连续性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import math

@dataclass
class RCALossConfig:
    """RCA损失函数配置"""
    # 分类损失配置
    classification_weight: float = 1.0
    use_focal_loss: bool = True
    focal_alpha: float = 0.25
    focal_gamma: float = 2.0
    label_smoothing: float = 0.1
    
    # 正则化损失配置
    regularization_weight: float = 0.1
    tv_loss_weight: float = 0.05
    entropy_regularization_weight: float = 0.02
    l2_regularization_weight: float = 0.001
    
    # 一致性损失配置
    consistency_weight: float = 0.5
    spatial_consistency_weight: float = 0.3
    temporal_consistency_weight: float = 0.2
    multi_view_consistency_weight: float = 0.4
    
    # 边界损失配置
    boundary_loss_weight: float = 0.2
    boundary_sharpness_weight: float = 0.1
    
    # 自适应权重配置
    adaptive_weighting: bool = True
    weight_adaptation_rate: float = 0.01
    min_weight_ratio: float = 0.1
    max_weight_ratio: float = 10.0

class FocalLoss(nn.Module):
    """
    🔥 Focal Loss实现
    处理类别不平衡问题
    """
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            predictions: [N, num_classes] 预测概率
            targets: [N] 目标类别标签
        """
        ce_loss = F.cross_entropy(predictions, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class LabelSmoothingCrossEntropy(nn.Module):
    """
    🔥 标签平滑交叉熵损失
    提高泛化能力
    """
    
    def __init__(self, smoothing: float = 0.1, reduction: str = 'mean'):
        super().__init__()
        self.smoothing = smoothing
        self.reduction = reduction
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            predictions: [N, num_classes] 预测logits
            targets: [N] 目标类别标签
        """
        num_classes = predictions.size(1)
        log_preds = F.log_softmax(predictions, dim=1)
        
        # 创建平滑标签
        smooth_targets = torch.zeros_like(log_preds)
        smooth_targets.fill_(self.smoothing / (num_classes - 1))
        smooth_targets.scatter_(1, targets.unsqueeze(1), 1.0 - self.smoothing)
        
        loss = -smooth_targets * log_preds
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss.sum(dim=1)

class TVLoss(nn.Module):
    """
    🔥 Total Variation Loss
    强制空间平滑性
    """
    
    def __init__(self, reduction: str = 'mean'):
        super().__init__()
        self.reduction = reduction
    
    def forward(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """
        Args:
            weight_maps: [batch_size, num_concepts, H, W] 权重图
        """
        batch_size, num_concepts, H, W = weight_maps.shape
        
        # 计算水平和垂直梯度
        tv_h = torch.abs(weight_maps[:, :, 1:, :] - weight_maps[:, :, :-1, :])
        tv_w = torch.abs(weight_maps[:, :, :, 1:] - weight_maps[:, :, :, :-1])
        
        tv_loss = tv_h.mean() + tv_w.mean()
        
        return tv_loss

class SpatialConsistencyLoss(nn.Module):
    """
    🔥 空间一致性损失
    确保相邻区域的权重连续性
    """
    
    def __init__(self, kernel_size: int = 3, sigma: float = 1.0):
        super().__init__()
        self.kernel_size = kernel_size
        self.sigma = sigma
        
        # 创建高斯核
        kernel = self._create_gaussian_kernel(kernel_size, sigma)
        self.register_buffer('gaussian_kernel', kernel)
    
    def _create_gaussian_kernel(self, kernel_size: int, sigma: float) -> torch.Tensor:
        """创建高斯平滑核"""
        coords = torch.arange(kernel_size, dtype=torch.float32)
        coords -= kernel_size // 2
        
        g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        g = g / g.sum()
        
        # 创建2D核
        kernel_2d = g[:, None] * g[None, :]
        kernel_4d = kernel_2d[None, None, :, :]
        
        return kernel_4d
    
    def forward(self, weight_maps: torch.Tensor, positions_3d: torch.Tensor = None) -> torch.Tensor:
        """
        Args:
            weight_maps: [batch_size, num_concepts, H, W] 权重图
            positions_3d: [N, 3] 可选的3D位置信息
        """
        batch_size, num_concepts, H, W = weight_maps.shape
        
        total_loss = 0.0
        
        # 对每个概念通道计算一致性损失
        for c in range(num_concepts):
            concept_weights = weight_maps[:, c:c+1, :, :]  # [batch, 1, H, W]
            
            # 应用高斯平滑
            smoothed_weights = F.conv2d(
                concept_weights, 
                self.gaussian_kernel.repeat(1, 1, 1, 1),
                padding=self.kernel_size // 2
            )
            
            # 计算与原始权重的差异
            consistency_loss = F.mse_loss(concept_weights, smoothed_weights)
            total_loss += consistency_loss
        
        return total_loss / num_concepts

class NeighborhoodConsistencyLoss(nn.Module):
    """
    🔥 邻域一致性损失
    基于3D空间邻域的权重一致性
    """
    
    def __init__(self, k_neighbors: int = 8, distance_threshold: float = 0.1):
        super().__init__()
        self.k_neighbors = k_neighbors
        self.distance_threshold = distance_threshold
    
    def forward(self, 
                concept_weights: torch.Tensor,
                positions_3d: torch.Tensor,
                neighbor_indices: torch.Tensor = None) -> torch.Tensor:
        """
        Args:
            concept_weights: [N, num_concepts] 点的概念权重
            positions_3d: [N, 3] 3D位置
            neighbor_indices: [N, k] 预计算的邻域索引
        """
        N, num_concepts = concept_weights.shape
        device = concept_weights.device
        
        if neighbor_indices is None:
            # 计算邻域索引
            distances = torch.cdist(positions_3d, positions_3d)  # [N, N]
            _, neighbor_indices = torch.topk(distances, k=self.k_neighbors + 1, 
                                           dim=1, largest=False)
            neighbor_indices = neighbor_indices[:, 1:]  # 排除自己
        
        total_loss = 0.0
        
        for i in range(N):
            neighbors = neighbor_indices[i]  # [k]
            
            # 计算距离权重
            neighbor_positions = positions_3d[neighbors]  # [k, 3]
            current_position = positions_3d[i:i+1]  # [1, 3]
            distances = torch.norm(neighbor_positions - current_position, dim=1)  # [k]
            
            # 距离权重（越近权重越大）
            weights = torch.exp(-distances / self.distance_threshold)
            weights = weights / (weights.sum() + 1e-8)
            
            # 当前点的概念权重
            current_weights = concept_weights[i]  # [num_concepts]
            neighbor_weights = concept_weights[neighbors]  # [k, num_concepts]
            
            # 加权平均邻域权重
            weighted_neighbor_weights = torch.sum(
                neighbor_weights * weights.unsqueeze(1), dim=0
            )  # [num_concepts]
            
            # 计算一致性损失
            consistency_loss = F.mse_loss(current_weights, weighted_neighbor_weights)
            total_loss += consistency_loss
        
        return total_loss / N

class BoundarySharpnessLoss(nn.Module):
    """
    🔥 边界锐化损失
    增强概念边界的清晰度
    """
    
    def __init__(self, edge_threshold: float = 0.1):
        super().__init__()
        self.edge_threshold = edge_threshold
        
        # Sobel算子用于边缘检测
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)
        
        self.register_buffer('sobel_x', sobel_x.view(1, 1, 3, 3))
        self.register_buffer('sobel_y', sobel_y.view(1, 1, 3, 3))
    
    def forward(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """
        Args:
            weight_maps: [batch_size, num_concepts, H, W] 权重图
        """
        batch_size, num_concepts, H, W = weight_maps.shape
        total_loss = 0.0
        
        for c in range(num_concepts):
            concept_weights = weight_maps[:, c:c+1, :, :]  # [batch, 1, H, W]
            
            # 计算梯度
            grad_x = F.conv2d(concept_weights, self.sobel_x, padding=1)
            grad_y = F.conv2d(concept_weights, self.sobel_y, padding=1)
            
            # 梯度幅度
            grad_magnitude = torch.sqrt(grad_x ** 2 + grad_y ** 2 + 1e-8)
            
            # 边界区域：梯度幅度大于阈值的区域
            edge_mask = (grad_magnitude > self.edge_threshold).float()
            
            # 鼓励边界区域有更大的梯度（更锐利）
            sharpness_loss = -torch.mean(grad_magnitude * edge_mask)
            total_loss += sharpness_loss
        
        return total_loss / num_concepts

class RCALossFunction(nn.Module):
    """
    🔥 RCA综合损失函数
    
    整合所有损失组件：
    - 分类损失 (Focal Loss + Label Smoothing)
    - 正则化损失 (TV Loss + 熵正则 + L2正则)
    - 一致性损失 (空间一致性 + 邻域一致性 + 时间一致性)
    - 边界损失 (边界锐化)
    """
    
    def __init__(self, config: RCALossConfig = None):
        super().__init__()
        self.config = config or RCALossConfig()
        
        # 初始化各个损失组件
        self._initialize_loss_components()
        
        # 自适应权重
        if self.config.adaptive_weighting:
            self._initialize_adaptive_weights()
        
        # 损失统计
        self.loss_history = {
            'classification': [],
            'regularization': [],
            'consistency': [],
            'boundary': [],
            'total': []
        }
        
        print(f"🎯 RCA综合损失函数初始化完成")
    
    def _initialize_loss_components(self):
        """初始化损失组件"""
        # 分类损失
        if self.config.use_focal_loss:
            self.focal_loss = FocalLoss(
                alpha=self.config.focal_alpha,
                gamma=self.config.focal_gamma
            )
        
        self.label_smoothing_loss = LabelSmoothingCrossEntropy(
            smoothing=self.config.label_smoothing
        )
        
        # 正则化损失
        self.tv_loss = TVLoss()
        
        # 一致性损失
        self.spatial_consistency_loss = SpatialConsistencyLoss()
        self.neighborhood_consistency_loss = NeighborhoodConsistencyLoss()
        
        # 边界损失
        self.boundary_sharpness_loss = BoundarySharpnessLoss()
    
    def _initialize_adaptive_weights(self):
        """初始化自适应权重"""
        self.adaptive_weights = nn.ParameterDict({
            'classification': nn.Parameter(torch.tensor(self.config.classification_weight)),
            'regularization': nn.Parameter(torch.tensor(self.config.regularization_weight)),
            'consistency': nn.Parameter(torch.tensor(self.config.consistency_weight)),
            'boundary': nn.Parameter(torch.tensor(self.config.boundary_loss_weight))
        })
    
    def forward(self, 
                predictions: torch.Tensor,
                targets: Dict[str, torch.Tensor],
                weight_maps: torch.Tensor,
                positions_3d: torch.Tensor,
                network_parameters: List[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        🔥 计算综合损失
        
        Args:
            predictions: [N, num_concepts] RCA网络预测的概念权重
            targets: 目标标签字典
            weight_maps: [batch_size, num_concepts, H, W] 渲染的权重图
            positions_3d: [N, 3] 3D位置
            network_parameters: 网络参数列表（用于L2正则）
            
        Returns:
            loss_dict: 各组件损失字典
        """
        device = predictions.device
        loss_dict = {}
        
        # 获取权重（自适应或固定）
        weights = self._get_current_weights()
        
        # 1. 🔥 分类损失
        classification_loss = self._compute_classification_loss(predictions, targets)
        loss_dict['classification'] = classification_loss
        
        # 2. 🔥 正则化损失
        regularization_loss = self._compute_regularization_loss(
            predictions, weight_maps, network_parameters
        )
        loss_dict['regularization'] = regularization_loss
        
        # 3. 🔥 一致性损失
        consistency_loss = self._compute_consistency_loss(
            predictions, weight_maps, positions_3d
        )
        loss_dict['consistency'] = consistency_loss
        
        # 4. 🔥 边界损失
        boundary_loss = self._compute_boundary_loss(weight_maps)
        loss_dict['boundary'] = boundary_loss
        
        # 5. 🔥 总损失
        total_loss = (
            weights['classification'] * classification_loss +
            weights['regularization'] * regularization_loss +
            weights['consistency'] * consistency_loss +
            weights['boundary'] * boundary_loss
        )
        loss_dict['total'] = total_loss
        
        # 更新损失历史
        self._update_loss_history(loss_dict)
        
        # 自适应权重更新
        if self.config.adaptive_weighting:
            self._update_adaptive_weights(loss_dict)
        
        return loss_dict
    
    def _compute_classification_loss(self, 
                                    predictions: torch.Tensor,
                                    targets: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算分类损失"""
        concept_ids = targets['concept_ids']  # [N]
        
        if self.config.use_focal_loss:
            # 使用Focal Loss
            focal_loss = self.focal_loss(predictions, concept_ids)
            return focal_loss
        else:
            # 使用标签平滑交叉熵
            smooth_loss = self.label_smoothing_loss(predictions, concept_ids)
            return smooth_loss
    
    def _compute_regularization_loss(self, 
                                    predictions: torch.Tensor,
                                    weight_maps: torch.Tensor,
                                    network_parameters: List[torch.Tensor] = None) -> torch.Tensor:
        """计算正则化损失"""
        total_reg_loss = torch.tensor(0.0, device=predictions.device)
        
        # TV损失（空间平滑性）
        if weight_maps is not None:
            tv_loss = self.tv_loss(weight_maps)
            total_reg_loss += self.config.tv_loss_weight * tv_loss
        
        # 熵正则化（避免过度自信）
        pred_probs = F.softmax(predictions, dim=1)
        entropy = -torch.sum(pred_probs * torch.log(pred_probs + 1e-8), dim=1).mean()
        entropy_reg = -self.config.entropy_regularization_weight * entropy
        total_reg_loss += entropy_reg
        
        # L2正则化（网络参数）
        if network_parameters is not None:
            l2_reg = torch.tensor(0.0, device=predictions.device)
            for param in network_parameters:
                l2_reg += torch.norm(param) ** 2
            total_reg_loss += self.config.l2_regularization_weight * l2_reg
        
        return total_reg_loss
    
    def _compute_consistency_loss(self, 
                                 predictions: torch.Tensor,
                                 weight_maps: torch.Tensor,
                                 positions_3d: torch.Tensor) -> torch.Tensor:
        """计算一致性损失"""
        total_consistency_loss = torch.tensor(0.0, device=predictions.device)
        
        # 空间一致性损失
        if weight_maps is not None:
            spatial_loss = self.spatial_consistency_loss(weight_maps, positions_3d)
            total_consistency_loss += self.config.spatial_consistency_weight * spatial_loss
        
        # 邻域一致性损失
        if positions_3d is not None:
            pred_weights = F.softmax(predictions, dim=1)
            neighborhood_loss = self.neighborhood_consistency_loss(pred_weights, positions_3d)
            total_consistency_loss += self.config.spatial_consistency_weight * neighborhood_loss
        
        return total_consistency_loss
    
    def _compute_boundary_loss(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """计算边界损失"""
        if weight_maps is not None:
            boundary_loss = self.boundary_sharpness_loss(weight_maps)
            return boundary_loss
        else:
            return torch.tensor(0.0, device=weight_maps.device if weight_maps is not None else 'cpu')
    
    def _get_current_weights(self) -> Dict[str, torch.Tensor]:
        """获取当前损失权重"""
        if self.config.adaptive_weighting:
            # 确保权重在合理范围内
            weights = {}
            for key, weight in self.adaptive_weights.items():
                clamped_weight = torch.clamp(
                    weight, 
                    self.config.min_weight_ratio, 
                    self.config.max_weight_ratio
                )
                weights[key] = clamped_weight
            return weights
        else:
            # 使用固定权重
            return {
                'classification': self.config.classification_weight,
                'regularization': self.config.regularization_weight,
                'consistency': self.config.consistency_weight,
                'boundary': self.config.boundary_loss_weight
            }
    
    def _update_loss_history(self, loss_dict: Dict[str, torch.Tensor]):
        """更新损失历史"""
        for key, loss in loss_dict.items():
            if key in self.loss_history:
                self.loss_history[key].append(loss.item())
                
                # 保持历史长度
                if len(self.loss_history[key]) > 1000:
                    self.loss_history[key] = self.loss_history[key][-1000:]
    
    def _update_adaptive_weights(self, loss_dict: Dict[str, torch.Tensor]):
        """更新自适应权重"""
        if len(self.loss_history['total']) < 10:
            return  # 需要足够的历史数据
        
        # 基于损失趋势调整权重
        for key in ['classification', 'regularization', 'consistency', 'boundary']:
            if key in loss_dict and key in self.adaptive_weights:
                recent_losses = self.loss_history[key][-10:]
                
                # 计算损失趋势
                if len(recent_losses) >= 2:
                    trend = recent_losses[-1] - recent_losses[-5] if len(recent_losses) >= 5 else 0
                    
                    # 如果损失下降缓慢，增加权重；如果下降太快，减少权重
                    if trend > 0:  # 损失在增加
                        self.adaptive_weights[key].data *= (1 + self.config.weight_adaptation_rate)
                    elif trend < -0.01:  # 损失下降很快
                        self.adaptive_weights[key].data *= (1 - self.config.weight_adaptation_rate)
    
    def get_loss_statistics(self) -> Dict[str, Any]:
        """获取损失统计信息"""
        stats = {}
        
        for key, history in self.loss_history.items():
            if len(history) > 0:
                stats[key] = {
                    'current': history[-1],
                    'mean': np.mean(history),
                    'std': np.std(history),
                    'min': np.min(history),
                    'max': np.max(history),
                    'trend': history[-1] - history[-10] if len(history) >= 10 else 0
                }
        
        return stats
    
    def reset_loss_history(self):
        """重置损失历史"""
        for key in self.loss_history:
            self.loss_history[key] = []
    
    def save_loss_curves(self, save_path: str):
        """保存损失曲线数据"""
        try:
            import matplotlib.pyplot as plt
            
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            axes = axes.flatten()
            
            plot_keys = ['classification', 'regularization', 'consistency', 'boundary']
            
            for i, key in enumerate(plot_keys):
                if key in self.loss_history and len(self.loss_history[key]) > 0:
                    axes[i].plot(self.loss_history[key])
                    axes[i].set_title(f'{key.capitalize()} Loss')
                    axes[i].set_xlabel('Iteration')
                    axes[i].set_ylabel('Loss')
                    axes[i].grid(True)
            
            plt.tight_layout()
            plt.savefig(save_path)
            plt.close()
            
            print(f"✅ 损失曲线已保存: {save_path}")
            
        except Exception as e:
            print(f"❌ 保存损失曲线失败: {str(e)}")

# 使用示例
if __name__ == "__main__":
    # 配置损失函数
    config = RCALossConfig(
        classification_weight=1.0,
        use_focal_loss=True,
        regularization_weight=0.1,
        consistency_weight=0.5,
        adaptive_weighting=True
    )
    
    # 创建损失函数
    loss_fn = RCALossFunction(config)
    
    # 模拟数据
    batch_size = 64
    num_concepts = 3
    H, W = 128, 128
    
    predictions = torch.randn(batch_size, num_concepts, requires_grad=True)
    targets = {
        'concept_ids': torch.randint(0, num_concepts, (batch_size,))
    }
    weight_maps = torch.rand(1, num_concepts, H, W, requires_grad=True)
    positions_3d = torch.randn(batch_size, 3)
    
    # 计算损失
    loss_dict = loss_fn(predictions, targets, weight_maps, positions_3d)
    
    print("损失计算结果:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value.item():.6f}")
    
    # 反向传播测试
    loss_dict['total'].backward()
    print("✅ 反向传播测试通过")