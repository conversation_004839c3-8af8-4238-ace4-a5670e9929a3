<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_rotate_vector</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_rotate_vector<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00142.html" title="GLM_GTX_rotate_vector ">glm/gtx/rotate_vector.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga1a32fceb71962e6160e8af295c91930a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1a32fceb71962e6160e8af295c91930a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#ga1a32fceb71962e6160e8af295c91930a">orientation</a> (vec&lt; 3, T, Q &gt; const &amp;Normal, vec&lt; 3, T, Q &gt; const &amp;Up)</td></tr>
<tr class="memdesc:ga1a32fceb71962e6160e8af295c91930a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a rotation matrix from a normal and a up vector.  <a href="a00356.html#ga1a32fceb71962e6160e8af295c91930a">More...</a><br /></td></tr>
<tr class="separator:ga1a32fceb71962e6160e8af295c91930a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab64a67b52ff4f86c3ba16595a5a25af6"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab64a67b52ff4f86c3ba16595a5a25af6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#gab64a67b52ff4f86c3ba16595a5a25af6">rotate</a> (vec&lt; 2, T, Q &gt; const &amp;v, T const &amp;angle)</td></tr>
<tr class="memdesc:gab64a67b52ff4f86c3ba16595a5a25af6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a two dimensional vector.  <a href="a00356.html#gab64a67b52ff4f86c3ba16595a5a25af6">More...</a><br /></td></tr>
<tr class="separator:gab64a67b52ff4f86c3ba16595a5a25af6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1ba501ef83d1a009a17ac774cc560f21"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1ba501ef83d1a009a17ac774cc560f21"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#ga1ba501ef83d1a009a17ac774cc560f21">rotate</a> (vec&lt; 3, T, Q &gt; const &amp;v, T const &amp;angle, vec&lt; 3, T, Q &gt; const &amp;normal)</td></tr>
<tr class="memdesc:ga1ba501ef83d1a009a17ac774cc560f21"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a three dimensional vector around an axis.  <a href="a00356.html#ga1ba501ef83d1a009a17ac774cc560f21">More...</a><br /></td></tr>
<tr class="separator:ga1ba501ef83d1a009a17ac774cc560f21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1005f1267ed9c57faa3f24cf6873b961"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1005f1267ed9c57faa3f24cf6873b961"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#ga1005f1267ed9c57faa3f24cf6873b961">rotate</a> (vec&lt; 4, T, Q &gt; const &amp;v, T const &amp;angle, vec&lt; 3, T, Q &gt; const &amp;normal)</td></tr>
<tr class="memdesc:ga1005f1267ed9c57faa3f24cf6873b961"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a four dimensional vector around an axis.  <a href="a00356.html#ga1005f1267ed9c57faa3f24cf6873b961">More...</a><br /></td></tr>
<tr class="separator:ga1005f1267ed9c57faa3f24cf6873b961"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga059fdbdba4cca35cdff172a9d0d0afc9"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga059fdbdba4cca35cdff172a9d0d0afc9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#ga059fdbdba4cca35cdff172a9d0d0afc9">rotateX</a> (vec&lt; 3, T, Q &gt; const &amp;v, T const &amp;angle)</td></tr>
<tr class="memdesc:ga059fdbdba4cca35cdff172a9d0d0afc9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a three dimensional vector around the X axis.  <a href="a00356.html#ga059fdbdba4cca35cdff172a9d0d0afc9">More...</a><br /></td></tr>
<tr class="separator:ga059fdbdba4cca35cdff172a9d0d0afc9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4333b1ea8ebf1bd52bc3801a7617398a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4333b1ea8ebf1bd52bc3801a7617398a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#ga4333b1ea8ebf1bd52bc3801a7617398a">rotateX</a> (vec&lt; 4, T, Q &gt; const &amp;v, T const &amp;angle)</td></tr>
<tr class="memdesc:ga4333b1ea8ebf1bd52bc3801a7617398a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a four dimensional vector around the X axis.  <a href="a00356.html#ga4333b1ea8ebf1bd52bc3801a7617398a">More...</a><br /></td></tr>
<tr class="separator:ga4333b1ea8ebf1bd52bc3801a7617398a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaebdc8b054ace27d9f62e054531c6f44d"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaebdc8b054ace27d9f62e054531c6f44d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#gaebdc8b054ace27d9f62e054531c6f44d">rotateY</a> (vec&lt; 3, T, Q &gt; const &amp;v, T const &amp;angle)</td></tr>
<tr class="memdesc:gaebdc8b054ace27d9f62e054531c6f44d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a three dimensional vector around the Y axis.  <a href="a00356.html#gaebdc8b054ace27d9f62e054531c6f44d">More...</a><br /></td></tr>
<tr class="separator:gaebdc8b054ace27d9f62e054531c6f44d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3ce3db0867b7f8efd878ee34f95a623b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3ce3db0867b7f8efd878ee34f95a623b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#ga3ce3db0867b7f8efd878ee34f95a623b">rotateY</a> (vec&lt; 4, T, Q &gt; const &amp;v, T const &amp;angle)</td></tr>
<tr class="memdesc:ga3ce3db0867b7f8efd878ee34f95a623b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a four dimensional vector around the Y axis.  <a href="a00356.html#ga3ce3db0867b7f8efd878ee34f95a623b">More...</a><br /></td></tr>
<tr class="separator:ga3ce3db0867b7f8efd878ee34f95a623b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a048838a03f6249acbacb4dbacf79c4"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5a048838a03f6249acbacb4dbacf79c4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#ga5a048838a03f6249acbacb4dbacf79c4">rotateZ</a> (vec&lt; 3, T, Q &gt; const &amp;v, T const &amp;angle)</td></tr>
<tr class="memdesc:ga5a048838a03f6249acbacb4dbacf79c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a three dimensional vector around the Z axis.  <a href="a00356.html#ga5a048838a03f6249acbacb4dbacf79c4">More...</a><br /></td></tr>
<tr class="separator:ga5a048838a03f6249acbacb4dbacf79c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga923b75c6448161053768822d880702e6"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga923b75c6448161053768822d880702e6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#ga923b75c6448161053768822d880702e6">rotateZ</a> (vec&lt; 4, T, Q &gt; const &amp;v, T const &amp;angle)</td></tr>
<tr class="memdesc:ga923b75c6448161053768822d880702e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate a four dimensional vector around the Z axis.  <a href="a00356.html#ga923b75c6448161053768822d880702e6">More...</a><br /></td></tr>
<tr class="separator:ga923b75c6448161053768822d880702e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b11b18ce824174ea1a5a69ea14e2cee"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8b11b18ce824174ea1a5a69ea14e2cee"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00356.html#ga8b11b18ce824174ea1a5a69ea14e2cee">slerp</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y, T const &amp;a)</td></tr>
<tr class="memdesc:ga8b11b18ce824174ea1a5a69ea14e2cee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns Spherical interpolation between two vectors.  <a href="a00356.html#ga8b11b18ce824174ea1a5a69ea14e2cee">More...</a><br /></td></tr>
<tr class="separator:ga8b11b18ce824174ea1a5a69ea14e2cee"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00142.html" title="GLM_GTX_rotate_vector ">glm/gtx/rotate_vector.hpp</a>&gt; to use the features of this extension. </p>
<p>Function to directly rotate a vector </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga1a32fceb71962e6160e8af295c91930a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::orientation </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Normal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Up</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a rotation matrix from a normal and a up vector. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="gab64a67b52ff4f86c3ba16595a5a25af6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate a two dimensional vector. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="ga1ba501ef83d1a009a17ac774cc560f21"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>normal</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate a three dimensional vector around an axis. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="ga1005f1267ed9c57faa3f24cf6873b961"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>normal</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate a four dimensional vector around an axis. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="ga059fdbdba4cca35cdff172a9d0d0afc9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rotateX </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate a three dimensional vector around the X axis. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="ga4333b1ea8ebf1bd52bc3801a7617398a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::rotateX </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate a four dimensional vector around the X axis. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="gaebdc8b054ace27d9f62e054531c6f44d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rotateY </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate a three dimensional vector around the Y axis. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="ga3ce3db0867b7f8efd878ee34f95a623b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::rotateY </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate a four dimensional vector around the Y axis. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="ga5a048838a03f6249acbacb4dbacf79c4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rotateZ </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate a three dimensional vector around the Z axis. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="ga923b75c6448161053768822d880702e6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::rotateZ </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate a four dimensional vector around the Z axis. </p>
<p>From GLM_GTX_rotate_vector extension. </p>

</div>
</div>
<a class="anchor" id="ga8b11b18ce824174ea1a5a69ea14e2cee"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::slerp </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>a</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns Spherical interpolation between two vectors. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td>A first vector </td></tr>
    <tr><td class="paramname">y</td><td>A second vector </td></tr>
    <tr><td class="paramname">a</td><td>Interpolation factor. The interpolation is defined beyond the range [0, 1].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00356.html" title="Include <glm/gtx/rotate_vector.hpp> to use the features of this extension. ">GLM_GTX_rotate_vector</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
