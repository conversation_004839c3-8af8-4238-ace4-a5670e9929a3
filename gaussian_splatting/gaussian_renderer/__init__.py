#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#


import random
import torch
import math
from diff_gaussian_rasterization import GaussianRasterizationSettings, GaussianRasterizer
from scene.gaussian_model import GaussianModel
from utils.sh_utils import eval_sh

def render(viewpoint_camera, pc : GaussianModel, pipe, bg_color : torch.Tensor, scaling_modifier = 1.0, override_color = None, random_color = False):
    """
    Render the scene. 
    
    Background tensor (bg_color) must be on GPU!
    """
 
    # Create zero tensor. We will use it to make pytorch return gradients of the 2D (screen-space) means
    screenspace_points = torch.zeros_like(pc.get_xyz, dtype=pc.get_xyz.dtype, requires_grad=True, device="cuda") + 0
    try:
        screenspace_points.retain_grad()
    except:
        pass

    # Set up rasterization configuration

    if random_color:
        random_number = random.random()
        if random_number>0.5:
            bg_color = torch.rand_like(bg_color)

        bg_color = torch.rand_like(bg_color)

    tanfovx = math.tan(viewpoint_camera.FoVx * 0.5)
    tanfovy = math.tan(viewpoint_camera.FoVy * 0.5)
    confidence = torch.ones_like(pc._opacity)
    raster_settings = GaussianRasterizationSettings(
        image_height=int(viewpoint_camera.image_height),
        image_width=int(viewpoint_camera.image_width),
        tanfovx=tanfovx,
        tanfovy=tanfovy,
        bg=bg_color,
        scale_modifier=scaling_modifier,
        viewmatrix=viewpoint_camera.world_view_transform,
        projmatrix=viewpoint_camera.full_proj_transform,
        sh_degree=pc.active_sh_degree,
        campos=viewpoint_camera.camera_center,
        prefiltered=False,
        debug=False,
        confidence=confidence
    )

    rasterizer = GaussianRasterizer(raster_settings=raster_settings)

    means3D = pc.get_xyz
    means2D = screenspace_points
    opacity = pc.get_opacity

    # If precomputed 3d covariance is provided, use it. If not, then it will be computed from
    # scaling / rotation by the rasterizer.
    scales = None
    rotations = None
    cov3D_precomp = None
    if pipe.compute_cov3D_python:
        cov3D_precomp = pc.get_covariance(scaling_modifier)
    else:
        scales = pc.get_scaling
        rotations = pc.get_rotation

    # If precomputed colors are provided, use them. Otherwise, if it is desired to precompute colors
    # from SHs in Python, do it. If not, then SH -> RGB conversion will be done by rasterizer.
    shs = None
    colors_precomp = None
    if override_color is None:
        if pipe.convert_SHs_python:
            shs_view = pc.get_features.transpose(1, 2).view(-1, 3, (pc.max_sh_degree+1)**2)
            dir_pp = (pc.get_xyz - viewpoint_camera.camera_center.repeat(pc.get_features.shape[0], 1))
            dir_pp_normalized = dir_pp/dir_pp.norm(dim=1, keepdim=True)
            sh2rgb = eval_sh(pc.active_sh_degree, shs_view, dir_pp_normalized)
            colors_precomp = torch.clamp_min(sh2rgb + 0.5, 0.0)
        else:
            shs = pc.get_features
    else:
        colors_precomp = override_color

    # Rasterize visible Gaussians to image, obtain their radii (on screen). 
    rendered_image, radii, depth, alpha = rasterizer(
        means3D = means3D,
        means2D = means2D,
        shs = shs,
        colors_precomp = colors_precomp,
        opacities = opacity,
        scales = scales,
        rotations = rotations,
        cov3D_precomp = cov3D_precomp)
    return {"render": rendered_image,
            "viewspace_points": screenspace_points,
            "visibility_filter" : radii > 0,
            "radii": radii,
            "depth_3dgs":depth}














# colors_precomp = torch.clamp(sh2rgb + 0.5, 0.0,1)
# len(torch.where(torch.all(colors_precomp == 1, dim=1))[0])

# 🔥 任务4: 用于RCA的2D概念权重图渲染函数
def render_concept_weights(viewpoint_camera, pc: GaussianModel, pipe, scaling_modifier=1.0, target_resolution=None):
    """
    渲染2D概念权重图，用于MultiDreamer3D的RCA机制

    Args:
        viewpoint_camera: 相机对象
        pc: 带有concept_membership的GaussianModel
        pipe: 渲染管线参数
        scaling_modifier: 缩放修饰符
        target_resolution: 目标分辨率 (H, W)，如果为None则使用相机原始分辨率

    Returns:
        dict: {
            "concept_weights": (num_concepts, H, W) 概念权重图
            "concept_weights_normalized": (num_concepts, H, W) 归一化的概念权重图
            "visibility_filter": 可见性过滤器
            "radii": 高斯点的屏幕半径
        }
    """

    # 确定渲染分辨率
    if target_resolution is not None:
        render_height, render_width = target_resolution
    else:
        render_height = int(viewpoint_camera.image_height)
        render_width = int(viewpoint_camera.image_width)

    # Create zero tensor for screen-space points
    screenspace_points = torch.zeros_like(pc.get_xyz, dtype=pc.get_xyz.dtype, requires_grad=True, device="cuda") + 0
    try:
        screenspace_points.retain_grad()
    except:
        pass

    # Set up rasterization configuration
    tanfovx = math.tan(viewpoint_camera.FoVx * 0.5)
    tanfovy = math.tan(viewpoint_camera.FoVy * 0.5)
    confidence = torch.ones_like(pc._opacity)

    # 为每个概念创建单独的渲染结果
    num_concepts = pc.num_concepts
    concept_weight_maps = []

    for concept_id in range(num_concepts):
        # 使用concept_membership作为颜色
        concept_colors = pc._concept_membership[:, concept_id:concept_id+1].repeat(1, 3)  # (N, 3)

        # 设置光栅化参数
        raster_settings = GaussianRasterizationSettings(
            image_height=render_height,
            image_width=render_width,
            tanfovx=tanfovx,
            tanfovy=tanfovy,
            bg=torch.zeros(3, device="cuda"),  # 黑色背景
            scale_modifier=scaling_modifier,
            viewmatrix=viewpoint_camera.world_view_transform,
            projmatrix=viewpoint_camera.full_proj_transform,
            sh_degree=0,  # 不使用球谐函数
            campos=viewpoint_camera.camera_center,
            prefiltered=False,
            debug=False,
            confidence=confidence
        )

        rasterizer = GaussianRasterizer(raster_settings=raster_settings)

        # 渲染当前概念的权重图
        rendered_concept, radii, depth, alpha = rasterizer(
            means3D=pc.get_xyz,
            means2D=screenspace_points,
            shs=None,
            colors_precomp=concept_colors,
            opacities=pc.get_opacity,
            scales=pc.get_scaling,
            rotations=pc.get_rotation,
            cov3D_precomp=None
        )

        # 取红色通道作为概念权重 (因为我们复制了3次相同的值)
        concept_weight_map = rendered_concept[0]  # (H, W)
        concept_weight_maps.append(concept_weight_map)

    # 堆叠所有概念权重图
    concept_weights = torch.stack(concept_weight_maps, dim=0)  # (num_concepts, H, W)

    # 归一化概念权重图 (每个像素的概念权重和为1)
    concept_weights_sum = concept_weights.sum(dim=0, keepdim=True)  # (1, H, W)
    concept_weights_sum = torch.where(concept_weights_sum < 1e-6,
                                     torch.ones_like(concept_weights_sum),
                                     concept_weights_sum)
    concept_weights_normalized = concept_weights / concept_weights_sum  # (num_concepts, H, W)

    return {
        "concept_weights": concept_weights,
        "concept_weights_normalized": concept_weights_normalized,
        "visibility_filter": radii > 0,
        "radii": radii,
        "viewspace_points": screenspace_points
    }

def render_concept_weights_batch(viewpoint_cameras, pc: GaussianModel, pipe, scaling_modifier=1.0, target_resolution=None):
    """
    批量渲染多个相机的概念权重图

    Args:
        viewpoint_cameras: 相机列表
        pc: 带有concept_membership的GaussianModel
        pipe: 渲染管线参数
        scaling_modifier: 缩放修饰符
        target_resolution: 目标分辨率 (H, W)

    Returns:
        dict: {
            "concept_weights_batch": (B, num_concepts, H, W) 批量概念权重图
            "concept_weights_normalized_batch": (B, num_concepts, H, W) 批量归一化概念权重图
            "camera_names": 相机名称列表
        }
    """

    batch_concept_weights = []
    batch_concept_weights_normalized = []
    camera_names = []

    for camera in viewpoint_cameras:
        result = render_concept_weights(camera, pc, pipe, scaling_modifier, target_resolution)

        batch_concept_weights.append(result["concept_weights"])
        batch_concept_weights_normalized.append(result["concept_weights_normalized"])
        camera_names.append(camera.image_name)

    # 堆叠为批量张量
    concept_weights_batch = torch.stack(batch_concept_weights, dim=0)  # (B, num_concepts, H, W)
    concept_weights_normalized_batch = torch.stack(batch_concept_weights_normalized, dim=0)  # (B, num_concepts, H, W)

    return {
        "concept_weights_batch": concept_weights_batch,
        "concept_weights_normalized_batch": concept_weights_normalized_batch,
        "camera_names": camera_names
    }