#!/usr/bin/env python3
"""
🔬 端到端 Concept Membership 初始化测试脚本 (最终版)

使用项目实际数据结构加载点云和相机，并动态加载概念掩码。
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
from PIL import Image
import matplotlib.pyplot as plt
import json # 用于加载 cameras.json

# 添加gaussian_splatting路径
sys.path.append('.') # 假设脚本在Infusion根目录运行
sys.path.append('./gaussian_splatting')
from scene import Scene, GaussianModel
from scene.cameras import Camera # 导入Camera类
from gaussian_splatting.gaussian_renderer import render as gaussian_render_func
from gaussian_splatting.utils.camera_utils import cameraList_from_camInfos #, camera_to_JSON, get_camera_lists_from_hf_dataset # 暂时不使用这些复杂加载器
from gaussian_splatting.utils.general_utils import safe_state
from gaussian_splatting.arguments import ModelParams, PipelineParams, OptimizationParams
from gaussian_splatting.utils.graphics_utils import BasicPointCloud
import argparse

class EndToEndConceptInitTest:
    def __init__(self, scene_base_path="output/garden_incomplete", concept_masks_base_path="stage1_results/concept_masks_expanded"):
        self.scene_base_path = Path(scene_base_path)
        self.concept_masks_base_path = Path(concept_masks_base_path) # 新增参数
        self.test_output_dir = self.scene_base_path / "test_e2e_concept_init_results"
        self.test_output_dir.mkdir(exist_ok=True)
        self.num_concepts = 3 # 与GaussianModel中一致

    def load_gaussian_model_from_ply(self, ply_path_str, sh_degree=3):
        """从给定的PLY文件创建GaussianModel实例"""
        print(f"💾 加载点云并创建高斯模型从: {ply_path_str}")
        ply_path = Path(ply_path_str)

        if not ply_path.exists():
            print(f"   ❌错误: PLY文件 {ply_path} 不存在!")
            # 创建一个非常小且简单的随机点云作为备用，以便脚本至少能运行
            num_points = 10
            points = np.random.randn(num_points, 3) * 0.1 
            colors = np.random.randint(0, 256, size=(num_points, 3), dtype=np.uint8)
            pcd = BasicPointCloud(points=points, colors=colors/255.0, normals=np.zeros_like(points))
            print(f"   ⚠️ 使用了包含 {num_points} 个点的随机点云作为备用。")
        else:
            from plyfile import PlyData
            try:
                plydata = PlyData.read(str(ply_path))
                vertices = plydata['vertex']
                points = np.vstack([vertices['x'], vertices['y'], vertices['z']]).T
                colors = np.vstack([vertices['red'], vertices['green'], vertices['blue']]).T / 255.0
                
                try:
                    normals = np.vstack([vertices['nx'], vertices['ny'], vertices['nz']]).T
                except KeyError:
                    normals = np.zeros_like(points)
                    print("   ⚠️ PLY文件中未找到法线，使用零向量代替。")

                pcd = BasicPointCloud(points=points, colors=colors, normals=normals)
                print(f"   ✅ 从 {ply_path.name} 加载了 {pcd.points.shape[0]} 个点")
            except Exception as e:
                print(f"   ❌ 无法读取PLY文件 {ply_path}: {e}")
                print(f"   ⚠️ 回退到创建随机点云。")
                num_points = 10
                points = np.random.randn(num_points, 3) * 0.1 
                colors = np.random.randint(0, 256, size=(num_points, 3), dtype=np.uint8)
                pcd = BasicPointCloud(points=points, colors=colors/255.0, normals=np.zeros_like(points))


        gaussians = GaussianModel(sh_degree=sh_degree)
        
        # 修复：spatial_lr_scale 通常在 ModelParams 中，或者直接提供一个默认值
        # 如果您希望它可配置，应从 ModelParams 中获取
        # 或者，如果只需要一个固定值，直接使用 0.002 或 0.0001
        # 这里我们直接提供一个默认值
        spatial_lr_scale = 0.002 # 常用值，根据您的模型和场景调整

        gaussians.create_from_pcd(pcd, spatial_lr_scale=spatial_lr_scale) # 使用修正后的值
        
        # 为了调用training_setup，我们仍然需要OptimizationParams
        opt_params = OptimizationParams(argparse.ArgumentParser(add_help=False)) 
        gaussians.training_setup(opt_params) 

        print(f"   📦 高斯模型创建完成. 总点数: {gaussians.get_xyz.shape[0]}")
        print(f"   ⚡️ 初始 _concept_membership requires_grad: {gaussians._concept_membership.requires_grad}")
        return gaussians

    def load_cameras_from_scene(self, dataset_split_path="train/ours_30000"):
        """
        从场景文件结构加载相机列表。
        此函数将尝试加载 cameras.json 并结合图片名称推断相机信息。
        """
        print(f"📷 从场景 {self.scene_base_path} 加载相机 (数据划分: {dataset_split_path})...")
        
        # 确保 dataset_split_path 是相对于 scene_base_path 的完整路径
        full_dataset_split_path = self.scene_base_path / dataset_split_path
        
        # 从 output/garden_incomplete/cameras.json 加载相机列表
        # 假设 cameras.json 包含所有相机的基本信息
        cameras_json_path = self.scene_base_path / "cameras.json"

        if not cameras_json_path.exists():
            print(f"   ❌错误: cameras.json 文件 {cameras_json_path} 不存在!")
            return self._create_dummy_cameras()
        
        try:
            with open(cameras_json_path, 'r') as f:
                camera_infos = json.load(f)
        except Exception as e:
            print(f"   ❌错误: 解析 cameras.json 失败: {e}")
            return self._create_dummy_cameras()

        cameras_list = []
        print(f"   共有 {len(camera_infos)} 个相机条目在 cameras.json 中。")

        # 遍历 cameras.json 中的每个相机信息
        for cam_info in camera_infos:
            try:
                # 获取图像文件名，例如 'DSC07956.png'
                image_name = cam_info.get("img_name") 
                if image_name is None:
                    # 如果没有img_name，尝试其他键或跳过
                    print(f"   ⚠️ 相机条目缺少 'img_name'，跳过。")
                    continue
                
                # 检查图像是否存在于当前数据集划分中 (例如 train/ours_30000/gt/)
                # 这取决于您的图像实际存储在哪里
                # 假设图像在 {dataset_split_path}/gt/{image_name}
                image_file_path = full_dataset_split_path / "gt" / image_name
                if not image_file_path.exists():
                    # print(f"   ⚠️ 图像文件 {image_file_path} 不存在，跳过此相机。")
                    continue # 跳过没有对应图像的相机

                # 获取相机姿态 (世界到相机矩阵的逆矩阵，即相机到世界 c2w)
                # 您可能需要从 c2w/ 目录下加载，或者直接从 cam_info 获取
                # 这里假设cam_info包含rotation和position，或者您可以从文件加载
                R_np = np.array(cam_info['rotation']) if 'rotation' in cam_info else None
                T_np = np.array(cam_info['position']) if 'position' in cam_info else None

                if R_np is None or T_np is None:
                    # 尝试从 c2w 文件加载 (例如 /c2w/DSC07956.txt)
                    c2w_file_path = full_dataset_split_path / "c2w" / f"{Path(image_name).stem}.txt"
                    if c2w_file_path.exists():
                        c2w_matrix = np.loadtxt(c2w_file_path)
                        # 将c2w转换为w2c
                        R_np = c2w_matrix[:3, :3].T 
                        T_np = -R_np @ c2w_matrix[:3, 3] # T_w2c = -R_w2c @ T_c2w
                    else:
                        print(f"   ⚠️ 相机 {image_name}: 未找到姿态信息 (c2w文件或cam_info)。")
                        continue # 跳过没有姿态的相机

                # 获取相机内参
                fx = cam_info.get('fx')
                fy = cam_info.get('fy')
                cx = cam_info.get('cx')
                cy = cam_info.get('cy')
                H = cam_info['height']
                W = cam_info['width']
                
                if fx is None or fy is None or cx is None or cy is None:
                    # 尝试从 intri 文件加载 (例如 /intri/DSC07956.txt)
                    intri_file_path = full_dataset_split_path / "intri" / f"{Path(image_name).stem}.txt"
                    if intri_file_path.exists():
                        intri_matrix = np.loadtxt(intri_file_path)
                        fx, fy = intri_matrix[0, 0], intri_matrix[1, 1]
                        cx, cy = intri_matrix[0, 2], intri_matrix[1, 2]
                    else:
                        print(f"   ⚠️ 相机 {image_name}: 未找到内参信息 (intri文件或cam_info)。")
                        continue # 跳过没有内参的相机

                # 将内参转换为FoV (3DGS Camera类期望FoV)
                FoVx = 2 * np.arctan(0.5 * W / fx)
                FoVy = 2 * np.arctan(0.5 * H / fy)

                # 修复：移除 image_width 和 image_height 参数
                camera = Camera(colmap_id=cam_info.get("id", len(cameras_list)), 
                                R=R_np, T=T_np, FoVx=FoVx, FoVy=FoVy, 
                                image=torch.zeros((3, H, W)), # 传入一个具有正确 H, W 的虚拟图像
                                gt_alpha_mask=None,
                                image_name=image_name, uid=len(cameras_list), data_device="cuda")
                cameras_list.append(camera)
            except Exception as e:
                print(f"   ❌ 加载相机 {image_name} 时出错: {e}")
                import traceback
                traceback.print_exc() # 打印完整堆栈，帮助调试
        
        print(f"   ✅ 成功加载/创建 {len(cameras_list)} 个相机对象。")
        if not cameras_list:
             print(f"   ⚠️ 未能加载任何相机，将使用默认的测试相机列表。")
             return self._create_dummy_cameras()

        return cameras_list

    def _create_dummy_cameras(self, num_cameras=3):
        """创建一组硬编码的测试相机作为备用"""
        print(f"   ⚠️ 回退到创建 {num_cameras} 个虚拟测试相机...")
        cameras = []
        # 相机位置示例
        positions = [
            torch.tensor([0.0, 0.0, 2.0]),
            torch.tensor([2.0, 0.0, 0.0]),
            torch.tensor([0.0, 1.5, 1.5]),
        ]
        look_at = torch.tensor([0.0, 0.0, 0.0], device="cuda")

        # 借用之前测试脚本中的矩阵构建方法 (这里为简化，直接硬编码)
        # 实际项目中应将这些辅助方法放在更合适的工具类中
        def build_view_matrix_local(camera_center, look_at_target, up_vector):
            cam_z = torch.nn.functional.normalize(camera_center - look_at_target, p=2, dim=0)
            cam_x = torch.nn.functional.normalize(torch.cross(up_vector, cam_z), p=2, dim=0)
            cam_y = torch.nn.functional.normalize(torch.cross(cam_z, cam_x), p=2, dim=0)
            R = torch.stack([cam_x, cam_y, cam_z], dim=0)
            t = -torch.matmul(R, camera_center)
            view_matrix = torch.eye(4, device="cuda")
            view_matrix[:3, :3] = R
            view_matrix[:3, 3] = t
            return view_matrix
        
        def build_projection_matrix_local(FoVx, FoVy, image_height, image_width):
            near, far = 0.1, 100.0
            H, W = image_height, image_width
            focal_y = H / (2.0 * np.tan(FoVy * 0.5))
            focal_x = W / (2.0 * np.tan(FoVx * 0.5))
            projection_matrix = torch.zeros((4, 4), device="cuda")
            projection_matrix[0, 0] = 2.0 * focal_x / W
            projection_matrix[1, 1] = 2.0 * focal_y / H
            projection_matrix[2, 2] = -(far + near) / (far - near)
            projection_matrix[2, 3] = -2.0 * far * near / (far - near)
            projection_matrix[3, 2] = -1.0
            return projection_matrix

        for i in range(num_cameras):
            pos = positions[i % len(positions)]
            cam_center = pos.cuda()
            
            # 修复：移除 image_width 和 image_height 参数
            # 确保传入一个具有正确 H, W 的虚拟图像
            dummy_H, dummy_W = 256, 256 # 虚拟图像尺寸
            
            temp_cam = Camera(colmap_id=i, 
                              R=build_view_matrix_local(cam_center, look_at, torch.tensor([0.0, 1.0, 0.0]).cuda())[:3,:3].cpu().numpy(), 
                              T=build_view_matrix_local(cam_center, look_at, torch.tensor([0.0, 1.0, 0.0]).cuda())[:3,3].cpu().numpy(), 
                              FoVx=1.2, FoVy=1.2, 
                              image=torch.zeros((3, dummy_H, dummy_W)), # 传入虚拟图像
                              gt_alpha_mask=None,
                              image_name=f"dummy_cam_{i:03d}.png", uid=i, data_device="cuda")
            
            # 手动设置 world_view_transform 和 projection_matrix
            # 确保这些矩阵是正确的，因为 Camera 类的构造函数中也会根据 R, T, FoV 计算
            # 这里的设置是为了确保它们在 Camera 类外部被正确访问时的值
            temp_cam.world_view_transform = build_view_matrix_local(cam_center, look_at, torch.tensor([0.0, 1.0, 0.0]).cuda())
            temp_cam.projection_matrix = build_projection_matrix_local(1.2, 1.2, dummy_H, dummy_W)
            temp_cam.full_proj_transform = (temp_cam.projection_matrix @ temp_cam.world_view_transform).T

            cameras.append(temp_cam)
        print(f"   ✅ 创建了 {len(cameras)} 个虚拟相机。")
        return cameras

    def run_test(self):
        print("🚀 开始端到端Concept Membership初始化测试 (最终版)...")
        print("=" * 60)

        # 1. 加载点云并创建高斯模型
        # 使用您提供的图片中的路径
        ply_file_to_load = str(self.scene_base_path / "input.ply")
        gaussians = self.load_gaussian_model_from_ply(ply_file_to_load, sh_degree=3)
        
        if gaussians.get_xyz.shape[0] == 0:
            print("   ❌ 错误: 高斯模型为空，无法继续测试。")
            return

        print("\n" + "=" * 60)
        # 2. 加载相机 (使用 'train/ours_30000' 作为数据划分，您可以根据需要调整)
        cameras = self.load_cameras_from_scene(dataset_split_path="train/ours_30000")
        if not cameras:
            print("   ❌ 错误: 未能加载相机，无法继续测试。")
            return

        print("\n" + "=" * 60)
        # 3. 调用 initialize_concept_memberships_from_masks
        print("⚙️ 调用 initialize_concept_memberships_from_masks...")
        
        # render_func 参数在 initialize_concept_memberships_from_masks 内部已不再用于投影
        # 我们传入一个原始的 render 函数作为占位符，以防将来需要其他渲染结果。
        # 如果您确定不需要任何渲染结果，可以传入一个 None 或虚拟函数。
        init_stats = gaussians.initialize_concept_memberships_from_masks(
            self.concept_masks_base_path, # 传入概念掩码根目录
            cameras,
            gaussian_render_func # 传入实际的渲染函数，或 None
        )

        print("\n" + "=" * 60)
        # 4. 检查结果
        print("📊 检查初始化结果...")
        if init_stats:
            print(f"   📈 初始化统计:")
            for key, value in init_stats.items():
                if isinstance(value, dict):
                    print(f"      {key}:")
                    for k_inner, v_inner in value.items():
                        print(f"         {k_inner}: {v_inner}")
                else:
                    print(f"      {key}: {value}")
            
            print(f"   ⚡️ 更新后 _concept_membership requires_grad: {gaussians._concept_membership.requires_grad}")

            num_sample_points = min(10, gaussians.get_xyz.shape[0])
            if num_sample_points > 0:
                sample_indices = torch.randperm(gaussians.get_xyz.shape[0])[:num_sample_points]
                print(f"\n   🔬 抽样检查 {num_sample_points} 个点的隶属度:")
                for i in sample_indices:
                    membership = gaussians._concept_membership[i].cpu().numpy()
                    dominant_concept = np.argmax(membership)
                    confidence = membership[dominant_concept]
                    print(f"      点 {i.item()}: Membership {membership}, Dominant: {dominant_concept} (Conf: {confidence:.3f})")
            
            # 可视化主导概念
            if cameras:
                self.visualize_dominant_concepts(gaussians, cameras[0])
            else:
                print("   ⚠️ 没有相机可供可视化。")

        else:
            print("   ❌ 初始化失败或未返回统计信息。")

        print(f"\n🎉 端到端测试完成！结果保存在: {self.test_output_dir}")

    def visualize_dominant_concepts(self, gaussians, camera):
        """可视化主导概念ID (投影到第一个相机视图)"""
        print("🎨 可视化主导概念...")
        if gaussians.get_xyz.shape[0] == 0:
            print("   ⚠️ 模型中没有点可供可视化。")
            return
        try:
            xyz_world = gaussians.get_xyz.detach()
            dominant_ids = gaussians.get_dominant_concept_id.detach().cpu().numpy()
            
            pixel_coords, visible_mask = gaussians.project_points_to_camera(camera)
            pixel_coords = pixel_coords.detach().cpu().numpy()
            visible_mask = visible_mask.detach().cpu().numpy()

            if visible_mask.sum() == 0:
                print("   ⚠️ 没有可见点可供可视化。")
                return

            visible_pixels = pixel_coords[visible_mask]
            visible_ids = dominant_ids[visible_mask]

            H, W = camera.image_height, camera.image_width
            image = np.ones((H, W, 3), dtype=np.float32) * 0.8 

            concept_colors = [
                [0.2, 0.2, 0.8], # 背景 (蓝色)
                [0.8, 0.2, 0.2], # 修复 (红色)
                [0.2, 0.8, 0.2], # 边界 (绿色)
            ]

            for i in range(len(visible_pixels)):
                x, y = int(visible_pixels[i, 0]), int(visible_pixels[i, 1])
                concept_idx = visible_ids[i]
                if 0 <= y < H and 0 <= x < W:
                    image[y, x] = concept_colors[concept_idx % len(concept_colors)]
            
            plt.figure(figsize=(6,6))
            plt.imshow(image)
            plt.title(f"Dominant Concepts (View from Camera {getattr(camera, 'uid', 'N/A')})")
            plt.axis('off')
            save_path = self.test_output_dir / f"dominant_concepts_cam{getattr(camera, 'uid', 'N/A')}.png"
            plt.savefig(save_path, dpi=100, bbox_inches='tight')
            plt.close()
            print(f"   💾 主导概念可视化已保存: {save_path}")

        except Exception as e:
            print(f"   ❌ 可视化失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    parser = argparse.ArgumentParser(description="端到端Concept Membership初始化测试脚本")
    parser.add_argument("--scene_path", type=str, default="output/garden_incomplete", 
                        help="场景的基础路径 (例如: output/garden_incomplete)")
    parser.add_argument("--concept_masks_path", type=str, default="stage1_results/concept_masks_expanded",
                        help="概念掩码的根路径 (例如: stage1_results/concept_masks_expanded)")
    args = parser.parse_args()

    print(f"🔥 开始测试，场景路径: {args.scene_path}")
    print(f"🔥 概念掩码路径: {args.concept_masks_path}")
    
    test_runner = EndToEndConceptInitTest(
        scene_base_path=args.scene_path,
        concept_masks_base_path=args.concept_masks_path
    )
    test_runner.run_test()

if __name__ == "__main__":
    main() 