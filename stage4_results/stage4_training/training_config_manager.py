# -*- coding: utf-8 -*-
"""
InFusion 阶段4: 训练配置管理器
统一管理RCA训练的所有配置参数和实验设置
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict, field
from pathlib import Path
import torch

@dataclass
class DataConfig:
    """数据配置"""
    # 数据路径
    stage3_output_dir: str = ""
    cache_dir: str = "./cache/training_data"
    
    # 采样配置
    points_per_batch: int = 8192
    concept_balance_strategy: str = "balanced"  # "balanced", "weighted", "focal"
    hard_mining_ratio: float = 0.3
    
    # 数据增强
    geometric_augmentation: bool = True
    noise_augmentation: bool = True
    occlusion_augmentation: bool = True
    
    # 邻域配置
    knn_neighbors: int = 16
    context_radius: float = 0.1

@dataclass
class ModelConfig:
    """模型配置"""
    # RCA网络架构
    hidden_dims: List[int] = field(default_factory=lambda: [256, 512, 256])
    num_attention_heads: int = 8
    num_concepts: int = 3
    dropout_rate: float = 0.1
    activation: str = "relu"  # "relu", "gelu", "silu"
    
    # 权重渲染配置
    image_height: int = 512
    image_width: int = 512
    render_num_samples: int = 256

@dataclass
class LossConfig:
    """损失函数配置"""
    # 主要损失权重
    classification_weight: float = 1.0
    regularization_weight: float = 0.1
    consistency_weight: float = 0.5
    boundary_loss_weight: float = 0.2
    
    # 分类损失配置
    use_focal_loss: bool = True
    focal_alpha: float = 0.25
    focal_gamma: float = 2.0
    label_smoothing: float = 0.1
    
    # 正则化配置
    tv_loss_weight: float = 0.05
    entropy_regularization_weight: float = 0.02
    l2_regularization_weight: float = 0.001
    
    # 一致性配置
    spatial_consistency_weight: float = 0.3
    temporal_consistency_weight: float = 0.2
    multi_view_consistency_weight: float = 0.4
    
    # 自适应权重
    adaptive_weighting: bool = True
    weight_adaptation_rate: float = 0.01

@dataclass
class OptimizerConfig:
    """优化器配置"""
    # 学习率配置
    rca_learning_rate: float = 0.001
    gaussians_learning_rate: float = 0.0001
    cism_learning_rate: float = 0.0005
    
    # 优化器类型
    optimizer_type: str = "adamw"
    weight_decay: float = 0.01
    beta1: float = 0.9
    beta2: float = 0.999
    eps: float = 1e-8
    
    # 学习率调度
    scheduler_type: str = "cosine"
    step_size: int = 1000
    gamma: float = 0.5
    patience: int = 100
    
    # 梯度控制
    gradient_clip_norm: float = 1.0
    gradient_accumulation_steps: int = 1
    
    # 预热设置
    warmup_steps: int = 500
    warmup_factor: float = 0.1

@dataclass
class TrainingConfig:
    """训练配置"""
    # 训练周期
    max_epochs: int = 1000
    max_iterations: int = 100000
    
    # 批次配置
    batch_size: int = 1
    num_workers: int = 4
    
    # 验证配置
    validation_interval: int = 100
    validation_split: float = 0.1
    
    # 保存配置
    save_interval: int = 500
    keep_n_checkpoints: int = 5
    
    # 早停配置
    early_stopping_patience: int = 200
    early_stopping_min_delta: float = 1e-6
    
    # 混合精度
    use_mixed_precision: bool = True
    loss_scale: float = 65536.0
    
    # 设备配置
    device: str = "cuda"
    multi_gpu: bool = False
    gpu_ids: List[int] = field(default_factory=lambda: [0])

@dataclass
class ExperimentConfig:
    """实验配置"""
    # 实验信息
    experiment_name: str = "rca_training"
    experiment_version: str = "v1.0"
    description: str = "RCA网络端到端训练实验"
    
    # 输出路径
    output_dir: str = "./outputs/stage4_training"
    log_dir: str = "./logs/stage4_training"
    checkpoint_dir: str = "./checkpoints/stage4_training"
    
    # 随机种子
    seed: int = 42
    deterministic: bool = True
    
    # 日志配置
    log_level: str = "INFO"
    log_to_file: bool = True
    log_to_console: bool = True
    
    # 可视化配置
    save_visualizations: bool = True
    visualization_interval: int = 200
    max_visualization_samples: int = 8

@dataclass
class Stage4TrainingConfig:
    """阶段4完整训练配置"""
    # 子配置组件
    data: DataConfig = field(default_factory=DataConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    loss: LossConfig = field(default_factory=LossConfig)
    optimizer: OptimizerConfig = field(default_factory=OptimizerConfig)
    training: TrainingConfig = field(default_factory=TrainingConfig)
    experiment: ExperimentConfig = field(default_factory=ExperimentConfig)
    
    # 全局配置
    config_version: str = "1.0"
    created_time: str = ""

class TrainingConfigManager:
    """
    🔥 训练配置管理器
    
    功能:
    - 统一管理所有训练配置
    - 支持配置文件加载/保存
    - 提供配置验证和默认值处理
    - 实现配置的继承和覆盖机制
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Args:
            config_path: 配置文件路径（可选）
        """
        self.config = Stage4TrainingConfig()
        
        # 设置创建时间
        import datetime
        self.config.created_time = datetime.datetime.now().isoformat()
        
        # 如果提供了配置文件路径，则加载配置
        if config_path and os.path.exists(config_path):
            self.load_config(config_path)
        
        print(f"🎯 训练配置管理器初始化完成")
    
    def load_config(self, config_path: str) -> bool:
        """
        🔥 从文件加载配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            print(f"📂 加载配置文件: {config_path}")
            
            config_path = Path(config_path)
            
            if config_path.suffix == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
            elif config_path.suffix in ['.yml', '.yaml']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_dict = yaml.safe_load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
            
            # 更新配置
            self._update_config_from_dict(config_dict)
            
            # 验证配置
            self.validate_config()
            
            print(f"✅ 配置文件加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件加载失败: {str(e)}")
            return False
    
    def save_config(self, save_path: str, format: str = 'json') -> bool:
        """
        🔥 保存配置到文件
        
        Args:
            save_path: 保存路径
            format: 保存格式 ('json' 或 'yaml')
            
        Returns:
            bool: 保存是否成功
        """
        try:
            print(f"💾 保存配置文件: {save_path}")
            
            # 创建保存目录
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 转换为字典
            config_dict = asdict(self.config)
            
            if format == 'json':
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
            elif format in ['yml', 'yaml']:
                with open(save_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_dict, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
            else:
                raise ValueError(f"不支持的保存格式: {format}")
            
            print(f"✅ 配置文件保存成功")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件保存失败: {str(e)}")
            return False
    
    def _update_config_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        # 更新各个子配置
        if 'data' in config_dict:
            self._update_dataclass_from_dict(self.config.data, config_dict['data'])
        
        if 'model' in config_dict:
            self._update_dataclass_from_dict(self.config.model, config_dict['model'])
        
        if 'loss' in config_dict:
            self._update_dataclass_from_dict(self.config.loss, config_dict['loss'])
        
        if 'optimizer' in config_dict:
            self._update_dataclass_from_dict(self.config.optimizer, config_dict['optimizer'])
        
        if 'training' in config_dict:
            self._update_dataclass_from_dict(self.config.training, config_dict['training'])
        
        if 'experiment' in config_dict:
            self._update_dataclass_from_dict(self.config.experiment, config_dict['experiment'])
        
        # 更新全局配置
        for key in ['config_version', 'created_time']:
            if key in config_dict:
                setattr(self.config, key, config_dict[key])
    
    def _update_dataclass_from_dict(self, target_obj: Any, source_dict: Dict[str, Any]):
        """更新数据类对象"""
        for key, value in source_dict.items():
            if hasattr(target_obj, key):
                setattr(target_obj, key, value)
    
    def validate_config(self) -> bool:
        """
        🔥 验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 验证数据配置
            assert self.config.data.points_per_batch > 0, "points_per_batch must be positive"
            assert self.config.data.concept_balance_strategy in ["balanced", "weighted", "focal"], \
                "Invalid concept_balance_strategy"
            
            # 验证模型配置
            assert len(self.config.model.hidden_dims) > 0, "hidden_dims cannot be empty"
            assert self.config.model.num_concepts > 0, "num_concepts must be positive"
            assert self.config.model.num_attention_heads > 0, "num_attention_heads must be positive"
            
            # 验证训练配置
            assert self.config.training.max_epochs > 0, "max_epochs must be positive"
            assert self.config.training.batch_size > 0, "batch_size must be positive"
            assert 0 < self.config.training.validation_split < 1, "validation_split must be in (0, 1)"
            
            # 验证优化器配置
            assert self.config.optimizer.rca_learning_rate > 0, "rca_learning_rate must be positive"
            assert self.config.optimizer.gaussians_learning_rate > 0, "gaussians_learning_rate must be positive"
            assert self.config.optimizer.optimizer_type in ["adam", "adamw", "sgd"], \
                "Invalid optimizer_type"
            
            # 验证设备配置
            if self.config.training.device == "cuda":
                assert torch.cuda.is_available(), "CUDA not available but specified in config"
            
            print("✅ 配置验证通过")
            return True
            
        except AssertionError as e:
            print(f"❌ 配置验证失败: {str(e)}")
            return False
        except Exception as e:
            print(f"❌ 配置验证错误: {str(e)}")
            return False
    
    def get_config(self) -> Stage4TrainingConfig:
        """获取完整配置"""
        return self.config
    
    def update_config(self, **kwargs):
        """
        🔥 更新配置参数
        
        支持嵌套更新，例如:
        update_config(
            data__points_per_batch=4096,
            training__max_epochs=500,
            optimizer__rca_learning_rate=0.002
        )
        """
        for key, value in kwargs.items():
            if '__' in key:
                # 处理嵌套配置更新
                parts = key.split('__')
                if len(parts) == 2:
                    section, param = parts
                    if hasattr(self.config, section):
                        section_obj = getattr(self.config, section)
                        if hasattr(section_obj, param):
                            setattr(section_obj, param, value)
                            print(f"✅ 更新配置: {section}.{param} = {value}")
                        else:
                            print(f"⚠️  未知参数: {section}.{param}")
                    else:
                        print(f"⚠️  未知配置段: {section}")
                else:
                    print(f"⚠️  无效的嵌套配置键: {key}")
            else:
                # 处理顶级配置更新
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
                    print(f"✅ 更新配置: {key} = {value}")
                else:
                    print(f"⚠️  未知配置键: {key}")
    
    def create_experiment_dirs(self) -> Dict[str, str]:
        """
        🔥 创建实验目录
        
        Returns:
            Dict[str, str]: 创建的目录路径
        """
        dirs = {
            'output': self.config.experiment.output_dir,
            'log': self.config.experiment.log_dir,
            'checkpoint': self.config.experiment.checkpoint_dir,
        }
        
        for name, path in dirs.items():
            os.makedirs(path, exist_ok=True)
            print(f"📁 创建{name}目录: {path}")
        
        return dirs
    
    def setup_logging(self):
        """设置日志配置"""
        import logging
        
        # 创建日志目录
        os.makedirs(self.config.experiment.log_dir, exist_ok=True)
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 根日志器配置
        logger = logging.getLogger()
        logger.setLevel(getattr(logging, self.config.experiment.log_level.upper()))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 控制台处理器
        if self.config.experiment.log_to_console:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 文件处理器
        if self.config.experiment.log_to_file:
            log_file = os.path.join(
                self.config.experiment.log_dir, 
                f"{self.config.experiment.experiment_name}.log"
            )
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        print(f"📝 日志配置完成")
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("\n" + "=" * 60)
        print("🎯 阶段4训练配置摘要")
        print("=" * 60)
        
        print(f"📊 数据配置:")
        print(f"   批次大小: {self.config.data.points_per_batch}")
        print(f"   概念平衡策略: {self.config.data.concept_balance_strategy}")
        print(f"   数据增强: {self.config.data.geometric_augmentation}")
        
        print(f"\n🧠 模型配置:")
        print(f"   隐藏层维度: {self.config.model.hidden_dims}")
        print(f"   注意力头数: {self.config.model.num_attention_heads}")
        print(f"   概念数量: {self.config.model.num_concepts}")
        
        print(f"\n⚖️  损失配置:")
        print(f"   分类权重: {self.config.loss.classification_weight}")
        print(f"   一致性权重: {self.config.loss.consistency_weight}")
        print(f"   使用Focal Loss: {self.config.loss.use_focal_loss}")
        
        print(f"\n🔧 优化器配置:")
        print(f"   RCA学习率: {self.config.optimizer.rca_learning_rate}")
        print(f"   优化器类型: {self.config.optimizer.optimizer_type}")
        print(f"   调度器类型: {self.config.optimizer.scheduler_type}")
        
        print(f"\n🚀 训练配置:")
        print(f"   最大轮次: {self.config.training.max_epochs}")
        print(f"   验证间隔: {self.config.training.validation_interval}")
        print(f"   混合精度: {self.config.training.use_mixed_precision}")
        print(f"   设备: {self.config.training.device}")
        
        print(f"\n🧪 实验配置:")
        print(f"   实验名称: {self.config.experiment.experiment_name}")
        print(f"   输出目录: {self.config.experiment.output_dir}")
        print(f"   随机种子: {self.config.experiment.seed}")
        
        print("=" * 60)

def create_default_config() -> TrainingConfigManager:
    """创建默认配置管理器"""
    return TrainingConfigManager()

def load_config_from_file(config_path: str) -> TrainingConfigManager:
    """从文件加载配置管理器"""
    return TrainingConfigManager(config_path)