#!/usr/bin/env python3
"""
🔥 InFusion-Enhanced 阶段1 - 增强概念初始化器

这个脚本负责：
1. 加载训练好的3DGS模型（30000次迭代结果）
2. 使用高质量概念掩码初始化concept_membership
3. 验证映射质量
4. 保存增强的模型

作者: Claude <PERSON>net 4 (Anthropic)
"""

import os
import sys
import torch
import numpy as np
import json
import argparse
from pathlib import Path
import logging

# 添加gaussian_splatting路径
sys.path.append('./gaussian_splatting')
from scene import Scene, GaussianModel
from gaussian_renderer import render
from utils.general_utils import safe_state
from arguments import ModelParams, PipelineParams

class EnhancedConceptInitializer:
    """增强概念初始化器"""
    
    def __init__(self, 
                 model_path: str,
                 concept_masks_path: str,
                 output_path: str = "stage1_results/enhanced_gaussians"):
        """
        初始化增强概念初始化器
        
        Args:
            model_path: 训练好的3DGS模型路径
            concept_masks_path: 概念掩码路径
            output_path: 输出路径
        """
        self.model_path = Path(model_path)
        self.concept_masks_path = Path(concept_masks_path)
        self.output_path = Path(output_path)
        
        # 创建输出目录
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.output_path / 'concept_initialization.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_gaussian_model(self) -> tuple[GaussianModel, Scene]:
        """加载3DGS模型"""
        self.logger.info("🎯 加载3DGS模型...")

        # 创建模型参数
        parser = argparse.ArgumentParser()
        model_params = ModelParams(parser)

        # 设置模型路径 - 需要指向原始数据路径
        args = argparse.Namespace()
        args.source_path = "data/garden"  # 指向原始数据
        args.model_path = str(self.model_path)
        args.images = "images"
        args.resolution = 4  # 降低分辨率以节省内存
        args.white_background = False
        args.data_device = "cuda"
        args.eval = True
        args.sh_degree = 0  # 降低球谐函数度数以匹配训练模型
        args.unique_image = "nothing"

        # 创建高斯模型
        gaussians = GaussianModel(args.sh_degree)

        # 加载模型
        scene = Scene(model_params.extract(args), gaussians, load_iteration=-1)

        self.logger.info(f"✅ 模型加载完成，高斯点数量: {gaussians.get_xyz.shape[0]}")

        return gaussians, scene
    
    def initialize_concept_memberships(self, gaussians: GaussianModel, scene: Scene) -> dict:
        """初始化concept_membership"""
        self.logger.info("🔥 开始初始化concept_membership...")
        
        # 获取训练相机
        cameras = scene.getTrainCameras()
        self.logger.info(f"📷 找到 {len(cameras)} 个训练相机")
        
        # 调用高斯模型的concept_membership初始化方法
        stats = gaussians.initialize_concept_memberships_from_masks(
            concept_masks_root_path=str(self.concept_masks_path),
            cameras=cameras
        )
        
        self.logger.info("✅ concept_membership初始化完成")
        return stats
    
    def validate_mapping_quality(self, gaussians: GaussianModel) -> dict:
        """验证映射质量"""
        self.logger.info("🔍 开始映射质量验证...")
        
        quality_metrics = {
            'concept_distribution_check': {},
            'confidence_analysis': {},
            'overall_quality_score': 0.0
        }
        
        # 1. 概念分布检查
        dominant_ids = gaussians.get_dominant_concept_id.cpu().numpy()
        concept_confidences = gaussians.get_concept_confidence.cpu().numpy()
        
        unique_concepts, counts = np.unique(dominant_ids, return_counts=True)
        total_points = len(dominant_ids)
        
        for concept_id, count in zip(unique_concepts, counts):
            ratio = count / total_points
            quality_metrics['concept_distribution_check'][int(concept_id)] = {
                'count': int(count),
                'ratio': float(ratio),
                'expected_range': self._get_expected_concept_ratio(concept_id)
            }
        
        # 2. 置信度分析
        for concept_id in unique_concepts:
            mask = dominant_ids == concept_id
            if mask.sum() > 0:
                concept_confidences_subset = concept_confidences[mask]
                quality_metrics['confidence_analysis'][int(concept_id)] = {
                    'mean_confidence': float(np.mean(concept_confidences_subset)),
                    'std_confidence': float(np.std(concept_confidences_subset)),
                    'high_confidence_ratio': float((concept_confidences_subset > 0.5).sum() / len(concept_confidences_subset))
                }
        
        # 3. 计算总体质量分数
        quality_metrics['overall_quality_score'] = self._calculate_overall_quality(quality_metrics)
        
        self.logger.info("✅ 映射质量验证完成")
        return quality_metrics
    
    def _get_expected_concept_ratio(self, concept_id: int) -> str:
        """获取概念的预期比例范围"""
        expected_ranges = {
            0: "85-95% (背景)",
            1: "3-12% (修复)",
            2: "2-5% (边界)"
        }
        return expected_ranges.get(concept_id, "未知")
    
    def _calculate_overall_quality(self, quality_metrics: dict) -> float:
        """计算总体质量分数"""
        score = 0.0
        weight_sum = 0.0
        
        # 概念分布质量 (权重: 0.4)
        if 'concept_distribution_check' in quality_metrics:
            dist_score = 0.0
            for concept_data in quality_metrics['concept_distribution_check'].values():
                ratio = concept_data['ratio']
                if 0.01 <= ratio <= 0.95:  # 基本合理范围
                    dist_score += 1.0
            dist_score /= max(1, len(quality_metrics['concept_distribution_check']))
            score += dist_score * 0.4
            weight_sum += 0.4
        
        # 置信度质量 (权重: 0.6)
        if 'confidence_analysis' in quality_metrics:
            conf_score = 0.0
            for concept_data in quality_metrics['confidence_analysis'].values():
                mean_conf = concept_data['mean_confidence']
                if mean_conf > 0.1:  # 基本置信度阈值
                    conf_score += min(1.0, mean_conf * 2)  # 归一化到[0,1]
            conf_score /= max(1, len(quality_metrics['confidence_analysis']))
            score += conf_score * 0.6
            weight_sum += 0.6
        
        return score / max(weight_sum, 1.0)

    def _convert_to_serializable(self, obj):
        """递归转换numpy类型为Python原生类型，用于JSON序列化"""
        if isinstance(obj, dict):
            return {str(k): self._convert_to_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj

    def save_results(self, gaussians: GaussianModel, stats: dict, quality_metrics: dict):
        """保存结果"""
        self.logger.info("💾 保存结果...")
        
        # 保存增强的高斯模型
        output_ply_path = self.output_path / "gaussians_with_concept_membership_enhanced.ply"
        gaussians.save_ply(str(output_ply_path))
        
        # 保存统计信息 (转换numpy类型为Python原生类型)
        stats_serializable = self._convert_to_serializable(stats)
        stats_path = self.output_path / "concept_initialization_stats.json"
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats_serializable, f, indent=2, ensure_ascii=False)

        # 保存质量评估结果
        quality_serializable = self._convert_to_serializable(quality_metrics)
        quality_path = self.output_path / "mapping_quality_assessment.json"
        with open(quality_path, 'w', encoding='utf-8') as f:
            json.dump(quality_serializable, f, indent=2, ensure_ascii=False)
        
        # 保存详细报告
        report_path = self.output_path / "initialization_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 🔥 InFusion-Enhanced 阶段1 概念初始化报告\n\n")
            f.write(f"## 📊 统计信息\n\n")
            f.write(f"- **总映射点数**: {stats['total_mapped_points']}\n")
            f.write(f"- **映射质量分数**: {stats['mapping_quality_score']:.3f}\n")
            f.write(f"- **概念分布**:\n")
            for concept_id, count in stats['concept_distribution'].items():
                f.write(f"  - 概念 {concept_id}: {count} 个点\n")
            
            f.write(f"\n## 🔍 质量评估\n\n")
            f.write(f"- **总体质量分数**: {quality_metrics['overall_quality_score']:.3f}\n\n")
            
            f.write("### 概念分布检查\n")
            for concept_id, data in quality_metrics['concept_distribution_check'].items():
                f.write(f"- **概念 {concept_id}**: {data['count']} 个点 ({data['ratio']:.3f}), 预期: {data['expected_range']}\n")
            
            f.write("\n### 置信度分析\n")
            for concept_id, data in quality_metrics['confidence_analysis'].items():
                f.write(f"- **概念 {concept_id}**: 平均置信度 {data['mean_confidence']:.3f}, 高置信度比例 {data['high_confidence_ratio']:.3f}\n")
            
            f.write(f"\n## 📁 输出文件\n\n")
            f.write(f"- **增强高斯模型**: `{output_ply_path}`\n")
            f.write(f"- **统计信息**: `{stats_path}`\n")
            f.write(f"- **质量评估**: `{quality_path}`\n")
        
        self.logger.info(f"✅ 结果保存完成: {self.output_path}")
    
    def run(self) -> bool:
        """运行完整的概念初始化流程"""
        self.logger.info("🚀 开始 InFusion-Enhanced 阶段1 概念初始化...")
        
        try:
            # 1. 加载3DGS模型
            gaussians, scene = self.load_gaussian_model()
            
            # 2. 初始化concept_membership
            stats = self.initialize_concept_memberships(gaussians, scene)
            
            # 3. 验证映射质量
            quality_metrics = self.validate_mapping_quality(gaussians)
            
            # 4. 保存结果
            self.save_results(gaussians, stats, quality_metrics)
            
            self.logger.info("🎉 InFusion-Enhanced 阶段1 概念初始化完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="InFusion-Enhanced 阶段1 概念初始化器")
    parser.add_argument("--model_path", type=str, default="output/garden_incomplete", 
                       help="3DGS模型路径")
    parser.add_argument("--concept_masks_path", type=str, default="stage1_results/concept_masks_enhanced",
                       help="概念掩码路径")
    parser.add_argument("--output_path", type=str, default="stage1_results/enhanced_gaussians",
                       help="输出路径")
    
    args = parser.parse_args()
    
    print("🔥 InFusion-Enhanced 阶段1 概念初始化器")
    print(f"模型路径: {args.model_path}")
    print(f"概念掩码路径: {args.concept_masks_path}")
    print(f"输出路径: {args.output_path}")
    
    # 创建初始化器并运行
    initializer = EnhancedConceptInitializer(
        model_path=args.model_path,
        concept_masks_path=args.concept_masks_path,
        output_path=args.output_path
    )
    
    success = initializer.run()
    
    if success:
        print("🎉 InFusion-Enhanced 阶段1 概念初始化成功！")
    else:
        print("❌ InFusion-Enhanced 阶段1 概念初始化失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
