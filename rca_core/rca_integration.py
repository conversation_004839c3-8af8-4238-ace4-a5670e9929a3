"""
RCA Integration: RCA机制集成器
阶段3.2: 将概念感知注意力处理器集成到DiffusionEngine中

本模块负责将RCA（区域概念注意力）机制安全地集成到现有的DiffusionEngine中，
使用Diffusers推荐的set_attn_processor方式，确保向后兼容性和系统稳定性。

主要功能：
- RCA集成器（RCAIntegrator）
- UNet注意力处理器替换
- RCA启用/禁用动态切换
- 完整的错误处理和回退机制

技术特性：
- 使用Diffusers推荐的AttnProcessor架构
- 支持动态启用/禁用RCA
- 保留原始注意力处理器作为备份
- 详细的集成状态监控

参考实现：
- MultiDreamer3D/guidance/sd_utils_casd.py
- StableDiffusionCASD.__init__中的monkey-patching逻辑

作者: Claude Sonnet 4 (Anthropic 最先进模型)
版本: 阶段3 - RCA机制移植与适配
"""

import torch
import torch.nn as nn
from typing import Dict, Optional, List, Union, Any
import logging
import warnings

# 抑制不重要的警告
warnings.filterwarnings("ignore", category=UserWarning, module="diffusers")

try:
    from diffusers.models.attention_processor import AttnProcessor
    from diffusers import UNet2DConditionModel
    DIFFUSERS_AVAILABLE = True
except ImportError:
    DIFFUSERS_AVAILABLE = False
    logging.warning("Diffusers not available for RCA integration")

from .concept_aware_attention import ConceptAwareAttnProcessor

class RCAIntegrator:
    """
    RCA机制集成器
    
    负责将概念感知注意力处理器安全地集成到UNet中，
    支持动态启用/禁用，确保系统稳定性和向后兼容性。
    """
    
    def __init__(
        self,
        enable_lora: bool = False,
        lora_scale: float = 1.0,
        debug_mode: bool = False
    ):
        """
        初始化RCA集成器
        
        Args:
            enable_lora: 是否启用LoRA增强
            lora_scale: LoRA缩放因子
            debug_mode: 是否启用调试模式
        """
        self.enable_lora = enable_lora
        self.lora_scale = lora_scale
        self.debug_mode = debug_mode
        
        # 集成状态
        self.is_integrated = False
        self.target_unet = None
        self.original_processors = {}
        self.concept_aware_processor = None
        
        if not DIFFUSERS_AVAILABLE:
            raise ImportError("Diffusers package is required for RCA integration")
        
        logging.info(f"RCAIntegrator initialized (LoRA: {enable_lora}, Debug: {debug_mode})")
    
    def integrate_rca(self, unet: UNet2DConditionModel) -> bool:
        """
        将RCA机制集成到UNet中
        
        Args:
            unet: 目标UNet模型
            
        Returns:
            success: 集成是否成功
        """
        try:
            if self.is_integrated:
                logging.warning("RCA already integrated, skipping")
                return True
            
            # 保存原始处理器
            self._backup_original_processors(unet)
            
            # 创建概念感知注意力处理器
            self.concept_aware_processor = ConceptAwareAttnProcessor(
                enable_lora=self.enable_lora,
                lora_scale=self.lora_scale,
                debug_mode=self.debug_mode
            )
            
            # 替换注意力处理器
            self._replace_attention_processors(unet)
            
            # 更新状态
            self.target_unet = unet
            self.is_integrated = True
            
            logging.info("RCA integration completed successfully")
            return True
            
        except Exception as e:
            logging.error(f"RCA integration failed: {e}")
            # 尝试恢复原始状态
            self._restore_original_processors()
            return False
    
    def disable_rca(self) -> bool:
        """
        禁用RCA机制，恢复标准注意力
        
        Returns:
            success: 禁用是否成功
        """
        try:
            if not self.is_integrated:
                logging.warning("RCA not integrated, nothing to disable")
                return True
            
            # 恢复原始处理器
            self._restore_original_processors()
            
            # 更新状态
            self.is_integrated = False
            self.target_unet = None
            
            logging.info("RCA disabled successfully")
            return True
            
        except Exception as e:
            logging.error(f"RCA disable failed: {e}")
            return False
    
    def is_rca_enabled(self) -> bool:
        """
        检查RCA是否已启用
        
        Returns:
            enabled: RCA是否已启用
        """
        return self.is_integrated and self.target_unet is not None
    
    def get_integration_status(self) -> Dict[str, Any]:
        """
        获取RCA集成状态信息
        
        Returns:
            status: 集成状态字典
        """
        return {
            "is_integrated": self.is_integrated,
            "enable_lora": self.enable_lora,
            "lora_scale": self.lora_scale,
            "debug_mode": self.debug_mode,
            "has_target_unet": self.target_unet is not None,
            "original_processors_count": len(self.original_processors),
            "concept_aware_processor_ready": self.concept_aware_processor is not None
        }
    
    def _backup_original_processors(self, unet: UNet2DConditionModel):
        """
        备份原始注意力处理器
        
        Args:
            unet: 目标UNet模型
        """
        self.original_processors = {}
        
        # 获取当前的注意力处理器
        if hasattr(unet, 'attn_processors'):
            self.original_processors = unet.attn_processors.copy()
            if self.debug_mode:
                logging.debug(f"Backed up {len(self.original_processors)} attention processors")
        else:
            # 回退方案：手动收集注意力模块
            for name, module in unet.named_modules():
                if hasattr(module, '__class__') and 'Attention' in module.__class__.__name__:
                    if hasattr(module, 'processor'):
                        self.original_processors[name] = module.processor
                    else:
                        # 创建默认处理器
                        self.original_processors[name] = AttnProcessor()
            
            if self.debug_mode:
                logging.debug(f"Manually backed up {len(self.original_processors)} attention processors")
    
    def _replace_attention_processors(self, unet: UNet2DConditionModel):
        """
        替换注意力处理器为概念感知版本
        
        Args:
            unet: 目标UNet模型
        """
        if hasattr(unet, 'set_attn_processor'):
            # 使用Diffusers推荐的方式
            processor_dict = {}
            for name in self.original_processors.keys():
                processor_dict[name] = self.concept_aware_processor
            
            unet.set_attn_processor(processor_dict)
            
            if self.debug_mode:
                logging.debug(f"Set {len(processor_dict)} concept-aware attention processors")
        else:
            # 回退方案：手动替换
            for name, module in unet.named_modules():
                if name in self.original_processors:
                    if hasattr(module, 'processor'):
                        module.processor = self.concept_aware_processor
                    elif hasattr(module, 'set_processor'):
                        module.set_processor(self.concept_aware_processor)
            
            if self.debug_mode:
                logging.debug(f"Manually set concept-aware processors for {len(self.original_processors)} modules")
    
    def _restore_original_processors(self):
        """
        恢复原始注意力处理器
        """
        if not self.target_unet or not self.original_processors:
            return
        
        try:
            if hasattr(self.target_unet, 'set_attn_processor'):
                # 使用Diffusers推荐的方式
                self.target_unet.set_attn_processor(self.original_processors)
                
                if self.debug_mode:
                    logging.debug(f"Restored {len(self.original_processors)} original attention processors")
            else:
                # 回退方案：手动恢复
                for name, module in self.target_unet.named_modules():
                    if name in self.original_processors:
                        if hasattr(module, 'processor'):
                            module.processor = self.original_processors[name]
                        elif hasattr(module, 'set_processor'):
                            module.set_processor(self.original_processors[name])
                
                if self.debug_mode:
                    logging.debug(f"Manually restored processors for {len(self.original_processors)} modules")
                    
        except Exception as e:
            logging.error(f"Failed to restore original processors: {e}")
    
    def __del__(self):
        """
        析构函数：确保资源清理
        """
        if self.is_integrated:
            try:
                self.disable_rca()
            except:
                pass  # 忽略析构时的错误


def create_rca_integrator(
    enable_lora: bool = False,
    lora_scale: float = 1.0,
    debug_mode: bool = False
) -> RCAIntegrator:
    """
    创建RCA集成器的便捷函数
    
    Args:
        enable_lora: 是否启用LoRA增强
        lora_scale: LoRA缩放因子
        debug_mode: 是否启用调试模式
        
    Returns:
        integrator: RCA集成器实例
    """
    return RCAIntegrator(
        enable_lora=enable_lora,
        lora_scale=lora_scale,
        debug_mode=debug_mode
    )


def integrate_rca_to_diffusion_engine(
    diffusion_engine,
    enable_lora: bool = False,
    lora_scale: float = 1.0,
    debug_mode: bool = False
) -> bool:
    """
    将RCA机制集成到DiffusionEngine的便捷函数
    
    Args:
        diffusion_engine: DiffusionEngine实例
        enable_lora: 是否启用LoRA增强
        lora_scale: LoRA缩放因子
        debug_mode: 是否启用调试模式
        
    Returns:
        success: 集成是否成功
    """
    try:
        # 创建RCA集成器
        integrator = create_rca_integrator(enable_lora, lora_scale, debug_mode)
        
        # 集成到UNet
        success = integrator.integrate_rca(diffusion_engine.unet)
        
        if success:
            # 将集成器保存到DiffusionEngine中
            diffusion_engine.rca_integrator = integrator
            logging.info("RCA successfully integrated to DiffusionEngine")
        else:
            logging.error("Failed to integrate RCA to DiffusionEngine")
        
        return success
        
    except Exception as e:
        logging.error(f"RCA integration to DiffusionEngine failed: {e}")
        return False
