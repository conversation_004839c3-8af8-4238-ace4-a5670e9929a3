import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import math
import numpy as np

@dataclass
class RCASpatialAttentionConfig:
    """RCA空间注意力网络配置"""
    # 网络结构
    input_dim: int = 3 + 3 + 1 + 3 + 3  # xyz + rgb + opacity + scale + concept_id_onehot
    hidden_dims: List[int] = None  # [256, 512, 256]
    num_attention_heads: int = 8
    attention_dropout: float = 0.1
    
    # 特征工程
    use_positional_encoding: bool = True
    positional_encoding_freq: int = 10
    use_geometric_features: bool = True
    use_appearance_features: bool = True
    use_context_features: bool = True
    
    # 网络参数
    activation: str = "GELU"  # "ReLU", "GELU", "SiLU"
    layer_norm: bool = True
    residual_connections: bool = True
    dropout_rate: float = 0.1
    
    # 输出配置
    num_concepts: int = 3  # background, repair, boundary
    output_activation: str = "softmax"  # "softmax", "sigmoid"
    
    # 训练配置
    temperature: float = 1.0  # softmax温度参数
    use_gumbel_softmax: bool = False
    gumbel_tau: float = 1.0

class PositionalEncoding(nn.Module):
    """正弦/余弦位置编码"""
    
    def __init__(self, d_model: int, max_freq: int = 10):
        super().__init__()
        self.d_model = d_model
        self.max_freq = max_freq
        
        # 创建频率
        freqs = torch.pow(10000, -torch.arange(0, d_model, 2).float() / d_model)
        self.register_buffer('freqs', freqs)
    
    def forward(self, xyz: torch.Tensor) -> torch.Tensor:
        """
        Args:
            xyz: [N, 3] 3D坐标
        Returns:
            pe: [N, d_model] 位置编码
        """
        N = xyz.shape[0]
        device = xyz.device
        
        # 计算位置编码
        pe = torch.zeros(N, self.d_model, device=device)
        
        # 对每个维度进行编码
        for i in range(3):  # x, y, z
            pos = xyz[:, i:i+1]  # [N, 1]
            
            # 正弦编码
            pe[:, i*self.d_model//6:(i+1)*self.d_model//6:2] = torch.sin(pos * self.freqs[:self.d_model//12])
            # 余弦编码  
            pe[:, i*self.d_model//6+1:(i+1)*self.d_model//6:2] = torch.cos(pos * self.freqs[:self.d_model//12])
        
        return pe

class GeometricFeatureExtractor(nn.Module):
    """几何特征提取器"""
    
    def __init__(self, k_neighbors: int = 16):
        super().__init__()
        self.k_neighbors = k_neighbors
    
    def forward(self, xyz: torch.Tensor, scales: torch.Tensor) -> torch.Tensor:
        """
        提取几何特征：法向量、曲率、密度等
        
        Args:
            xyz: [N, 3] 3D坐标
            scales: [N, 3] 高斯尺度
            
        Returns:
            geo_features: [N, feature_dim] 几何特征
        """
        N = xyz.shape[0]
        device = xyz.device
        
        # 1. 🔥 计算局部密度 (基于最近邻)
        with torch.no_grad():
            # 计算距离矩阵
            dist_matrix = torch.cdist(xyz, xyz)  # [N, N]
            
            # 获取k最近邻的距离
            knn_distances, _ = torch.topk(dist_matrix, k=self.k_neighbors+1, largest=False, dim=1)
            knn_distances = knn_distances[:, 1:]  # 排除自己
            
            # 计算局部密度 (k最近邻距离的倒数均值)
            local_density = 1.0 / (knn_distances.mean(dim=1, keepdim=True) + 1e-6)  # [N, 1]
        
        # 2. 🔥 计算尺度特征
        scale_volume = torch.prod(scales, dim=1, keepdim=True)  # [N, 1] 体积
        scale_aspect = scales.max(dim=1, keepdim=True)[0] / (scales.min(dim=1, keepdim=True)[0] + 1e-6)  # [N, 1] 长宽比
        
        # 3. 🔥 计算坐标统计特征
        coord_mean = xyz.mean(dim=0, keepdim=True).expand(N, -1)  # [N, 3]
        coord_std = xyz.std(dim=0, keepdim=True).expand(N, -1)   # [N, 3]
        normalized_xyz = (xyz - coord_mean) / (coord_std + 1e-6)  # [N, 3]
        
        # 4. 🔥 组合几何特征
        geo_features = torch.cat([
            local_density,           # [N, 1] 局部密度
            scale_volume,           # [N, 1] 体积
            scale_aspect,           # [N, 1] 长宽比
            normalized_xyz,         # [N, 3] 标准化坐标
            scales,                 # [N, 3] 原始尺度
        ], dim=1)  # [N, 9]
        
        return geo_features

class AppearanceFeatureExtractor(nn.Module):
    """外观特征提取器"""
    
    def __init__(self, color_bins: int = 32):
        super().__init__()
        self.color_bins = color_bins
    
    def forward(self, rgb: torch.Tensor, opacity: torch.Tensor) -> torch.Tensor:
        """
        提取外观特征：颜色统计、透明度等
        
        Args:
            rgb: [N, 3] RGB颜色
            opacity: [N, 1] 透明度
            
        Returns:
            app_features: [N, feature_dim] 外观特征
        """
        # 1. 🔥 颜色特征
        # RGB均值和标准差
        rgb_stats = torch.cat([rgb, rgb.pow(2)], dim=1)  # [N, 6] 一阶和二阶矩
        
        # 颜色亮度和饱和度
        brightness = rgb.mean(dim=1, keepdim=True)  # [N, 1]
        saturation = rgb.std(dim=1, keepdim=True)   # [N, 1]
        
        # 2. 🔥 透明度特征
        opacity_features = torch.cat([
            opacity,                    # [N, 1] 原始透明度
            torch.sigmoid(opacity),     # [N, 1] sigmoid透明度
            (opacity > 0.5).float(),    # [N, 1] 二值化透明度
        ], dim=1)  # [N, 3]
        
        # 3. 🔥 组合外观特征
        app_features = torch.cat([
            rgb_stats,          # [N, 6] RGB统计
            brightness,         # [N, 1] 亮度
            saturation,         # [N, 1] 饱和度  
            opacity_features,   # [N, 3] 透明度特征
        ], dim=1)  # [N, 11]
        
        return app_features

class ContextFeatureExtractor(nn.Module):
    """上下文特征提取器"""
    
    def __init__(self, k_neighbors: int = 16, context_dim: int = 32):
        super().__init__()
        self.k_neighbors = k_neighbors
        self.context_aggregator = nn.Sequential(
            nn.Linear(k_neighbors, context_dim),
            nn.ReLU(),
            nn.Linear(context_dim, context_dim)
        )
    
    def forward(self, xyz: torch.Tensor, features: torch.Tensor) -> torch.Tensor:
        """
        提取邻域上下文特征
        
        Args:
            xyz: [N, 3] 3D坐标
            features: [N, F] 点特征
            
        Returns:
            context_features: [N, context_dim] 上下文特征
        """
        N = xyz.shape[0]
        
        with torch.no_grad():
            # 计算k最近邻
            dist_matrix = torch.cdist(xyz, xyz)  # [N, N]
            _, knn_indices = torch.topk(dist_matrix, k=self.k_neighbors+1, largest=False, dim=1)
            knn_indices = knn_indices[:, 1:]  # [N, k] 排除自己
        
        # 聚合邻域特征
        neighbor_features = features[knn_indices.flatten()].view(N, self.k_neighbors, -1)  # [N, k, F]
        
        # 简单的平均池化
        context_features = neighbor_features.mean(dim=2)  # [N, k]
        
        # 通过MLP处理
        context_features = self.context_aggregator(context_features)  # [N, context_dim]
        
        return context_features

class MultiHeadSpatialAttention(nn.Module):
    """多头空间注意力机制"""
    
    def __init__(self, 
                 d_model: int, 
                 num_heads: int = 8, 
                 dropout: float = 0.1):
        super().__init__()
        
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            x: [N, d_model] 输入特征
            mask: [N, N] 注意力掩码 (可选)
            
        Returns:
            output: [N, d_model] 输出特征
        """
        N = x.shape[0]
        residual = x
        
        # 1. 计算Q, K, V
        Q = self.w_q(x).view(N, self.num_heads, self.d_k)  # [N, h, d_k]
        K = self.w_k(x).view(N, self.num_heads, self.d_k)  # [N, h, d_k]
        V = self.w_v(x).view(N, self.num_heads, self.d_k)  # [N, h, d_k]
        
        # 2. 计算注意力分数
        scores = torch.einsum('ihd,jhd->ijh', Q, K) / math.sqrt(self.d_k)  # [N, N, h]
        
        # 3. 应用掩码
        if mask is not None:
            scores = scores.masked_fill(mask.unsqueeze(-1) == 0, -1e9)
        
        # 4. Softmax
        attn_weights = F.softmax(scores, dim=1)  # [N, N, h]
        attn_weights = self.dropout(attn_weights)
        
        # 5. 加权求和
        output = torch.einsum('ijh,jhd->ihd', attn_weights, V)  # [N, h, d_k]
        output = output.contiguous().view(N, self.d_model)  # [N, d_model]
        
        # 6. 输出投影
        output = self.w_o(output)
        
        # 7. 残差连接和层归一化
        output = self.layer_norm(output + residual)
        
        return output

class RCASpatialAttentionNetwork(nn.Module):
    """
    🔥 RCA空间注意力网络主体
    
    功能:
    - 从3D高斯点特征提取空间注意力权重
    - 输出每个点对不同概念的响应权重
    - 支持可学习的区域概念分配
    """
    
    def __init__(self, config: RCASpatialAttentionConfig = None):
        super().__init__()
        
        self.config = config or RCASpatialAttentionConfig()
        
        # 设置默认隐藏层维度
        if self.config.hidden_dims is None:
            self.config.hidden_dims = [256, 512, 256]
        
        # 🔥 特征提取器
        self.pos_encoder = PositionalEncoding(
            d_model=64, max_freq=self.config.positional_encoding_freq
        ) if self.config.use_positional_encoding else None
        
        self.geo_extractor = GeometricFeatureExtractor() if self.config.use_geometric_features else None
        self.app_extractor = AppearanceFeatureExtractor() if self.config.use_appearance_features else None
        self.ctx_extractor = ContextFeatureExtractor() if self.config.use_context_features else None
        
        # 🔥 计算总输入维度
        self.total_input_dim = self._calculate_input_dim()
        
        # 🔥 特征投影层
        self.feature_projection = nn.Linear(self.total_input_dim, self.config.hidden_dims[0])
        
        # 🔥 多层感知机主干
        self.mlp_layers = nn.ModuleList()
        for i in range(len(self.config.hidden_dims) - 1):
            self.mlp_layers.append(self._make_mlp_layer(
                self.config.hidden_dims[i], 
                self.config.hidden_dims[i + 1]
            ))
        
        # 🔥 多头注意力层
        self.spatial_attention = MultiHeadSpatialAttention(
            d_model=self.config.hidden_dims[-1],
            num_heads=self.config.num_attention_heads,
            dropout=self.config.attention_dropout
        )
        
        # 🔥 概念权重输出头
        self.concept_head = nn.Sequential(
            nn.Linear(self.config.hidden_dims[-1], self.config.hidden_dims[-1] // 2),
            self._get_activation(),
            nn.Dropout(self.config.dropout_rate),
            nn.Linear(self.config.hidden_dims[-1] // 2, self.config.num_concepts)
        )
        
        # 初始化权重
        self._initialize_weights()
        
        print(f"🎯 RCA空间注意力网络初始化完成")
        print(f"   输入维度: {self.total_input_dim}")
        print(f"   隐藏维度: {self.config.hidden_dims}")
        print(f"   注意力头数: {self.config.num_attention_heads}")
        print(f"   输出概念数: {self.config.num_concepts}")
    
    def _calculate_input_dim(self) -> int:
        """计算总输入维度"""
        dim = self.config.input_dim  # 基础特征: xyz + rgb + opacity + scale + concept_id_onehot
        
        if self.config.use_positional_encoding:
            dim += 64  # 位置编码维度
        if self.config.use_geometric_features:
            dim += 9   # 几何特征维度
        if self.config.use_appearance_features:
            dim += 11  # 外观特征维度
        if self.config.use_context_features:
            dim += 32  # 上下文特征维度
        
        return dim
    
    def _make_mlp_layer(self, in_dim: int, out_dim: int) -> nn.Module:
        """创建MLP层"""
        layers = [nn.Linear(in_dim, out_dim)]
        
        if self.config.layer_norm:
            layers.append(nn.LayerNorm(out_dim))
        
        layers.append(self._get_activation())
        
        if self.config.dropout_rate > 0:
            layers.append(nn.Dropout(self.config.dropout_rate))
        
        return nn.Sequential(*layers)
    
    def _get_activation(self) -> nn.Module:
        """获取激活函数"""
        if self.config.activation == "ReLU":
            return nn.ReLU()
        elif self.config.activation == "GELU":
            return nn.GELU()
        elif self.config.activation == "SiLU":
            return nn.SiLU()
        else:
            return nn.ReLU()
    
    def _initialize_weights(self):
        """权重初始化"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, gaussian_features: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        🔥 前向传播
        
        Args:
            gaussian_features: 3D高斯点特征字典
                - 'xyz': [N, 3] 3D坐标
                - 'rgb': [N, 3] RGB颜色
                - 'opacity': [N, 1] 透明度
                - 'scales': [N, 3] 尺度
                - 'concept_id': [N,] 概念ID (转换为one-hot)
                
        Returns:
            concept_weights: [N, num_concepts] 概念权重
        """
        xyz = gaussian_features['xyz']          # [N, 3]
        rgb = gaussian_features['rgb']          # [N, 3]
        opacity = gaussian_features['opacity']  # [N, 1]
        scales = gaussian_features['scales']    # [N, 3]
        concept_id = gaussian_features['concept_id']  # [N,]
        
        N = xyz.shape[0]
        device = xyz.device
        
        # 1. 🔥 概念ID转one-hot
        concept_id_onehot = F.one_hot(concept_id, num_classes=self.config.num_concepts).float()  # [N, num_concepts]
        
        # 2. 🔥 基础特征拼接
        base_features = torch.cat([xyz, rgb, opacity, scales, concept_id_onehot], dim=1)  # [N, input_dim]
        
        # 3. 🔥 扩展特征提取
        feature_list = [base_features]
        
        if self.pos_encoder is not None:
            pos_features = self.pos_encoder(xyz)
            feature_list.append(pos_features)
        
        if self.geo_extractor is not None:
            geo_features = self.geo_extractor(xyz, scales)
            feature_list.append(geo_features)
        
        if self.app_extractor is not None:
            app_features = self.app_extractor(rgb, opacity)
            feature_list.append(app_features)
        
        if self.ctx_extractor is not None:
            # 使用基础特征作为上下文特征的输入
            ctx_features = self.ctx_extractor(xyz, base_features)
            feature_list.append(ctx_features)
        
        # 4. 🔥 特征融合
        all_features = torch.cat(feature_list, dim=1)  # [N, total_input_dim]
        
        # 5. 🔥 特征投影
        x = self.feature_projection(all_features)  # [N, hidden_dims[0]]
        
        # 6. 🔥 MLP特征提取
        for mlp_layer in self.mlp_layers:
            if self.config.residual_connections and x.shape[1] == mlp_layer[0].out_features:
                x = x + mlp_layer(x)
            else:
                x = mlp_layer(x)
        
        # 7. 🔥 空间注意力
        x = self.spatial_attention(x)  # [N, hidden_dims[-1]]
        
        # 8. 🔥 概念权重预测
        concept_logits = self.concept_head(x)  # [N, num_concepts]
        
        # 9. 🔥 输出激活
        if self.config.output_activation == "softmax":
            if self.config.use_gumbel_softmax and self.training:
                concept_weights = F.gumbel_softmax(
                    concept_logits / self.config.temperature, 
                    tau=self.config.gumbel_tau, 
                    hard=False
                )
            else:
                concept_weights = F.softmax(concept_logits / self.config.temperature, dim=1)
        elif self.config.output_activation == "sigmoid":
            concept_weights = torch.sigmoid(concept_logits)
        else:
            concept_weights = concept_logits
        
        return concept_weights
    
    def get_attention_weights(self, gaussian_features: Dict[str, torch.Tensor]) -> torch.Tensor:
        """获取注意力权重矩阵 (用于可视化)"""
        # 这需要修改spatial_attention以返回注意力权重
        # 暂时返回identity矩阵作为占位符
        N = gaussian_features['xyz'].shape[0]
        return torch.eye(N, device=gaussian_features['xyz'].device)

# 使用示例和测试
if __name__ == "__main__":
    # 创建配置
    config = RCASpatialAttentionConfig(
        hidden_dims=[256, 512, 256],
        num_attention_heads=8,
        num_concepts=3
    )
    
    # 创建网络
    rca_network = RCASpatialAttentionNetwork(config)
    
    # 模拟数据
    N = 1000
    gaussian_features = {
        'xyz': torch.randn(N, 3),
        'rgb': torch.rand(N, 3),
        'opacity': torch.rand(N, 1),
        'scales': torch.rand(N, 3),
        'concept_id': torch.randint(0, 3, (N,))
    }
    
    # 前向传播测试
    concept_weights = rca_network(gaussian_features)
    
    print(f"🧪 RCA网络测试结果:")
    print(f"   输入点数: {N}")
    print(f"   概念权重形状: {concept_weights.shape}")
    print(f"   权重和范围: {concept_weights.sum(dim=1).min():.3f} - {concept_weights.sum(dim=1).max():.3f}")
    print(f"   各概念平均权重: {concept_weights.mean(dim=0)}")
    
    print("🎉 RCA空间注意力网络测试完成")