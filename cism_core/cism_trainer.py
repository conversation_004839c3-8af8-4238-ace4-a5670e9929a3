"""
CISM训练器：集成CISM到3D高斯训练流程
阶段2: 完整的CISM语义引导训练系统
"""

import torch
import torch.nn as nn
from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path
import time

from .diffusion_engine import DiffusionEngine
from .concept_guidance import ConceptGuidance
from .sds_loss import SDSLoss

class CISMTrainer:
    """
    CISM训练器
    
    功能：
    1. 集成扩散模型到3DGS训练
    2. 管理概念引导训练流程
    3. 监控训练进度和损失
    4. 提供训练配置和调度
    """
    
    def __init__(self, config: Dict):
        """
        🔥 初始化CISM训练器 - 真正集成版本

        Args:
            config: 训练配置字典
        """
        self.config = config
        self.device = config.get('device', 'cuda')

        # 训练参数 - 支持嵌套配置
        training_schedule = config.get('training_schedule', {})
        self.cism_start_iter = training_schedule.get('cism_start_iter', config.get('cism_start_iter', 2000))
        self.cism_end_iter = training_schedule.get('cism_end_iter', config.get('cism_end_iter', 25000))
        self.cism_interval = training_schedule.get('cism_interval', config.get('cism_interval', 100))
        self.cism_weight = config.get('cism_weight', 0.1)

        logging.info(f"CISM调度参数: start={self.cism_start_iter}, end={self.cism_end_iter}, interval={self.cism_interval}")

        # 初始化CISM组件
        self._init_cism_components()

        # 设置训练调度（包括sds_weight_schedule）
        self.setup_training_schedule()

        # 训练状态
        self.iteration = 0
        self.training_stats = {
            'sds_losses': [],
            'concept_losses': {},
            'training_times': []
        }

        logging.info("🔥 CISMTrainer initialized for true integration")
    
    def _init_cism_components(self):
        """初始化CISM核心组件"""
        # 扩散模型引擎
        self.diffusion_engine = DiffusionEngine(
            model_id=self.config.get('model_id', 'runwayml/stable-diffusion-v1-5'),
            device=self.device,
            half_precision=self.config.get('half_precision', True),
            enable_attention_slicing=self.config.get('attention_slicing', True),
            enable_memory_efficient_attention=self.config.get('memory_efficient_attention', True)
        )
        
        # 概念引导系统
        concept_config_path = self.config.get('concept_config_path', 'configs/concept_prompts.yaml')
        self.concept_guidance = ConceptGuidance(
            self.diffusion_engine,
            concept_config_path,
            self.device
        )
        
        # SDS损失计算器
        self.sds_loss = SDSLoss(
            self.diffusion_engine,
            self.concept_guidance,
            min_step=self.config.get('min_step', 20),
            max_step=self.config.get('max_step', 980),
            device=self.device
        )
        
        # 设置用户概念
        user_concept = self.config.get('user_concept')
        if user_concept:
            self.concept_guidance.set_user_concept(1, user_concept)
            logging.info(f"Set user concept: {user_concept}")
    
    def setup_training_schedule(self):
        """设置训练调度"""
        # 使用已经在__init__中设置的参数，不要覆盖它们
        # self.cism_start_iter, self.cism_end_iter, self.cism_interval 已经在__init__中正确设置
        
        # SDS损失权重调度
        self.sds_weight_schedule = self._create_weight_schedule()
        
        logging.info(f"CISM training: iter {self.cism_start_iter}-{self.cism_end_iter}, interval {self.cism_interval}")
    
    def _create_weight_schedule(self) -> Dict:
        """创建SDS损失权重调度"""
        # 从嵌套的training_schedule配置中读取参数
        training_schedule = self.config.get('training_schedule', {})

        schedule = {
            'type': training_schedule.get('weight_schedule_type', self.config.get('weight_schedule_type', 'linear')),
            'start_weight': training_schedule.get('sds_start_weight', self.config.get('sds_start_weight', 0.1)),
            'end_weight': training_schedule.get('sds_end_weight', self.config.get('sds_end_weight', 1.0)),
            'warmup_iters': training_schedule.get('sds_warmup_iters', self.config.get('sds_warmup_iters', 500))
        }
        return schedule
    
    def get_sds_weight(self, iteration: int) -> float:
        """获取当前迭代的SDS损失权重"""
        if iteration < self.cism_start_iter:
            return 0.0
        
        if iteration > self.cism_end_iter:
            return 0.0
        
        # 计算相对进度
        progress = (iteration - self.cism_start_iter) / (self.cism_end_iter - self.cism_start_iter)
        
        schedule = self.sds_weight_schedule
        if schedule['type'] == 'linear':
            weight = schedule['start_weight'] + progress * (schedule['end_weight'] - schedule['start_weight'])
        elif schedule['type'] == 'cosine':
            weight = schedule['start_weight'] + 0.5 * (schedule['end_weight'] - schedule['start_weight']) * (1 + torch.cos(torch.tensor(progress * 3.14159)))
        else:
            weight = schedule['end_weight']
        
        return float(weight)
    
    def should_apply_cism(self, iteration: int) -> bool:
        """判断是否应该应用CISM"""
        if iteration < self.cism_start_iter or iteration > self.cism_end_iter:
            return False
        
        return (iteration - self.cism_start_iter) % self.cism_interval == 0
    
    def compute_cism_loss(
        self,
        rendered_images: torch.Tensor,
        viewpoint_camera,
        iteration: int
    ) -> Tuple[torch.Tensor, Dict]:
        """
        计算CISM损失
        
        Args:
            rendered_images: 渲染图像 [batch_size, 3, H, W]
            viewpoint_camera: 视点相机
            iteration: 当前迭代
            
        Returns:
            loss: CISM损失
            info: 损失信息
        """
        start_time = time.time()
        
        # 获取概念配置
        concept_ids = self.config.get('active_concepts', [0, 1, 2])
        guidance_scale = self.config.get('guidance_scale', 7.5)
        
        # 计算SDS损失
        if self.config.get('use_multi_concept', True):
            # 多概念SDS损失
            concept_weights = torch.tensor(
                self.config.get('concept_weights', [0.3, 1.0, 0.5]),
                device=self.device
            )
            sds_loss, sds_info = self.sds_loss.compute_sds_loss_multi_concept(
                rendered_images,
                concept_ids,
                concept_weights,
                guidance_scale
            )
        else:
            # 单概念SDS损失（主要修复概念）
            main_concept_id = self.config.get('main_concept_id', 1)
            sds_loss, sds_info = self.sds_loss.compute_sds_loss_single_concept(
                rendered_images,
                main_concept_id,
                guidance_scale
            )
        
        # 应用权重调度
        sds_weight = self.get_sds_weight(iteration)
        weighted_sds_loss = sds_weight * sds_loss
        
        # 计算时间
        compute_time = time.time() - start_time
        
        # 收集信息
        info = {
            'sds_loss': sds_loss.item(),
            'sds_weight': sds_weight,
            'weighted_sds_loss': weighted_sds_loss.item(),
            'compute_time': compute_time,
            'sds_info': sds_info
        }
        
        return weighted_sds_loss, info

    def train_step(
        self,
        rendered_images: torch.Tensor,
        gaussians_model,
        optimizer,
        iteration: int,
        viewpoint_camera=None
    ) -> Optional[Tuple[torch.Tensor, Dict]]:
        """
        核心训练步骤接口（测试脚本期望的方法名）

        这是测试脚本检查的关键接口，实现完整的CISM训练步骤：
        1. 检查是否应该应用CISM
        2. 计算CISM损失
        3. 执行反向传播
        4. 更新3DGS参数

        Args:
            rendered_images: 渲染图像 [batch_size, 3, H, W]
            gaussians_model: 3DGS模型（包含可训练参数）
            optimizer: 优化器
            iteration: 当前迭代
            viewpoint_camera: 视点相机（可选）

        Returns:
            loss和信息，如果不应用CISM则返回None
        """
        self.iteration = iteration

        if not self.should_apply_cism(iteration):
            return None

        try:
            # 清零梯度
            optimizer.zero_grad()

            # 计算CISM损失
            cism_loss, info = self.compute_cism_loss(rendered_images, viewpoint_camera, iteration)

            # 反向传播
            cism_loss.backward()

            # 检查梯度
            grad_info = self._check_gradients(gaussians_model)
            info.update(grad_info)

            # 优化器步骤
            optimizer.step()

            # 更新统计信息
            self._update_training_stats(info)

            return cism_loss, info

        except Exception as e:
            logging.error(f"CISM train_step failed at iteration {iteration}: {e}")
            import traceback
            traceback.print_exc()
            return None

    def training_step(
        self,
        rendered_images: torch.Tensor,
        viewpoint_camera,
        iteration: int
    ) -> Optional[Tuple[torch.Tensor, Dict]]:
        """
        CISM训练步骤（兼容性接口）

        Args:
            rendered_images: 渲染图像
            viewpoint_camera: 视点相机
            iteration: 当前迭代

        Returns:
            loss和信息，如果不应用CISM则返回None
        """
        self.iteration = iteration

        if not self.should_apply_cism(iteration):
            return None

        try:
            # 计算CISM损失
            cism_loss, info = self.compute_cism_loss(rendered_images, viewpoint_camera, iteration)

            # 更新统计信息
            self._update_training_stats(info)

            return cism_loss, info

        except Exception as e:
            logging.error(f"CISM training step failed at iteration {iteration}: {e}")
            return None

    def _check_gradients(self, gaussians_model) -> Dict:
        """
        检查3DGS模型参数的梯度情况

        Args:
            gaussians_model: 3DGS模型

        Returns:
            grad_info: 梯度检查信息
        """
        grad_info = {
            'has_gradients': False,
            'grad_norms': {},
            'grad_stats': {},
            'total_params_with_grad': 0,
            'total_params': 0
        }

        try:
            # 获取可训练参数
            if hasattr(gaussians_model, 'get_trainable_params'):
                # 如果模型有get_trainable_params方法
                trainable_params = gaussians_model.get_trainable_params()
                param_names = ['_xyz', '_features_dc', '_features_rest', '_opacity', '_scaling', '_rotation']
            elif hasattr(gaussians_model, '_xyz'):
                # 直接访问3DGS参数
                trainable_params = [
                    gaussians_model._xyz,
                    gaussians_model._features_dc,
                    gaussians_model._features_rest,
                    gaussians_model._opacity,
                    gaussians_model._scaling,
                    gaussians_model._rotation
                ]
                param_names = ['_xyz', '_features_dc', '_features_rest', '_opacity', '_scaling', '_rotation']
            else:
                # 通用参数获取
                trainable_params = [p for p in gaussians_model.parameters() if p.requires_grad]
                param_names = [f'param_{i}' for i in range(len(trainable_params))]

            grad_info['total_params'] = len(trainable_params)

            for i, (param, name) in enumerate(zip(trainable_params, param_names)):
                if param.grad is not None:
                    grad_norm = param.grad.norm().item()
                    grad_mean = param.grad.mean().item()
                    grad_std = param.grad.std().item()

                    grad_info['grad_norms'][name] = grad_norm
                    grad_info['grad_stats'][name] = {
                        'norm': grad_norm,
                        'mean': grad_mean,
                        'std': grad_std,
                        'shape': list(param.grad.shape)
                    }
                    grad_info['total_params_with_grad'] += 1
                    grad_info['has_gradients'] = True

            # 计算总体梯度统计
            if grad_info['has_gradients']:
                all_norms = list(grad_info['grad_norms'].values())
                grad_info['avg_grad_norm'] = sum(all_norms) / len(all_norms)
                grad_info['max_grad_norm'] = max(all_norms)
                grad_info['min_grad_norm'] = min(all_norms)
            else:
                grad_info['avg_grad_norm'] = 0.0
                grad_info['max_grad_norm'] = 0.0
                grad_info['min_grad_norm'] = 0.0

        except Exception as e:
            logging.warning(f"梯度检查失败: {e}")
            grad_info['error'] = str(e)

        return grad_info

    def _update_training_stats(self, info: Dict):
        """更新训练统计信息"""
        self.training_stats['sds_losses'].append(info['sds_loss'])
        self.training_stats['training_times'].append(info['compute_time'])
        
        # 限制统计历史长度
        max_history = 1000
        if len(self.training_stats['sds_losses']) > max_history:
            self.training_stats['sds_losses'] = self.training_stats['sds_losses'][-max_history:]
            self.training_stats['training_times'] = self.training_stats['training_times'][-max_history:]
    
    def get_training_stats(self) -> Dict:
        """获取训练统计信息"""
        stats = self.training_stats.copy()
        
        if stats['sds_losses']:
            stats['avg_sds_loss'] = sum(stats['sds_losses']) / len(stats['sds_losses'])
            stats['avg_compute_time'] = sum(stats['training_times']) / len(stats['training_times'])
        else:
            stats['avg_sds_loss'] = 0.0
            stats['avg_compute_time'] = 0.0
        
        stats['total_cism_steps'] = len(stats['sds_losses'])
        stats['concept_stats'] = self.concept_guidance.get_concept_statistics()
        
        return stats
    
    def save_training_state(self, save_path: str):
        """保存训练状态"""
        state = {
            'iteration': self.iteration,
            'config': self.config,
            'training_stats': self.training_stats,
            'concept_prompts': self.concept_guidance.concept_prompts
        }
        
        torch.save(state, save_path)
        logging.info(f"CISM training state saved to {save_path}")
    
    def load_training_state(self, load_path: str):
        """加载训练状态"""
        if Path(load_path).exists():
            state = torch.load(load_path, map_location=self.device)
            self.iteration = state.get('iteration', 0)
            self.training_stats = state.get('training_stats', {'sds_losses': [], 'concept_losses': {}, 'training_times': []})
            
            # 恢复概念提示
            concept_prompts = state.get('concept_prompts', {})
            for concept_id, prompt in concept_prompts.items():
                if prompt != "USER_DEFINED_CONCEPT":
                    self.concept_guidance.set_user_concept(concept_id, prompt)
            
            logging.info(f"CISM training state loaded from {load_path}")
        else:
            logging.warning(f"Training state file not found: {load_path}")
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'diffusion_engine'):
            self.diffusion_engine.cleanup()
        if hasattr(self, 'concept_guidance'):
            self.concept_guidance.cleanup()
        
        torch.cuda.empty_cache()
        logging.info("CISMTrainer cleaned up")
