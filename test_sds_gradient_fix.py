#!/usr/bin/env python3
"""
SDS梯度修复验证测试
验证SpecifyGradient是否正确解决了梯度断裂问题
"""

import os
import sys
import torch
import logging

# 添加项目路径
sys.path.append('.')
sys.path.append('./gaussian_splatting')

# 导入3DGS参数类
from gaussian_splatting.arguments import OptimizationParams

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_specify_gradient():
    """测试SpecifyGradient类"""
    logger.info("🔥 测试SpecifyGradient类...")
    
    try:
        from cism_core.specify_gradient import SpecifyGradient, sds_loss_with_specify_gradient
        
        # 创建测试张量
        latents = torch.randn(1, 4, 8, 8, requires_grad=True)
        pred_noise = torch.randn(1, 4, 8, 8)
        target_noise = torch.randn(1, 4, 8, 8)
        timesteps = torch.tensor([500])
        alphas = torch.linspace(0.99, 0.01, 1000)  # 模拟scheduler的alphas
        
        logger.info(f"   - latents requires_grad: {latents.requires_grad}")
        logger.info(f"   - pred_noise requires_grad: {pred_noise.requires_grad}")
        logger.info(f"   - target_noise requires_grad: {target_noise.requires_grad}")
        
        # 使用SpecifyGradient计算损失
        loss = sds_loss_with_specify_gradient(
            latents=latents,
            pred_noise=pred_noise,
            target_noise=target_noise,
            timesteps=timesteps,
            alphas=alphas,
            grad_scale=1.0,
            weight_type="dreamfusion"
        )
        
        logger.info(f"   - 损失值: {loss.item():.6f}")
        logger.info(f"   - 损失requires_grad: {loss.requires_grad}")
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        if latents.grad is not None:
            grad_norm = latents.grad.norm().item()
            logger.info(f"   ✅ latents梯度存在，范数: {grad_norm:.6f}")
            return True
        else:
            logger.error("   ❌ latents没有梯度")
            return False
            
    except Exception as e:
        logger.error(f"❌ SpecifyGradient测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_sds_pipeline():
    """测试完整的SDS管道"""
    logger.info("🔥 测试完整SDS管道...")
    
    try:
        from scene.gaussian_model import GaussianModel
        from cism_core.cism_trainer import CISMTrainer
        import yaml
        
        # 1. 加载高斯模型
        logger.info("   - 加载高斯模型...")
        gaussians = GaussianModel(sh_degree=0)
        model_path = "stage1_results/garden_fixed_final/gaussians_with_concept_membership_enhanced.ply"
        gaussians.load_ply(model_path)
        
        # 2. 设置训练
        logger.info("   - 设置训练...")
        import argparse
        parser = argparse.ArgumentParser()
        opt_params = OptimizationParams(parser)
        
        args = argparse.Namespace()
        for attr_name in dir(opt_params):
            if not attr_name.startswith('_'):
                setattr(args, attr_name, getattr(opt_params, attr_name))
        
        training_args = opt_params.extract(args)
        gaussians.training_setup(training_args)
        
        # 3. 初始化CISM训练器
        logger.info("   - 初始化CISM训练器...")
        with open("configs/cism_training_config.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        config['device'] = 'cuda'
        config['half_precision'] = True
        
        cism_trainer = CISMTrainer(config)
        
        # 4. 创建模拟渲染图像（连接到3DGS参数）
        logger.info("   - 创建模拟渲染...")
        # 使用一个简单的线性变换来模拟渲染过程
        # 这确保了rendered_images与gaussians参数有梯度连接
        xyz = gaussians.get_xyz  # [N, 3]
        
        # 简化的"渲染"：将3D点投影到2D并生成图像
        # 这里我们只是创建一个与xyz相关的张量来模拟渲染
        batch_size = 1
        height, width = 64, 64
        
        # 创建一个简单的可微渲染
        # 使用xyz的均值来影响图像的像素值
        xyz_mean = xyz.mean(dim=0)  # [3]
        rendered_images = torch.ones(batch_size, 3, height, width, device='cuda') * 0.5
        
        # 让图像依赖于xyz（确保梯度连接）
        for i in range(3):
            rendered_images[:, i, :, :] = rendered_images[:, i, :, :] + xyz_mean[i] * 0.01
        
        logger.info(f"   - rendered_images shape: {rendered_images.shape}")
        logger.info(f"   - rendered_images requires_grad: {rendered_images.requires_grad}")
        
        # 5. 计算CISM损失
        logger.info("   - 计算CISM损失...")
        
        # 清零梯度
        gaussians.optimizer.zero_grad()
        
        # 计算SDS损失
        sds_loss = cism_trainer.sds_loss.compute_sds_loss(
            rendered_images=rendered_images,
            concept_id=1,  # 使用概念1
            guidance_scale=7.5,
            weight_type="dreamfusion",
            use_ism=False  # 先测试简单版本
        )
        
        loss_value = sds_loss[0] if isinstance(sds_loss, tuple) else sds_loss
        logger.info(f"   - SDS损失值: {loss_value.item():.6f}")
        
        # 6. 反向传播
        logger.info("   - 执行反向传播...")
        loss_value.backward()
        
        # 7. 检查梯度
        logger.info("   - 检查梯度...")
        has_gradients = False
        gradient_info = []
        
        for param_group in gaussians.params_list:
            param_name = param_group["name"]
            params = param_group["params"]
            
            for i, param in enumerate(params):
                if param.grad is not None:
                    grad_norm = param.grad.abs().sum().item()
                    if grad_norm > 0:
                        has_gradients = True
                        gradient_info.append(f"{param_name}[{i}]: {grad_norm:.6f}")
                    else:
                        gradient_info.append(f"{param_name}[{i}]: 0.0")
                else:
                    gradient_info.append(f"{param_name}[{i}]: None")
        
        if has_gradients:
            logger.info("   ✅ SDS梯度传播成功！")
            logger.info(f"     梯度详情: {'; '.join(gradient_info[:3])}...")
            return True
        else:
            logger.error("   ❌ SDS梯度传播失败")
            logger.error(f"     参数状态: {'; '.join(gradient_info[:3])}...")
            return False
            
    except Exception as e:
        logger.error(f"❌ 完整SDS管道测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🔥 SDS梯度修复验证测试")
    logger.info("🎯 验证SpecifyGradient是否解决了梯度断裂问题")
    logger.info("=" * 80)
    
    tests = [
        ("SpecifyGradient类测试", test_specify_gradient),
        ("完整SDS管道测试", test_full_sds_pipeline)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 {test_name}")
        logger.info("-" * 60)
        
        try:
            if test_func():
                passed_tests += 1
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    # 总结
    success_rate = passed_tests / total_tests
    logger.info(f"\n🎯 总体状态: {passed_tests}/{total_tests} 测试通过 ({success_rate:.1%})")
    
    if success_rate >= 0.8:
        logger.info("🎉 SDS梯度修复验证成功！")
        logger.info("💡 梯度断裂问题已解决，可以进入阶段3")
    else:
        logger.info("❌ SDS梯度修复验证失败")
        logger.info("🔧 需要进一步调试梯度计算")
    
    return success_rate >= 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
