<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: type_aligned.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">gtc/type_aligned.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00161.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#if (GLM_CONFIG_ALIGNED_GENTYPES == GLM_DISABLE)</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#       error &quot;GLM: Aligned gentypes require to enable C++ language extensions. Define GLM_FORCE_ALIGNED_GENTYPES before including GLM headers to use aligned types.&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor"># pragma message(&quot;GLM: GLM_GTC_type_aligned extension included&quot;)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;../mat4x4.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../mat4x3.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;../mat4x2.hpp&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;../mat3x4.hpp&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;../mat3x3.hpp&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &quot;../mat3x2.hpp&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &quot;../mat2x4.hpp&quot;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;../mat2x3.hpp&quot;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;../mat2x2.hpp&quot;</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;../gtc/vec1.hpp&quot;</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;../vec2.hpp&quot;</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;../vec3.hpp&quot;</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;../vec4.hpp&quot;</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;{</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="comment">// -- *vec1 --</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div>
<div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="a00303.html#ga4d0bd70d5fac49b800546d608b707513">   45</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, aligned_highp&gt;    <a class="code" href="a00303.html#ga4d0bd70d5fac49b800546d608b707513">aligned_highp_vec1</a>;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div>
<div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="a00303.html#ga6b797eec76fa471e300158f3453b3b2e">   48</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, aligned_mediump&gt;  <a class="code" href="a00303.html#ga6b797eec76fa471e300158f3453b3b2e">aligned_mediump_vec1</a>;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="a00303.html#gab34aee3d5e121c543fea11d2c50ecc43">   51</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, aligned_lowp&gt;             <a class="code" href="a00303.html#gab34aee3d5e121c543fea11d2c50ecc43">aligned_lowp_vec1</a>;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="a00303.html#gaf0448b0f7ceb8273f7eda3a92205eefc">   54</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, double, aligned_highp&gt;   <a class="code" href="a00303.html#gaf0448b0f7ceb8273f7eda3a92205eefc">aligned_highp_dvec1</a>;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="a00303.html#ga7180b685c581adb224406a7f831608e3">   57</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, double, aligned_mediump&gt; <a class="code" href="a00303.html#ga7180b685c581adb224406a7f831608e3">aligned_mediump_dvec1</a>;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div>
<div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="a00303.html#ga7f8a2cc5a686e52b1615761f4978ca62">   60</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, double, aligned_lowp&gt;    <a class="code" href="a00303.html#ga7f8a2cc5a686e52b1615761f4978ca62">aligned_lowp_dvec1</a>;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div>
<div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="a00303.html#gad63b8c5b4dc0500d54d7414ef555178f">   63</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, int, aligned_highp&gt;              <a class="code" href="a00303.html#gad63b8c5b4dc0500d54d7414ef555178f">aligned_highp_ivec1</a>;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div>
<div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="a00303.html#ga20e63dd980b81af10cadbbe219316650">   66</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, int, aligned_mediump&gt;    <a class="code" href="a00303.html#ga20e63dd980b81af10cadbbe219316650">aligned_mediump_ivec1</a>;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="a00303.html#ga1101d3a82b2e3f5f8828bd8f3adab3e1">   69</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, int, aligned_lowp&gt;               <a class="code" href="a00303.html#ga1101d3a82b2e3f5f8828bd8f3adab3e1">aligned_lowp_ivec1</a>;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="a00303.html#ga5b80e28396c6ef7d32c6fd18df498451">   72</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, uint, aligned_highp&gt;             <a class="code" href="a00303.html#ga5b80e28396c6ef7d32c6fd18df498451">aligned_highp_uvec1</a>;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div>
<div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="a00303.html#gacb78126ea2eb779b41c7511128ff1283">   75</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, uint, aligned_mediump&gt;   <a class="code" href="a00303.html#gacb78126ea2eb779b41c7511128ff1283">aligned_mediump_uvec1</a>;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div>
<div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="a00303.html#gad09b93acc43c43423408d17a64f6d7ca">   78</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, uint, aligned_lowp&gt;              <a class="code" href="a00303.html#gad09b93acc43c43423408d17a64f6d7ca">aligned_lowp_uvec1</a>;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div>
<div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="a00303.html#ga862843a45b01c35ffe4d44c47ea774ad">   81</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, bool, aligned_highp&gt;             <a class="code" href="a00303.html#ga862843a45b01c35ffe4d44c47ea774ad">aligned_highp_bvec1</a>;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div>
<div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="a00303.html#gadd3b8bd71a758f7fb0da8e525156f34e">   84</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, bool, aligned_mediump&gt;   <a class="code" href="a00303.html#gadd3b8bd71a758f7fb0da8e525156f34e">aligned_mediump_bvec1</a>;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div>
<div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="a00303.html#gac6036449ab1c4abf8efe1ea00fcdd1c9">   87</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, bool, aligned_lowp&gt;              <a class="code" href="a00303.html#gac6036449ab1c4abf8efe1ea00fcdd1c9">aligned_lowp_bvec1</a>;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div>
<div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="a00303.html#ga56473759d2702ee19ab7f91d0017fa70">   90</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, packed_highp&gt;             <a class="code" href="a00303.html#ga56473759d2702ee19ab7f91d0017fa70">packed_highp_vec1</a>;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="a00303.html#ga71d63cead1e113fca0bcdaaa33aad050">   93</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, packed_mediump&gt;   <a class="code" href="a00303.html#ga71d63cead1e113fca0bcdaaa33aad050">packed_mediump_vec1</a>;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="a00303.html#ga0a6198fe64166a6a61084d43c71518a9">   96</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, packed_lowp&gt;              <a class="code" href="a00303.html#ga0a6198fe64166a6a61084d43c71518a9">packed_lowp_vec1</a>;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="a00303.html#gab472b2d917b5e6efd76e8c7dbfbbf9f1">   99</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, double, packed_highp&gt;    <a class="code" href="a00303.html#gab472b2d917b5e6efd76e8c7dbfbbf9f1">packed_highp_dvec1</a>;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div>
<div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="a00303.html#ga8920e90ea9c01d9c97e604a938ce2cbd">  102</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, double, packed_mediump&gt;  <a class="code" href="a00303.html#ga8920e90ea9c01d9c97e604a938ce2cbd">packed_mediump_dvec1</a>;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="a00303.html#ga054050e9d4e78d81db0e6d1573b1c624">  105</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, double, packed_lowp&gt;             <a class="code" href="a00303.html#ga054050e9d4e78d81db0e6d1573b1c624">packed_lowp_dvec1</a>;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="a00303.html#ga7245acc887a5438f46fd85fdf076bb3b">  108</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, int, packed_highp&gt;               <a class="code" href="a00303.html#ga7245acc887a5438f46fd85fdf076bb3b">packed_highp_ivec1</a>;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div>
<div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="a00303.html#ga09507ef020a49517a7bcd50438f05056">  111</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, int, packed_mediump&gt;             <a class="code" href="a00303.html#ga09507ef020a49517a7bcd50438f05056">packed_mediump_ivec1</a>;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="a00303.html#gaf22b77f1cf3e73b8b1dddfe7f959357c">  114</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, int, packed_lowp&gt;                <a class="code" href="a00303.html#gaf22b77f1cf3e73b8b1dddfe7f959357c">packed_lowp_ivec1</a>;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div>
<div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="a00303.html#ga8c32b53f628a3616aa5061e58d66fe74">  117</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, uint, packed_highp&gt;              <a class="code" href="a00303.html#ga8c32b53f628a3616aa5061e58d66fe74">packed_highp_uvec1</a>;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="a00303.html#ga2c29fb42bab9a4f9b66bc60b2e514a34">  120</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, uint, packed_mediump&gt;    <a class="code" href="a00303.html#ga2c29fb42bab9a4f9b66bc60b2e514a34">packed_mediump_uvec1</a>;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div>
<div class="line"><a name="l00123"></a><span class="lineno"><a class="line" href="a00303.html#gaf111fed760ecce16cb1988807569bee5">  123</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, uint, packed_lowp&gt;               <a class="code" href="a00303.html#gaf111fed760ecce16cb1988807569bee5">packed_lowp_uvec1</a>;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div>
<div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="a00303.html#ga439e97795314b81cd15abd4e5c2e6e7a">  126</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, bool, packed_highp&gt;              <a class="code" href="a00303.html#ga439e97795314b81cd15abd4e5c2e6e7a">packed_highp_bvec1</a>;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div>
<div class="line"><a name="l00129"></a><span class="lineno"><a class="line" href="a00303.html#ga5546d828d63010a8f9cf81161ad0275a">  129</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, bool, packed_mediump&gt;    <a class="code" href="a00303.html#ga5546d828d63010a8f9cf81161ad0275a">packed_mediump_bvec1</a>;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div>
<div class="line"><a name="l00132"></a><span class="lineno"><a class="line" href="a00303.html#gae3c8750f53259ece334d3aa3b3649a40">  132</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, bool, packed_lowp&gt;               <a class="code" href="a00303.html#gae3c8750f53259ece334d3aa3b3649a40">packed_lowp_bvec1</a>;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;        <span class="comment">// -- *vec2 --</span></div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div>
<div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="a00303.html#gac9f8482dde741fb6bab7248b81a45465">  137</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, aligned_highp&gt;    <a class="code" href="a00303.html#gac9f8482dde741fb6bab7248b81a45465">aligned_highp_vec2</a>;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;</div>
<div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="a00303.html#ga026a55ddbf2bafb1432f1157a2708616">  140</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, aligned_mediump&gt;  <a class="code" href="a00303.html#ga026a55ddbf2bafb1432f1157a2708616">aligned_mediump_vec2</a>;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00143"></a><span class="lineno"><a class="line" href="a00303.html#ga53ac5d252317f1fa43c2ef921857bf13">  143</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, aligned_lowp&gt;             <a class="code" href="a00303.html#ga53ac5d252317f1fa43c2ef921857bf13">aligned_lowp_vec2</a>;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;</div>
<div class="line"><a name="l00146"></a><span class="lineno"><a class="line" href="a00303.html#gab173a333e6b7ce153ceba66ac4a321cf">  146</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, double, aligned_highp&gt;   <a class="code" href="a00303.html#gab173a333e6b7ce153ceba66ac4a321cf">aligned_highp_dvec2</a>;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div>
<div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="a00303.html#ga9af1eabe22f569e70d9893be72eda0f5">  149</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, double, aligned_mediump&gt; <a class="code" href="a00303.html#ga9af1eabe22f569e70d9893be72eda0f5">aligned_mediump_dvec2</a>;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div>
<div class="line"><a name="l00152"></a><span class="lineno"><a class="line" href="a00303.html#ga0e37cff4a43cca866101f0a35f01db6d">  152</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, double, aligned_lowp&gt;    <a class="code" href="a00303.html#ga0e37cff4a43cca866101f0a35f01db6d">aligned_lowp_dvec2</a>;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div>
<div class="line"><a name="l00155"></a><span class="lineno"><a class="line" href="a00303.html#ga41563650f36cb7f479e080de21e08418">  155</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, int, aligned_highp&gt;              <a class="code" href="a00303.html#ga41563650f36cb7f479e080de21e08418">aligned_highp_ivec2</a>;</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div>
<div class="line"><a name="l00158"></a><span class="lineno"><a class="line" href="a00303.html#gaea13d89d49daca2c796aeaa82fc2c2f2">  158</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, int, aligned_mediump&gt;    <a class="code" href="a00303.html#gaea13d89d49daca2c796aeaa82fc2c2f2">aligned_mediump_ivec2</a>;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div>
<div class="line"><a name="l00161"></a><span class="lineno"><a class="line" href="a00303.html#ga44c4accad582cfbd7226a19b83b0cadc">  161</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, int, aligned_lowp&gt;               <a class="code" href="a00303.html#ga44c4accad582cfbd7226a19b83b0cadc">aligned_lowp_ivec2</a>;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div>
<div class="line"><a name="l00164"></a><span class="lineno"><a class="line" href="a00303.html#ga04db692662a4908beeaf5a5ba6e19483">  164</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, uint, aligned_highp&gt;             <a class="code" href="a00303.html#ga04db692662a4908beeaf5a5ba6e19483">aligned_highp_uvec2</a>;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;</div>
<div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="a00303.html#ga081d53e0a71443d0b68ea61c870f9adc">  167</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, uint, aligned_mediump&gt;   <a class="code" href="a00303.html#ga081d53e0a71443d0b68ea61c870f9adc">aligned_mediump_uvec2</a>;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div>
<div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="a00303.html#ga6f94fcd28dde906fc6cad5f742b55c1a">  170</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, uint, aligned_lowp&gt;              <a class="code" href="a00303.html#ga6f94fcd28dde906fc6cad5f742b55c1a">aligned_lowp_uvec2</a>;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div>
<div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="a00303.html#ga0731b593c5e33559954c80f8687e76c6">  173</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, bool, aligned_highp&gt;             <a class="code" href="a00303.html#ga0731b593c5e33559954c80f8687e76c6">aligned_highp_bvec2</a>;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div>
<div class="line"><a name="l00176"></a><span class="lineno"><a class="line" href="a00303.html#gacb183eb5e67ec0d0ea5a016cba962810">  176</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, bool, aligned_mediump&gt;   <a class="code" href="a00303.html#gacb183eb5e67ec0d0ea5a016cba962810">aligned_mediump_bvec2</a>;</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div>
<div class="line"><a name="l00179"></a><span class="lineno"><a class="line" href="a00303.html#ga59fadcd3835646e419372ae8b43c5d37">  179</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, bool, aligned_lowp&gt;              <a class="code" href="a00303.html#ga59fadcd3835646e419372ae8b43c5d37">aligned_lowp_bvec2</a>;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div>
<div class="line"><a name="l00182"></a><span class="lineno"><a class="line" href="a00303.html#ga6b8b9475e7c3b16aed13edbc460bbc4d">  182</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, packed_highp&gt;             <a class="code" href="a00303.html#ga6b8b9475e7c3b16aed13edbc460bbc4d">packed_highp_vec2</a>;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div>
<div class="line"><a name="l00185"></a><span class="lineno"><a class="line" href="a00303.html#ga6844c6f4691d1bf67673240850430948">  185</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, packed_mediump&gt;   <a class="code" href="a00303.html#ga6844c6f4691d1bf67673240850430948">packed_mediump_vec2</a>;</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div>
<div class="line"><a name="l00188"></a><span class="lineno"><a class="line" href="a00303.html#gafbf1c2cce307c5594b165819ed83bf5d">  188</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, packed_lowp&gt;              <a class="code" href="a00303.html#gafbf1c2cce307c5594b165819ed83bf5d">packed_lowp_vec2</a>;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;</div>
<div class="line"><a name="l00191"></a><span class="lineno"><a class="line" href="a00303.html#ga5b2dc48fa19b684d207d69c6b145eb63">  191</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, double, packed_highp&gt;    <a class="code" href="a00303.html#ga5b2dc48fa19b684d207d69c6b145eb63">packed_highp_dvec2</a>;</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div>
<div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="a00303.html#ga0c754a783b6fcf80374c013371c4dae9">  194</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, double, packed_mediump&gt;  <a class="code" href="a00303.html#ga0c754a783b6fcf80374c013371c4dae9">packed_mediump_dvec2</a>;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div>
<div class="line"><a name="l00197"></a><span class="lineno"><a class="line" href="a00303.html#gadc19938ddb204bfcb4d9ef35b1e2bf93">  197</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, double, packed_lowp&gt;             <a class="code" href="a00303.html#gadc19938ddb204bfcb4d9ef35b1e2bf93">packed_lowp_dvec2</a>;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;</div>
<div class="line"><a name="l00200"></a><span class="lineno"><a class="line" href="a00303.html#ga54f368ec6b514a5aa4f28d40e6f93ef7">  200</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, int, packed_highp&gt;               <a class="code" href="a00303.html#ga54f368ec6b514a5aa4f28d40e6f93ef7">packed_highp_ivec2</a>;</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;</div>
<div class="line"><a name="l00203"></a><span class="lineno"><a class="line" href="a00303.html#gaaa891048dddef4627df33809ec726219">  203</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, int, packed_mediump&gt;             <a class="code" href="a00303.html#gaaa891048dddef4627df33809ec726219">packed_mediump_ivec2</a>;</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div>
<div class="line"><a name="l00206"></a><span class="lineno"><a class="line" href="a00303.html#ga52635859f5ef660ab999d22c11b7867f">  206</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, int, packed_lowp&gt;                <a class="code" href="a00303.html#ga52635859f5ef660ab999d22c11b7867f">packed_lowp_ivec2</a>;</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div>
<div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="a00303.html#gab704d4fb15f6f96d70e363d5db7060cd">  209</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, uint, packed_highp&gt;              <a class="code" href="a00303.html#gab704d4fb15f6f96d70e363d5db7060cd">packed_highp_uvec2</a>;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;</div>
<div class="line"><a name="l00212"></a><span class="lineno"><a class="line" href="a00303.html#gaa1f95690a78dc12e39da32943243aeef">  212</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, uint, packed_mediump&gt;    <a class="code" href="a00303.html#gaa1f95690a78dc12e39da32943243aeef">packed_mediump_uvec2</a>;</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div>
<div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="a00303.html#ga958210fe245a75b058325d367c951132">  215</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, uint, packed_lowp&gt;               <a class="code" href="a00303.html#ga958210fe245a75b058325d367c951132">packed_lowp_uvec2</a>;</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;</div>
<div class="line"><a name="l00218"></a><span class="lineno"><a class="line" href="a00303.html#gad791d671f4fcf1ed1ea41f752916b70a">  218</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, bool, packed_highp&gt;              <a class="code" href="a00303.html#gad791d671f4fcf1ed1ea41f752916b70a">packed_highp_bvec2</a>;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div>
<div class="line"><a name="l00221"></a><span class="lineno"><a class="line" href="a00303.html#gab4c6414a59539e66a242ad4cf4b476b4">  221</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, bool, packed_mediump&gt;    <a class="code" href="a00303.html#gab4c6414a59539e66a242ad4cf4b476b4">packed_mediump_bvec2</a>;</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;</div>
<div class="line"><a name="l00224"></a><span class="lineno"><a class="line" href="a00303.html#gac969befedbda69eb78d4e23f751fdbee">  224</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, bool, packed_lowp&gt;               <a class="code" href="a00303.html#gac969befedbda69eb78d4e23f751fdbee">packed_lowp_bvec2</a>;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;        <span class="comment">// -- *vec3 --</span></div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div>
<div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="a00303.html#ga65415d2d68c9cc0ca554524a8f5510b2">  229</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, aligned_highp&gt;    <a class="code" href="a00303.html#ga65415d2d68c9cc0ca554524a8f5510b2">aligned_highp_vec3</a>;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;</div>
<div class="line"><a name="l00232"></a><span class="lineno"><a class="line" href="a00303.html#ga3a25e494173f6a64637b08a1b50a2132">  232</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, aligned_mediump&gt;  <a class="code" href="a00303.html#ga3a25e494173f6a64637b08a1b50a2132">aligned_mediump_vec3</a>;</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;</div>
<div class="line"><a name="l00235"></a><span class="lineno"><a class="line" href="a00303.html#ga98f0b5cd65fce164ff1367c2a3b3aa1e">  235</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, aligned_lowp&gt;             <a class="code" href="a00303.html#ga98f0b5cd65fce164ff1367c2a3b3aa1e">aligned_lowp_vec3</a>;</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;</div>
<div class="line"><a name="l00238"></a><span class="lineno"><a class="line" href="a00303.html#gae94ef61edfa047d05bc69b6065fc42ba">  238</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, double, aligned_highp&gt;   <a class="code" href="a00303.html#gae94ef61edfa047d05bc69b6065fc42ba">aligned_highp_dvec3</a>;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;</div>
<div class="line"><a name="l00241"></a><span class="lineno"><a class="line" href="a00303.html#ga058e7ddab1428e47f2197bdd3a5a6953">  241</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, double, aligned_mediump&gt; <a class="code" href="a00303.html#ga058e7ddab1428e47f2197bdd3a5a6953">aligned_mediump_dvec3</a>;</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;</div>
<div class="line"><a name="l00244"></a><span class="lineno"><a class="line" href="a00303.html#gab9e669c4efd52d3347fc6d5f6b20fd59">  244</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, double, aligned_lowp&gt;    <a class="code" href="a00303.html#gab9e669c4efd52d3347fc6d5f6b20fd59">aligned_lowp_dvec3</a>;</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div>
<div class="line"><a name="l00247"></a><span class="lineno"><a class="line" href="a00303.html#ga6eca5170bb35eac90b4972590fd31a06">  247</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, int, aligned_highp&gt;              <a class="code" href="a00303.html#ga6eca5170bb35eac90b4972590fd31a06">aligned_highp_ivec3</a>;</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;</div>
<div class="line"><a name="l00250"></a><span class="lineno"><a class="line" href="a00303.html#gabbf0f15e9c3d9868e43241ad018f82bd">  250</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, int, aligned_mediump&gt;    <a class="code" href="a00303.html#gabbf0f15e9c3d9868e43241ad018f82bd">aligned_mediump_ivec3</a>;</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div>
<div class="line"><a name="l00253"></a><span class="lineno"><a class="line" href="a00303.html#ga65663f10a02e52cedcddbcfe36ddf38d">  253</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, int, aligned_lowp&gt;               <a class="code" href="a00303.html#ga65663f10a02e52cedcddbcfe36ddf38d">aligned_lowp_ivec3</a>;</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;</div>
<div class="line"><a name="l00256"></a><span class="lineno"><a class="line" href="a00303.html#ga073fd6e8b241afade6d8afbd676b2667">  256</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, uint, aligned_highp&gt;             <a class="code" href="a00303.html#ga073fd6e8b241afade6d8afbd676b2667">aligned_highp_uvec3</a>;</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;</div>
<div class="line"><a name="l00259"></a><span class="lineno"><a class="line" href="a00303.html#gad6fc921bdde2bdbc7e09b028e1e9b379">  259</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, uint, aligned_mediump&gt;   <a class="code" href="a00303.html#gad6fc921bdde2bdbc7e09b028e1e9b379">aligned_mediump_uvec3</a>;</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;</div>
<div class="line"><a name="l00262"></a><span class="lineno"><a class="line" href="a00303.html#ga9e9f006970b1a00862e3e6e599eedd4c">  262</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, uint, aligned_lowp&gt;              <a class="code" href="a00303.html#ga9e9f006970b1a00862e3e6e599eedd4c">aligned_lowp_uvec3</a>;</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;</div>
<div class="line"><a name="l00265"></a><span class="lineno"><a class="line" href="a00303.html#ga0913bdf048d0cb74af1d2512aec675bc">  265</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, bool, aligned_highp&gt;             <a class="code" href="a00303.html#ga0913bdf048d0cb74af1d2512aec675bc">aligned_highp_bvec3</a>;</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;</div>
<div class="line"><a name="l00268"></a><span class="lineno"><a class="line" href="a00303.html#gacfa4a542f1b20a5b63ad702dfb6fd587">  268</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, bool, aligned_mediump&gt;   <a class="code" href="a00303.html#gacfa4a542f1b20a5b63ad702dfb6fd587">aligned_mediump_bvec3</a>;</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;</div>
<div class="line"><a name="l00271"></a><span class="lineno"><a class="line" href="a00303.html#ga83aab4d191053f169c93a3e364f2e118">  271</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, bool, aligned_lowp&gt;              <a class="code" href="a00303.html#ga83aab4d191053f169c93a3e364f2e118">aligned_lowp_bvec3</a>;</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;</div>
<div class="line"><a name="l00274"></a><span class="lineno"><a class="line" href="a00303.html#ga3815661df0e2de79beff8168c09adf1e">  274</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, packed_highp&gt;             <a class="code" href="a00303.html#ga3815661df0e2de79beff8168c09adf1e">packed_highp_vec3</a>;</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;</div>
<div class="line"><a name="l00277"></a><span class="lineno"><a class="line" href="a00303.html#gab0eb771b708c5b2205d9b14dd1434fd8">  277</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, packed_mediump&gt;   <a class="code" href="a00303.html#gab0eb771b708c5b2205d9b14dd1434fd8">packed_mediump_vec3</a>;</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;</div>
<div class="line"><a name="l00280"></a><span class="lineno"><a class="line" href="a00303.html#ga3a30c137c1f8cce478c28eab0427a570">  280</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, packed_lowp&gt;              <a class="code" href="a00303.html#ga3a30c137c1f8cce478c28eab0427a570">packed_lowp_vec3</a>;</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;</div>
<div class="line"><a name="l00283"></a><span class="lineno"><a class="line" href="a00303.html#gaaac6b356ef00154da41aaae7d1549193">  283</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, double, packed_highp&gt;    <a class="code" href="a00303.html#gaaac6b356ef00154da41aaae7d1549193">packed_highp_dvec3</a>;</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;</div>
<div class="line"><a name="l00286"></a><span class="lineno"><a class="line" href="a00303.html#ga1f18ada6f7cdd8c46db33ba987280fc4">  286</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, double, packed_mediump&gt;  <a class="code" href="a00303.html#ga1f18ada6f7cdd8c46db33ba987280fc4">packed_mediump_dvec3</a>;</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;</div>
<div class="line"><a name="l00289"></a><span class="lineno"><a class="line" href="a00303.html#ga9189210cabd6651a5e14a4c46fb20598">  289</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, double, packed_lowp&gt;             <a class="code" href="a00303.html#ga9189210cabd6651a5e14a4c46fb20598">packed_lowp_dvec3</a>;</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;</div>
<div class="line"><a name="l00292"></a><span class="lineno"><a class="line" href="a00303.html#ga865a9c7bb22434b1b8c5ac31e164b628">  292</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, int, packed_highp&gt;               <a class="code" href="a00303.html#ga865a9c7bb22434b1b8c5ac31e164b628">packed_highp_ivec3</a>;</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;</div>
<div class="line"><a name="l00295"></a><span class="lineno"><a class="line" href="a00303.html#ga06f26d54dca30994eb1fdadb8e69f4a2">  295</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, int, packed_mediump&gt;             <a class="code" href="a00303.html#ga06f26d54dca30994eb1fdadb8e69f4a2">packed_mediump_ivec3</a>;</div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;</div>
<div class="line"><a name="l00298"></a><span class="lineno"><a class="line" href="a00303.html#ga98c9d122a959e9f3ce10a5623c310f5d">  298</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, int, packed_lowp&gt;                <a class="code" href="a00303.html#ga98c9d122a959e9f3ce10a5623c310f5d">packed_lowp_ivec3</a>;</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;</div>
<div class="line"><a name="l00301"></a><span class="lineno"><a class="line" href="a00303.html#ga0b570da473fec4619db5aa0dce5133b0">  301</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, uint, packed_highp&gt;              <a class="code" href="a00303.html#ga0b570da473fec4619db5aa0dce5133b0">packed_highp_uvec3</a>;</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;</div>
<div class="line"><a name="l00304"></a><span class="lineno"><a class="line" href="a00303.html#ga1ea2bbdbcb0a69242f6d884663c1b0ab">  304</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, uint, packed_mediump&gt;    <a class="code" href="a00303.html#ga1ea2bbdbcb0a69242f6d884663c1b0ab">packed_mediump_uvec3</a>;</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;</div>
<div class="line"><a name="l00307"></a><span class="lineno"><a class="line" href="a00303.html#ga576a3f8372197a56a79dee1c8280f485">  307</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, uint, packed_lowp&gt;               <a class="code" href="a00303.html#ga576a3f8372197a56a79dee1c8280f485">packed_lowp_uvec3</a>;</div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;</div>
<div class="line"><a name="l00310"></a><span class="lineno"><a class="line" href="a00303.html#ga6a5a3250b57dfadc66735bc72911437f">  310</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, bool, packed_highp&gt;              <a class="code" href="a00303.html#ga6a5a3250b57dfadc66735bc72911437f">packed_highp_bvec3</a>;</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;</div>
<div class="line"><a name="l00313"></a><span class="lineno"><a class="line" href="a00303.html#ga70147763edff3fe96b03a0b98d6339a2">  313</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, bool, packed_mediump&gt;    <a class="code" href="a00303.html#ga70147763edff3fe96b03a0b98d6339a2">packed_mediump_bvec3</a>;</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;</div>
<div class="line"><a name="l00316"></a><span class="lineno"><a class="line" href="a00303.html#ga7c20adbe1409e3fe4544677a7f6fe954">  316</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, bool, packed_lowp&gt;               <a class="code" href="a00303.html#ga7c20adbe1409e3fe4544677a7f6fe954">packed_lowp_bvec3</a>;</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;        <span class="comment">// -- *vec4 --</span></div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div>
<div class="line"><a name="l00321"></a><span class="lineno"><a class="line" href="a00303.html#ga7cb26d354dd69d23849c34c4fba88da9">  321</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, aligned_highp&gt;    <a class="code" href="a00303.html#ga7cb26d354dd69d23849c34c4fba88da9">aligned_highp_vec4</a>;</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;</div>
<div class="line"><a name="l00324"></a><span class="lineno"><a class="line" href="a00303.html#ga320d1c661cff2ef214eb50241f2928b2">  324</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, aligned_mediump&gt;  <a class="code" href="a00303.html#ga320d1c661cff2ef214eb50241f2928b2">aligned_mediump_vec4</a>;</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;</div>
<div class="line"><a name="l00327"></a><span class="lineno"><a class="line" href="a00303.html#ga82f7275d6102593a69ce38cdad680409">  327</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, aligned_lowp&gt;             <a class="code" href="a00303.html#ga82f7275d6102593a69ce38cdad680409">aligned_lowp_vec4</a>;</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;</div>
<div class="line"><a name="l00330"></a><span class="lineno"><a class="line" href="a00303.html#ga8fad35c5677f228e261fe541f15363a4">  330</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, double, aligned_highp&gt;   <a class="code" href="a00303.html#ga8fad35c5677f228e261fe541f15363a4">aligned_highp_dvec4</a>;</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;</div>
<div class="line"><a name="l00333"></a><span class="lineno"><a class="line" href="a00303.html#gaffd747ea2aea1e69c2ecb04e68521b21">  333</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, double, aligned_mediump&gt; <a class="code" href="a00303.html#gaffd747ea2aea1e69c2ecb04e68521b21">aligned_mediump_dvec4</a>;</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;</div>
<div class="line"><a name="l00336"></a><span class="lineno"><a class="line" href="a00303.html#ga226f5ec7a953cea559c16fe3aff9924f">  336</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, double, aligned_lowp&gt;    <a class="code" href="a00303.html#ga226f5ec7a953cea559c16fe3aff9924f">aligned_lowp_dvec4</a>;</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;</div>
<div class="line"><a name="l00339"></a><span class="lineno"><a class="line" href="a00303.html#ga31bfa801e1579fdba752ec3f7a45ec91">  339</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, int, aligned_highp&gt;              <a class="code" href="a00303.html#ga31bfa801e1579fdba752ec3f7a45ec91">aligned_highp_ivec4</a>;</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;</div>
<div class="line"><a name="l00342"></a><span class="lineno"><a class="line" href="a00303.html#ga6099dd7878d0a78101a4250d8cd2d736">  342</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, int, aligned_mediump&gt;    <a class="code" href="a00303.html#ga6099dd7878d0a78101a4250d8cd2d736">aligned_mediump_ivec4</a>;</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;</div>
<div class="line"><a name="l00345"></a><span class="lineno"><a class="line" href="a00303.html#gaae92fcec8b2e0328ffbeac31cc4fc419">  345</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, int, aligned_lowp&gt;               <a class="code" href="a00303.html#gaae92fcec8b2e0328ffbeac31cc4fc419">aligned_lowp_ivec4</a>;</div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;</div>
<div class="line"><a name="l00348"></a><span class="lineno"><a class="line" href="a00303.html#gabdd60462042859f876c17c7346c732a5">  348</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, uint, aligned_highp&gt;             <a class="code" href="a00303.html#gabdd60462042859f876c17c7346c732a5">aligned_highp_uvec4</a>;</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;</div>
<div class="line"><a name="l00351"></a><span class="lineno"><a class="line" href="a00303.html#ga73ea0c1ba31580e107d21270883f51fc">  351</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, uint, aligned_mediump&gt;   <a class="code" href="a00303.html#ga73ea0c1ba31580e107d21270883f51fc">aligned_mediump_uvec4</a>;</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;</div>
<div class="line"><a name="l00354"></a><span class="lineno"><a class="line" href="a00303.html#ga46b1b0b9eb8625a5d69137bd66cd13dc">  354</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, uint, aligned_lowp&gt;              <a class="code" href="a00303.html#ga46b1b0b9eb8625a5d69137bd66cd13dc">aligned_lowp_uvec4</a>;</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;</div>
<div class="line"><a name="l00357"></a><span class="lineno"><a class="line" href="a00303.html#ga9df1d0c425852cf63a57e533b7a83f4f">  357</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, bool, aligned_highp&gt;             <a class="code" href="a00303.html#ga9df1d0c425852cf63a57e533b7a83f4f">aligned_highp_bvec4</a>;</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;</div>
<div class="line"><a name="l00360"></a><span class="lineno"><a class="line" href="a00303.html#ga91bc1f513bb9b0fd60281d57ded9a48c">  360</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, bool, aligned_mediump&gt;   <a class="code" href="a00303.html#ga91bc1f513bb9b0fd60281d57ded9a48c">aligned_mediump_bvec4</a>;</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;</div>
<div class="line"><a name="l00363"></a><span class="lineno"><a class="line" href="a00303.html#gaa7a76555ee4853614e5755181a8dd54e">  363</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, bool, aligned_lowp&gt;              <a class="code" href="a00303.html#gaa7a76555ee4853614e5755181a8dd54e">aligned_lowp_bvec4</a>;</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;</div>
<div class="line"><a name="l00366"></a><span class="lineno"><a class="line" href="a00303.html#ga4015f36bf5a5adb6ac5d45beed959867">  366</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, packed_highp&gt;             <a class="code" href="a00303.html#ga4015f36bf5a5adb6ac5d45beed959867">packed_highp_vec4</a>;</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;</div>
<div class="line"><a name="l00369"></a><span class="lineno"><a class="line" href="a00303.html#ga68c9bb24f387b312bae6a0a68e74d95e">  369</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, packed_mediump&gt;   <a class="code" href="a00303.html#ga68c9bb24f387b312bae6a0a68e74d95e">packed_mediump_vec4</a>;</div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;</div>
<div class="line"><a name="l00372"></a><span class="lineno"><a class="line" href="a00303.html#ga3cc94fb8de80bbd8a4aa7a5b206d304a">  372</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, packed_lowp&gt;              <a class="code" href="a00303.html#ga3cc94fb8de80bbd8a4aa7a5b206d304a">packed_lowp_vec4</a>;</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;</div>
<div class="line"><a name="l00375"></a><span class="lineno"><a class="line" href="a00303.html#ga81b5368fe485e2630aa9b44832d592e7">  375</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, double, packed_highp&gt;    <a class="code" href="a00303.html#ga81b5368fe485e2630aa9b44832d592e7">packed_highp_dvec4</a>;</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;</div>
<div class="line"><a name="l00378"></a><span class="lineno"><a class="line" href="a00303.html#ga568b850f1116b667043533cf77826968">  378</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, double, packed_mediump&gt;  <a class="code" href="a00303.html#ga568b850f1116b667043533cf77826968">packed_mediump_dvec4</a>;</div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;</div>
<div class="line"><a name="l00381"></a><span class="lineno"><a class="line" href="a00303.html#ga262dafd0c001c3a38d1cc91d024ca738">  381</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, double, packed_lowp&gt;             <a class="code" href="a00303.html#ga262dafd0c001c3a38d1cc91d024ca738">packed_lowp_dvec4</a>;</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;</div>
<div class="line"><a name="l00384"></a><span class="lineno"><a class="line" href="a00303.html#gad6f1b4e3a51c2c051814b60d5d1b8895">  384</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, int, packed_highp&gt;               <a class="code" href="a00303.html#gad6f1b4e3a51c2c051814b60d5d1b8895">packed_highp_ivec4</a>;</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;</div>
<div class="line"><a name="l00387"></a><span class="lineno"><a class="line" href="a00303.html#ga70130dc8ed9c966ec2a221ce586d45d8">  387</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, int, packed_mediump&gt;             <a class="code" href="a00303.html#ga70130dc8ed9c966ec2a221ce586d45d8">packed_mediump_ivec4</a>;</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;</div>
<div class="line"><a name="l00390"></a><span class="lineno"><a class="line" href="a00303.html#ga931731b8ae3b54c7ecc221509dae96bc">  390</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, int, packed_lowp&gt;                <a class="code" href="a00303.html#ga931731b8ae3b54c7ecc221509dae96bc">packed_lowp_ivec4</a>;</div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;</div>
<div class="line"><a name="l00393"></a><span class="lineno"><a class="line" href="a00303.html#gaa582f38c82aef61dea7aaedf15bb06a6">  393</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, uint, packed_highp&gt;              <a class="code" href="a00303.html#gaa582f38c82aef61dea7aaedf15bb06a6">packed_highp_uvec4</a>;</div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;</div>
<div class="line"><a name="l00396"></a><span class="lineno"><a class="line" href="a00303.html#ga63a73be86a4f07ea7a7499ab0bfebe45">  396</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, uint, packed_mediump&gt;    <a class="code" href="a00303.html#ga63a73be86a4f07ea7a7499ab0bfebe45">packed_mediump_uvec4</a>;</div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;</div>
<div class="line"><a name="l00399"></a><span class="lineno"><a class="line" href="a00303.html#gafdd97922b4a2a42cd0c99a13877ff4da">  399</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, uint, packed_lowp&gt;               <a class="code" href="a00303.html#gafdd97922b4a2a42cd0c99a13877ff4da">packed_lowp_uvec4</a>;</div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;</div>
<div class="line"><a name="l00402"></a><span class="lineno"><a class="line" href="a00303.html#ga09f517d88b996ef1b2f42fd54222b82d">  402</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, bool, packed_highp&gt;              <a class="code" href="a00303.html#ga09f517d88b996ef1b2f42fd54222b82d">packed_highp_bvec4</a>;</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;</div>
<div class="line"><a name="l00405"></a><span class="lineno"><a class="line" href="a00303.html#ga7b1620f259595b9da47a6374fc44588a">  405</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, bool, packed_mediump&gt;    <a class="code" href="a00303.html#ga7b1620f259595b9da47a6374fc44588a">packed_mediump_bvec4</a>;</div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;</div>
<div class="line"><a name="l00408"></a><span class="lineno"><a class="line" href="a00303.html#gae473587cff3092edc0877fc691c26a0b">  408</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, bool, packed_lowp&gt;               <a class="code" href="a00303.html#gae473587cff3092edc0877fc691c26a0b">packed_lowp_bvec4</a>;</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;</div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;        <span class="comment">// -- *mat2 --</span></div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;</div>
<div class="line"><a name="l00413"></a><span class="lineno"><a class="line" href="a00303.html#gaf9db5e8a929c317da5aa12cc53741b63">  413</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, aligned_highp&gt;         <a class="code" href="a00303.html#gaf9db5e8a929c317da5aa12cc53741b63">aligned_highp_mat2</a>;</div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;</div>
<div class="line"><a name="l00416"></a><span class="lineno"><a class="line" href="a00303.html#gaf6f041b212c57664d88bc6aefb7e36f3">  416</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, aligned_mediump&gt;       <a class="code" href="a00303.html#gaf6f041b212c57664d88bc6aefb7e36f3">aligned_mediump_mat2</a>;</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;</div>
<div class="line"><a name="l00419"></a><span class="lineno"><a class="line" href="a00303.html#ga17c424412207b00dba1cf587b099eea3">  419</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, aligned_lowp&gt;          <a class="code" href="a00303.html#ga17c424412207b00dba1cf587b099eea3">aligned_lowp_mat2</a>;</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;</div>
<div class="line"><a name="l00422"></a><span class="lineno"><a class="line" href="a00303.html#ga3a7eeae43cb7673e14cc89bf02f7dd45">  422</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, aligned_highp&gt;        <a class="code" href="a00303.html#ga3a7eeae43cb7673e14cc89bf02f7dd45">aligned_highp_dmat2</a>;</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;</div>
<div class="line"><a name="l00425"></a><span class="lineno"><a class="line" href="a00303.html#ga62a2dfd668c91072b72c3109fc6cda28">  425</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, aligned_mediump&gt;      <a class="code" href="a00303.html#ga62a2dfd668c91072b72c3109fc6cda28">aligned_mediump_dmat2</a>;</div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;</div>
<div class="line"><a name="l00428"></a><span class="lineno"><a class="line" href="a00303.html#ga79a90173d8faa9816dc852ce447d66ca">  428</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, aligned_lowp&gt;         <a class="code" href="a00303.html#ga79a90173d8faa9816dc852ce447d66ca">aligned_lowp_dmat2</a>;</div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;</div>
<div class="line"><a name="l00431"></a><span class="lineno"><a class="line" href="a00303.html#ga2f2d913d8cca2f935b2522964408c0b2">  431</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, packed_highp&gt;          <a class="code" href="a00303.html#ga2f2d913d8cca2f935b2522964408c0b2">packed_highp_mat2</a>;</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;</div>
<div class="line"><a name="l00434"></a><span class="lineno"><a class="line" href="a00303.html#ga43cd36d430c5187bfdca34a23cb41581">  434</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, packed_mediump&gt;        <a class="code" href="a00303.html#ga43cd36d430c5187bfdca34a23cb41581">packed_mediump_mat2</a>;</div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;</div>
<div class="line"><a name="l00437"></a><span class="lineno"><a class="line" href="a00303.html#ga70dcb9ef0b24e832772a7405efa9669a">  437</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga70dcb9ef0b24e832772a7405efa9669a">packed_lowp_mat2</a>;</div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;</div>
<div class="line"><a name="l00440"></a><span class="lineno"><a class="line" href="a00303.html#gae29686632fd05efac0675d9a6370d77b">  440</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, packed_highp&gt;         <a class="code" href="a00303.html#gae29686632fd05efac0675d9a6370d77b">packed_highp_dmat2</a>;</div>
<div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;</div>
<div class="line"><a name="l00443"></a><span class="lineno"><a class="line" href="a00303.html#ga9d60e32d3fcb51f817046cd881fdbf57">  443</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, packed_mediump&gt;       <a class="code" href="a00303.html#ga9d60e32d3fcb51f817046cd881fdbf57">packed_mediump_dmat2</a>;</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;</div>
<div class="line"><a name="l00446"></a><span class="lineno"><a class="line" href="a00303.html#gac93f9b1a35b9de4f456b9f2dfeaf1097">  446</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, packed_lowp&gt;          <a class="code" href="a00303.html#gac93f9b1a35b9de4f456b9f2dfeaf1097">packed_lowp_dmat2</a>;</div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;        <span class="comment">// -- *mat3 --</span></div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;</div>
<div class="line"><a name="l00451"></a><span class="lineno"><a class="line" href="a00303.html#gabab3afcc04459c7b123604ae5dc663f6">  451</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, aligned_highp&gt;         <a class="code" href="a00303.html#gabab3afcc04459c7b123604ae5dc663f6">aligned_highp_mat3</a>;</div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;</div>
<div class="line"><a name="l00454"></a><span class="lineno"><a class="line" href="a00303.html#ga3b76ba17ae5d53debeb6f7e55919a57c">  454</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga3b76ba17ae5d53debeb6f7e55919a57c">aligned_mediump_mat3</a>;</div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;</div>
<div class="line"><a name="l00457"></a><span class="lineno"><a class="line" href="a00303.html#ga1eb9076cc28ead5020fd3029fd0472c5">  457</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, aligned_lowp&gt;          <a class="code" href="a00303.html#ga1eb9076cc28ead5020fd3029fd0472c5">aligned_lowp_mat3</a>;</div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;</div>
<div class="line"><a name="l00460"></a><span class="lineno"><a class="line" href="a00303.html#gad8f6abb2c9994850b5d5c04a5f979ed8">  460</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, aligned_highp&gt;        <a class="code" href="a00303.html#gad8f6abb2c9994850b5d5c04a5f979ed8">aligned_highp_dmat3</a>;</div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;</div>
<div class="line"><a name="l00463"></a><span class="lineno"><a class="line" href="a00303.html#ga6dc2832b747c00e0a0df621aba196960">  463</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, aligned_mediump&gt;      <a class="code" href="a00303.html#ga6dc2832b747c00e0a0df621aba196960">aligned_mediump_dmat3</a>;</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;</div>
<div class="line"><a name="l00466"></a><span class="lineno"><a class="line" href="a00303.html#gac00e15efded8a57c9dec3aed0fb547e7">  466</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, aligned_lowp&gt;         <a class="code" href="a00303.html#gac00e15efded8a57c9dec3aed0fb547e7">aligned_lowp_dmat3</a>;</div>
<div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;</div>
<div class="line"><a name="l00469"></a><span class="lineno"><a class="line" href="a00303.html#gabdd5fbffe8b8b8a7b33523f25b120dbe">  469</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, packed_highp&gt;          <a class="code" href="a00303.html#gabdd5fbffe8b8b8a7b33523f25b120dbe">packed_highp_mat3</a>;</div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;</div>
<div class="line"><a name="l00472"></a><span class="lineno"><a class="line" href="a00303.html#ga13a75c6cbd0a411f694bc82486cd1e55">  472</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, packed_mediump&gt;        <a class="code" href="a00303.html#ga13a75c6cbd0a411f694bc82486cd1e55">packed_mediump_mat3</a>;</div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;</div>
<div class="line"><a name="l00475"></a><span class="lineno"><a class="line" href="a00303.html#ga0d22400969dd223465b2900fecfb4f53">  475</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga0d22400969dd223465b2900fecfb4f53">packed_lowp_mat3</a>;</div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;</div>
<div class="line"><a name="l00478"></a><span class="lineno"><a class="line" href="a00303.html#gadac7c040c4810dd52b36fcd09d097400">  478</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, packed_highp&gt;         <a class="code" href="a00303.html#gadac7c040c4810dd52b36fcd09d097400">packed_highp_dmat3</a>;</div>
<div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;</div>
<div class="line"><a name="l00481"></a><span class="lineno"><a class="line" href="a00303.html#gaf969eb879c76a5f4576e4a1e10095cf6">  481</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, packed_mediump&gt;       <a class="code" href="a00303.html#gaf969eb879c76a5f4576e4a1e10095cf6">packed_mediump_dmat3</a>;</div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;</div>
<div class="line"><a name="l00484"></a><span class="lineno"><a class="line" href="a00303.html#ga3894a059eeaacec8791c25de398d9955">  484</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, packed_lowp&gt;          <a class="code" href="a00303.html#ga3894a059eeaacec8791c25de398d9955">packed_lowp_dmat3</a>;</div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;</div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;        <span class="comment">// -- *mat4 --</span></div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;</div>
<div class="line"><a name="l00489"></a><span class="lineno"><a class="line" href="a00303.html#ga058ae939bfdbcbb80521dd4a3b01afba">  489</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, aligned_highp&gt;         <a class="code" href="a00303.html#ga058ae939bfdbcbb80521dd4a3b01afba">aligned_highp_mat4</a>;</div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;</div>
<div class="line"><a name="l00492"></a><span class="lineno"><a class="line" href="a00303.html#gaeefee8317192174596852ce19b602720">  492</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, aligned_mediump&gt;       <a class="code" href="a00303.html#gaeefee8317192174596852ce19b602720">aligned_mediump_mat4</a>;</div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;</div>
<div class="line"><a name="l00495"></a><span class="lineno"><a class="line" href="a00303.html#ga25ea2f684e36aa5e978b4f2f86593824">  495</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, aligned_lowp&gt;          <a class="code" href="a00303.html#ga25ea2f684e36aa5e978b4f2f86593824">aligned_lowp_mat4</a>;</div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;</div>
<div class="line"><a name="l00498"></a><span class="lineno"><a class="line" href="a00303.html#gacaa7407ea00ffdd322ce86a57adb547e">  498</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, aligned_highp&gt;        <a class="code" href="a00303.html#gacaa7407ea00ffdd322ce86a57adb547e">aligned_highp_dmat4</a>;</div>
<div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;</div>
<div class="line"><a name="l00501"></a><span class="lineno"><a class="line" href="a00303.html#ga8a9376d82f0e946e25137eb55543e6ce">  501</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, aligned_mediump&gt;      <a class="code" href="a00303.html#ga8a9376d82f0e946e25137eb55543e6ce">aligned_mediump_dmat4</a>;</div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;</div>
<div class="line"><a name="l00504"></a><span class="lineno"><a class="line" href="a00303.html#gab92c6d7d58d43dfb8147e9aedfe8351b">  504</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, aligned_lowp&gt;         <a class="code" href="a00303.html#gab92c6d7d58d43dfb8147e9aedfe8351b">aligned_lowp_dmat4</a>;</div>
<div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;</div>
<div class="line"><a name="l00507"></a><span class="lineno"><a class="line" href="a00303.html#ga253e8379b08d2dc6fe2800b2fb913203">  507</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, packed_highp&gt;          <a class="code" href="a00303.html#ga253e8379b08d2dc6fe2800b2fb913203">packed_highp_mat4</a>;</div>
<div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;</div>
<div class="line"><a name="l00510"></a><span class="lineno"><a class="line" href="a00303.html#gae89d72ffc149147f61df701bbc8755bf">  510</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, packed_mediump&gt;        <a class="code" href="a00303.html#gae89d72ffc149147f61df701bbc8755bf">packed_mediump_mat4</a>;</div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;</div>
<div class="line"><a name="l00513"></a><span class="lineno"><a class="line" href="a00303.html#ga2a1dd2387725a335413d4c4fee8609c4">  513</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga2a1dd2387725a335413d4c4fee8609c4">packed_lowp_mat4</a>;</div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;</div>
<div class="line"><a name="l00516"></a><span class="lineno"><a class="line" href="a00303.html#ga6718822cd7af005a9b5bd6ee282f6ba6">  516</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, packed_highp&gt;         <a class="code" href="a00303.html#ga6718822cd7af005a9b5bd6ee282f6ba6">packed_highp_dmat4</a>;</div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;</div>
<div class="line"><a name="l00519"></a><span class="lineno"><a class="line" href="a00303.html#ga4b0ee7996651ddd04eaa0c4cdbb66332">  519</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, packed_mediump&gt;       <a class="code" href="a00303.html#ga4b0ee7996651ddd04eaa0c4cdbb66332">packed_mediump_dmat4</a>;</div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;</div>
<div class="line"><a name="l00522"></a><span class="lineno"><a class="line" href="a00303.html#ga03e1edf5666c40affe39aee35c87956f">  522</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, packed_lowp&gt;          <a class="code" href="a00303.html#ga03e1edf5666c40affe39aee35c87956f">packed_lowp_dmat4</a>;</div>
<div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;</div>
<div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;        <span class="comment">// -- *mat2x2 --</span></div>
<div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;</div>
<div class="line"><a name="l00527"></a><span class="lineno"><a class="line" href="a00303.html#gab559d943abf92bc588bcd3f4c0e4664b">  527</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, aligned_highp&gt;         <a class="code" href="a00303.html#gab559d943abf92bc588bcd3f4c0e4664b">aligned_highp_mat2x2</a>;</div>
<div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;</div>
<div class="line"><a name="l00530"></a><span class="lineno"><a class="line" href="a00303.html#ga04bf49316ee777d42fcfe681ee37d7be">  530</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga04bf49316ee777d42fcfe681ee37d7be">aligned_mediump_mat2x2</a>;</div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;</div>
<div class="line"><a name="l00533"></a><span class="lineno"><a class="line" href="a00303.html#ga0e44aeb930a47f9cbf2db15b56433b0f">  533</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, aligned_lowp&gt;          <a class="code" href="a00303.html#ga0e44aeb930a47f9cbf2db15b56433b0f">aligned_lowp_mat2x2</a>;</div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;</div>
<div class="line"><a name="l00536"></a><span class="lineno"><a class="line" href="a00303.html#gaef26dfe3855a91644665b55c9096a8c8">  536</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, aligned_highp&gt;        <a class="code" href="a00303.html#gaef26dfe3855a91644665b55c9096a8c8">aligned_highp_dmat2x2</a>;</div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;</div>
<div class="line"><a name="l00539"></a><span class="lineno"><a class="line" href="a00303.html#ga9b7feec247d378dd407ba81f56ea96c8">  539</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, aligned_mediump&gt;      <a class="code" href="a00303.html#ga9b7feec247d378dd407ba81f56ea96c8">aligned_mediump_dmat2x2</a>;</div>
<div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;</div>
<div class="line"><a name="l00542"></a><span class="lineno"><a class="line" href="a00303.html#ga07cb8e846666cbf56045b064fb553d2e">  542</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, aligned_lowp&gt;         <a class="code" href="a00303.html#ga07cb8e846666cbf56045b064fb553d2e">aligned_lowp_dmat2x2</a>;</div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;</div>
<div class="line"><a name="l00545"></a><span class="lineno"><a class="line" href="a00303.html#ga245c12d2daf67feecaa2d3277c8f6661">  545</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, packed_highp&gt;          <a class="code" href="a00303.html#ga245c12d2daf67feecaa2d3277c8f6661">packed_highp_mat2x2</a>;</div>
<div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;</div>
<div class="line"><a name="l00548"></a><span class="lineno"><a class="line" href="a00303.html#ga2d2a73e662759e301c22b8931ff6a526">  548</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, packed_mediump&gt;        <a class="code" href="a00303.html#ga2d2a73e662759e301c22b8931ff6a526">packed_mediump_mat2x2</a>;</div>
<div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;</div>
<div class="line"><a name="l00551"></a><span class="lineno"><a class="line" href="a00303.html#gac70667c7642ec8d50245e6e6936a3927">  551</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, float, packed_lowp&gt;           <a class="code" href="a00303.html#gac70667c7642ec8d50245e6e6936a3927">packed_lowp_mat2x2</a>;</div>
<div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;</div>
<div class="line"><a name="l00554"></a><span class="lineno"><a class="line" href="a00303.html#ga22bd6382b16052e301edbfc031b9f37a">  554</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, packed_highp&gt;         <a class="code" href="a00303.html#ga22bd6382b16052e301edbfc031b9f37a">packed_highp_dmat2x2</a>;</div>
<div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;</div>
<div class="line"><a name="l00557"></a><span class="lineno"><a class="line" href="a00303.html#ga39e8bb9b70e5694964e8266a21ba534e">  557</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, packed_mediump&gt;       <a class="code" href="a00303.html#ga39e8bb9b70e5694964e8266a21ba534e">packed_mediump_dmat2x2</a>;</div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;</div>
<div class="line"><a name="l00560"></a><span class="lineno"><a class="line" href="a00303.html#gaeeaff6c132ec91ebd21da3a2399548ea">  560</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, double, packed_lowp&gt;          <a class="code" href="a00303.html#gaeeaff6c132ec91ebd21da3a2399548ea">packed_lowp_dmat2x2</a>;</div>
<div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;</div>
<div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;        <span class="comment">// -- *mat2x3 --</span></div>
<div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;</div>
<div class="line"><a name="l00565"></a><span class="lineno"><a class="line" href="a00303.html#ga50c9af5aa3a848956d625fc64dc8488e">  565</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, float, aligned_highp&gt;         <a class="code" href="a00303.html#ga50c9af5aa3a848956d625fc64dc8488e">aligned_highp_mat2x3</a>;</div>
<div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;</div>
<div class="line"><a name="l00568"></a><span class="lineno"><a class="line" href="a00303.html#ga26a0b61e444a51a37b9737cf4d84291b">  568</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga26a0b61e444a51a37b9737cf4d84291b">aligned_mediump_mat2x3</a>;</div>
<div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;</div>
<div class="line"><a name="l00571"></a><span class="lineno"><a class="line" href="a00303.html#ga7dec6d96bc61312b1e56d137c9c74030">  571</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, float, aligned_lowp&gt;          <a class="code" href="a00303.html#ga7dec6d96bc61312b1e56d137c9c74030">aligned_lowp_mat2x3</a>;</div>
<div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;</div>
<div class="line"><a name="l00574"></a><span class="lineno"><a class="line" href="a00303.html#gaa7c9d4ab7ab651cdf8001fe7843e238b">  574</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, double, aligned_highp&gt;        <a class="code" href="a00303.html#gaa7c9d4ab7ab651cdf8001fe7843e238b">aligned_highp_dmat2x3</a>;</div>
<div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;</div>
<div class="line"><a name="l00577"></a><span class="lineno"><a class="line" href="a00303.html#gafcb189f4f93648fe7ca802ca4aca2eb8">  577</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, double, aligned_mediump&gt;      <a class="code" href="a00303.html#gafcb189f4f93648fe7ca802ca4aca2eb8">aligned_mediump_dmat2x3</a>;</div>
<div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;</div>
<div class="line"><a name="l00580"></a><span class="lineno"><a class="line" href="a00303.html#ga7a4536b6e1f2ebb690f63816b5d7e48b">  580</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, double, aligned_lowp&gt;         <a class="code" href="a00303.html#ga7a4536b6e1f2ebb690f63816b5d7e48b">aligned_lowp_dmat2x3</a>;</div>
<div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;</div>
<div class="line"><a name="l00583"></a><span class="lineno"><a class="line" href="a00303.html#ga069cc8892aadae144c00f35297617d44">  583</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, float, packed_highp&gt;          <a class="code" href="a00303.html#ga069cc8892aadae144c00f35297617d44">packed_highp_mat2x3</a>;</div>
<div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;</div>
<div class="line"><a name="l00586"></a><span class="lineno"><a class="line" href="a00303.html#ga99049db01faf1e95ed9fb875a47dffe2">  586</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, float, packed_mediump&gt;        <a class="code" href="a00303.html#ga99049db01faf1e95ed9fb875a47dffe2">packed_mediump_mat2x3</a>;</div>
<div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;</div>
<div class="line"><a name="l00589"></a><span class="lineno"><a class="line" href="a00303.html#ga3e7df5a11e1be27bc29a4c0d3956f234">  589</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga3e7df5a11e1be27bc29a4c0d3956f234">packed_lowp_mat2x3</a>;</div>
<div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;</div>
<div class="line"><a name="l00592"></a><span class="lineno"><a class="line" href="a00303.html#ga999d82719696d4c59f4d236dd08f273d">  592</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, double, packed_highp&gt;         <a class="code" href="a00303.html#ga999d82719696d4c59f4d236dd08f273d">packed_highp_dmat2x3</a>;</div>
<div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;</div>
<div class="line"><a name="l00595"></a><span class="lineno"><a class="line" href="a00303.html#ga8897c6d9adb4140b1c3b0a07b8f0a430">  595</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, double, packed_mediump&gt;       <a class="code" href="a00303.html#ga8897c6d9adb4140b1c3b0a07b8f0a430">packed_mediump_dmat2x3</a>;</div>
<div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;</div>
<div class="line"><a name="l00598"></a><span class="lineno"><a class="line" href="a00303.html#ga2ccdcd4846775cbe4f9d12e71d55b5d2">  598</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, double, packed_lowp&gt;          <a class="code" href="a00303.html#ga2ccdcd4846775cbe4f9d12e71d55b5d2">packed_lowp_dmat2x3</a>;</div>
<div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;</div>
<div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;        <span class="comment">// -- *mat2x4 --</span></div>
<div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;</div>
<div class="line"><a name="l00603"></a><span class="lineno"><a class="line" href="a00303.html#ga0edcfdd179f8a158342eead48a4d0c2a">  603</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, float, aligned_highp&gt;         <a class="code" href="a00303.html#ga0edcfdd179f8a158342eead48a4d0c2a">aligned_highp_mat2x4</a>;</div>
<div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;</div>
<div class="line"><a name="l00606"></a><span class="lineno"><a class="line" href="a00303.html#ga163facc9ed2692ea1300ed57c5d12b17">  606</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga163facc9ed2692ea1300ed57c5d12b17">aligned_mediump_mat2x4</a>;</div>
<div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;</div>
<div class="line"><a name="l00609"></a><span class="lineno"><a class="line" href="a00303.html#gaa694fab1f8df5f658846573ba8ffc563">  609</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, float, aligned_lowp&gt;          <a class="code" href="a00303.html#gaa694fab1f8df5f658846573ba8ffc563">aligned_lowp_mat2x4</a>;</div>
<div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;</div>
<div class="line"><a name="l00612"></a><span class="lineno"><a class="line" href="a00303.html#gaa0d2b8a75f1908dcf32c27f8524bdced">  612</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, double, aligned_highp&gt;        <a class="code" href="a00303.html#gaa0d2b8a75f1908dcf32c27f8524bdced">aligned_highp_dmat2x4</a>;</div>
<div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;</div>
<div class="line"><a name="l00615"></a><span class="lineno"><a class="line" href="a00303.html#ga92f8873e3bbd5ca1323c8bbe5725cc5e">  615</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, double, aligned_mediump&gt;      <a class="code" href="a00303.html#ga92f8873e3bbd5ca1323c8bbe5725cc5e">aligned_mediump_dmat2x4</a>;</div>
<div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;</div>
<div class="line"><a name="l00618"></a><span class="lineno"><a class="line" href="a00303.html#gab0cf4f7c9a264941519acad286e055ea">  618</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, double, aligned_lowp&gt;         <a class="code" href="a00303.html#gab0cf4f7c9a264941519acad286e055ea">aligned_lowp_dmat2x4</a>;</div>
<div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;</div>
<div class="line"><a name="l00621"></a><span class="lineno"><a class="line" href="a00303.html#ga6904d09b62141d09712b76983892f95b">  621</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, float, packed_highp&gt;          <a class="code" href="a00303.html#ga6904d09b62141d09712b76983892f95b">packed_highp_mat2x4</a>;</div>
<div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;</div>
<div class="line"><a name="l00624"></a><span class="lineno"><a class="line" href="a00303.html#gad43a240533f388ce0504b495d9df3d52">  624</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, float, packed_mediump&gt;        <a class="code" href="a00303.html#gad43a240533f388ce0504b495d9df3d52">packed_mediump_mat2x4</a>;</div>
<div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;</div>
<div class="line"><a name="l00627"></a><span class="lineno"><a class="line" href="a00303.html#gaea9c555e669dc56c45d95dcc75d59bf3">  627</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, float, packed_lowp&gt;           <a class="code" href="a00303.html#gaea9c555e669dc56c45d95dcc75d59bf3">packed_lowp_mat2x4</a>;</div>
<div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;</div>
<div class="line"><a name="l00630"></a><span class="lineno"><a class="line" href="a00303.html#ga6998ac2a8d7fe456b651a6336ed26bb0">  630</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, double, packed_highp&gt;         <a class="code" href="a00303.html#ga6998ac2a8d7fe456b651a6336ed26bb0">packed_highp_dmat2x4</a>;</div>
<div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;</div>
<div class="line"><a name="l00633"></a><span class="lineno"><a class="line" href="a00303.html#gaaa4126969c765e7faa2ebf6951c22ffb">  633</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, double, packed_mediump&gt;       <a class="code" href="a00303.html#gaaa4126969c765e7faa2ebf6951c22ffb">packed_mediump_dmat2x4</a>;</div>
<div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;</div>
<div class="line"><a name="l00636"></a><span class="lineno"><a class="line" href="a00303.html#gac870c47d2d9d48503f6c9ee3baec8ce1">  636</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, double, packed_lowp&gt;          <a class="code" href="a00303.html#gac870c47d2d9d48503f6c9ee3baec8ce1">packed_lowp_dmat2x4</a>;</div>
<div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;</div>
<div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;        <span class="comment">// -- *mat3x2 --</span></div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;</div>
<div class="line"><a name="l00641"></a><span class="lineno"><a class="line" href="a00303.html#ga9fc2167b47c9be9295f2d8eea7f0ca75">  641</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, float, aligned_highp&gt;         <a class="code" href="a00303.html#ga9fc2167b47c9be9295f2d8eea7f0ca75">aligned_highp_mat3x2</a>;</div>
<div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;</div>
<div class="line"><a name="l00644"></a><span class="lineno"><a class="line" href="a00303.html#ga80dee705d714300378e0847f45059097">  644</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga80dee705d714300378e0847f45059097">aligned_mediump_mat3x2</a>;</div>
<div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;</div>
<div class="line"><a name="l00647"></a><span class="lineno"><a class="line" href="a00303.html#ga2d6639f0bd777bae1ee0eba71cd7bfdc">  647</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, float, aligned_lowp&gt;          <a class="code" href="a00303.html#ga2d6639f0bd777bae1ee0eba71cd7bfdc">aligned_lowp_mat3x2</a>;</div>
<div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;</div>
<div class="line"><a name="l00650"></a><span class="lineno"><a class="line" href="a00303.html#gab069b2fc2ec785fc4e193cf26c022679">  650</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, double, aligned_highp&gt;        <a class="code" href="a00303.html#gab069b2fc2ec785fc4e193cf26c022679">aligned_highp_dmat3x2</a>;</div>
<div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;</div>
<div class="line"><a name="l00653"></a><span class="lineno"><a class="line" href="a00303.html#ga5a97f0355d801de3444d42c1d5b40438">  653</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, double, aligned_mediump&gt;      <a class="code" href="a00303.html#ga5a97f0355d801de3444d42c1d5b40438">aligned_mediump_dmat3x2</a>;</div>
<div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;</div>
<div class="line"><a name="l00656"></a><span class="lineno"><a class="line" href="a00303.html#gaa281a47d5d627313984d0f8df993b648">  656</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, double, aligned_lowp&gt;         <a class="code" href="a00303.html#gaa281a47d5d627313984d0f8df993b648">aligned_lowp_dmat3x2</a>;</div>
<div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;</div>
<div class="line"><a name="l00659"></a><span class="lineno"><a class="line" href="a00303.html#ga2624719cb251d8de8cad1beaefc3a3f9">  659</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, float, packed_highp&gt;          <a class="code" href="a00303.html#ga2624719cb251d8de8cad1beaefc3a3f9">packed_highp_mat3x2</a>;</div>
<div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;</div>
<div class="line"><a name="l00662"></a><span class="lineno"><a class="line" href="a00303.html#ga04cfaf1421284df3c24ea0985dab24e7">  662</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, float, packed_mediump&gt;        <a class="code" href="a00303.html#ga04cfaf1421284df3c24ea0985dab24e7">packed_mediump_mat3x2</a>;</div>
<div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;</div>
<div class="line"><a name="l00665"></a><span class="lineno"><a class="line" href="a00303.html#ga128cd52649621861635fab746df91735">  665</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga128cd52649621861635fab746df91735">packed_lowp_mat3x2</a>;</div>
<div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160;</div>
<div class="line"><a name="l00668"></a><span class="lineno"><a class="line" href="a00303.html#gab462744977beb85fb5c782bc2eea7b15">  668</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, double, packed_highp&gt;         <a class="code" href="a00303.html#gab462744977beb85fb5c782bc2eea7b15">packed_highp_dmat3x2</a>;</div>
<div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;</div>
<div class="line"><a name="l00671"></a><span class="lineno"><a class="line" href="a00303.html#ga86efe91cdaa2864c828a5d6d46356c6a">  671</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, double, packed_mediump&gt;       <a class="code" href="a00303.html#ga86efe91cdaa2864c828a5d6d46356c6a">packed_mediump_dmat3x2</a>;</div>
<div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;</div>
<div class="line"><a name="l00674"></a><span class="lineno"><a class="line" href="a00303.html#ga23ec236950f5859f59197663266b535d">  674</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, double, packed_lowp&gt;          <a class="code" href="a00303.html#ga23ec236950f5859f59197663266b535d">packed_lowp_dmat3x2</a>;</div>
<div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;</div>
<div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;        <span class="comment">// -- *mat3x3 --</span></div>
<div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;</div>
<div class="line"><a name="l00679"></a><span class="lineno"><a class="line" href="a00303.html#ga2f7b8c99ba6f2d07c73a195a8143c259">  679</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, aligned_highp&gt;         <a class="code" href="a00303.html#ga2f7b8c99ba6f2d07c73a195a8143c259">aligned_highp_mat3x3</a>;</div>
<div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;</div>
<div class="line"><a name="l00682"></a><span class="lineno"><a class="line" href="a00303.html#ga721f5404caf40d68962dcc0529de71d9">  682</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga721f5404caf40d68962dcc0529de71d9">aligned_mediump_mat3x3</a>;</div>
<div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160;</div>
<div class="line"><a name="l00685"></a><span class="lineno"><a class="line" href="a00303.html#gaeaab04e378a90956eec8d68a99d777ed">  685</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, aligned_lowp&gt;          <a class="code" href="a00303.html#gaeaab04e378a90956eec8d68a99d777ed">aligned_lowp_mat3x3</a>;</div>
<div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;</div>
<div class="line"><a name="l00688"></a><span class="lineno"><a class="line" href="a00303.html#ga66073b1ddef34b681741f572338ddb8e">  688</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, aligned_highp&gt;        <a class="code" href="a00303.html#ga66073b1ddef34b681741f572338ddb8e">aligned_highp_dmat3x3</a>;</div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;</div>
<div class="line"><a name="l00691"></a><span class="lineno"><a class="line" href="a00303.html#ga649d0acf01054b17e679cf00e150e025">  691</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, aligned_mediump&gt;      <a class="code" href="a00303.html#ga649d0acf01054b17e679cf00e150e025">aligned_mediump_dmat3x3</a>;</div>
<div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;</div>
<div class="line"><a name="l00694"></a><span class="lineno"><a class="line" href="a00303.html#ga7f3148a72355e39932d6855baca42ebc">  694</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, aligned_lowp&gt;         <a class="code" href="a00303.html#ga7f3148a72355e39932d6855baca42ebc">aligned_lowp_dmat3x3</a>;</div>
<div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;</div>
<div class="line"><a name="l00697"></a><span class="lineno"><a class="line" href="a00303.html#gaf2e07527d678440bf0c20adbeb9177c5">  697</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, packed_highp&gt;          <a class="code" href="a00303.html#gaf2e07527d678440bf0c20adbeb9177c5">packed_highp_mat3x3</a>;</div>
<div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;</div>
<div class="line"><a name="l00700"></a><span class="lineno"><a class="line" href="a00303.html#gaaa9cea174d342dd9650e3436823cab23">  700</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, packed_mediump&gt;        <a class="code" href="a00303.html#gaaa9cea174d342dd9650e3436823cab23">packed_mediump_mat3x3</a>;</div>
<div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;</div>
<div class="line"><a name="l00703"></a><span class="lineno"><a class="line" href="a00303.html#ga5adf1802c5375a9dfb1729691bedd94e">  703</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga5adf1802c5375a9dfb1729691bedd94e">packed_lowp_mat3x3</a>;</div>
<div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;</div>
<div class="line"><a name="l00706"></a><span class="lineno"><a class="line" href="a00303.html#ga49e5a709d098523823b2f824e48672a6">  706</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, packed_highp&gt;         <a class="code" href="a00303.html#ga49e5a709d098523823b2f824e48672a6">packed_highp_dmat3x3</a>;</div>
<div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;</div>
<div class="line"><a name="l00709"></a><span class="lineno"><a class="line" href="a00303.html#gaf85877d38d8cfbc21d59d939afd72375">  709</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, packed_mediump&gt;       <a class="code" href="a00303.html#gaf85877d38d8cfbc21d59d939afd72375">packed_mediump_dmat3x3</a>;</div>
<div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;</div>
<div class="line"><a name="l00712"></a><span class="lineno"><a class="line" href="a00303.html#ga4a7c7d8c3a663d0ec2a858cbfa14e54c">  712</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, double, packed_lowp&gt;          <a class="code" href="a00303.html#ga4a7c7d8c3a663d0ec2a858cbfa14e54c">packed_lowp_dmat3x3</a>;</div>
<div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;</div>
<div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;        <span class="comment">// -- *mat3x4 --</span></div>
<div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;</div>
<div class="line"><a name="l00717"></a><span class="lineno"><a class="line" href="a00303.html#ga52e00afd0eb181e6738f40cf41787049">  717</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, float, aligned_highp&gt;         <a class="code" href="a00303.html#ga52e00afd0eb181e6738f40cf41787049">aligned_highp_mat3x4</a>;</div>
<div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;</div>
<div class="line"><a name="l00720"></a><span class="lineno"><a class="line" href="a00303.html#ga98f4dc6722a2541a990918c074075359">  720</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga98f4dc6722a2541a990918c074075359">aligned_mediump_mat3x4</a>;</div>
<div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;</div>
<div class="line"><a name="l00723"></a><span class="lineno"><a class="line" href="a00303.html#ga1f03696ab066572c6c044e63edf635a2">  723</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, float, aligned_lowp&gt;          <a class="code" href="a00303.html#ga1f03696ab066572c6c044e63edf635a2">aligned_lowp_mat3x4</a>;</div>
<div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;</div>
<div class="line"><a name="l00726"></a><span class="lineno"><a class="line" href="a00303.html#ga683c8ca66de323ea533a760abedd0efc">  726</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, double, aligned_highp&gt;        <a class="code" href="a00303.html#ga683c8ca66de323ea533a760abedd0efc">aligned_highp_dmat3x4</a>;</div>
<div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;</div>
<div class="line"><a name="l00729"></a><span class="lineno"><a class="line" href="a00303.html#ga45e155a4840f69b2fa4ed8047a676860">  729</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, double, aligned_mediump&gt;      <a class="code" href="a00303.html#ga45e155a4840f69b2fa4ed8047a676860">aligned_mediump_dmat3x4</a>;</div>
<div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;</div>
<div class="line"><a name="l00732"></a><span class="lineno"><a class="line" href="a00303.html#gaea3ccc5ef5b178e6e49b4fa1427605d3">  732</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, double, aligned_lowp&gt;         <a class="code" href="a00303.html#gaea3ccc5ef5b178e6e49b4fa1427605d3">aligned_lowp_dmat3x4</a>;</div>
<div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;</div>
<div class="line"><a name="l00735"></a><span class="lineno"><a class="line" href="a00303.html#ga72102fa6ac2445aa3bb203128ad52449">  735</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, float, packed_highp&gt;          <a class="code" href="a00303.html#ga72102fa6ac2445aa3bb203128ad52449">packed_highp_mat3x4</a>;</div>
<div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;</div>
<div class="line"><a name="l00738"></a><span class="lineno"><a class="line" href="a00303.html#gabc93a9560593bd32e099c908531305f5">  738</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, float, packed_mediump&gt;        <a class="code" href="a00303.html#gabc93a9560593bd32e099c908531305f5">packed_mediump_mat3x4</a>;</div>
<div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;</div>
<div class="line"><a name="l00741"></a><span class="lineno"><a class="line" href="a00303.html#ga92247ca09fa03c4013ba364f3a0fca7f">  741</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga92247ca09fa03c4013ba364f3a0fca7f">packed_lowp_mat3x4</a>;</div>
<div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;</div>
<div class="line"><a name="l00744"></a><span class="lineno"><a class="line" href="a00303.html#ga2c67b3b0adab71c8680c3d819f1fa9b7">  744</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, double, packed_highp&gt;         <a class="code" href="a00303.html#ga2c67b3b0adab71c8680c3d819f1fa9b7">packed_highp_dmat3x4</a>;</div>
<div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;</div>
<div class="line"><a name="l00747"></a><span class="lineno"><a class="line" href="a00303.html#gad5dcaf93df267bc3029174e430e0907f">  747</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, double, packed_mediump&gt;       <a class="code" href="a00303.html#gad5dcaf93df267bc3029174e430e0907f">packed_mediump_dmat3x4</a>;</div>
<div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;</div>
<div class="line"><a name="l00750"></a><span class="lineno"><a class="line" href="a00303.html#ga8fc0e66da83599071b7ec17510686cd9">  750</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, double, packed_lowp&gt;          <a class="code" href="a00303.html#ga8fc0e66da83599071b7ec17510686cd9">packed_lowp_dmat3x4</a>;</div>
<div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160;</div>
<div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;        <span class="comment">// -- *mat4x2 --</span></div>
<div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;</div>
<div class="line"><a name="l00755"></a><span class="lineno"><a class="line" href="a00303.html#ga84e1f5e0718952a079b748825c03f956">  755</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, float, aligned_highp&gt;         <a class="code" href="a00303.html#ga84e1f5e0718952a079b748825c03f956">aligned_highp_mat4x2</a>;</div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;</div>
<div class="line"><a name="l00758"></a><span class="lineno"><a class="line" href="a00303.html#ga46f372a006345c252a41267657cc22c0">  758</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga46f372a006345c252a41267657cc22c0">aligned_mediump_mat4x2</a>;</div>
<div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160;</div>
<div class="line"><a name="l00761"></a><span class="lineno"><a class="line" href="a00303.html#ga2cb16c3fdfb15e0719d942ee3b548bc4">  761</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, float, aligned_lowp&gt;          <a class="code" href="a00303.html#ga2cb16c3fdfb15e0719d942ee3b548bc4">aligned_lowp_mat4x2</a>;</div>
<div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;</div>
<div class="line"><a name="l00764"></a><span class="lineno"><a class="line" href="a00303.html#ga93a23ca3d42818d56e0702213c66354b">  764</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, double, aligned_highp&gt;        <a class="code" href="a00303.html#ga93a23ca3d42818d56e0702213c66354b">aligned_highp_dmat4x2</a>;</div>
<div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160;</div>
<div class="line"><a name="l00767"></a><span class="lineno"><a class="line" href="a00303.html#gabc25e547f4de4af62403492532cd1b6d">  767</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, double, aligned_mediump&gt;      <a class="code" href="a00303.html#gabc25e547f4de4af62403492532cd1b6d">aligned_mediump_dmat4x2</a>;</div>
<div class="line"><a name="l00768"></a><span class="lineno">  768</span>&#160;</div>
<div class="line"><a name="l00770"></a><span class="lineno"><a class="line" href="a00303.html#gaf806dfdaffb2e9f7681b1cd2825898ce">  770</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, double, aligned_lowp&gt;         <a class="code" href="a00303.html#gaf806dfdaffb2e9f7681b1cd2825898ce">aligned_lowp_dmat4x2</a>;</div>
<div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160;</div>
<div class="line"><a name="l00773"></a><span class="lineno"><a class="line" href="a00303.html#gae389c2071cf3cdb33e7812c6fd156710">  773</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, float, packed_highp&gt;          <a class="code" href="a00303.html#gae389c2071cf3cdb33e7812c6fd156710">packed_highp_mat4x2</a>;</div>
<div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;</div>
<div class="line"><a name="l00776"></a><span class="lineno"><a class="line" href="a00303.html#gaa458f9d9e0934bae3097e2a373b24707">  776</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, float, packed_mediump&gt;        <a class="code" href="a00303.html#gaa458f9d9e0934bae3097e2a373b24707">packed_mediump_mat4x2</a>;</div>
<div class="line"><a name="l00777"></a><span class="lineno">  777</span>&#160;</div>
<div class="line"><a name="l00779"></a><span class="lineno"><a class="line" href="a00303.html#ga8f22607dcd090cd280071ccc689f4079">  779</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga8f22607dcd090cd280071ccc689f4079">packed_lowp_mat4x2</a>;</div>
<div class="line"><a name="l00780"></a><span class="lineno">  780</span>&#160;</div>
<div class="line"><a name="l00782"></a><span class="lineno"><a class="line" href="a00303.html#ga12e39e797fb724a5b51fcbea2513a7da">  782</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, double, packed_highp&gt;         <a class="code" href="a00303.html#ga12e39e797fb724a5b51fcbea2513a7da">packed_highp_dmat4x2</a>;</div>
<div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;</div>
<div class="line"><a name="l00785"></a><span class="lineno"><a class="line" href="a00303.html#ga9a15514a0631f700de6312b9d5db3a73">  785</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, double, packed_mediump&gt;       <a class="code" href="a00303.html#ga9a15514a0631f700de6312b9d5db3a73">packed_mediump_dmat4x2</a>;</div>
<div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;</div>
<div class="line"><a name="l00788"></a><span class="lineno"><a class="line" href="a00303.html#ga39658fb13369db869d363684bd8399c0">  788</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, double, packed_lowp&gt;          <a class="code" href="a00303.html#ga39658fb13369db869d363684bd8399c0">packed_lowp_dmat4x2</a>;</div>
<div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160;</div>
<div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;        <span class="comment">// -- *mat4x3 --</span></div>
<div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;</div>
<div class="line"><a name="l00793"></a><span class="lineno"><a class="line" href="a00303.html#gafff1684c4ff19b4a818138ccacc1e78d">  793</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, float, aligned_highp&gt;         <a class="code" href="a00303.html#gafff1684c4ff19b4a818138ccacc1e78d">aligned_highp_mat4x3</a>;</div>
<div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160;</div>
<div class="line"><a name="l00796"></a><span class="lineno"><a class="line" href="a00303.html#ga0effece4545acdebdc2a5512a303110e">  796</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga0effece4545acdebdc2a5512a303110e">aligned_mediump_mat4x3</a>;</div>
<div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;</div>
<div class="line"><a name="l00799"></a><span class="lineno"><a class="line" href="a00303.html#ga7e96981e872f17a780d9f1c22dc1f512">  799</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, float, aligned_lowp&gt;          <a class="code" href="a00303.html#ga7e96981e872f17a780d9f1c22dc1f512">aligned_lowp_mat4x3</a>;</div>
<div class="line"><a name="l00800"></a><span class="lineno">  800</span>&#160;</div>
<div class="line"><a name="l00802"></a><span class="lineno"><a class="line" href="a00303.html#gacab7374b560745cb1d0a306a90353f58">  802</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, double, aligned_highp&gt;        <a class="code" href="a00303.html#gacab7374b560745cb1d0a306a90353f58">aligned_highp_dmat4x3</a>;</div>
<div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;</div>
<div class="line"><a name="l00805"></a><span class="lineno"><a class="line" href="a00303.html#gae84f4763ecdc7457ecb7930bad12057c">  805</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, double, aligned_mediump&gt;      <a class="code" href="a00303.html#gae84f4763ecdc7457ecb7930bad12057c">aligned_mediump_dmat4x3</a>;</div>
<div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;</div>
<div class="line"><a name="l00808"></a><span class="lineno"><a class="line" href="a00303.html#gab0931ac7807fa1428c7bbf249efcdf0d">  808</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, double, aligned_lowp&gt;         <a class="code" href="a00303.html#gab0931ac7807fa1428c7bbf249efcdf0d">aligned_lowp_dmat4x3</a>;</div>
<div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160;</div>
<div class="line"><a name="l00811"></a><span class="lineno"><a class="line" href="a00303.html#ga4584f64394bd7123b7a8534741e4916c">  811</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, float, packed_highp&gt;          <a class="code" href="a00303.html#ga4584f64394bd7123b7a8534741e4916c">packed_highp_mat4x3</a>;</div>
<div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160;</div>
<div class="line"><a name="l00814"></a><span class="lineno"><a class="line" href="a00303.html#ga02ca6255394aa778abaeb0f733c4d2b6">  814</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, float, packed_mediump&gt;        <a class="code" href="a00303.html#ga02ca6255394aa778abaeb0f733c4d2b6">packed_mediump_mat4x3</a>;</div>
<div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160;</div>
<div class="line"><a name="l00817"></a><span class="lineno"><a class="line" href="a00303.html#ga7661d759d6ad218e132e3d051e7b2c6c">  817</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga7661d759d6ad218e132e3d051e7b2c6c">packed_lowp_mat4x3</a>;</div>
<div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;</div>
<div class="line"><a name="l00820"></a><span class="lineno"><a class="line" href="a00303.html#ga79c2e9f82e67963c1ecad0ad6d0ec72e">  820</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, double, packed_highp&gt;         <a class="code" href="a00303.html#ga79c2e9f82e67963c1ecad0ad6d0ec72e">packed_highp_dmat4x3</a>;</div>
<div class="line"><a name="l00821"></a><span class="lineno">  821</span>&#160;</div>
<div class="line"><a name="l00823"></a><span class="lineno"><a class="line" href="a00303.html#gab5b36cc9caee1bb1c5178fe191bf5713">  823</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, double, packed_mediump&gt;       <a class="code" href="a00303.html#gab5b36cc9caee1bb1c5178fe191bf5713">packed_mediump_dmat4x3</a>;</div>
<div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160;</div>
<div class="line"><a name="l00826"></a><span class="lineno"><a class="line" href="a00303.html#ga30b0351eebc18c6056101359bdd3a359">  826</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, double, packed_lowp&gt;          <a class="code" href="a00303.html#ga30b0351eebc18c6056101359bdd3a359">packed_lowp_dmat4x3</a>;</div>
<div class="line"><a name="l00827"></a><span class="lineno">  827</span>&#160;</div>
<div class="line"><a name="l00828"></a><span class="lineno">  828</span>&#160;        <span class="comment">// -- *mat4x4 --</span></div>
<div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;</div>
<div class="line"><a name="l00831"></a><span class="lineno"><a class="line" href="a00303.html#ga40d49648083a0498a12a4bb41ae6ece8">  831</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, aligned_highp&gt;         <a class="code" href="a00303.html#ga40d49648083a0498a12a4bb41ae6ece8">aligned_highp_mat4x4</a>;</div>
<div class="line"><a name="l00832"></a><span class="lineno">  832</span>&#160;</div>
<div class="line"><a name="l00834"></a><span class="lineno"><a class="line" href="a00303.html#ga312864244cae4e8f10f478cffd0f76de">  834</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, aligned_mediump&gt;       <a class="code" href="a00303.html#ga312864244cae4e8f10f478cffd0f76de">aligned_mediump_mat4x4</a>;</div>
<div class="line"><a name="l00835"></a><span class="lineno">  835</span>&#160;</div>
<div class="line"><a name="l00837"></a><span class="lineno"><a class="line" href="a00303.html#gadae3dcfc22d28c64d0548cbfd9d08719">  837</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, aligned_lowp&gt;          <a class="code" href="a00303.html#gadae3dcfc22d28c64d0548cbfd9d08719">aligned_lowp_mat4x4</a>;</div>
<div class="line"><a name="l00838"></a><span class="lineno">  838</span>&#160;</div>
<div class="line"><a name="l00840"></a><span class="lineno"><a class="line" href="a00303.html#ga1fbfba14368b742972d3b58a0a303682">  840</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, aligned_highp&gt;        <a class="code" href="a00303.html#ga1fbfba14368b742972d3b58a0a303682">aligned_highp_dmat4x4</a>;</div>
<div class="line"><a name="l00841"></a><span class="lineno">  841</span>&#160;</div>
<div class="line"><a name="l00843"></a><span class="lineno"><a class="line" href="a00303.html#gaa292ebaa907afdecb2d5967fb4fb1247">  843</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, aligned_mediump&gt;      <a class="code" href="a00303.html#gaa292ebaa907afdecb2d5967fb4fb1247">aligned_mediump_dmat4x4</a>;</div>
<div class="line"><a name="l00844"></a><span class="lineno">  844</span>&#160;</div>
<div class="line"><a name="l00846"></a><span class="lineno"><a class="line" href="a00303.html#gad8220a93d2fca2dd707821b4ab6f809e">  846</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, aligned_lowp&gt;         <a class="code" href="a00303.html#gad8220a93d2fca2dd707821b4ab6f809e">aligned_lowp_dmat4x4</a>;</div>
<div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;</div>
<div class="line"><a name="l00849"></a><span class="lineno"><a class="line" href="a00303.html#ga0149fe15668925147e07c94fd2c2d6ae">  849</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, packed_highp&gt;          <a class="code" href="a00303.html#ga0149fe15668925147e07c94fd2c2d6ae">packed_highp_mat4x4</a>;</div>
<div class="line"><a name="l00850"></a><span class="lineno">  850</span>&#160;</div>
<div class="line"><a name="l00852"></a><span class="lineno"><a class="line" href="a00303.html#gaf304f64c06743c1571401504d3f50259">  852</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, packed_mediump&gt;        <a class="code" href="a00303.html#gaf304f64c06743c1571401504d3f50259">packed_mediump_mat4x4</a>;</div>
<div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;</div>
<div class="line"><a name="l00855"></a><span class="lineno"><a class="line" href="a00303.html#ga776f18d1a6e7d399f05d386167dc60f5">  855</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, float, packed_lowp&gt;           <a class="code" href="a00303.html#ga776f18d1a6e7d399f05d386167dc60f5">packed_lowp_mat4x4</a>;</div>
<div class="line"><a name="l00856"></a><span class="lineno">  856</span>&#160;</div>
<div class="line"><a name="l00858"></a><span class="lineno"><a class="line" href="a00303.html#ga2df58e03e5afded28707b4f7d077afb4">  858</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, packed_highp&gt;         <a class="code" href="a00303.html#ga2df58e03e5afded28707b4f7d077afb4">packed_highp_dmat4x4</a>;</div>
<div class="line"><a name="l00859"></a><span class="lineno">  859</span>&#160;</div>
<div class="line"><a name="l00861"></a><span class="lineno"><a class="line" href="a00303.html#ga21e86cf2f6c126bacf31b8985db06bd4">  861</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, packed_mediump&gt;       <a class="code" href="a00303.html#ga21e86cf2f6c126bacf31b8985db06bd4">packed_mediump_dmat4x4</a>;</div>
<div class="line"><a name="l00862"></a><span class="lineno">  862</span>&#160;</div>
<div class="line"><a name="l00864"></a><span class="lineno"><a class="line" href="a00303.html#ga0294d4c45151425c86a11deee7693c0e">  864</a></span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, double, packed_lowp&gt;          <a class="code" href="a00303.html#ga0294d4c45151425c86a11deee7693c0e">packed_lowp_dmat4x4</a>;</div>
<div class="line"><a name="l00865"></a><span class="lineno">  865</span>&#160;</div>
<div class="line"><a name="l00866"></a><span class="lineno">  866</span>&#160;        <span class="comment">// -- default --</span></div>
<div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;</div>
<div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160;<span class="preprocessor">#if(defined(GLM_PRECISION_LOWP_FLOAT))</span></div>
<div class="line"><a name="l00869"></a><span class="lineno">  869</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_vec1                       <a class="code" href="a00303.html#ga05e6d4c908965d04191c2070a8d0a65e">aligned_vec1</a>;</div>
<div class="line"><a name="l00870"></a><span class="lineno">  870</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_vec2                       <a class="code" href="a00303.html#ga0682462f8096a226773e20fac993cde5">aligned_vec2</a>;</div>
<div class="line"><a name="l00871"></a><span class="lineno">  871</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_vec3                       <a class="code" href="a00303.html#ga7cf643b66664e0cd3c48759ae66c2bd0">aligned_vec3</a>;</div>
<div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_vec4                       <a class="code" href="a00303.html#ga85d89e83cb8137e1be1446de8c3b643a">aligned_vec4</a>;</div>
<div class="line"><a name="l00873"></a><span class="lineno">  873</span>&#160;        <span class="keyword">typedef</span> packed_lowp_vec1                        <a class="code" href="a00303.html#ga14741e3d9da9ae83765389927f837331">packed_vec1</a>;</div>
<div class="line"><a name="l00874"></a><span class="lineno">  874</span>&#160;        <span class="keyword">typedef</span> packed_lowp_vec2                        <a class="code" href="a00303.html#ga3254defa5a8f0ae4b02b45fedba84a66">packed_vec2</a>;</div>
<div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160;        <span class="keyword">typedef</span> packed_lowp_vec3                        <a class="code" href="a00303.html#gaccccd090e185450caa28b5b63ad4e8f0">packed_vec3</a>;</div>
<div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;        <span class="keyword">typedef</span> packed_lowp_vec4                        <a class="code" href="a00303.html#ga37a0e0bf653169b581c5eea3d547fa5d">packed_vec4</a>;</div>
<div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160;</div>
<div class="line"><a name="l00878"></a><span class="lineno">  878</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat2                       <a class="code" href="a00303.html#ga5a8a5f8c47cd7d5502dd9932f83472b9">aligned_mat2</a>;</div>
<div class="line"><a name="l00879"></a><span class="lineno">  879</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat3                       <a class="code" href="a00303.html#ga43a92a24ca863e0e0f3b65834b3cf714">aligned_mat3</a>;</div>
<div class="line"><a name="l00880"></a><span class="lineno">  880</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat4                       <a class="code" href="a00303.html#gab0fddcf95dd51cbcbf624ea7c40dfeb8">aligned_mat4</a>;</div>
<div class="line"><a name="l00881"></a><span class="lineno">  881</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat2                        <a class="code" href="a00303.html#gadd019b43fcf42e1590d45dddaa504a1a">packed_mat2</a>;</div>
<div class="line"><a name="l00882"></a><span class="lineno">  882</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat3                        <a class="code" href="a00303.html#ga9bc12b0ab7be8448836711b77cc7b83a">packed_mat3</a>;</div>
<div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat4                        <a class="code" href="a00303.html#ga2c139854e5b04cf08a957dee3b510441">packed_mat4</a>;</div>
<div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;</div>
<div class="line"><a name="l00885"></a><span class="lineno">  885</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat2x2                     <a class="code" href="a00303.html#gabb04f459d81d753d278b2072e2375e8e">aligned_mat2x2</a>;</div>
<div class="line"><a name="l00886"></a><span class="lineno">  886</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat2x3                     <a class="code" href="a00303.html#ga832476bb1c59ef673db37433ff34e399">aligned_mat2x3</a>;</div>
<div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat2x4                     <a class="code" href="a00303.html#gadab11a7504430825b648ff7c7e36b725">aligned_mat2x4</a>;</div>
<div class="line"><a name="l00888"></a><span class="lineno">  888</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat3x2                     <a class="code" href="a00303.html#ga5c0df24ba85eafafc0eb0c90690510ed">aligned_mat3x2</a>;</div>
<div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat3x3                     <a class="code" href="a00303.html#gadb065dbe5c11271fef8cf2ea8608f187">aligned_mat3x3</a>;</div>
<div class="line"><a name="l00890"></a><span class="lineno">  890</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat3x4                     <a class="code" href="a00303.html#ga88061c72c997b94c420f2b0a60d9df26">aligned_mat3x4</a>;</div>
<div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat4x2                     <a class="code" href="a00303.html#gac9a2d0fb815fd5c2bd58b869c55e32d3">aligned_mat4x2</a>;</div>
<div class="line"><a name="l00892"></a><span class="lineno">  892</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat4x3                     <a class="code" href="a00303.html#ga452bbbfd26e244de216e4d004d50bb74">aligned_mat4x3</a>;</div>
<div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_mat4x4                     <a class="code" href="a00303.html#ga8b8fb86973a0b768c5bd802c92fac1a1">aligned_mat4x4</a>;</div>
<div class="line"><a name="l00894"></a><span class="lineno">  894</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat2x2                      <a class="code" href="a00303.html#ga51eaadcdc292c8750f746a5dc3e6c517">packed_mat2x2</a>;</div>
<div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat2x3                      <a class="code" href="a00303.html#ga301b76a89b8a9625501ca58815017f20">packed_mat2x3</a>;</div>
<div class="line"><a name="l00896"></a><span class="lineno">  896</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat2x4                      <a class="code" href="a00303.html#gac401da1dd9177ad81d7618a2a5541e23">packed_mat2x4</a>;</div>
<div class="line"><a name="l00897"></a><span class="lineno">  897</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat3x2                      <a class="code" href="a00303.html#ga134f0d99fbd2459c13cd9ebd056509fa">packed_mat3x2</a>;</div>
<div class="line"><a name="l00898"></a><span class="lineno">  898</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat3x3                      <a class="code" href="a00303.html#ga6c1dbe8cde9fbb231284b01f8aeaaa99">packed_mat3x3</a>;</div>
<div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat3x4                      <a class="code" href="a00303.html#gad63515526cccfe88ffa8fe5ed64f95f8">packed_mat3x4</a>;</div>
<div class="line"><a name="l00900"></a><span class="lineno">  900</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat4x2                      <a class="code" href="a00303.html#ga379c1153f1339bdeaefd592bebf538e8">packed_mat4x2</a>;</div>
<div class="line"><a name="l00901"></a><span class="lineno">  901</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat4x3                      <a class="code" href="a00303.html#gab286466e19f7399c8d25089da9400d43">packed_mat4x3</a>;</div>
<div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160;        <span class="keyword">typedef</span> packed_lowp_mat4x4                      <a class="code" href="a00303.html#ga67e7102557d6067bb6ac00d4ad0e1374">packed_mat4x4</a>;</div>
<div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_MEDIUMP_FLOAT))</span></div>
<div class="line"><a name="l00904"></a><span class="lineno">  904</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_vec1            <a class="code" href="a00303.html#ga05e6d4c908965d04191c2070a8d0a65e">aligned_vec1</a>;</div>
<div class="line"><a name="l00905"></a><span class="lineno">  905</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_vec2            <a class="code" href="a00303.html#ga0682462f8096a226773e20fac993cde5">aligned_vec2</a>;</div>
<div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_vec3            <a class="code" href="a00303.html#ga7cf643b66664e0cd3c48759ae66c2bd0">aligned_vec3</a>;</div>
<div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_vec4            <a class="code" href="a00303.html#ga85d89e83cb8137e1be1446de8c3b643a">aligned_vec4</a>;</div>
<div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160;        <span class="keyword">typedef</span> packed_mediump_vec1                     <a class="code" href="a00303.html#ga14741e3d9da9ae83765389927f837331">packed_vec1</a>;</div>
<div class="line"><a name="l00909"></a><span class="lineno">  909</span>&#160;        <span class="keyword">typedef</span> packed_mediump_vec2                     <a class="code" href="a00303.html#ga3254defa5a8f0ae4b02b45fedba84a66">packed_vec2</a>;</div>
<div class="line"><a name="l00910"></a><span class="lineno">  910</span>&#160;        <span class="keyword">typedef</span> packed_mediump_vec3                     <a class="code" href="a00303.html#gaccccd090e185450caa28b5b63ad4e8f0">packed_vec3</a>;</div>
<div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;        <span class="keyword">typedef</span> packed_mediump_vec4                     <a class="code" href="a00303.html#ga37a0e0bf653169b581c5eea3d547fa5d">packed_vec4</a>;</div>
<div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;</div>
<div class="line"><a name="l00913"></a><span class="lineno">  913</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat2            <a class="code" href="a00303.html#ga5a8a5f8c47cd7d5502dd9932f83472b9">aligned_mat2</a>;</div>
<div class="line"><a name="l00914"></a><span class="lineno">  914</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat3            <a class="code" href="a00303.html#ga43a92a24ca863e0e0f3b65834b3cf714">aligned_mat3</a>;</div>
<div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat4            <a class="code" href="a00303.html#gab0fddcf95dd51cbcbf624ea7c40dfeb8">aligned_mat4</a>;</div>
<div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat2                     <a class="code" href="a00303.html#gadd019b43fcf42e1590d45dddaa504a1a">packed_mat2</a>;</div>
<div class="line"><a name="l00917"></a><span class="lineno">  917</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat3                     <a class="code" href="a00303.html#ga9bc12b0ab7be8448836711b77cc7b83a">packed_mat3</a>;</div>
<div class="line"><a name="l00918"></a><span class="lineno">  918</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat4                     <a class="code" href="a00303.html#ga2c139854e5b04cf08a957dee3b510441">packed_mat4</a>;</div>
<div class="line"><a name="l00919"></a><span class="lineno">  919</span>&#160;</div>
<div class="line"><a name="l00920"></a><span class="lineno">  920</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat2x2          <a class="code" href="a00303.html#gabb04f459d81d753d278b2072e2375e8e">aligned_mat2x2</a>;</div>
<div class="line"><a name="l00921"></a><span class="lineno">  921</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat2x3          <a class="code" href="a00303.html#ga832476bb1c59ef673db37433ff34e399">aligned_mat2x3</a>;</div>
<div class="line"><a name="l00922"></a><span class="lineno">  922</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat2x4          <a class="code" href="a00303.html#gadab11a7504430825b648ff7c7e36b725">aligned_mat2x4</a>;</div>
<div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat3x2          <a class="code" href="a00303.html#ga5c0df24ba85eafafc0eb0c90690510ed">aligned_mat3x2</a>;</div>
<div class="line"><a name="l00924"></a><span class="lineno">  924</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat3x3          <a class="code" href="a00303.html#gadb065dbe5c11271fef8cf2ea8608f187">aligned_mat3x3</a>;</div>
<div class="line"><a name="l00925"></a><span class="lineno">  925</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat3x4          <a class="code" href="a00303.html#ga88061c72c997b94c420f2b0a60d9df26">aligned_mat3x4</a>;</div>
<div class="line"><a name="l00926"></a><span class="lineno">  926</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat4x2          <a class="code" href="a00303.html#gac9a2d0fb815fd5c2bd58b869c55e32d3">aligned_mat4x2</a>;</div>
<div class="line"><a name="l00927"></a><span class="lineno">  927</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat4x3          <a class="code" href="a00303.html#ga452bbbfd26e244de216e4d004d50bb74">aligned_mat4x3</a>;</div>
<div class="line"><a name="l00928"></a><span class="lineno">  928</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_mat4x4          <a class="code" href="a00303.html#ga8b8fb86973a0b768c5bd802c92fac1a1">aligned_mat4x4</a>;</div>
<div class="line"><a name="l00929"></a><span class="lineno">  929</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat2x2           <a class="code" href="a00303.html#ga51eaadcdc292c8750f746a5dc3e6c517">packed_mat2x2</a>;</div>
<div class="line"><a name="l00930"></a><span class="lineno">  930</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat2x3           <a class="code" href="a00303.html#ga301b76a89b8a9625501ca58815017f20">packed_mat2x3</a>;</div>
<div class="line"><a name="l00931"></a><span class="lineno">  931</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat2x4           <a class="code" href="a00303.html#gac401da1dd9177ad81d7618a2a5541e23">packed_mat2x4</a>;</div>
<div class="line"><a name="l00932"></a><span class="lineno">  932</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat3x2           <a class="code" href="a00303.html#ga134f0d99fbd2459c13cd9ebd056509fa">packed_mat3x2</a>;</div>
<div class="line"><a name="l00933"></a><span class="lineno">  933</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat3x3           <a class="code" href="a00303.html#ga6c1dbe8cde9fbb231284b01f8aeaaa99">packed_mat3x3</a>;</div>
<div class="line"><a name="l00934"></a><span class="lineno">  934</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat3x4           <a class="code" href="a00303.html#gad63515526cccfe88ffa8fe5ed64f95f8">packed_mat3x4</a>;</div>
<div class="line"><a name="l00935"></a><span class="lineno">  935</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat4x2           <a class="code" href="a00303.html#ga379c1153f1339bdeaefd592bebf538e8">packed_mat4x2</a>;</div>
<div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat4x3           <a class="code" href="a00303.html#gab286466e19f7399c8d25089da9400d43">packed_mat4x3</a>;</div>
<div class="line"><a name="l00937"></a><span class="lineno">  937</span>&#160;        <span class="keyword">typedef</span> packed_mediump_mat4x4           <a class="code" href="a00303.html#ga67e7102557d6067bb6ac00d4ad0e1374">packed_mat4x4</a>;</div>
<div class="line"><a name="l00938"></a><span class="lineno">  938</span>&#160;<span class="preprocessor">#else //defined(GLM_PRECISION_HIGHP_FLOAT)</span></div>
<div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;        <span class="keyword">typedef</span> aligned_highp_vec1                      <a class="code" href="a00303.html#ga05e6d4c908965d04191c2070a8d0a65e">aligned_vec1</a>;</div>
<div class="line"><a name="l00941"></a><span class="lineno">  941</span>&#160;</div>
<div class="line"><a name="l00943"></a><span class="lineno"><a class="line" href="a00303.html#ga0682462f8096a226773e20fac993cde5">  943</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_vec2                      <a class="code" href="a00303.html#ga0682462f8096a226773e20fac993cde5">aligned_vec2</a>;</div>
<div class="line"><a name="l00944"></a><span class="lineno">  944</span>&#160;</div>
<div class="line"><a name="l00946"></a><span class="lineno"><a class="line" href="a00303.html#ga7cf643b66664e0cd3c48759ae66c2bd0">  946</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_vec3                      <a class="code" href="a00303.html#ga7cf643b66664e0cd3c48759ae66c2bd0">aligned_vec3</a>;</div>
<div class="line"><a name="l00947"></a><span class="lineno">  947</span>&#160;</div>
<div class="line"><a name="l00949"></a><span class="lineno"><a class="line" href="a00303.html#ga85d89e83cb8137e1be1446de8c3b643a">  949</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_vec4                      <a class="code" href="a00303.html#ga85d89e83cb8137e1be1446de8c3b643a">aligned_vec4</a>;</div>
<div class="line"><a name="l00950"></a><span class="lineno">  950</span>&#160;</div>
<div class="line"><a name="l00952"></a><span class="lineno"><a class="line" href="a00303.html#ga14741e3d9da9ae83765389927f837331">  952</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_vec1                       <a class="code" href="a00303.html#ga14741e3d9da9ae83765389927f837331">packed_vec1</a>;</div>
<div class="line"><a name="l00953"></a><span class="lineno">  953</span>&#160;</div>
<div class="line"><a name="l00955"></a><span class="lineno"><a class="line" href="a00303.html#ga3254defa5a8f0ae4b02b45fedba84a66">  955</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_vec2                       <a class="code" href="a00303.html#ga3254defa5a8f0ae4b02b45fedba84a66">packed_vec2</a>;</div>
<div class="line"><a name="l00956"></a><span class="lineno">  956</span>&#160;</div>
<div class="line"><a name="l00958"></a><span class="lineno"><a class="line" href="a00303.html#gaccccd090e185450caa28b5b63ad4e8f0">  958</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_vec3                       <a class="code" href="a00303.html#gaccccd090e185450caa28b5b63ad4e8f0">packed_vec3</a>;</div>
<div class="line"><a name="l00959"></a><span class="lineno">  959</span>&#160;</div>
<div class="line"><a name="l00961"></a><span class="lineno"><a class="line" href="a00303.html#ga37a0e0bf653169b581c5eea3d547fa5d">  961</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_vec4                       <a class="code" href="a00303.html#ga37a0e0bf653169b581c5eea3d547fa5d">packed_vec4</a>;</div>
<div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;</div>
<div class="line"><a name="l00964"></a><span class="lineno"><a class="line" href="a00303.html#ga5a8a5f8c47cd7d5502dd9932f83472b9">  964</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat2                      <a class="code" href="a00303.html#ga5a8a5f8c47cd7d5502dd9932f83472b9">aligned_mat2</a>;</div>
<div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;</div>
<div class="line"><a name="l00967"></a><span class="lineno"><a class="line" href="a00303.html#ga43a92a24ca863e0e0f3b65834b3cf714">  967</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat3                      <a class="code" href="a00303.html#ga43a92a24ca863e0e0f3b65834b3cf714">aligned_mat3</a>;</div>
<div class="line"><a name="l00968"></a><span class="lineno">  968</span>&#160;</div>
<div class="line"><a name="l00970"></a><span class="lineno"><a class="line" href="a00303.html#gab0fddcf95dd51cbcbf624ea7c40dfeb8">  970</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat4                      <a class="code" href="a00303.html#gab0fddcf95dd51cbcbf624ea7c40dfeb8">aligned_mat4</a>;</div>
<div class="line"><a name="l00971"></a><span class="lineno">  971</span>&#160;</div>
<div class="line"><a name="l00973"></a><span class="lineno"><a class="line" href="a00303.html#gadd019b43fcf42e1590d45dddaa504a1a">  973</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat2                       <a class="code" href="a00303.html#gadd019b43fcf42e1590d45dddaa504a1a">packed_mat2</a>;</div>
<div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;</div>
<div class="line"><a name="l00976"></a><span class="lineno"><a class="line" href="a00303.html#ga9bc12b0ab7be8448836711b77cc7b83a">  976</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat3                       <a class="code" href="a00303.html#ga9bc12b0ab7be8448836711b77cc7b83a">packed_mat3</a>;</div>
<div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;</div>
<div class="line"><a name="l00979"></a><span class="lineno"><a class="line" href="a00303.html#ga2c139854e5b04cf08a957dee3b510441">  979</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat4                       <a class="code" href="a00303.html#ga2c139854e5b04cf08a957dee3b510441">packed_mat4</a>;</div>
<div class="line"><a name="l00980"></a><span class="lineno">  980</span>&#160;</div>
<div class="line"><a name="l00982"></a><span class="lineno"><a class="line" href="a00303.html#gabb04f459d81d753d278b2072e2375e8e">  982</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat2x2            <a class="code" href="a00303.html#gabb04f459d81d753d278b2072e2375e8e">aligned_mat2x2</a>;</div>
<div class="line"><a name="l00983"></a><span class="lineno">  983</span>&#160;</div>
<div class="line"><a name="l00985"></a><span class="lineno"><a class="line" href="a00303.html#ga832476bb1c59ef673db37433ff34e399">  985</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat2x3            <a class="code" href="a00303.html#ga832476bb1c59ef673db37433ff34e399">aligned_mat2x3</a>;</div>
<div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;</div>
<div class="line"><a name="l00988"></a><span class="lineno"><a class="line" href="a00303.html#gadab11a7504430825b648ff7c7e36b725">  988</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat2x4            <a class="code" href="a00303.html#gadab11a7504430825b648ff7c7e36b725">aligned_mat2x4</a>;</div>
<div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;</div>
<div class="line"><a name="l00991"></a><span class="lineno"><a class="line" href="a00303.html#ga5c0df24ba85eafafc0eb0c90690510ed">  991</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat3x2            <a class="code" href="a00303.html#ga5c0df24ba85eafafc0eb0c90690510ed">aligned_mat3x2</a>;</div>
<div class="line"><a name="l00992"></a><span class="lineno">  992</span>&#160;</div>
<div class="line"><a name="l00994"></a><span class="lineno"><a class="line" href="a00303.html#gadb065dbe5c11271fef8cf2ea8608f187">  994</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat3x3            <a class="code" href="a00303.html#gadb065dbe5c11271fef8cf2ea8608f187">aligned_mat3x3</a>;</div>
<div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;</div>
<div class="line"><a name="l00997"></a><span class="lineno"><a class="line" href="a00303.html#ga88061c72c997b94c420f2b0a60d9df26">  997</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat3x4            <a class="code" href="a00303.html#ga88061c72c997b94c420f2b0a60d9df26">aligned_mat3x4</a>;</div>
<div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;</div>
<div class="line"><a name="l01000"></a><span class="lineno"><a class="line" href="a00303.html#gac9a2d0fb815fd5c2bd58b869c55e32d3"> 1000</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat4x2            <a class="code" href="a00303.html#gac9a2d0fb815fd5c2bd58b869c55e32d3">aligned_mat4x2</a>;</div>
<div class="line"><a name="l01001"></a><span class="lineno"> 1001</span>&#160;</div>
<div class="line"><a name="l01003"></a><span class="lineno"><a class="line" href="a00303.html#ga452bbbfd26e244de216e4d004d50bb74"> 1003</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat4x3            <a class="code" href="a00303.html#ga452bbbfd26e244de216e4d004d50bb74">aligned_mat4x3</a>;</div>
<div class="line"><a name="l01004"></a><span class="lineno"> 1004</span>&#160;</div>
<div class="line"><a name="l01006"></a><span class="lineno"><a class="line" href="a00303.html#ga8b8fb86973a0b768c5bd802c92fac1a1"> 1006</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_mat4x4            <a class="code" href="a00303.html#ga8b8fb86973a0b768c5bd802c92fac1a1">aligned_mat4x4</a>;</div>
<div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;</div>
<div class="line"><a name="l01009"></a><span class="lineno"><a class="line" href="a00303.html#ga51eaadcdc292c8750f746a5dc3e6c517"> 1009</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat2x2                     <a class="code" href="a00303.html#ga51eaadcdc292c8750f746a5dc3e6c517">packed_mat2x2</a>;</div>
<div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;</div>
<div class="line"><a name="l01012"></a><span class="lineno"><a class="line" href="a00303.html#ga301b76a89b8a9625501ca58815017f20"> 1012</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat2x3                     <a class="code" href="a00303.html#ga301b76a89b8a9625501ca58815017f20">packed_mat2x3</a>;</div>
<div class="line"><a name="l01013"></a><span class="lineno"> 1013</span>&#160;</div>
<div class="line"><a name="l01015"></a><span class="lineno"><a class="line" href="a00303.html#gac401da1dd9177ad81d7618a2a5541e23"> 1015</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat2x4                     <a class="code" href="a00303.html#gac401da1dd9177ad81d7618a2a5541e23">packed_mat2x4</a>;</div>
<div class="line"><a name="l01016"></a><span class="lineno"> 1016</span>&#160;</div>
<div class="line"><a name="l01018"></a><span class="lineno"><a class="line" href="a00303.html#ga134f0d99fbd2459c13cd9ebd056509fa"> 1018</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat3x2                     <a class="code" href="a00303.html#ga134f0d99fbd2459c13cd9ebd056509fa">packed_mat3x2</a>;</div>
<div class="line"><a name="l01019"></a><span class="lineno"> 1019</span>&#160;</div>
<div class="line"><a name="l01021"></a><span class="lineno"><a class="line" href="a00303.html#ga6c1dbe8cde9fbb231284b01f8aeaaa99"> 1021</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat3x3                     <a class="code" href="a00303.html#ga6c1dbe8cde9fbb231284b01f8aeaaa99">packed_mat3x3</a>;</div>
<div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;</div>
<div class="line"><a name="l01024"></a><span class="lineno"><a class="line" href="a00303.html#gad63515526cccfe88ffa8fe5ed64f95f8"> 1024</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat3x4                     <a class="code" href="a00303.html#gad63515526cccfe88ffa8fe5ed64f95f8">packed_mat3x4</a>;</div>
<div class="line"><a name="l01025"></a><span class="lineno"> 1025</span>&#160;</div>
<div class="line"><a name="l01027"></a><span class="lineno"><a class="line" href="a00303.html#ga379c1153f1339bdeaefd592bebf538e8"> 1027</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat4x2                     <a class="code" href="a00303.html#ga379c1153f1339bdeaefd592bebf538e8">packed_mat4x2</a>;</div>
<div class="line"><a name="l01028"></a><span class="lineno"> 1028</span>&#160;</div>
<div class="line"><a name="l01030"></a><span class="lineno"><a class="line" href="a00303.html#gab286466e19f7399c8d25089da9400d43"> 1030</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat4x3                     <a class="code" href="a00303.html#gab286466e19f7399c8d25089da9400d43">packed_mat4x3</a>;</div>
<div class="line"><a name="l01031"></a><span class="lineno"> 1031</span>&#160;</div>
<div class="line"><a name="l01033"></a><span class="lineno"><a class="line" href="a00303.html#ga67e7102557d6067bb6ac00d4ad0e1374"> 1033</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_mat4x4                     <a class="code" href="a00303.html#ga67e7102557d6067bb6ac00d4ad0e1374">packed_mat4x4</a>;</div>
<div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;<span class="preprocessor">#endif//GLM_PRECISION</span></div>
<div class="line"><a name="l01035"></a><span class="lineno"> 1035</span>&#160;</div>
<div class="line"><a name="l01036"></a><span class="lineno"> 1036</span>&#160;<span class="preprocessor">#if(defined(GLM_PRECISION_LOWP_DOUBLE))</span></div>
<div class="line"><a name="l01037"></a><span class="lineno"> 1037</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dvec1                      <a class="code" href="a00303.html#ga4974f46ae5a19415d91316960a53617a">aligned_dvec1</a>;</div>
<div class="line"><a name="l01038"></a><span class="lineno"> 1038</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dvec2                      <a class="code" href="a00303.html#ga18d859f87122b2b3b2992ffe86dbebc0">aligned_dvec2</a>;</div>
<div class="line"><a name="l01039"></a><span class="lineno"> 1039</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dvec3                      <a class="code" href="a00303.html#gaa37869eea77d28419b2fb0ff70b69bf0">aligned_dvec3</a>;</div>
<div class="line"><a name="l01040"></a><span class="lineno"> 1040</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dvec4                      <a class="code" href="a00303.html#ga8a9f0a4795ccc442fa9901845026f9f5">aligned_dvec4</a>;</div>
<div class="line"><a name="l01041"></a><span class="lineno"> 1041</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dvec1                       <a class="code" href="a00303.html#ga532f0c940649b1ee303acd572fc35531">packed_dvec1</a>;</div>
<div class="line"><a name="l01042"></a><span class="lineno"> 1042</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dvec2                       <a class="code" href="a00303.html#ga5c194b11fbda636f2ab20c3bd0079196">packed_dvec2</a>;</div>
<div class="line"><a name="l01043"></a><span class="lineno"> 1043</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dvec3                       <a class="code" href="a00303.html#ga0581ea552d86b2b5de7a2804bed80e72">packed_dvec3</a>;</div>
<div class="line"><a name="l01044"></a><span class="lineno"> 1044</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dvec4                       <a class="code" href="a00303.html#gae8a9b181f9dc813ad6e125a52b14b935">packed_dvec4</a>;</div>
<div class="line"><a name="l01045"></a><span class="lineno"> 1045</span>&#160;</div>
<div class="line"><a name="l01046"></a><span class="lineno"> 1046</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat2                      <a class="code" href="a00303.html#ga6783859382677d35fcd5dac7dcbefdbd">aligned_dmat2</a>;</div>
<div class="line"><a name="l01047"></a><span class="lineno"> 1047</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat3                      <a class="code" href="a00303.html#ga19aa695ffdb45ce29f7ea0b5029627de">aligned_dmat3</a>;</div>
<div class="line"><a name="l01048"></a><span class="lineno"> 1048</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat4                      <a class="code" href="a00303.html#ga001bb387ae8192fa94dbd8b23b600439">aligned_dmat4</a>;</div>
<div class="line"><a name="l01049"></a><span class="lineno"> 1049</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat2                       <a class="code" href="a00303.html#gad87408a8350918711f845f071bbe43fb">packed_dmat2</a>;</div>
<div class="line"><a name="l01050"></a><span class="lineno"> 1050</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat3                       <a class="code" href="a00303.html#ga03dfc90d539cc87ea3a15a9caa5d2245">packed_dmat3</a>;</div>
<div class="line"><a name="l01051"></a><span class="lineno"> 1051</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat4                       <a class="code" href="a00303.html#gada980a3485640aa8151f368f17ad3086">packed_dmat4</a>;</div>
<div class="line"><a name="l01052"></a><span class="lineno"> 1052</span>&#160;</div>
<div class="line"><a name="l01053"></a><span class="lineno"> 1053</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat2x2            <a class="code" href="a00303.html#ga449a3ec2dde6b6bb4bb94c49a6aad388">aligned_dmat2x2</a>;</div>
<div class="line"><a name="l01054"></a><span class="lineno"> 1054</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat2x3            <a class="code" href="a00303.html#ga53d519a7b1bfb69076b3ec206a6b3bd1">aligned_dmat2x3</a>;</div>
<div class="line"><a name="l01055"></a><span class="lineno"> 1055</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat2x4            <a class="code" href="a00303.html#ga5ccb2baeb0ab57b818c24e0d486c59d0">aligned_dmat2x4</a>;</div>
<div class="line"><a name="l01056"></a><span class="lineno"> 1056</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat3x2            <a class="code" href="a00303.html#ga5f5123d834bd1170edf8c386834e112c">aligned_dmat3x2</a>;</div>
<div class="line"><a name="l01057"></a><span class="lineno"> 1057</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat3x3            <a class="code" href="a00303.html#ga635bf3732281a2c2ca54d8f9d33d178f">aligned_dmat3x3</a>;</div>
<div class="line"><a name="l01058"></a><span class="lineno"> 1058</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat3x4            <a class="code" href="a00303.html#gaf488c6ad88c185054595d4d5c7ba5b9d">aligned_dmat3x4</a>;</div>
<div class="line"><a name="l01059"></a><span class="lineno"> 1059</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat4x2            <a class="code" href="a00303.html#gaa409cfb737bd59b68dc683e9b03930cc">aligned_dmat4x2</a>;</div>
<div class="line"><a name="l01060"></a><span class="lineno"> 1060</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat4x3            <a class="code" href="a00303.html#ga621e89ca1dbdcb7b5a3e7de237c44121">aligned_dmat4x3</a>;</div>
<div class="line"><a name="l01061"></a><span class="lineno"> 1061</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_dmat4x4            <a class="code" href="a00303.html#gac9bda778d0b7ad82f656dab99b71857a">aligned_dmat4x4</a>;</div>
<div class="line"><a name="l01062"></a><span class="lineno"> 1062</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat2x2                     <a class="code" href="a00303.html#gaaa33d8e06657a777efb0c72c44ce87a9">packed_dmat2x2</a>;</div>
<div class="line"><a name="l01063"></a><span class="lineno"> 1063</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat2x3                     <a class="code" href="a00303.html#gac3a5315f588ba04ad255188071ec4e22">packed_dmat2x3</a>;</div>
<div class="line"><a name="l01064"></a><span class="lineno"> 1064</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat2x4                     <a class="code" href="a00303.html#gae398fc3156f51d3684b08f62c1a5a6d4">packed_dmat2x4</a>;</div>
<div class="line"><a name="l01065"></a><span class="lineno"> 1065</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat3x2                     <a class="code" href="a00303.html#gae36de20a4c0e0b1444b7903ae811d94e">packed_dmat3x2</a>;</div>
<div class="line"><a name="l01066"></a><span class="lineno"> 1066</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat3x3                     <a class="code" href="a00303.html#gab9b909f1392d86854334350efcae85f5">packed_dmat3x3</a>;</div>
<div class="line"><a name="l01067"></a><span class="lineno"> 1067</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat3x4                     <a class="code" href="a00303.html#ga199131fd279c92c2ac12df6d978f1dd6">packed_dmat3x4</a>;</div>
<div class="line"><a name="l01068"></a><span class="lineno"> 1068</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat4x2                     <a class="code" href="a00303.html#ga6dc65249730698d3cc9ac5d7e1bc4d72">packed_dmat4x2</a>;</div>
<div class="line"><a name="l01069"></a><span class="lineno"> 1069</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat4x3                     <a class="code" href="a00303.html#gadf202aaa9ed71c09f9bbe347e43f8764">packed_dmat4x3</a>;</div>
<div class="line"><a name="l01070"></a><span class="lineno"> 1070</span>&#160;        <span class="keyword">typedef</span> packed_lowp_dmat4x4                     <a class="code" href="a00303.html#gae20617435a6d042d7c38da2badd64a09">packed_dmat4x4</a>;</div>
<div class="line"><a name="l01071"></a><span class="lineno"> 1071</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_MEDIUMP_DOUBLE))</span></div>
<div class="line"><a name="l01072"></a><span class="lineno"> 1072</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dvec1           <a class="code" href="a00303.html#ga4974f46ae5a19415d91316960a53617a">aligned_dvec1</a>;</div>
<div class="line"><a name="l01073"></a><span class="lineno"> 1073</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dvec2           <a class="code" href="a00303.html#ga18d859f87122b2b3b2992ffe86dbebc0">aligned_dvec2</a>;</div>
<div class="line"><a name="l01074"></a><span class="lineno"> 1074</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dvec3           <a class="code" href="a00303.html#gaa37869eea77d28419b2fb0ff70b69bf0">aligned_dvec3</a>;</div>
<div class="line"><a name="l01075"></a><span class="lineno"> 1075</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dvec4           <a class="code" href="a00303.html#ga8a9f0a4795ccc442fa9901845026f9f5">aligned_dvec4</a>;</div>
<div class="line"><a name="l01076"></a><span class="lineno"> 1076</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dvec1            <a class="code" href="a00303.html#ga532f0c940649b1ee303acd572fc35531">packed_dvec1</a>;</div>
<div class="line"><a name="l01077"></a><span class="lineno"> 1077</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dvec2            <a class="code" href="a00303.html#ga5c194b11fbda636f2ab20c3bd0079196">packed_dvec2</a>;</div>
<div class="line"><a name="l01078"></a><span class="lineno"> 1078</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dvec3            <a class="code" href="a00303.html#ga0581ea552d86b2b5de7a2804bed80e72">packed_dvec3</a>;</div>
<div class="line"><a name="l01079"></a><span class="lineno"> 1079</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dvec4            <a class="code" href="a00303.html#gae8a9b181f9dc813ad6e125a52b14b935">packed_dvec4</a>;</div>
<div class="line"><a name="l01080"></a><span class="lineno"> 1080</span>&#160;</div>
<div class="line"><a name="l01081"></a><span class="lineno"> 1081</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat2           <a class="code" href="a00303.html#ga6783859382677d35fcd5dac7dcbefdbd">aligned_dmat2</a>;</div>
<div class="line"><a name="l01082"></a><span class="lineno"> 1082</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat3           <a class="code" href="a00303.html#ga19aa695ffdb45ce29f7ea0b5029627de">aligned_dmat3</a>;</div>
<div class="line"><a name="l01083"></a><span class="lineno"> 1083</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat4           <a class="code" href="a00303.html#ga001bb387ae8192fa94dbd8b23b600439">aligned_dmat4</a>;</div>
<div class="line"><a name="l01084"></a><span class="lineno"> 1084</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat2            <a class="code" href="a00303.html#gad87408a8350918711f845f071bbe43fb">packed_dmat2</a>;</div>
<div class="line"><a name="l01085"></a><span class="lineno"> 1085</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat3            <a class="code" href="a00303.html#ga03dfc90d539cc87ea3a15a9caa5d2245">packed_dmat3</a>;</div>
<div class="line"><a name="l01086"></a><span class="lineno"> 1086</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat4            <a class="code" href="a00303.html#gada980a3485640aa8151f368f17ad3086">packed_dmat4</a>;</div>
<div class="line"><a name="l01087"></a><span class="lineno"> 1087</span>&#160;</div>
<div class="line"><a name="l01088"></a><span class="lineno"> 1088</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat2x2         <a class="code" href="a00303.html#ga449a3ec2dde6b6bb4bb94c49a6aad388">aligned_dmat2x2</a>;</div>
<div class="line"><a name="l01089"></a><span class="lineno"> 1089</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat2x3         <a class="code" href="a00303.html#ga53d519a7b1bfb69076b3ec206a6b3bd1">aligned_dmat2x3</a>;</div>
<div class="line"><a name="l01090"></a><span class="lineno"> 1090</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat2x4         <a class="code" href="a00303.html#ga5ccb2baeb0ab57b818c24e0d486c59d0">aligned_dmat2x4</a>;</div>
<div class="line"><a name="l01091"></a><span class="lineno"> 1091</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat3x2         <a class="code" href="a00303.html#ga5f5123d834bd1170edf8c386834e112c">aligned_dmat3x2</a>;</div>
<div class="line"><a name="l01092"></a><span class="lineno"> 1092</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat3x3         <a class="code" href="a00303.html#ga635bf3732281a2c2ca54d8f9d33d178f">aligned_dmat3x3</a>;</div>
<div class="line"><a name="l01093"></a><span class="lineno"> 1093</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat3x4         <a class="code" href="a00303.html#gaf488c6ad88c185054595d4d5c7ba5b9d">aligned_dmat3x4</a>;</div>
<div class="line"><a name="l01094"></a><span class="lineno"> 1094</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat4x2         <a class="code" href="a00303.html#gaa409cfb737bd59b68dc683e9b03930cc">aligned_dmat4x2</a>;</div>
<div class="line"><a name="l01095"></a><span class="lineno"> 1095</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat4x3         <a class="code" href="a00303.html#ga621e89ca1dbdcb7b5a3e7de237c44121">aligned_dmat4x3</a>;</div>
<div class="line"><a name="l01096"></a><span class="lineno"> 1096</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_dmat4x4         <a class="code" href="a00303.html#gac9bda778d0b7ad82f656dab99b71857a">aligned_dmat4x4</a>;</div>
<div class="line"><a name="l01097"></a><span class="lineno"> 1097</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat2x2          <a class="code" href="a00303.html#gaaa33d8e06657a777efb0c72c44ce87a9">packed_dmat2x2</a>;</div>
<div class="line"><a name="l01098"></a><span class="lineno"> 1098</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat2x3          <a class="code" href="a00303.html#gac3a5315f588ba04ad255188071ec4e22">packed_dmat2x3</a>;</div>
<div class="line"><a name="l01099"></a><span class="lineno"> 1099</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat2x4          <a class="code" href="a00303.html#gae398fc3156f51d3684b08f62c1a5a6d4">packed_dmat2x4</a>;</div>
<div class="line"><a name="l01100"></a><span class="lineno"> 1100</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat3x2          <a class="code" href="a00303.html#gae36de20a4c0e0b1444b7903ae811d94e">packed_dmat3x2</a>;</div>
<div class="line"><a name="l01101"></a><span class="lineno"> 1101</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat3x3          <a class="code" href="a00303.html#gab9b909f1392d86854334350efcae85f5">packed_dmat3x3</a>;</div>
<div class="line"><a name="l01102"></a><span class="lineno"> 1102</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat3x4          <a class="code" href="a00303.html#ga199131fd279c92c2ac12df6d978f1dd6">packed_dmat3x4</a>;</div>
<div class="line"><a name="l01103"></a><span class="lineno"> 1103</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat4x2          <a class="code" href="a00303.html#ga6dc65249730698d3cc9ac5d7e1bc4d72">packed_dmat4x2</a>;</div>
<div class="line"><a name="l01104"></a><span class="lineno"> 1104</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat4x3          <a class="code" href="a00303.html#gadf202aaa9ed71c09f9bbe347e43f8764">packed_dmat4x3</a>;</div>
<div class="line"><a name="l01105"></a><span class="lineno"> 1105</span>&#160;        <span class="keyword">typedef</span> packed_mediump_dmat4x4          <a class="code" href="a00303.html#gae20617435a6d042d7c38da2badd64a09">packed_dmat4x4</a>;</div>
<div class="line"><a name="l01106"></a><span class="lineno"> 1106</span>&#160;<span class="preprocessor">#else //defined(GLM_PRECISION_HIGHP_DOUBLE)</span></div>
<div class="line"><a name="l01107"></a><span class="lineno"> 1107</span>&#160;        <span class="keyword">typedef</span> aligned_highp_dvec1                     <a class="code" href="a00303.html#ga4974f46ae5a19415d91316960a53617a">aligned_dvec1</a>;</div>
<div class="line"><a name="l01109"></a><span class="lineno"> 1109</span>&#160;</div>
<div class="line"><a name="l01111"></a><span class="lineno"><a class="line" href="a00303.html#ga18d859f87122b2b3b2992ffe86dbebc0"> 1111</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dvec2                     <a class="code" href="a00303.html#ga18d859f87122b2b3b2992ffe86dbebc0">aligned_dvec2</a>;</div>
<div class="line"><a name="l01112"></a><span class="lineno"> 1112</span>&#160;</div>
<div class="line"><a name="l01114"></a><span class="lineno"><a class="line" href="a00303.html#gaa37869eea77d28419b2fb0ff70b69bf0"> 1114</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dvec3                     <a class="code" href="a00303.html#gaa37869eea77d28419b2fb0ff70b69bf0">aligned_dvec3</a>;</div>
<div class="line"><a name="l01115"></a><span class="lineno"> 1115</span>&#160;</div>
<div class="line"><a name="l01117"></a><span class="lineno"><a class="line" href="a00303.html#ga8a9f0a4795ccc442fa9901845026f9f5"> 1117</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dvec4                     <a class="code" href="a00303.html#ga8a9f0a4795ccc442fa9901845026f9f5">aligned_dvec4</a>;</div>
<div class="line"><a name="l01118"></a><span class="lineno"> 1118</span>&#160;</div>
<div class="line"><a name="l01120"></a><span class="lineno"><a class="line" href="a00303.html#ga532f0c940649b1ee303acd572fc35531"> 1120</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dvec1                      <a class="code" href="a00303.html#ga532f0c940649b1ee303acd572fc35531">packed_dvec1</a>;</div>
<div class="line"><a name="l01121"></a><span class="lineno"> 1121</span>&#160;</div>
<div class="line"><a name="l01123"></a><span class="lineno"><a class="line" href="a00303.html#ga5c194b11fbda636f2ab20c3bd0079196"> 1123</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dvec2                      <a class="code" href="a00303.html#ga5c194b11fbda636f2ab20c3bd0079196">packed_dvec2</a>;</div>
<div class="line"><a name="l01124"></a><span class="lineno"> 1124</span>&#160;</div>
<div class="line"><a name="l01126"></a><span class="lineno"><a class="line" href="a00303.html#ga0581ea552d86b2b5de7a2804bed80e72"> 1126</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dvec3                      <a class="code" href="a00303.html#ga0581ea552d86b2b5de7a2804bed80e72">packed_dvec3</a>;</div>
<div class="line"><a name="l01127"></a><span class="lineno"> 1127</span>&#160;</div>
<div class="line"><a name="l01129"></a><span class="lineno"><a class="line" href="a00303.html#gae8a9b181f9dc813ad6e125a52b14b935"> 1129</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dvec4                      <a class="code" href="a00303.html#gae8a9b181f9dc813ad6e125a52b14b935">packed_dvec4</a>;</div>
<div class="line"><a name="l01130"></a><span class="lineno"> 1130</span>&#160;</div>
<div class="line"><a name="l01132"></a><span class="lineno"><a class="line" href="a00303.html#ga6783859382677d35fcd5dac7dcbefdbd"> 1132</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat2                     <a class="code" href="a00303.html#ga6783859382677d35fcd5dac7dcbefdbd">aligned_dmat2</a>;</div>
<div class="line"><a name="l01133"></a><span class="lineno"> 1133</span>&#160;</div>
<div class="line"><a name="l01135"></a><span class="lineno"><a class="line" href="a00303.html#ga19aa695ffdb45ce29f7ea0b5029627de"> 1135</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat3                     <a class="code" href="a00303.html#ga19aa695ffdb45ce29f7ea0b5029627de">aligned_dmat3</a>;</div>
<div class="line"><a name="l01136"></a><span class="lineno"> 1136</span>&#160;</div>
<div class="line"><a name="l01138"></a><span class="lineno"><a class="line" href="a00303.html#ga001bb387ae8192fa94dbd8b23b600439"> 1138</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat4                     <a class="code" href="a00303.html#ga001bb387ae8192fa94dbd8b23b600439">aligned_dmat4</a>;</div>
<div class="line"><a name="l01139"></a><span class="lineno"> 1139</span>&#160;</div>
<div class="line"><a name="l01141"></a><span class="lineno"><a class="line" href="a00303.html#gad87408a8350918711f845f071bbe43fb"> 1141</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat2                      <a class="code" href="a00303.html#gad87408a8350918711f845f071bbe43fb">packed_dmat2</a>;</div>
<div class="line"><a name="l01142"></a><span class="lineno"> 1142</span>&#160;</div>
<div class="line"><a name="l01144"></a><span class="lineno"><a class="line" href="a00303.html#ga03dfc90d539cc87ea3a15a9caa5d2245"> 1144</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat3                      <a class="code" href="a00303.html#ga03dfc90d539cc87ea3a15a9caa5d2245">packed_dmat3</a>;</div>
<div class="line"><a name="l01145"></a><span class="lineno"> 1145</span>&#160;</div>
<div class="line"><a name="l01147"></a><span class="lineno"><a class="line" href="a00303.html#gada980a3485640aa8151f368f17ad3086"> 1147</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat4                      <a class="code" href="a00303.html#gada980a3485640aa8151f368f17ad3086">packed_dmat4</a>;</div>
<div class="line"><a name="l01148"></a><span class="lineno"> 1148</span>&#160;</div>
<div class="line"><a name="l01150"></a><span class="lineno"><a class="line" href="a00303.html#ga449a3ec2dde6b6bb4bb94c49a6aad388"> 1150</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat2x2           <a class="code" href="a00303.html#ga449a3ec2dde6b6bb4bb94c49a6aad388">aligned_dmat2x2</a>;</div>
<div class="line"><a name="l01151"></a><span class="lineno"> 1151</span>&#160;</div>
<div class="line"><a name="l01153"></a><span class="lineno"><a class="line" href="a00303.html#ga53d519a7b1bfb69076b3ec206a6b3bd1"> 1153</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat2x3           <a class="code" href="a00303.html#ga53d519a7b1bfb69076b3ec206a6b3bd1">aligned_dmat2x3</a>;</div>
<div class="line"><a name="l01154"></a><span class="lineno"> 1154</span>&#160;</div>
<div class="line"><a name="l01156"></a><span class="lineno"><a class="line" href="a00303.html#ga5ccb2baeb0ab57b818c24e0d486c59d0"> 1156</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat2x4           <a class="code" href="a00303.html#ga5ccb2baeb0ab57b818c24e0d486c59d0">aligned_dmat2x4</a>;</div>
<div class="line"><a name="l01157"></a><span class="lineno"> 1157</span>&#160;</div>
<div class="line"><a name="l01159"></a><span class="lineno"><a class="line" href="a00303.html#ga5f5123d834bd1170edf8c386834e112c"> 1159</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat3x2           <a class="code" href="a00303.html#ga5f5123d834bd1170edf8c386834e112c">aligned_dmat3x2</a>;</div>
<div class="line"><a name="l01160"></a><span class="lineno"> 1160</span>&#160;</div>
<div class="line"><a name="l01162"></a><span class="lineno"><a class="line" href="a00303.html#ga635bf3732281a2c2ca54d8f9d33d178f"> 1162</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat3x3           <a class="code" href="a00303.html#ga635bf3732281a2c2ca54d8f9d33d178f">aligned_dmat3x3</a>;</div>
<div class="line"><a name="l01163"></a><span class="lineno"> 1163</span>&#160;</div>
<div class="line"><a name="l01165"></a><span class="lineno"><a class="line" href="a00303.html#gaf488c6ad88c185054595d4d5c7ba5b9d"> 1165</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat3x4           <a class="code" href="a00303.html#gaf488c6ad88c185054595d4d5c7ba5b9d">aligned_dmat3x4</a>;</div>
<div class="line"><a name="l01166"></a><span class="lineno"> 1166</span>&#160;</div>
<div class="line"><a name="l01168"></a><span class="lineno"><a class="line" href="a00303.html#gaa409cfb737bd59b68dc683e9b03930cc"> 1168</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat4x2           <a class="code" href="a00303.html#gaa409cfb737bd59b68dc683e9b03930cc">aligned_dmat4x2</a>;</div>
<div class="line"><a name="l01169"></a><span class="lineno"> 1169</span>&#160;</div>
<div class="line"><a name="l01171"></a><span class="lineno"><a class="line" href="a00303.html#ga621e89ca1dbdcb7b5a3e7de237c44121"> 1171</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat4x3           <a class="code" href="a00303.html#ga621e89ca1dbdcb7b5a3e7de237c44121">aligned_dmat4x3</a>;</div>
<div class="line"><a name="l01172"></a><span class="lineno"> 1172</span>&#160;</div>
<div class="line"><a name="l01174"></a><span class="lineno"><a class="line" href="a00303.html#gac9bda778d0b7ad82f656dab99b71857a"> 1174</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_dmat4x4           <a class="code" href="a00303.html#gac9bda778d0b7ad82f656dab99b71857a">aligned_dmat4x4</a>;</div>
<div class="line"><a name="l01175"></a><span class="lineno"> 1175</span>&#160;</div>
<div class="line"><a name="l01177"></a><span class="lineno"><a class="line" href="a00303.html#gaaa33d8e06657a777efb0c72c44ce87a9"> 1177</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat2x2            <a class="code" href="a00303.html#gaaa33d8e06657a777efb0c72c44ce87a9">packed_dmat2x2</a>;</div>
<div class="line"><a name="l01178"></a><span class="lineno"> 1178</span>&#160;</div>
<div class="line"><a name="l01180"></a><span class="lineno"><a class="line" href="a00303.html#gac3a5315f588ba04ad255188071ec4e22"> 1180</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat2x3            <a class="code" href="a00303.html#gac3a5315f588ba04ad255188071ec4e22">packed_dmat2x3</a>;</div>
<div class="line"><a name="l01181"></a><span class="lineno"> 1181</span>&#160;</div>
<div class="line"><a name="l01183"></a><span class="lineno"><a class="line" href="a00303.html#gae398fc3156f51d3684b08f62c1a5a6d4"> 1183</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat2x4            <a class="code" href="a00303.html#gae398fc3156f51d3684b08f62c1a5a6d4">packed_dmat2x4</a>;</div>
<div class="line"><a name="l01184"></a><span class="lineno"> 1184</span>&#160;</div>
<div class="line"><a name="l01186"></a><span class="lineno"><a class="line" href="a00303.html#gae36de20a4c0e0b1444b7903ae811d94e"> 1186</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat3x2            <a class="code" href="a00303.html#gae36de20a4c0e0b1444b7903ae811d94e">packed_dmat3x2</a>;</div>
<div class="line"><a name="l01187"></a><span class="lineno"> 1187</span>&#160;</div>
<div class="line"><a name="l01189"></a><span class="lineno"><a class="line" href="a00303.html#gab9b909f1392d86854334350efcae85f5"> 1189</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat3x3            <a class="code" href="a00303.html#gab9b909f1392d86854334350efcae85f5">packed_dmat3x3</a>;</div>
<div class="line"><a name="l01190"></a><span class="lineno"> 1190</span>&#160;</div>
<div class="line"><a name="l01192"></a><span class="lineno"><a class="line" href="a00303.html#ga199131fd279c92c2ac12df6d978f1dd6"> 1192</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat3x4            <a class="code" href="a00303.html#ga199131fd279c92c2ac12df6d978f1dd6">packed_dmat3x4</a>;</div>
<div class="line"><a name="l01193"></a><span class="lineno"> 1193</span>&#160;</div>
<div class="line"><a name="l01195"></a><span class="lineno"><a class="line" href="a00303.html#ga6dc65249730698d3cc9ac5d7e1bc4d72"> 1195</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat4x2            <a class="code" href="a00303.html#ga6dc65249730698d3cc9ac5d7e1bc4d72">packed_dmat4x2</a>;</div>
<div class="line"><a name="l01196"></a><span class="lineno"> 1196</span>&#160;</div>
<div class="line"><a name="l01198"></a><span class="lineno"><a class="line" href="a00303.html#gadf202aaa9ed71c09f9bbe347e43f8764"> 1198</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat4x3            <a class="code" href="a00303.html#gadf202aaa9ed71c09f9bbe347e43f8764">packed_dmat4x3</a>;</div>
<div class="line"><a name="l01199"></a><span class="lineno"> 1199</span>&#160;</div>
<div class="line"><a name="l01201"></a><span class="lineno"><a class="line" href="a00303.html#gae20617435a6d042d7c38da2badd64a09"> 1201</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_dmat4x4            <a class="code" href="a00303.html#gae20617435a6d042d7c38da2badd64a09">packed_dmat4x4</a>;</div>
<div class="line"><a name="l01202"></a><span class="lineno"> 1202</span>&#160;<span class="preprocessor">#endif//GLM_PRECISION</span></div>
<div class="line"><a name="l01203"></a><span class="lineno"> 1203</span>&#160;</div>
<div class="line"><a name="l01204"></a><span class="lineno"> 1204</span>&#160;<span class="preprocessor">#if(defined(GLM_PRECISION_LOWP_INT))</span></div>
<div class="line"><a name="l01205"></a><span class="lineno"> 1205</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_ivec1                      <a class="code" href="a00303.html#ga76298aed82a439063c3d55980c84aa0b">aligned_ivec1</a>;</div>
<div class="line"><a name="l01206"></a><span class="lineno"> 1206</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_ivec2                      <a class="code" href="a00303.html#gae4f38fd2c86cee6940986197777b3ca4">aligned_ivec2</a>;</div>
<div class="line"><a name="l01207"></a><span class="lineno"> 1207</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_ivec3                      <a class="code" href="a00303.html#ga32794322d294e5ace7fed4a61896f270">aligned_ivec3</a>;</div>
<div class="line"><a name="l01208"></a><span class="lineno"> 1208</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_ivec4                      <a class="code" href="a00303.html#ga7f79eae5927c9033d84617e49f6f34e4">aligned_ivec4</a>;</div>
<div class="line"><a name="l01209"></a><span class="lineno"> 1209</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_MEDIUMP_INT))</span></div>
<div class="line"><a name="l01210"></a><span class="lineno"> 1210</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_ivec1           <a class="code" href="a00303.html#ga76298aed82a439063c3d55980c84aa0b">aligned_ivec1</a>;</div>
<div class="line"><a name="l01211"></a><span class="lineno"> 1211</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_ivec2           <a class="code" href="a00303.html#gae4f38fd2c86cee6940986197777b3ca4">aligned_ivec2</a>;</div>
<div class="line"><a name="l01212"></a><span class="lineno"> 1212</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_ivec3           <a class="code" href="a00303.html#ga32794322d294e5ace7fed4a61896f270">aligned_ivec3</a>;</div>
<div class="line"><a name="l01213"></a><span class="lineno"> 1213</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_ivec4           <a class="code" href="a00303.html#ga7f79eae5927c9033d84617e49f6f34e4">aligned_ivec4</a>;</div>
<div class="line"><a name="l01214"></a><span class="lineno"> 1214</span>&#160;<span class="preprocessor">#else //defined(GLM_PRECISION_HIGHP_INT)</span></div>
<div class="line"><a name="l01215"></a><span class="lineno"> 1215</span>&#160;        <span class="keyword">typedef</span> aligned_highp_ivec1                     <a class="code" href="a00303.html#ga76298aed82a439063c3d55980c84aa0b">aligned_ivec1</a>;</div>
<div class="line"><a name="l01217"></a><span class="lineno"> 1217</span>&#160;</div>
<div class="line"><a name="l01219"></a><span class="lineno"><a class="line" href="a00303.html#gae4f38fd2c86cee6940986197777b3ca4"> 1219</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_ivec2                     <a class="code" href="a00303.html#gae4f38fd2c86cee6940986197777b3ca4">aligned_ivec2</a>;</div>
<div class="line"><a name="l01220"></a><span class="lineno"> 1220</span>&#160;</div>
<div class="line"><a name="l01222"></a><span class="lineno"><a class="line" href="a00303.html#ga32794322d294e5ace7fed4a61896f270"> 1222</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_ivec3                     <a class="code" href="a00303.html#ga32794322d294e5ace7fed4a61896f270">aligned_ivec3</a>;</div>
<div class="line"><a name="l01223"></a><span class="lineno"> 1223</span>&#160;</div>
<div class="line"><a name="l01225"></a><span class="lineno"><a class="line" href="a00303.html#ga7f79eae5927c9033d84617e49f6f34e4"> 1225</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_ivec4                     <a class="code" href="a00303.html#ga7f79eae5927c9033d84617e49f6f34e4">aligned_ivec4</a>;</div>
<div class="line"><a name="l01226"></a><span class="lineno"> 1226</span>&#160;</div>
<div class="line"><a name="l01228"></a><span class="lineno"><a class="line" href="a00303.html#ga11581a06fc7bf941fa4d4b6aca29812c"> 1228</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_ivec1                      <a class="code" href="a00303.html#ga11581a06fc7bf941fa4d4b6aca29812c">packed_ivec1</a>;</div>
<div class="line"><a name="l01229"></a><span class="lineno"> 1229</span>&#160;</div>
<div class="line"><a name="l01231"></a><span class="lineno"><a class="line" href="a00303.html#ga1fe4c5f56b8087d773aa90dc88a257a7"> 1231</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_ivec2                      <a class="code" href="a00303.html#ga1fe4c5f56b8087d773aa90dc88a257a7">packed_ivec2</a>;</div>
<div class="line"><a name="l01232"></a><span class="lineno"> 1232</span>&#160;</div>
<div class="line"><a name="l01234"></a><span class="lineno"><a class="line" href="a00303.html#gae157682a7847161787951ba1db4cf325"> 1234</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_ivec3                      <a class="code" href="a00303.html#gae157682a7847161787951ba1db4cf325">packed_ivec3</a>;</div>
<div class="line"><a name="l01235"></a><span class="lineno"> 1235</span>&#160;</div>
<div class="line"><a name="l01237"></a><span class="lineno"><a class="line" href="a00303.html#gac228b70372abd561340d5f926a7c1778"> 1237</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_ivec4                      <a class="code" href="a00303.html#gac228b70372abd561340d5f926a7c1778">packed_ivec4</a>;</div>
<div class="line"><a name="l01238"></a><span class="lineno"> 1238</span>&#160;<span class="preprocessor">#endif//GLM_PRECISION</span></div>
<div class="line"><a name="l01239"></a><span class="lineno"> 1239</span>&#160;</div>
<div class="line"><a name="l01240"></a><span class="lineno"> 1240</span>&#160;        <span class="comment">// -- Unsigned integer definition --</span></div>
<div class="line"><a name="l01241"></a><span class="lineno"> 1241</span>&#160;</div>
<div class="line"><a name="l01242"></a><span class="lineno"> 1242</span>&#160;<span class="preprocessor">#if(defined(GLM_PRECISION_LOWP_UINT))</span></div>
<div class="line"><a name="l01243"></a><span class="lineno"> 1243</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_uvec1                      <a class="code" href="a00303.html#ga1ff8ed402c93d280ff0597c1c5e7c548">aligned_uvec1</a>;</div>
<div class="line"><a name="l01244"></a><span class="lineno"> 1244</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_uvec2                      <a class="code" href="a00303.html#ga074137e3be58528d67041c223d49f398">aligned_uvec2</a>;</div>
<div class="line"><a name="l01245"></a><span class="lineno"> 1245</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_uvec3                      <a class="code" href="a00303.html#ga2a8d9c3046f89d854eb758adfa0811c0">aligned_uvec3</a>;</div>
<div class="line"><a name="l01246"></a><span class="lineno"> 1246</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_uvec4                      <a class="code" href="a00303.html#gabf842c45eea186170c267a328e3f3b7d">aligned_uvec4</a>;</div>
<div class="line"><a name="l01247"></a><span class="lineno"> 1247</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_MEDIUMP_UINT))</span></div>
<div class="line"><a name="l01248"></a><span class="lineno"> 1248</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_uvec1           <a class="code" href="a00303.html#ga1ff8ed402c93d280ff0597c1c5e7c548">aligned_uvec1</a>;</div>
<div class="line"><a name="l01249"></a><span class="lineno"> 1249</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_uvec2           <a class="code" href="a00303.html#ga074137e3be58528d67041c223d49f398">aligned_uvec2</a>;</div>
<div class="line"><a name="l01250"></a><span class="lineno"> 1250</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_uvec3           <a class="code" href="a00303.html#ga2a8d9c3046f89d854eb758adfa0811c0">aligned_uvec3</a>;</div>
<div class="line"><a name="l01251"></a><span class="lineno"> 1251</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_uvec4           <a class="code" href="a00303.html#gabf842c45eea186170c267a328e3f3b7d">aligned_uvec4</a>;</div>
<div class="line"><a name="l01252"></a><span class="lineno"> 1252</span>&#160;<span class="preprocessor">#else //defined(GLM_PRECISION_HIGHP_UINT)</span></div>
<div class="line"><a name="l01253"></a><span class="lineno"> 1253</span>&#160;        <span class="keyword">typedef</span> aligned_highp_uvec1                     <a class="code" href="a00303.html#ga1ff8ed402c93d280ff0597c1c5e7c548">aligned_uvec1</a>;</div>
<div class="line"><a name="l01255"></a><span class="lineno"> 1255</span>&#160;</div>
<div class="line"><a name="l01257"></a><span class="lineno"><a class="line" href="a00303.html#ga074137e3be58528d67041c223d49f398"> 1257</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_uvec2                     <a class="code" href="a00303.html#ga074137e3be58528d67041c223d49f398">aligned_uvec2</a>;</div>
<div class="line"><a name="l01258"></a><span class="lineno"> 1258</span>&#160;</div>
<div class="line"><a name="l01260"></a><span class="lineno"><a class="line" href="a00303.html#ga2a8d9c3046f89d854eb758adfa0811c0"> 1260</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_uvec3                     <a class="code" href="a00303.html#ga2a8d9c3046f89d854eb758adfa0811c0">aligned_uvec3</a>;</div>
<div class="line"><a name="l01261"></a><span class="lineno"> 1261</span>&#160;</div>
<div class="line"><a name="l01263"></a><span class="lineno"><a class="line" href="a00303.html#gabf842c45eea186170c267a328e3f3b7d"> 1263</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_uvec4                     <a class="code" href="a00303.html#gabf842c45eea186170c267a328e3f3b7d">aligned_uvec4</a>;</div>
<div class="line"><a name="l01264"></a><span class="lineno"> 1264</span>&#160;</div>
<div class="line"><a name="l01266"></a><span class="lineno"><a class="line" href="a00303.html#ga5621493caac01bdd22ab6be4416b0314"> 1266</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_uvec1                      <a class="code" href="a00303.html#ga5621493caac01bdd22ab6be4416b0314">packed_uvec1</a>;</div>
<div class="line"><a name="l01267"></a><span class="lineno"> 1267</span>&#160;</div>
<div class="line"><a name="l01269"></a><span class="lineno"><a class="line" href="a00303.html#gabcc33efb4d5e83b8fe4706360e75b932"> 1269</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_uvec2                      <a class="code" href="a00303.html#gabcc33efb4d5e83b8fe4706360e75b932">packed_uvec2</a>;</div>
<div class="line"><a name="l01270"></a><span class="lineno"> 1270</span>&#160;</div>
<div class="line"><a name="l01272"></a><span class="lineno"><a class="line" href="a00303.html#gab96804e99e3a72a35740fec690c79617"> 1272</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_uvec3                      <a class="code" href="a00303.html#gab96804e99e3a72a35740fec690c79617">packed_uvec3</a>;</div>
<div class="line"><a name="l01273"></a><span class="lineno"> 1273</span>&#160;</div>
<div class="line"><a name="l01275"></a><span class="lineno"><a class="line" href="a00303.html#ga8e5d92e84ebdbe2480cf96bc17d6e2f2"> 1275</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_uvec4                      <a class="code" href="a00303.html#ga8e5d92e84ebdbe2480cf96bc17d6e2f2">packed_uvec4</a>;</div>
<div class="line"><a name="l01276"></a><span class="lineno"> 1276</span>&#160;<span class="preprocessor">#endif//GLM_PRECISION</span></div>
<div class="line"><a name="l01277"></a><span class="lineno"> 1277</span>&#160;</div>
<div class="line"><a name="l01278"></a><span class="lineno"> 1278</span>&#160;<span class="preprocessor">#if(defined(GLM_PRECISION_LOWP_BOOL))</span></div>
<div class="line"><a name="l01279"></a><span class="lineno"> 1279</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_bvec1                      <a class="code" href="a00303.html#ga780a35f764020f553a9601a3fcdcd059">aligned_bvec1</a>;</div>
<div class="line"><a name="l01280"></a><span class="lineno"> 1280</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_bvec2                      <a class="code" href="a00303.html#gae766b317c5afec852bfb3d74a3c54bc8">aligned_bvec2</a>;</div>
<div class="line"><a name="l01281"></a><span class="lineno"> 1281</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_bvec3                      <a class="code" href="a00303.html#gae1964ba70d15915e5b710926decbb3cb">aligned_bvec3</a>;</div>
<div class="line"><a name="l01282"></a><span class="lineno"> 1282</span>&#160;        <span class="keyword">typedef</span> aligned_lowp_bvec4                      <a class="code" href="a00303.html#gae164a1f7879f828bc35e50b79d786b05">aligned_bvec4</a>;</div>
<div class="line"><a name="l01283"></a><span class="lineno"> 1283</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_MEDIUMP_BOOL))</span></div>
<div class="line"><a name="l01284"></a><span class="lineno"> 1284</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_bvec1           <a class="code" href="a00303.html#ga780a35f764020f553a9601a3fcdcd059">aligned_bvec1</a>;</div>
<div class="line"><a name="l01285"></a><span class="lineno"> 1285</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_bvec2           <a class="code" href="a00303.html#gae766b317c5afec852bfb3d74a3c54bc8">aligned_bvec2</a>;</div>
<div class="line"><a name="l01286"></a><span class="lineno"> 1286</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_bvec3           <a class="code" href="a00303.html#gae1964ba70d15915e5b710926decbb3cb">aligned_bvec3</a>;</div>
<div class="line"><a name="l01287"></a><span class="lineno"> 1287</span>&#160;        <span class="keyword">typedef</span> aligned_mediump_bvec4           <a class="code" href="a00303.html#gae164a1f7879f828bc35e50b79d786b05">aligned_bvec4</a>;</div>
<div class="line"><a name="l01288"></a><span class="lineno"> 1288</span>&#160;<span class="preprocessor">#else //defined(GLM_PRECISION_HIGHP_BOOL)</span></div>
<div class="line"><a name="l01289"></a><span class="lineno"> 1289</span>&#160;        <span class="keyword">typedef</span> aligned_highp_bvec1                     <a class="code" href="a00303.html#ga780a35f764020f553a9601a3fcdcd059">aligned_bvec1</a>;</div>
<div class="line"><a name="l01291"></a><span class="lineno"> 1291</span>&#160;</div>
<div class="line"><a name="l01293"></a><span class="lineno"><a class="line" href="a00303.html#gae766b317c5afec852bfb3d74a3c54bc8"> 1293</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_bvec2                     <a class="code" href="a00303.html#gae766b317c5afec852bfb3d74a3c54bc8">aligned_bvec2</a>;</div>
<div class="line"><a name="l01294"></a><span class="lineno"> 1294</span>&#160;</div>
<div class="line"><a name="l01296"></a><span class="lineno"><a class="line" href="a00303.html#gae1964ba70d15915e5b710926decbb3cb"> 1296</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_bvec3                     <a class="code" href="a00303.html#gae1964ba70d15915e5b710926decbb3cb">aligned_bvec3</a>;</div>
<div class="line"><a name="l01297"></a><span class="lineno"> 1297</span>&#160;</div>
<div class="line"><a name="l01299"></a><span class="lineno"><a class="line" href="a00303.html#gae164a1f7879f828bc35e50b79d786b05"> 1299</a></span>&#160;        <span class="keyword">typedef</span> aligned_highp_bvec4                     <a class="code" href="a00303.html#gae164a1f7879f828bc35e50b79d786b05">aligned_bvec4</a>;</div>
<div class="line"><a name="l01300"></a><span class="lineno"> 1300</span>&#160;</div>
<div class="line"><a name="l01302"></a><span class="lineno"><a class="line" href="a00303.html#ga88632cea9008ac0ac1388e94e804a53c"> 1302</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_bvec1                      <a class="code" href="a00303.html#ga88632cea9008ac0ac1388e94e804a53c">packed_bvec1</a>;</div>
<div class="line"><a name="l01303"></a><span class="lineno"> 1303</span>&#160;</div>
<div class="line"><a name="l01305"></a><span class="lineno"><a class="line" href="a00303.html#gab85245913eaa40ab82adabcae37086cb"> 1305</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_bvec2                      <a class="code" href="a00303.html#gab85245913eaa40ab82adabcae37086cb">packed_bvec2</a>;</div>
<div class="line"><a name="l01306"></a><span class="lineno"> 1306</span>&#160;</div>
<div class="line"><a name="l01308"></a><span class="lineno"><a class="line" href="a00303.html#ga0c48f9417f649e27f3fb0c9f733a18bd"> 1308</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_bvec3                      <a class="code" href="a00303.html#ga0c48f9417f649e27f3fb0c9f733a18bd">packed_bvec3</a>;</div>
<div class="line"><a name="l01309"></a><span class="lineno"> 1309</span>&#160;</div>
<div class="line"><a name="l01311"></a><span class="lineno"><a class="line" href="a00303.html#ga3180d7db84a74c402157df3bbc0ae3ed"> 1311</a></span>&#160;        <span class="keyword">typedef</span> packed_highp_bvec4                      <a class="code" href="a00303.html#ga3180d7db84a74c402157df3bbc0ae3ed">packed_bvec4</a>;</div>
<div class="line"><a name="l01312"></a><span class="lineno"> 1312</span>&#160;<span class="preprocessor">#endif//GLM_PRECISION</span></div>
<div class="line"><a name="l01313"></a><span class="lineno"> 1313</span>&#160;</div>
<div class="line"><a name="l01315"></a><span class="lineno"> 1315</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="ttc" id="a00303_html_gab96804e99e3a72a35740fec690c79617"><div class="ttname"><a href="a00303.html#gab96804e99e3a72a35740fec690c79617">glm::packed_uvec3</a></div><div class="ttdeci">packed_highp_uvec3 packed_uvec3</div><div class="ttdoc">3 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01272">gtc/type_aligned.hpp:1272</a></div></div>
<div class="ttc" id="a00303_html_ga51eaadcdc292c8750f746a5dc3e6c517"><div class="ttname"><a href="a00303.html#ga51eaadcdc292c8750f746a5dc3e6c517">glm::packed_mat2x2</a></div><div class="ttdeci">packed_highp_mat2x2 packed_mat2x2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01009">gtc/type_aligned.hpp:1009</a></div></div>
<div class="ttc" id="a00303_html_gaa694fab1f8df5f658846573ba8ffc563"><div class="ttname"><a href="a00303.html#gaa694fab1f8df5f658846573ba8ffc563">glm::aligned_lowp_mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, float, aligned_lowp &gt; aligned_lowp_mat2x4</div><div class="ttdoc">2 by 4 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00609">gtc/type_aligned.hpp:609</a></div></div>
<div class="ttc" id="a00303_html_gaa7a76555ee4853614e5755181a8dd54e"><div class="ttname"><a href="a00303.html#gaa7a76555ee4853614e5755181a8dd54e">glm::aligned_lowp_bvec4</a></div><div class="ttdeci">vec&lt; 4, bool, aligned_lowp &gt; aligned_lowp_bvec4</div><div class="ttdoc">4 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00363">gtc/type_aligned.hpp:363</a></div></div>
<div class="ttc" id="a00303_html_ga81b5368fe485e2630aa9b44832d592e7"><div class="ttname"><a href="a00303.html#ga81b5368fe485e2630aa9b44832d592e7">glm::packed_highp_dvec4</a></div><div class="ttdeci">vec&lt; 4, double, packed_highp &gt; packed_highp_dvec4</div><div class="ttdoc">4 components vector tightly packed in memory of double-precision floating-point numbers using high pr...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00375">gtc/type_aligned.hpp:375</a></div></div>
<div class="ttc" id="a00303_html_gac3a5315f588ba04ad255188071ec4e22"><div class="ttname"><a href="a00303.html#gac3a5315f588ba04ad255188071ec4e22">glm::packed_dmat2x3</a></div><div class="ttdeci">packed_highp_dmat2x3 packed_dmat2x3</div><div class="ttdoc">2 by 3 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01180">gtc/type_aligned.hpp:1180</a></div></div>
<div class="ttc" id="a00303_html_ga7c20adbe1409e3fe4544677a7f6fe954"><div class="ttname"><a href="a00303.html#ga7c20adbe1409e3fe4544677a7f6fe954">glm::packed_lowp_bvec3</a></div><div class="ttdeci">vec&lt; 3, bool, packed_lowp &gt; packed_lowp_bvec3</div><div class="ttdoc">3 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00316">gtc/type_aligned.hpp:316</a></div></div>
<div class="ttc" id="a00303_html_ga2c139854e5b04cf08a957dee3b510441"><div class="ttname"><a href="a00303.html#ga2c139854e5b04cf08a957dee3b510441">glm::packed_mat4</a></div><div class="ttdeci">packed_highp_mat4 packed_mat4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00979">gtc/type_aligned.hpp:979</a></div></div>
<div class="ttc" id="a00303_html_ga074137e3be58528d67041c223d49f398"><div class="ttname"><a href="a00303.html#ga074137e3be58528d67041c223d49f398">glm::aligned_uvec2</a></div><div class="ttdeci">aligned_highp_uvec2 aligned_uvec2</div><div class="ttdoc">2 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01257">gtc/type_aligned.hpp:1257</a></div></div>
<div class="ttc" id="a00303_html_ga59fadcd3835646e419372ae8b43c5d37"><div class="ttname"><a href="a00303.html#ga59fadcd3835646e419372ae8b43c5d37">glm::aligned_lowp_bvec2</a></div><div class="ttdeci">vec&lt; 2, bool, aligned_lowp &gt; aligned_lowp_bvec2</div><div class="ttdoc">2 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00179">gtc/type_aligned.hpp:179</a></div></div>
<div class="ttc" id="a00303_html_ga865a9c7bb22434b1b8c5ac31e164b628"><div class="ttname"><a href="a00303.html#ga865a9c7bb22434b1b8c5ac31e164b628">glm::packed_highp_ivec3</a></div><div class="ttdeci">vec&lt; 3, int, packed_highp &gt; packed_highp_ivec3</div><div class="ttdoc">3 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00292">gtc/type_aligned.hpp:292</a></div></div>
<div class="ttc" id="a00303_html_ga12e39e797fb724a5b51fcbea2513a7da"><div class="ttname"><a href="a00303.html#ga12e39e797fb724a5b51fcbea2513a7da">glm::packed_highp_dmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, double, packed_highp &gt; packed_highp_dmat4x2</div><div class="ttdoc">4 by 2 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00782">gtc/type_aligned.hpp:782</a></div></div>
<div class="ttc" id="a00303_html_gad87408a8350918711f845f071bbe43fb"><div class="ttname"><a href="a00303.html#gad87408a8350918711f845f071bbe43fb">glm::packed_dmat2</a></div><div class="ttdeci">packed_highp_dmat2 packed_dmat2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01141">gtc/type_aligned.hpp:1141</a></div></div>
<div class="ttc" id="a00303_html_gadac7c040c4810dd52b36fcd09d097400"><div class="ttname"><a href="a00303.html#gadac7c040c4810dd52b36fcd09d097400">glm::packed_highp_dmat3</a></div><div class="ttdeci">mat&lt; 3, 3, double, packed_highp &gt; packed_highp_dmat3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00478">gtc/type_aligned.hpp:478</a></div></div>
<div class="ttc" id="a00303_html_ga7e96981e872f17a780d9f1c22dc1f512"><div class="ttname"><a href="a00303.html#ga7e96981e872f17a780d9f1c22dc1f512">glm::aligned_lowp_mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, float, aligned_lowp &gt; aligned_lowp_mat4x3</div><div class="ttdoc">4 by 3 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00799">gtc/type_aligned.hpp:799</a></div></div>
<div class="ttc" id="a00303_html_ga6998ac2a8d7fe456b651a6336ed26bb0"><div class="ttname"><a href="a00303.html#ga6998ac2a8d7fe456b651a6336ed26bb0">glm::packed_highp_dmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, double, packed_highp &gt; packed_highp_dmat2x4</div><div class="ttdoc">2 by 4 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00630">gtc/type_aligned.hpp:630</a></div></div>
<div class="ttc" id="a00303_html_gaea13d89d49daca2c796aeaa82fc2c2f2"><div class="ttname"><a href="a00303.html#gaea13d89d49daca2c796aeaa82fc2c2f2">glm::aligned_mediump_ivec2</a></div><div class="ttdeci">vec&lt; 2, int, aligned_mediump &gt; aligned_mediump_ivec2</div><div class="ttdoc">2 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00158">gtc/type_aligned.hpp:158</a></div></div>
<div class="ttc" id="a00303_html_ga02ca6255394aa778abaeb0f733c4d2b6"><div class="ttname"><a href="a00303.html#ga02ca6255394aa778abaeb0f733c4d2b6">glm::packed_mediump_mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, float, packed_mediump &gt; packed_mediump_mat4x3</div><div class="ttdoc">4 by 3 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00814">gtc/type_aligned.hpp:814</a></div></div>
<div class="ttc" id="a00303_html_gae157682a7847161787951ba1db4cf325"><div class="ttname"><a href="a00303.html#gae157682a7847161787951ba1db4cf325">glm::packed_ivec3</a></div><div class="ttdeci">packed_highp_ivec3 packed_ivec3</div><div class="ttdoc">3 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01234">gtc/type_aligned.hpp:1234</a></div></div>
<div class="ttc" id="a00303_html_ga683c8ca66de323ea533a760abedd0efc"><div class="ttname"><a href="a00303.html#ga683c8ca66de323ea533a760abedd0efc">glm::aligned_highp_dmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, double, aligned_highp &gt; aligned_highp_dmat3x4</div><div class="ttdoc">3 by 4 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00726">gtc/type_aligned.hpp:726</a></div></div>
<div class="ttc" id="a00303_html_gaf85877d38d8cfbc21d59d939afd72375"><div class="ttname"><a href="a00303.html#gaf85877d38d8cfbc21d59d939afd72375">glm::packed_mediump_dmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, packed_mediump &gt; packed_mediump_dmat3x3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00709">gtc/type_aligned.hpp:709</a></div></div>
<div class="ttc" id="a00303_html_gadd019b43fcf42e1590d45dddaa504a1a"><div class="ttname"><a href="a00303.html#gadd019b43fcf42e1590d45dddaa504a1a">glm::packed_mat2</a></div><div class="ttdeci">packed_highp_mat2 packed_mat2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00973">gtc/type_aligned.hpp:973</a></div></div>
<div class="ttc" id="a00303_html_ga8fc0e66da83599071b7ec17510686cd9"><div class="ttname"><a href="a00303.html#ga8fc0e66da83599071b7ec17510686cd9">glm::packed_lowp_dmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, double, packed_lowp &gt; packed_lowp_dmat3x4</div><div class="ttdoc">3 by 4 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00750">gtc/type_aligned.hpp:750</a></div></div>
<div class="ttc" id="a00303_html_ga026a55ddbf2bafb1432f1157a2708616"><div class="ttname"><a href="a00303.html#ga026a55ddbf2bafb1432f1157a2708616">glm::aligned_mediump_vec2</a></div><div class="ttdeci">vec&lt; 2, float, aligned_mediump &gt; aligned_mediump_vec2</div><div class="ttdoc">2 components vector aligned in memory of single-precision floating-point numbers using medium precisi...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00140">gtc/type_aligned.hpp:140</a></div></div>
<div class="ttc" id="a00303_html_gadae3dcfc22d28c64d0548cbfd9d08719"><div class="ttname"><a href="a00303.html#gadae3dcfc22d28c64d0548cbfd9d08719">glm::aligned_lowp_mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, aligned_lowp &gt; aligned_lowp_mat4x4</div><div class="ttdoc">4 by 4 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00837">gtc/type_aligned.hpp:837</a></div></div>
<div class="ttc" id="a00303_html_ga9bc12b0ab7be8448836711b77cc7b83a"><div class="ttname"><a href="a00303.html#ga9bc12b0ab7be8448836711b77cc7b83a">glm::packed_mat3</a></div><div class="ttdeci">packed_highp_mat3 packed_mat3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00976">gtc/type_aligned.hpp:976</a></div></div>
<div class="ttc" id="a00303_html_gada980a3485640aa8151f368f17ad3086"><div class="ttname"><a href="a00303.html#gada980a3485640aa8151f368f17ad3086">glm::packed_dmat4</a></div><div class="ttdeci">packed_highp_dmat4 packed_dmat4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01147">gtc/type_aligned.hpp:1147</a></div></div>
<div class="ttc" id="a00303_html_ga37a0e0bf653169b581c5eea3d547fa5d"><div class="ttname"><a href="a00303.html#ga37a0e0bf653169b581c5eea3d547fa5d">glm::packed_vec4</a></div><div class="ttdeci">packed_highp_vec4 packed_vec4</div><div class="ttdoc">4 components vector tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00961">gtc/type_aligned.hpp:961</a></div></div>
<div class="ttc" id="a00303_html_ga7cb26d354dd69d23849c34c4fba88da9"><div class="ttname"><a href="a00303.html#ga7cb26d354dd69d23849c34c4fba88da9">glm::aligned_highp_vec4</a></div><div class="ttdeci">vec&lt; 4, float, aligned_highp &gt; aligned_highp_vec4</div><div class="ttdoc">4 components vector aligned in memory of single-precision floating-point numbers using high precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00321">gtc/type_aligned.hpp:321</a></div></div>
<div class="ttc" id="a00303_html_ga2df58e03e5afded28707b4f7d077afb4"><div class="ttname"><a href="a00303.html#ga2df58e03e5afded28707b4f7d077afb4">glm::packed_highp_dmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, double, packed_highp &gt; packed_highp_dmat4x4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00858">gtc/type_aligned.hpp:858</a></div></div>
<div class="ttc" id="a00303_html_ga7180b685c581adb224406a7f831608e3"><div class="ttname"><a href="a00303.html#ga7180b685c581adb224406a7f831608e3">glm::aligned_mediump_dvec1</a></div><div class="ttdeci">vec&lt; 1, double, aligned_mediump &gt; aligned_mediump_dvec1</div><div class="ttdoc">1 component vector aligned in memory of double-precision floating-point numbers using medium precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00057">gtc/type_aligned.hpp:57</a></div></div>
<div class="ttc" id="a00303_html_ga66073b1ddef34b681741f572338ddb8e"><div class="ttname"><a href="a00303.html#ga66073b1ddef34b681741f572338ddb8e">glm::aligned_highp_dmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, aligned_highp &gt; aligned_highp_dmat3x3</div><div class="ttdoc">3 by 3 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00688">gtc/type_aligned.hpp:688</a></div></div>
<div class="ttc" id="a00303_html_ga0581ea552d86b2b5de7a2804bed80e72"><div class="ttname"><a href="a00303.html#ga0581ea552d86b2b5de7a2804bed80e72">glm::packed_dvec3</a></div><div class="ttdeci">packed_highp_dvec3 packed_dvec3</div><div class="ttdoc">3 components vector tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01126">gtc/type_aligned.hpp:1126</a></div></div>
<div class="ttc" id="a00303_html_ga8920e90ea9c01d9c97e604a938ce2cbd"><div class="ttname"><a href="a00303.html#ga8920e90ea9c01d9c97e604a938ce2cbd">glm::packed_mediump_dvec1</a></div><div class="ttdeci">vec&lt; 1, double, packed_mediump &gt; packed_mediump_dvec1</div><div class="ttdoc">1 component vector tightly packed in memory of double-precision floating-point numbers using medium p...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00102">gtc/type_aligned.hpp:102</a></div></div>
<div class="ttc" id="a00303_html_ga5621493caac01bdd22ab6be4416b0314"><div class="ttname"><a href="a00303.html#ga5621493caac01bdd22ab6be4416b0314">glm::packed_uvec1</a></div><div class="ttdeci">packed_highp_uvec1 packed_uvec1</div><div class="ttdoc">1 component vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01266">gtc/type_aligned.hpp:1266</a></div></div>
<div class="ttc" id="a00303_html_ga92247ca09fa03c4013ba364f3a0fca7f"><div class="ttname"><a href="a00303.html#ga92247ca09fa03c4013ba364f3a0fca7f">glm::packed_lowp_mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, float, packed_lowp &gt; packed_lowp_mat3x4</div><div class="ttdoc">3 by 4 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00741">gtc/type_aligned.hpp:741</a></div></div>
<div class="ttc" id="a00303_html_gad09b93acc43c43423408d17a64f6d7ca"><div class="ttname"><a href="a00303.html#gad09b93acc43c43423408d17a64f6d7ca">glm::aligned_lowp_uvec1</a></div><div class="ttdeci">vec&lt; 1, uint, aligned_lowp &gt; aligned_lowp_uvec1</div><div class="ttdoc">1 component vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00078">gtc/type_aligned.hpp:78</a></div></div>
<div class="ttc" id="a00303_html_gac870c47d2d9d48503f6c9ee3baec8ce1"><div class="ttname"><a href="a00303.html#gac870c47d2d9d48503f6c9ee3baec8ce1">glm::packed_lowp_dmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, double, packed_lowp &gt; packed_lowp_dmat2x4</div><div class="ttdoc">2 by 4 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00636">gtc/type_aligned.hpp:636</a></div></div>
<div class="ttc" id="a00303_html_ga32794322d294e5ace7fed4a61896f270"><div class="ttname"><a href="a00303.html#ga32794322d294e5ace7fed4a61896f270">glm::aligned_ivec3</a></div><div class="ttdeci">aligned_highp_ivec3 aligned_ivec3</div><div class="ttdoc">3 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01222">gtc/type_aligned.hpp:1222</a></div></div>
<div class="ttc" id="a00303_html_ga2c67b3b0adab71c8680c3d819f1fa9b7"><div class="ttname"><a href="a00303.html#ga2c67b3b0adab71c8680c3d819f1fa9b7">glm::packed_highp_dmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, double, packed_highp &gt; packed_highp_dmat3x4</div><div class="ttdoc">3 by 4 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00744">gtc/type_aligned.hpp:744</a></div></div>
<div class="ttc" id="a00303_html_ga3254defa5a8f0ae4b02b45fedba84a66"><div class="ttname"><a href="a00303.html#ga3254defa5a8f0ae4b02b45fedba84a66">glm::packed_vec2</a></div><div class="ttdeci">packed_highp_vec2 packed_vec2</div><div class="ttdoc">2 components vector tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00955">gtc/type_aligned.hpp:955</a></div></div>
<div class="ttc" id="a00303_html_ga8c32b53f628a3616aa5061e58d66fe74"><div class="ttname"><a href="a00303.html#ga8c32b53f628a3616aa5061e58d66fe74">glm::packed_highp_uvec1</a></div><div class="ttdeci">vec&lt; 1, uint, packed_highp &gt; packed_highp_uvec1</div><div class="ttdoc">1 component vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00117">gtc/type_aligned.hpp:117</a></div></div>
<div class="ttc" id="a00303_html_gac70667c7642ec8d50245e6e6936a3927"><div class="ttname"><a href="a00303.html#gac70667c7642ec8d50245e6e6936a3927">glm::packed_lowp_mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, float, packed_lowp &gt; packed_lowp_mat2x2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00551">gtc/type_aligned.hpp:551</a></div></div>
<div class="ttc" id="a00303_html_ga439e97795314b81cd15abd4e5c2e6e7a"><div class="ttname"><a href="a00303.html#ga439e97795314b81cd15abd4e5c2e6e7a">glm::packed_highp_bvec1</a></div><div class="ttdeci">vec&lt; 1, bool, packed_highp &gt; packed_highp_bvec1</div><div class="ttdoc">1 component vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00126">gtc/type_aligned.hpp:126</a></div></div>
<div class="ttc" id="a00303_html_gae164a1f7879f828bc35e50b79d786b05"><div class="ttname"><a href="a00303.html#gae164a1f7879f828bc35e50b79d786b05">glm::aligned_bvec4</a></div><div class="ttdeci">aligned_highp_bvec4 aligned_bvec4</div><div class="ttdoc">4 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01299">gtc/type_aligned.hpp:1299</a></div></div>
<div class="ttc" id="a00303_html_ga7cf643b66664e0cd3c48759ae66c2bd0"><div class="ttname"><a href="a00303.html#ga7cf643b66664e0cd3c48759ae66c2bd0">glm::aligned_vec3</a></div><div class="ttdeci">aligned_highp_vec3 aligned_vec3</div><div class="ttdoc">3 components vector aligned in memory of single-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00946">gtc/type_aligned.hpp:946</a></div></div>
<div class="ttc" id="a00303_html_ga4a7c7d8c3a663d0ec2a858cbfa14e54c"><div class="ttname"><a href="a00303.html#ga4a7c7d8c3a663d0ec2a858cbfa14e54c">glm::packed_lowp_dmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, packed_lowp &gt; packed_lowp_dmat3x3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00712">gtc/type_aligned.hpp:712</a></div></div>
<div class="ttc" id="a00303_html_ga2a8d9c3046f89d854eb758adfa0811c0"><div class="ttname"><a href="a00303.html#ga2a8d9c3046f89d854eb758adfa0811c0">glm::aligned_uvec3</a></div><div class="ttdeci">aligned_highp_uvec3 aligned_uvec3</div><div class="ttdoc">3 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01260">gtc/type_aligned.hpp:1260</a></div></div>
<div class="ttc" id="a00303_html_gabc25e547f4de4af62403492532cd1b6d"><div class="ttname"><a href="a00303.html#gabc25e547f4de4af62403492532cd1b6d">glm::aligned_mediump_dmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, double, aligned_mediump &gt; aligned_mediump_dmat4x2</div><div class="ttdoc">4 by 2 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00767">gtc/type_aligned.hpp:767</a></div></div>
<div class="ttc" id="a00303_html_gabab3afcc04459c7b123604ae5dc663f6"><div class="ttname"><a href="a00303.html#gabab3afcc04459c7b123604ae5dc663f6">glm::aligned_highp_mat3</a></div><div class="ttdeci">mat&lt; 3, 3, float, aligned_highp &gt; aligned_highp_mat3</div><div class="ttdoc">3 by 3 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00451">gtc/type_aligned.hpp:451</a></div></div>
<div class="ttc" id="a00303_html_gaa1f95690a78dc12e39da32943243aeef"><div class="ttname"><a href="a00303.html#gaa1f95690a78dc12e39da32943243aeef">glm::packed_mediump_uvec2</a></div><div class="ttdeci">vec&lt; 2, uint, packed_mediump &gt; packed_mediump_uvec2</div><div class="ttdoc">2 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00212">gtc/type_aligned.hpp:212</a></div></div>
<div class="ttc" id="a00303_html_ga65415d2d68c9cc0ca554524a8f5510b2"><div class="ttname"><a href="a00303.html#ga65415d2d68c9cc0ca554524a8f5510b2">glm::aligned_highp_vec3</a></div><div class="ttdeci">vec&lt; 3, float, aligned_highp &gt; aligned_highp_vec3</div><div class="ttdoc">3 components vector aligned in memory of single-precision floating-point numbers using high precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00229">gtc/type_aligned.hpp:229</a></div></div>
<div class="ttc" id="a00303_html_ga452bbbfd26e244de216e4d004d50bb74"><div class="ttname"><a href="a00303.html#ga452bbbfd26e244de216e4d004d50bb74">glm::aligned_mat4x3</a></div><div class="ttdeci">aligned_highp_mat4x3 aligned_mat4x3</div><div class="ttdoc">4 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01003">gtc/type_aligned.hpp:1003</a></div></div>
<div class="ttc" id="a00303_html_gaae92fcec8b2e0328ffbeac31cc4fc419"><div class="ttname"><a href="a00303.html#gaae92fcec8b2e0328ffbeac31cc4fc419">glm::aligned_lowp_ivec4</a></div><div class="ttdeci">vec&lt; 4, int, aligned_lowp &gt; aligned_lowp_ivec4</div><div class="ttdoc">4 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00345">gtc/type_aligned.hpp:345</a></div></div>
<div class="ttc" id="a00303_html_gaf9db5e8a929c317da5aa12cc53741b63"><div class="ttname"><a href="a00303.html#gaf9db5e8a929c317da5aa12cc53741b63">glm::aligned_highp_mat2</a></div><div class="ttdeci">mat&lt; 2, 2, float, aligned_highp &gt; aligned_highp_mat2</div><div class="ttdoc">2 by 2 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00413">gtc/type_aligned.hpp:413</a></div></div>
<div class="ttc" id="a00303_html_ga14741e3d9da9ae83765389927f837331"><div class="ttname"><a href="a00303.html#ga14741e3d9da9ae83765389927f837331">glm::packed_vec1</a></div><div class="ttdeci">packed_highp_vec1 packed_vec1</div><div class="ttdoc">1 component vector tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00952">gtc/type_aligned.hpp:952</a></div></div>
<div class="ttc" id="a00303_html_ga44c4accad582cfbd7226a19b83b0cadc"><div class="ttname"><a href="a00303.html#ga44c4accad582cfbd7226a19b83b0cadc">glm::aligned_lowp_ivec2</a></div><div class="ttdeci">vec&lt; 2, int, aligned_lowp &gt; aligned_lowp_ivec2</div><div class="ttdoc">2 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00161">gtc/type_aligned.hpp:161</a></div></div>
<div class="ttc" id="a00303_html_ga39658fb13369db869d363684bd8399c0"><div class="ttname"><a href="a00303.html#ga39658fb13369db869d363684bd8399c0">glm::packed_lowp_dmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, double, packed_lowp &gt; packed_lowp_dmat4x2</div><div class="ttdoc">4 by 2 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00788">gtc/type_aligned.hpp:788</a></div></div>
<div class="ttc" id="a00303_html_gaa37869eea77d28419b2fb0ff70b69bf0"><div class="ttname"><a href="a00303.html#gaa37869eea77d28419b2fb0ff70b69bf0">glm::aligned_dvec3</a></div><div class="ttdeci">aligned_highp_dvec3 aligned_dvec3</div><div class="ttdoc">3 components vector aligned in memory of double-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01114">gtc/type_aligned.hpp:1114</a></div></div>
<div class="ttc" id="a00303_html_ga0a6198fe64166a6a61084d43c71518a9"><div class="ttname"><a href="a00303.html#ga0a6198fe64166a6a61084d43c71518a9">glm::packed_lowp_vec1</a></div><div class="ttdeci">vec&lt; 1, float, packed_lowp &gt; packed_lowp_vec1</div><div class="ttdoc">1 component vector tightly packed in memory of single-precision floating-point numbers using low prec...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00096">gtc/type_aligned.hpp:96</a></div></div>
<div class="ttc" id="a00303_html_ga70147763edff3fe96b03a0b98d6339a2"><div class="ttname"><a href="a00303.html#ga70147763edff3fe96b03a0b98d6339a2">glm::packed_mediump_bvec3</a></div><div class="ttdeci">vec&lt; 3, bool, packed_mediump &gt; packed_mediump_bvec3</div><div class="ttdoc">3 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00313">gtc/type_aligned.hpp:313</a></div></div>
<div class="ttc" id="a00303_html_ga4974f46ae5a19415d91316960a53617a"><div class="ttname"><a href="a00303.html#ga4974f46ae5a19415d91316960a53617a">glm::aligned_dvec1</a></div><div class="ttdeci">aligned_highp_dvec1 aligned_dvec1</div><div class="ttdoc">1 component vector aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01108">gtc/type_aligned.hpp:1108</a></div></div>
<div class="ttc" id="a00303_html_ga8e5d92e84ebdbe2480cf96bc17d6e2f2"><div class="ttname"><a href="a00303.html#ga8e5d92e84ebdbe2480cf96bc17d6e2f2">glm::packed_uvec4</a></div><div class="ttdeci">packed_highp_uvec4 packed_uvec4</div><div class="ttdoc">4 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01275">gtc/type_aligned.hpp:1275</a></div></div>
<div class="ttc" id="a00303_html_ga03dfc90d539cc87ea3a15a9caa5d2245"><div class="ttname"><a href="a00303.html#ga03dfc90d539cc87ea3a15a9caa5d2245">glm::packed_dmat3</a></div><div class="ttdeci">packed_highp_dmat3 packed_dmat3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01144">gtc/type_aligned.hpp:1144</a></div></div>
<div class="ttc" id="a00303_html_gac9f8482dde741fb6bab7248b81a45465"><div class="ttname"><a href="a00303.html#gac9f8482dde741fb6bab7248b81a45465">glm::aligned_highp_vec2</a></div><div class="ttdeci">vec&lt; 2, float, aligned_highp &gt; aligned_highp_vec2</div><div class="ttdoc">2 components vector aligned in memory of single-precision floating-point numbers using high precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00137">gtc/type_aligned.hpp:137</a></div></div>
<div class="ttc" id="a00303_html_gaef26dfe3855a91644665b55c9096a8c8"><div class="ttname"><a href="a00303.html#gaef26dfe3855a91644665b55c9096a8c8">glm::aligned_highp_dmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, double, aligned_highp &gt; aligned_highp_dmat2x2</div><div class="ttdoc">2 by 2 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00536">gtc/type_aligned.hpp:536</a></div></div>
<div class="ttc" id="a00303_html_ga9e9f006970b1a00862e3e6e599eedd4c"><div class="ttname"><a href="a00303.html#ga9e9f006970b1a00862e3e6e599eedd4c">glm::aligned_lowp_uvec3</a></div><div class="ttdeci">vec&lt; 3, uint, aligned_lowp &gt; aligned_lowp_uvec3</div><div class="ttdoc">3 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00262">gtc/type_aligned.hpp:262</a></div></div>
<div class="ttc" id="a00303_html_ga7f8a2cc5a686e52b1615761f4978ca62"><div class="ttname"><a href="a00303.html#ga7f8a2cc5a686e52b1615761f4978ca62">glm::aligned_lowp_dvec1</a></div><div class="ttdeci">vec&lt; 1, double, aligned_lowp &gt; aligned_lowp_dvec1</div><div class="ttdoc">1 component vector aligned in memory of double-precision floating-point numbers using low precision a...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00060">gtc/type_aligned.hpp:60</a></div></div>
<div class="ttc" id="a00303_html_ga6b797eec76fa471e300158f3453b3b2e"><div class="ttname"><a href="a00303.html#ga6b797eec76fa471e300158f3453b3b2e">glm::aligned_mediump_vec1</a></div><div class="ttdeci">vec&lt; 1, float, aligned_mediump &gt; aligned_mediump_vec1</div><div class="ttdoc">1 component vector aligned in memory of single-precision floating-point numbers using medium precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00048">gtc/type_aligned.hpp:48</a></div></div>
<div class="ttc" id="a00303_html_ga30b0351eebc18c6056101359bdd3a359"><div class="ttname"><a href="a00303.html#ga30b0351eebc18c6056101359bdd3a359">glm::packed_lowp_dmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, double, packed_lowp &gt; packed_lowp_dmat4x3</div><div class="ttdoc">4 by 3 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00826">gtc/type_aligned.hpp:826</a></div></div>
<div class="ttc" id="a00303_html_gaea3ccc5ef5b178e6e49b4fa1427605d3"><div class="ttname"><a href="a00303.html#gaea3ccc5ef5b178e6e49b4fa1427605d3">glm::aligned_lowp_dmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, double, aligned_lowp &gt; aligned_lowp_dmat3x4</div><div class="ttdoc">3 by 4 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00732">gtc/type_aligned.hpp:732</a></div></div>
<div class="ttc" id="a00303_html_gae389c2071cf3cdb33e7812c6fd156710"><div class="ttname"><a href="a00303.html#gae389c2071cf3cdb33e7812c6fd156710">glm::packed_highp_mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, float, packed_highp &gt; packed_highp_mat4x2</div><div class="ttdoc">4 by 2 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00773">gtc/type_aligned.hpp:773</a></div></div>
<div class="ttc" id="a00303_html_ga80dee705d714300378e0847f45059097"><div class="ttname"><a href="a00303.html#ga80dee705d714300378e0847f45059097">glm::aligned_mediump_mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, float, aligned_mediump &gt; aligned_mediump_mat3x2</div><div class="ttdoc">3 by 2 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00644">gtc/type_aligned.hpp:644</a></div></div>
<div class="ttc" id="a00303_html_ga0effece4545acdebdc2a5512a303110e"><div class="ttname"><a href="a00303.html#ga0effece4545acdebdc2a5512a303110e">glm::aligned_mediump_mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, float, aligned_mediump &gt; aligned_mediump_mat4x3</div><div class="ttdoc">4 by 3 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00796">gtc/type_aligned.hpp:796</a></div></div>
<div class="ttc" id="a00303_html_gab9e669c4efd52d3347fc6d5f6b20fd59"><div class="ttname"><a href="a00303.html#gab9e669c4efd52d3347fc6d5f6b20fd59">glm::aligned_lowp_dvec3</a></div><div class="ttdeci">vec&lt; 3, double, aligned_lowp &gt; aligned_lowp_dvec3</div><div class="ttdoc">3 components vector aligned in memory of double-precision floating-point numbers using low precision ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00244">gtc/type_aligned.hpp:244</a></div></div>
<div class="ttc" id="a00303_html_ga43cd36d430c5187bfdca34a23cb41581"><div class="ttname"><a href="a00303.html#ga43cd36d430c5187bfdca34a23cb41581">glm::packed_mediump_mat2</a></div><div class="ttdeci">mat&lt; 2, 2, float, packed_mediump &gt; packed_mediump_mat2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00434">gtc/type_aligned.hpp:434</a></div></div>
<div class="ttc" id="a00303_html_ga6c1dbe8cde9fbb231284b01f8aeaaa99"><div class="ttname"><a href="a00303.html#ga6c1dbe8cde9fbb231284b01f8aeaaa99">glm::packed_mat3x3</a></div><div class="ttdeci">packed_highp_mat3x3 packed_mat3x3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01021">gtc/type_aligned.hpp:1021</a></div></div>
<div class="ttc" id="a00303_html_gab472b2d917b5e6efd76e8c7dbfbbf9f1"><div class="ttname"><a href="a00303.html#gab472b2d917b5e6efd76e8c7dbfbbf9f1">glm::packed_highp_dvec1</a></div><div class="ttdeci">vec&lt; 1, double, packed_highp &gt; packed_highp_dvec1</div><div class="ttdoc">1 component vector tightly packed in memory of double-precision floating-point numbers using high pre...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00099">gtc/type_aligned.hpp:99</a></div></div>
<div class="ttc" id="a00303_html_gaa281a47d5d627313984d0f8df993b648"><div class="ttname"><a href="a00303.html#gaa281a47d5d627313984d0f8df993b648">glm::aligned_lowp_dmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, double, aligned_lowp &gt; aligned_lowp_dmat3x2</div><div class="ttdoc">3 by 2 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00656">gtc/type_aligned.hpp:656</a></div></div>
<div class="ttc" id="a00303_html_ga13a75c6cbd0a411f694bc82486cd1e55"><div class="ttname"><a href="a00303.html#ga13a75c6cbd0a411f694bc82486cd1e55">glm::packed_mediump_mat3</a></div><div class="ttdeci">mat&lt; 3, 3, float, packed_mediump &gt; packed_mediump_mat3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00472">gtc/type_aligned.hpp:472</a></div></div>
<div class="ttc" id="a00303_html_ga17c424412207b00dba1cf587b099eea3"><div class="ttname"><a href="a00303.html#ga17c424412207b00dba1cf587b099eea3">glm::aligned_lowp_mat2</a></div><div class="ttdeci">mat&lt; 2, 2, float, aligned_lowp &gt; aligned_lowp_mat2</div><div class="ttdoc">2 by 2 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00419">gtc/type_aligned.hpp:419</a></div></div>
<div class="ttc" id="a00303_html_gae20617435a6d042d7c38da2badd64a09"><div class="ttname"><a href="a00303.html#gae20617435a6d042d7c38da2badd64a09">glm::packed_dmat4x4</a></div><div class="ttdeci">packed_highp_dmat4x4 packed_dmat4x4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01201">gtc/type_aligned.hpp:1201</a></div></div>
<div class="ttc" id="a00303_html_ga7a4536b6e1f2ebb690f63816b5d7e48b"><div class="ttname"><a href="a00303.html#ga7a4536b6e1f2ebb690f63816b5d7e48b">glm::aligned_lowp_dmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, double, aligned_lowp &gt; aligned_lowp_dmat2x3</div><div class="ttdoc">2 by 3 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00580">gtc/type_aligned.hpp:580</a></div></div>
<div class="ttc" id="a00303_html_ga2a1dd2387725a335413d4c4fee8609c4"><div class="ttname"><a href="a00303.html#ga2a1dd2387725a335413d4c4fee8609c4">glm::packed_lowp_mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, packed_lowp &gt; packed_lowp_mat4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00513">gtc/type_aligned.hpp:513</a></div></div>
<div class="ttc" id="a00303_html_ga56473759d2702ee19ab7f91d0017fa70"><div class="ttname"><a href="a00303.html#ga56473759d2702ee19ab7f91d0017fa70">glm::packed_highp_vec1</a></div><div class="ttdeci">vec&lt; 1, float, packed_highp &gt; packed_highp_vec1</div><div class="ttdoc">1 component vector tightly packed in memory of single-precision floating-point numbers using high pre...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00090">gtc/type_aligned.hpp:90</a></div></div>
<div class="ttc" id="a00303_html_ga26a0b61e444a51a37b9737cf4d84291b"><div class="ttname"><a href="a00303.html#ga26a0b61e444a51a37b9737cf4d84291b">glm::aligned_mediump_mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, float, aligned_mediump &gt; aligned_mediump_mat2x3</div><div class="ttdoc">2 by 3 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00568">gtc/type_aligned.hpp:568</a></div></div>
<div class="ttc" id="a00303_html_ga245c12d2daf67feecaa2d3277c8f6661"><div class="ttname"><a href="a00303.html#ga245c12d2daf67feecaa2d3277c8f6661">glm::packed_highp_mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, float, packed_highp &gt; packed_highp_mat2x2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00545">gtc/type_aligned.hpp:545</a></div></div>
<div class="ttc" id="a00303_html_gacb183eb5e67ec0d0ea5a016cba962810"><div class="ttname"><a href="a00303.html#gacb183eb5e67ec0d0ea5a016cba962810">glm::aligned_mediump_bvec2</a></div><div class="ttdeci">vec&lt; 2, bool, aligned_mediump &gt; aligned_mediump_bvec2</div><div class="ttdoc">2 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00176">gtc/type_aligned.hpp:176</a></div></div>
<div class="ttc" id="a00303_html_gac93f9b1a35b9de4f456b9f2dfeaf1097"><div class="ttname"><a href="a00303.html#gac93f9b1a35b9de4f456b9f2dfeaf1097">glm::packed_lowp_dmat2</a></div><div class="ttdeci">mat&lt; 2, 2, double, packed_lowp &gt; packed_lowp_dmat2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00446">gtc/type_aligned.hpp:446</a></div></div>
<div class="ttc" id="a00303_html_ga62a2dfd668c91072b72c3109fc6cda28"><div class="ttname"><a href="a00303.html#ga62a2dfd668c91072b72c3109fc6cda28">glm::aligned_mediump_dmat2</a></div><div class="ttdeci">mat&lt; 2, 2, double, aligned_mediump &gt; aligned_mediump_dmat2</div><div class="ttdoc">2 by 2 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00425">gtc/type_aligned.hpp:425</a></div></div>
<div class="ttc" id="a00303_html_ga68c9bb24f387b312bae6a0a68e74d95e"><div class="ttname"><a href="a00303.html#ga68c9bb24f387b312bae6a0a68e74d95e">glm::packed_mediump_vec4</a></div><div class="ttdeci">vec&lt; 4, float, packed_mediump &gt; packed_mediump_vec4</div><div class="ttdoc">4 components vector tightly packed in memory of single-precision floating-point numbers using medium ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00369">gtc/type_aligned.hpp:369</a></div></div>
<div class="ttc" id="a00303_html_gaa409cfb737bd59b68dc683e9b03930cc"><div class="ttname"><a href="a00303.html#gaa409cfb737bd59b68dc683e9b03930cc">glm::aligned_dmat4x2</a></div><div class="ttdeci">aligned_highp_dmat4x2 aligned_dmat4x2</div><div class="ttdoc">4 by 2 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01168">gtc/type_aligned.hpp:1168</a></div></div>
<div class="ttc" id="a00303_html_ga0294d4c45151425c86a11deee7693c0e"><div class="ttname"><a href="a00303.html#ga0294d4c45151425c86a11deee7693c0e">glm::packed_lowp_dmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, double, packed_lowp &gt; packed_lowp_dmat4x4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00864">gtc/type_aligned.hpp:864</a></div></div>
<div class="ttc" id="a00303_html_ga22bd6382b16052e301edbfc031b9f37a"><div class="ttname"><a href="a00303.html#ga22bd6382b16052e301edbfc031b9f37a">glm::packed_highp_dmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, double, packed_highp &gt; packed_highp_dmat2x2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00554">gtc/type_aligned.hpp:554</a></div></div>
<div class="ttc" id="a00303_html_ga5adf1802c5375a9dfb1729691bedd94e"><div class="ttname"><a href="a00303.html#ga5adf1802c5375a9dfb1729691bedd94e">glm::packed_lowp_mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, float, packed_lowp &gt; packed_lowp_mat3x3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00703">gtc/type_aligned.hpp:703</a></div></div>
<div class="ttc" id="a00303_html_ga4584f64394bd7123b7a8534741e4916c"><div class="ttname"><a href="a00303.html#ga4584f64394bd7123b7a8534741e4916c">glm::packed_highp_mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, float, packed_highp &gt; packed_highp_mat4x3</div><div class="ttdoc">4 by 3 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00811">gtc/type_aligned.hpp:811</a></div></div>
<div class="ttc" id="a00303_html_ga3b76ba17ae5d53debeb6f7e55919a57c"><div class="ttname"><a href="a00303.html#ga3b76ba17ae5d53debeb6f7e55919a57c">glm::aligned_mediump_mat3</a></div><div class="ttdeci">mat&lt; 3, 3, float, aligned_mediump &gt; aligned_mediump_mat3</div><div class="ttdoc">3 by 3 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00454">gtc/type_aligned.hpp:454</a></div></div>
<div class="ttc" id="a00303_html_gacab7374b560745cb1d0a306a90353f58"><div class="ttname"><a href="a00303.html#gacab7374b560745cb1d0a306a90353f58">glm::aligned_highp_dmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, double, aligned_highp &gt; aligned_highp_dmat4x3</div><div class="ttdoc">4 by 3 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00802">gtc/type_aligned.hpp:802</a></div></div>
<div class="ttc" id="a00303_html_gac6036449ab1c4abf8efe1ea00fcdd1c9"><div class="ttname"><a href="a00303.html#gac6036449ab1c4abf8efe1ea00fcdd1c9">glm::aligned_lowp_bvec1</a></div><div class="ttdeci">vec&lt; 1, bool, aligned_lowp &gt; aligned_lowp_bvec1</div><div class="ttdoc">1 component vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00087">gtc/type_aligned.hpp:87</a></div></div>
<div class="ttc" id="a00303_html_ga5a8a5f8c47cd7d5502dd9932f83472b9"><div class="ttname"><a href="a00303.html#ga5a8a5f8c47cd7d5502dd9932f83472b9">glm::aligned_mat2</a></div><div class="ttdeci">aligned_highp_mat2 aligned_mat2</div><div class="ttdoc">2 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00964">gtc/type_aligned.hpp:964</a></div></div>
<div class="ttc" id="a00303_html_gaa292ebaa907afdecb2d5967fb4fb1247"><div class="ttname"><a href="a00303.html#gaa292ebaa907afdecb2d5967fb4fb1247">glm::aligned_mediump_dmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, double, aligned_mediump &gt; aligned_mediump_dmat4x4</div><div class="ttdoc">4 by 4 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00843">gtc/type_aligned.hpp:843</a></div></div>
<div class="ttc" id="a00303_html_gabbf0f15e9c3d9868e43241ad018f82bd"><div class="ttname"><a href="a00303.html#gabbf0f15e9c3d9868e43241ad018f82bd">glm::aligned_mediump_ivec3</a></div><div class="ttdeci">vec&lt; 3, int, aligned_mediump &gt; aligned_mediump_ivec3</div><div class="ttdoc">3 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00250">gtc/type_aligned.hpp:250</a></div></div>
<div class="ttc" id="a00303_html_gae1964ba70d15915e5b710926decbb3cb"><div class="ttname"><a href="a00303.html#gae1964ba70d15915e5b710926decbb3cb">glm::aligned_bvec3</a></div><div class="ttdeci">aligned_highp_bvec3 aligned_bvec3</div><div class="ttdoc">3 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01296">gtc/type_aligned.hpp:1296</a></div></div>
<div class="ttc" id="a00303_html_gabcc33efb4d5e83b8fe4706360e75b932"><div class="ttname"><a href="a00303.html#gabcc33efb4d5e83b8fe4706360e75b932">glm::packed_uvec2</a></div><div class="ttdeci">packed_highp_uvec2 packed_uvec2</div><div class="ttdoc">2 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01269">gtc/type_aligned.hpp:1269</a></div></div>
<div class="ttc" id="a00303_html_ga226f5ec7a953cea559c16fe3aff9924f"><div class="ttname"><a href="a00303.html#ga226f5ec7a953cea559c16fe3aff9924f">glm::aligned_lowp_dvec4</a></div><div class="ttdeci">vec&lt; 4, double, aligned_lowp &gt; aligned_lowp_dvec4</div><div class="ttdoc">4 components vector aligned in memory of double-precision floating-point numbers using low precision ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00336">gtc/type_aligned.hpp:336</a></div></div>
<div class="ttc" id="a00303_html_gac00e15efded8a57c9dec3aed0fb547e7"><div class="ttname"><a href="a00303.html#gac00e15efded8a57c9dec3aed0fb547e7">glm::aligned_lowp_dmat3</a></div><div class="ttdeci">mat&lt; 3, 3, double, aligned_lowp &gt; aligned_lowp_dmat3</div><div class="ttdoc">3 by 3 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00466">gtc/type_aligned.hpp:466</a></div></div>
<div class="ttc" id="a00303_html_gaf304f64c06743c1571401504d3f50259"><div class="ttname"><a href="a00303.html#gaf304f64c06743c1571401504d3f50259">glm::packed_mediump_mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, packed_mediump &gt; packed_mediump_mat4x4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00852">gtc/type_aligned.hpp:852</a></div></div>
<div class="ttc" id="a00303_html_gabdd60462042859f876c17c7346c732a5"><div class="ttname"><a href="a00303.html#gabdd60462042859f876c17c7346c732a5">glm::aligned_highp_uvec4</a></div><div class="ttdeci">vec&lt; 4, uint, aligned_highp &gt; aligned_highp_uvec4</div><div class="ttdoc">4 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00348">gtc/type_aligned.hpp:348</a></div></div>
<div class="ttc" id="a00303_html_ga79c2e9f82e67963c1ecad0ad6d0ec72e"><div class="ttname"><a href="a00303.html#ga79c2e9f82e67963c1ecad0ad6d0ec72e">glm::packed_highp_dmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, double, packed_highp &gt; packed_highp_dmat4x3</div><div class="ttdoc">4 by 3 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00820">gtc/type_aligned.hpp:820</a></div></div>
<div class="ttc" id="a00303_html_ga7661d759d6ad218e132e3d051e7b2c6c"><div class="ttname"><a href="a00303.html#ga7661d759d6ad218e132e3d051e7b2c6c">glm::packed_lowp_mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, float, packed_lowp &gt; packed_lowp_mat4x3</div><div class="ttdoc">4 by 3 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00817">gtc/type_aligned.hpp:817</a></div></div>
<div class="ttc" id="a00303_html_ga53ac5d252317f1fa43c2ef921857bf13"><div class="ttname"><a href="a00303.html#ga53ac5d252317f1fa43c2ef921857bf13">glm::aligned_lowp_vec2</a></div><div class="ttdeci">vec&lt; 2, float, aligned_lowp &gt; aligned_lowp_vec2</div><div class="ttdoc">2 components vector aligned in memory of single-precision floating-point numbers using low precision ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00143">gtc/type_aligned.hpp:143</a></div></div>
<div class="ttc" id="a00303_html_gaf22b77f1cf3e73b8b1dddfe7f959357c"><div class="ttname"><a href="a00303.html#gaf22b77f1cf3e73b8b1dddfe7f959357c">glm::packed_lowp_ivec1</a></div><div class="ttdeci">vec&lt; 1, int, packed_lowp &gt; packed_lowp_ivec1</div><div class="ttdoc">1 component vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00114">gtc/type_aligned.hpp:114</a></div></div>
<div class="ttc" id="a00303_html_ga83aab4d191053f169c93a3e364f2e118"><div class="ttname"><a href="a00303.html#ga83aab4d191053f169c93a3e364f2e118">glm::aligned_lowp_bvec3</a></div><div class="ttdeci">vec&lt; 3, bool, aligned_lowp &gt; aligned_lowp_bvec3</div><div class="ttdoc">3 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00271">gtc/type_aligned.hpp:271</a></div></div>
<div class="ttc" id="a00303_html_ga8a9376d82f0e946e25137eb55543e6ce"><div class="ttname"><a href="a00303.html#ga8a9376d82f0e946e25137eb55543e6ce">glm::aligned_mediump_dmat4</a></div><div class="ttdeci">mat&lt; 4, 4, double, aligned_mediump &gt; aligned_mediump_dmat4</div><div class="ttdoc">4 by 4 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00501">gtc/type_aligned.hpp:501</a></div></div>
<div class="ttc" id="a00303_html_gad43a240533f388ce0504b495d9df3d52"><div class="ttname"><a href="a00303.html#gad43a240533f388ce0504b495d9df3d52">glm::packed_mediump_mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, float, packed_mediump &gt; packed_mediump_mat2x4</div><div class="ttdoc">2 by 4 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00624">gtc/type_aligned.hpp:624</a></div></div>
<div class="ttc" id="a00303_html_ga6718822cd7af005a9b5bd6ee282f6ba6"><div class="ttname"><a href="a00303.html#ga6718822cd7af005a9b5bd6ee282f6ba6">glm::packed_highp_dmat4</a></div><div class="ttdeci">mat&lt; 4, 4, double, packed_highp &gt; packed_highp_dmat4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00516">gtc/type_aligned.hpp:516</a></div></div>
<div class="ttc" id="a00303_html_ga46f372a006345c252a41267657cc22c0"><div class="ttname"><a href="a00303.html#ga46f372a006345c252a41267657cc22c0">glm::aligned_mediump_mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, float, aligned_mediump &gt; aligned_mediump_mat4x2</div><div class="ttdoc">4 by 2 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00758">gtc/type_aligned.hpp:758</a></div></div>
<div class="ttc" id="a00303_html_gabc93a9560593bd32e099c908531305f5"><div class="ttname"><a href="a00303.html#gabc93a9560593bd32e099c908531305f5">glm::packed_mediump_mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, float, packed_mediump &gt; packed_mediump_mat3x4</div><div class="ttdoc">3 by 4 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00738">gtc/type_aligned.hpp:738</a></div></div>
<div class="ttc" id="a00303_html_ga0b570da473fec4619db5aa0dce5133b0"><div class="ttname"><a href="a00303.html#ga0b570da473fec4619db5aa0dce5133b0">glm::packed_highp_uvec3</a></div><div class="ttdeci">vec&lt; 3, uint, packed_highp &gt; packed_highp_uvec3</div><div class="ttdoc">3 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00301">gtc/type_aligned.hpp:301</a></div></div>
<div class="ttc" id="a00303_html_ga449a3ec2dde6b6bb4bb94c49a6aad388"><div class="ttname"><a href="a00303.html#ga449a3ec2dde6b6bb4bb94c49a6aad388">glm::aligned_dmat2x2</a></div><div class="ttdeci">aligned_highp_dmat2x2 aligned_dmat2x2</div><div class="ttdoc">2 by 2 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01150">gtc/type_aligned.hpp:1150</a></div></div>
<div class="ttc" id="a00303_html_ga9d60e32d3fcb51f817046cd881fdbf57"><div class="ttname"><a href="a00303.html#ga9d60e32d3fcb51f817046cd881fdbf57">glm::packed_mediump_dmat2</a></div><div class="ttdeci">mat&lt; 2, 2, double, packed_mediump &gt; packed_mediump_dmat2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00443">gtc/type_aligned.hpp:443</a></div></div>
<div class="ttc" id="a00303_html_ga72102fa6ac2445aa3bb203128ad52449"><div class="ttname"><a href="a00303.html#ga72102fa6ac2445aa3bb203128ad52449">glm::packed_highp_mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, float, packed_highp &gt; packed_highp_mat3x4</div><div class="ttdoc">3 by 4 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00735">gtc/type_aligned.hpp:735</a></div></div>
<div class="ttc" id="a00303_html_gad63515526cccfe88ffa8fe5ed64f95f8"><div class="ttname"><a href="a00303.html#gad63515526cccfe88ffa8fe5ed64f95f8">glm::packed_mat3x4</a></div><div class="ttdeci">packed_highp_mat3x4 packed_mat3x4</div><div class="ttdoc">3 by 4 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01024">gtc/type_aligned.hpp:1024</a></div></div>
<div class="ttc" id="a00303_html_gaaa4126969c765e7faa2ebf6951c22ffb"><div class="ttname"><a href="a00303.html#gaaa4126969c765e7faa2ebf6951c22ffb">glm::packed_mediump_dmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, double, packed_mediump &gt; packed_mediump_dmat2x4</div><div class="ttdoc">2 by 4 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00633">gtc/type_aligned.hpp:633</a></div></div>
<div class="ttc" id="a00303_html_ga2c29fb42bab9a4f9b66bc60b2e514a34"><div class="ttname"><a href="a00303.html#ga2c29fb42bab9a4f9b66bc60b2e514a34">glm::packed_mediump_uvec1</a></div><div class="ttdeci">vec&lt; 1, uint, packed_mediump &gt; packed_mediump_uvec1</div><div class="ttdoc">1 component vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00120">gtc/type_aligned.hpp:120</a></div></div>
<div class="ttc" id="a00303_html_gab92c6d7d58d43dfb8147e9aedfe8351b"><div class="ttname"><a href="a00303.html#gab92c6d7d58d43dfb8147e9aedfe8351b">glm::aligned_lowp_dmat4</a></div><div class="ttdeci">mat&lt; 4, 4, double, aligned_lowp &gt; aligned_lowp_dmat4</div><div class="ttdoc">4 by 4 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00504">gtc/type_aligned.hpp:504</a></div></div>
<div class="ttc" id="a00303_html_gab286466e19f7399c8d25089da9400d43"><div class="ttname"><a href="a00303.html#gab286466e19f7399c8d25089da9400d43">glm::packed_mat4x3</a></div><div class="ttdeci">packed_highp_mat4x3 packed_mat4x3</div><div class="ttdoc">4 by 3 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01030">gtc/type_aligned.hpp:1030</a></div></div>
<div class="ttc" id="a00303_html_ga931731b8ae3b54c7ecc221509dae96bc"><div class="ttname"><a href="a00303.html#ga931731b8ae3b54c7ecc221509dae96bc">glm::packed_lowp_ivec4</a></div><div class="ttdeci">vec&lt; 4, int, packed_lowp &gt; packed_lowp_ivec4</div><div class="ttdoc">4 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00390">gtc/type_aligned.hpp:390</a></div></div>
<div class="ttc" id="a00303_html_ga70130dc8ed9c966ec2a221ce586d45d8"><div class="ttname"><a href="a00303.html#ga70130dc8ed9c966ec2a221ce586d45d8">glm::packed_mediump_ivec4</a></div><div class="ttdeci">vec&lt; 4, int, packed_mediump &gt; packed_mediump_ivec4</div><div class="ttdoc">4 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00387">gtc/type_aligned.hpp:387</a></div></div>
<div class="ttc" id="a00303_html_ga9af1eabe22f569e70d9893be72eda0f5"><div class="ttname"><a href="a00303.html#ga9af1eabe22f569e70d9893be72eda0f5">glm::aligned_mediump_dvec2</a></div><div class="ttdeci">vec&lt; 2, double, aligned_mediump &gt; aligned_mediump_dvec2</div><div class="ttdoc">2 components vector aligned in memory of double-precision floating-point numbers using medium precisi...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00149">gtc/type_aligned.hpp:149</a></div></div>
<div class="ttc" id="a00303_html_ga1fe4c5f56b8087d773aa90dc88a257a7"><div class="ttname"><a href="a00303.html#ga1fe4c5f56b8087d773aa90dc88a257a7">glm::packed_ivec2</a></div><div class="ttdeci">packed_highp_ivec2 packed_ivec2</div><div class="ttdoc">2 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01231">gtc/type_aligned.hpp:1231</a></div></div>
<div class="ttc" id="a00303_html_ga76298aed82a439063c3d55980c84aa0b"><div class="ttname"><a href="a00303.html#ga76298aed82a439063c3d55980c84aa0b">glm::aligned_ivec1</a></div><div class="ttdeci">aligned_highp_ivec1 aligned_ivec1</div><div class="ttdoc">1 component vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01216">gtc/type_aligned.hpp:1216</a></div></div>
<div class="ttc" id="a00303_html_ga06f26d54dca30994eb1fdadb8e69f4a2"><div class="ttname"><a href="a00303.html#ga06f26d54dca30994eb1fdadb8e69f4a2">glm::packed_mediump_ivec3</a></div><div class="ttdeci">vec&lt; 3, int, packed_mediump &gt; packed_mediump_ivec3</div><div class="ttdoc">3 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00295">gtc/type_aligned.hpp:295</a></div></div>
<div class="ttc" id="a00303_html_ga576a3f8372197a56a79dee1c8280f485"><div class="ttname"><a href="a00303.html#ga576a3f8372197a56a79dee1c8280f485">glm::packed_lowp_uvec3</a></div><div class="ttdeci">vec&lt; 3, uint, packed_lowp &gt; packed_lowp_uvec3</div><div class="ttdoc">3 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00307">gtc/type_aligned.hpp:307</a></div></div>
<div class="ttc" id="a00303_html_ga6dc65249730698d3cc9ac5d7e1bc4d72"><div class="ttname"><a href="a00303.html#ga6dc65249730698d3cc9ac5d7e1bc4d72">glm::packed_dmat4x2</a></div><div class="ttdeci">packed_highp_dmat4x2 packed_dmat4x2</div><div class="ttdoc">4 by 2 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01195">gtc/type_aligned.hpp:1195</a></div></div>
<div class="ttc" id="a00303_html_ga91bc1f513bb9b0fd60281d57ded9a48c"><div class="ttname"><a href="a00303.html#ga91bc1f513bb9b0fd60281d57ded9a48c">glm::aligned_mediump_bvec4</a></div><div class="ttdeci">vec&lt; 4, bool, aligned_mediump &gt; aligned_mediump_bvec4</div><div class="ttdoc">4 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00360">gtc/type_aligned.hpp:360</a></div></div>
<div class="ttc" id="a00303_html_ga0731b593c5e33559954c80f8687e76c6"><div class="ttname"><a href="a00303.html#ga0731b593c5e33559954c80f8687e76c6">glm::aligned_highp_bvec2</a></div><div class="ttdeci">vec&lt; 2, bool, aligned_highp &gt; aligned_highp_bvec2</div><div class="ttdoc">2 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00173">gtc/type_aligned.hpp:173</a></div></div>
<div class="ttc" id="a00303_html_ga3cc94fb8de80bbd8a4aa7a5b206d304a"><div class="ttname"><a href="a00303.html#ga3cc94fb8de80bbd8a4aa7a5b206d304a">glm::packed_lowp_vec4</a></div><div class="ttdeci">vec&lt; 4, float, packed_lowp &gt; packed_lowp_vec4</div><div class="ttdoc">4 components vector tightly packed in memory of single-precision floating-point numbers using low pre...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00372">gtc/type_aligned.hpp:372</a></div></div>
<div class="ttc" id="a00303_html_ga8fad35c5677f228e261fe541f15363a4"><div class="ttname"><a href="a00303.html#ga8fad35c5677f228e261fe541f15363a4">glm::aligned_highp_dvec4</a></div><div class="ttdeci">vec&lt; 4, double, aligned_highp &gt; aligned_highp_dvec4</div><div class="ttdoc">4 components vector aligned in memory of double-precision floating-point numbers using high precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00330">gtc/type_aligned.hpp:330</a></div></div>
<div class="ttc" id="a00303_html_ga999d82719696d4c59f4d236dd08f273d"><div class="ttname"><a href="a00303.html#ga999d82719696d4c59f4d236dd08f273d">glm::packed_highp_dmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, double, packed_highp &gt; packed_highp_dmat2x3</div><div class="ttdoc">2 by 3 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00592">gtc/type_aligned.hpp:592</a></div></div>
<div class="ttc" id="a00303_html_gaf6f041b212c57664d88bc6aefb7e36f3"><div class="ttname"><a href="a00303.html#gaf6f041b212c57664d88bc6aefb7e36f3">glm::aligned_mediump_mat2</a></div><div class="ttdeci">mat&lt; 2, 2, float, aligned_mediump &gt; aligned_mediump_mat2</div><div class="ttdoc">2 by 2 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00416">gtc/type_aligned.hpp:416</a></div></div>
<div class="ttc" id="a00303_html_ga92f8873e3bbd5ca1323c8bbe5725cc5e"><div class="ttname"><a href="a00303.html#ga92f8873e3bbd5ca1323c8bbe5725cc5e">glm::aligned_mediump_dmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, double, aligned_mediump &gt; aligned_mediump_dmat2x4</div><div class="ttdoc">2 by 4 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00615">gtc/type_aligned.hpp:615</a></div></div>
<div class="ttc" id="a00303_html_gae473587cff3092edc0877fc691c26a0b"><div class="ttname"><a href="a00303.html#gae473587cff3092edc0877fc691c26a0b">glm::packed_lowp_bvec4</a></div><div class="ttdeci">vec&lt; 4, bool, packed_lowp &gt; packed_lowp_bvec4</div><div class="ttdoc">4 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00408">gtc/type_aligned.hpp:408</a></div></div>
<div class="ttc" id="a00303_html_ga0c754a783b6fcf80374c013371c4dae9"><div class="ttname"><a href="a00303.html#ga0c754a783b6fcf80374c013371c4dae9">glm::packed_mediump_dvec2</a></div><div class="ttdeci">vec&lt; 2, double, packed_mediump &gt; packed_mediump_dvec2</div><div class="ttdoc">2 components vector tightly packed in memory of double-precision floating-point numbers using medium ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00194">gtc/type_aligned.hpp:194</a></div></div>
<div class="ttc" id="a00303_html_gab173a333e6b7ce153ceba66ac4a321cf"><div class="ttname"><a href="a00303.html#gab173a333e6b7ce153ceba66ac4a321cf">glm::aligned_highp_dvec2</a></div><div class="ttdeci">vec&lt; 2, double, aligned_highp &gt; aligned_highp_dvec2</div><div class="ttdoc">2 components vector aligned in memory of double-precision floating-point numbers using high precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00146">gtc/type_aligned.hpp:146</a></div></div>
<div class="ttc" id="a00303_html_gab0cf4f7c9a264941519acad286e055ea"><div class="ttname"><a href="a00303.html#gab0cf4f7c9a264941519acad286e055ea">glm::aligned_lowp_dmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, double, aligned_lowp &gt; aligned_lowp_dmat2x4</div><div class="ttdoc">2 by 4 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00618">gtc/type_aligned.hpp:618</a></div></div>
<div class="ttc" id="a00303_html_ga832476bb1c59ef673db37433ff34e399"><div class="ttname"><a href="a00303.html#ga832476bb1c59ef673db37433ff34e399">glm::aligned_mat2x3</a></div><div class="ttdeci">aligned_highp_mat2x3 aligned_mat2x3</div><div class="ttdoc">2 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00985">gtc/type_aligned.hpp:985</a></div></div>
<div class="ttc" id="a00303_html_ga70dcb9ef0b24e832772a7405efa9669a"><div class="ttname"><a href="a00303.html#ga70dcb9ef0b24e832772a7405efa9669a">glm::packed_lowp_mat2</a></div><div class="ttdeci">mat&lt; 2, 2, float, packed_lowp &gt; packed_lowp_mat2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00437">gtc/type_aligned.hpp:437</a></div></div>
<div class="ttc" id="a00303_html_ga6099dd7878d0a78101a4250d8cd2d736"><div class="ttname"><a href="a00303.html#ga6099dd7878d0a78101a4250d8cd2d736">glm::aligned_mediump_ivec4</a></div><div class="ttdeci">vec&lt; 4, int, aligned_mediump &gt; aligned_mediump_ivec4</div><div class="ttdoc">4 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00342">gtc/type_aligned.hpp:342</a></div></div>
<div class="ttc" id="a00303_html_gac969befedbda69eb78d4e23f751fdbee"><div class="ttname"><a href="a00303.html#gac969befedbda69eb78d4e23f751fdbee">glm::packed_lowp_bvec2</a></div><div class="ttdeci">vec&lt; 2, bool, packed_lowp &gt; packed_lowp_bvec2</div><div class="ttdoc">2 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00224">gtc/type_aligned.hpp:224</a></div></div>
<div class="ttc" id="a00303_html_ga54f368ec6b514a5aa4f28d40e6f93ef7"><div class="ttname"><a href="a00303.html#ga54f368ec6b514a5aa4f28d40e6f93ef7">glm::packed_highp_ivec2</a></div><div class="ttdeci">vec&lt; 2, int, packed_highp &gt; packed_highp_ivec2</div><div class="ttdoc">2 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00200">gtc/type_aligned.hpp:200</a></div></div>
<div class="ttc" id="a00303_html_ga199131fd279c92c2ac12df6d978f1dd6"><div class="ttname"><a href="a00303.html#ga199131fd279c92c2ac12df6d978f1dd6">glm::packed_dmat3x4</a></div><div class="ttdeci">packed_highp_dmat3x4 packed_dmat3x4</div><div class="ttdoc">3 by 4 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01192">gtc/type_aligned.hpp:1192</a></div></div>
<div class="ttc" id="a00303_html_gaf969eb879c76a5f4576e4a1e10095cf6"><div class="ttname"><a href="a00303.html#gaf969eb879c76a5f4576e4a1e10095cf6">glm::packed_mediump_dmat3</a></div><div class="ttdeci">mat&lt; 3, 3, double, packed_mediump &gt; packed_mediump_dmat3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00481">gtc/type_aligned.hpp:481</a></div></div>
<div class="ttc" id="a00303_html_gab0931ac7807fa1428c7bbf249efcdf0d"><div class="ttname"><a href="a00303.html#gab0931ac7807fa1428c7bbf249efcdf0d">glm::aligned_lowp_dmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, double, aligned_lowp &gt; aligned_lowp_dmat4x3</div><div class="ttdoc">4 by 3 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00808">gtc/type_aligned.hpp:808</a></div></div>
<div class="ttc" id="a00303_html_ga1fbfba14368b742972d3b58a0a303682"><div class="ttname"><a href="a00303.html#ga1fbfba14368b742972d3b58a0a303682">glm::aligned_highp_dmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, double, aligned_highp &gt; aligned_highp_dmat4x4</div><div class="ttdoc">4 by 4 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00840">gtc/type_aligned.hpp:840</a></div></div>
<div class="ttc" id="a00303_html_ga9a15514a0631f700de6312b9d5db3a73"><div class="ttname"><a href="a00303.html#ga9a15514a0631f700de6312b9d5db3a73">glm::packed_mediump_dmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, double, packed_mediump &gt; packed_mediump_dmat4x2</div><div class="ttdoc">4 by 2 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00785">gtc/type_aligned.hpp:785</a></div></div>
<div class="ttc" id="a00303_html_ga67e7102557d6067bb6ac00d4ad0e1374"><div class="ttname"><a href="a00303.html#ga67e7102557d6067bb6ac00d4ad0e1374">glm::packed_mat4x4</a></div><div class="ttdeci">packed_highp_mat4x4 packed_mat4x4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01033">gtc/type_aligned.hpp:1033</a></div></div>
<div class="ttc" id="a00303_html_ga253e8379b08d2dc6fe2800b2fb913203"><div class="ttname"><a href="a00303.html#ga253e8379b08d2dc6fe2800b2fb913203">glm::packed_highp_mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, packed_highp &gt; packed_highp_mat4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00507">gtc/type_aligned.hpp:507</a></div></div>
<div class="ttc" id="a00303_html_gacaa7407ea00ffdd322ce86a57adb547e"><div class="ttname"><a href="a00303.html#gacaa7407ea00ffdd322ce86a57adb547e">glm::aligned_highp_dmat4</a></div><div class="ttdeci">mat&lt; 4, 4, double, aligned_highp &gt; aligned_highp_dmat4</div><div class="ttdoc">4 by 4 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00498">gtc/type_aligned.hpp:498</a></div></div>
<div class="ttc" id="a00303_html_gab069b2fc2ec785fc4e193cf26c022679"><div class="ttname"><a href="a00303.html#gab069b2fc2ec785fc4e193cf26c022679">glm::aligned_highp_dmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, double, aligned_highp &gt; aligned_highp_dmat3x2</div><div class="ttdoc">3 by 2 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00650">gtc/type_aligned.hpp:650</a></div></div>
<div class="ttc" id="a00303_html_ga054050e9d4e78d81db0e6d1573b1c624"><div class="ttname"><a href="a00303.html#ga054050e9d4e78d81db0e6d1573b1c624">glm::packed_lowp_dvec1</a></div><div class="ttdeci">vec&lt; 1, double, packed_lowp &gt; packed_lowp_dvec1</div><div class="ttdoc">1 component vector tightly packed in memory of double-precision floating-point numbers using low prec...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00105">gtc/type_aligned.hpp:105</a></div></div>
<div class="ttc" id="a00303_html_ga25ea2f684e36aa5e978b4f2f86593824"><div class="ttname"><a href="a00303.html#ga25ea2f684e36aa5e978b4f2f86593824">glm::aligned_lowp_mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, aligned_lowp &gt; aligned_lowp_mat4</div><div class="ttdoc">4 by 4 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00495">gtc/type_aligned.hpp:495</a></div></div>
<div class="ttc" id="a00303_html_ga1ea2bbdbcb0a69242f6d884663c1b0ab"><div class="ttname"><a href="a00303.html#ga1ea2bbdbcb0a69242f6d884663c1b0ab">glm::packed_mediump_uvec3</a></div><div class="ttdeci">vec&lt; 3, uint, packed_mediump &gt; packed_mediump_uvec3</div><div class="ttdoc">3 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00304">gtc/type_aligned.hpp:304</a></div></div>
<div class="ttc" id="a00303_html_ga621e89ca1dbdcb7b5a3e7de237c44121"><div class="ttname"><a href="a00303.html#ga621e89ca1dbdcb7b5a3e7de237c44121">glm::aligned_dmat4x3</a></div><div class="ttdeci">aligned_highp_dmat4x3 aligned_dmat4x3</div><div class="ttdoc">4 by 3 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01171">gtc/type_aligned.hpp:1171</a></div></div>
<div class="ttc" id="a00303_html_gaf806dfdaffb2e9f7681b1cd2825898ce"><div class="ttname"><a href="a00303.html#gaf806dfdaffb2e9f7681b1cd2825898ce">glm::aligned_lowp_dmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, double, aligned_lowp &gt; aligned_lowp_dmat4x2</div><div class="ttdoc">4 by 2 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00770">gtc/type_aligned.hpp:770</a></div></div>
<div class="ttc" id="a00303_html_ga8897c6d9adb4140b1c3b0a07b8f0a430"><div class="ttname"><a href="a00303.html#ga8897c6d9adb4140b1c3b0a07b8f0a430">glm::packed_mediump_dmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, double, packed_mediump &gt; packed_mediump_dmat2x3</div><div class="ttdoc">2 by 3 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00595">gtc/type_aligned.hpp:595</a></div></div>
<div class="ttc" id="a00303_html_ga93a23ca3d42818d56e0702213c66354b"><div class="ttname"><a href="a00303.html#ga93a23ca3d42818d56e0702213c66354b">glm::aligned_highp_dmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, double, aligned_highp &gt; aligned_highp_dmat4x2</div><div class="ttdoc">4 by 2 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00764">gtc/type_aligned.hpp:764</a></div></div>
<div class="ttc" id="a00303_html_ga88061c72c997b94c420f2b0a60d9df26"><div class="ttname"><a href="a00303.html#ga88061c72c997b94c420f2b0a60d9df26">glm::aligned_mat3x4</a></div><div class="ttdeci">aligned_highp_mat3x4 aligned_mat3x4</div><div class="ttdoc">3 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00997">gtc/type_aligned.hpp:997</a></div></div>
<div class="ttc" id="a00303_html_gad8220a93d2fca2dd707821b4ab6f809e"><div class="ttname"><a href="a00303.html#gad8220a93d2fca2dd707821b4ab6f809e">glm::aligned_lowp_dmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, double, aligned_lowp &gt; aligned_lowp_dmat4x4</div><div class="ttdoc">4 by 4 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00846">gtc/type_aligned.hpp:846</a></div></div>
<div class="ttc" id="a00303_html_ga312864244cae4e8f10f478cffd0f76de"><div class="ttname"><a href="a00303.html#ga312864244cae4e8f10f478cffd0f76de">glm::aligned_mediump_mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, aligned_mediump &gt; aligned_mediump_mat4x4</div><div class="ttdoc">4 by 4 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00834">gtc/type_aligned.hpp:834</a></div></div>
<div class="ttc" id="a00303_html_gab0fddcf95dd51cbcbf624ea7c40dfeb8"><div class="ttname"><a href="a00303.html#gab0fddcf95dd51cbcbf624ea7c40dfeb8">glm::aligned_mat4</a></div><div class="ttdeci">aligned_highp_mat4 aligned_mat4</div><div class="ttdoc">4 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00970">gtc/type_aligned.hpp:970</a></div></div>
<div class="ttc" id="a00303_html_gaeeaff6c132ec91ebd21da3a2399548ea"><div class="ttname"><a href="a00303.html#gaeeaff6c132ec91ebd21da3a2399548ea">glm::packed_lowp_dmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, double, packed_lowp &gt; packed_lowp_dmat2x2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00560">gtc/type_aligned.hpp:560</a></div></div>
<div class="ttc" id="a00303_html_gaaa891048dddef4627df33809ec726219"><div class="ttname"><a href="a00303.html#gaaa891048dddef4627df33809ec726219">glm::packed_mediump_ivec2</a></div><div class="ttdeci">vec&lt; 2, int, packed_mediump &gt; packed_mediump_ivec2</div><div class="ttdoc">2 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00203">gtc/type_aligned.hpp:203</a></div></div>
<div class="ttc" id="a00303_html_gae36de20a4c0e0b1444b7903ae811d94e"><div class="ttname"><a href="a00303.html#gae36de20a4c0e0b1444b7903ae811d94e">glm::packed_dmat3x2</a></div><div class="ttdeci">packed_highp_dmat3x2 packed_dmat3x2</div><div class="ttdoc">3 by 2 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01186">gtc/type_aligned.hpp:1186</a></div></div>
<div class="ttc" id="a00303_html_ga4b0ee7996651ddd04eaa0c4cdbb66332"><div class="ttname"><a href="a00303.html#ga4b0ee7996651ddd04eaa0c4cdbb66332">glm::packed_mediump_dmat4</a></div><div class="ttdeci">mat&lt; 4, 4, double, packed_mediump &gt; packed_mediump_dmat4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00519">gtc/type_aligned.hpp:519</a></div></div>
<div class="ttc" id="a00303_html_ga98f0b5cd65fce164ff1367c2a3b3aa1e"><div class="ttname"><a href="a00303.html#ga98f0b5cd65fce164ff1367c2a3b3aa1e">glm::aligned_lowp_vec3</a></div><div class="ttdeci">vec&lt; 3, float, aligned_lowp &gt; aligned_lowp_vec3</div><div class="ttdoc">3 components vector aligned in memory of single-precision floating-point numbers using low precision ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00235">gtc/type_aligned.hpp:235</a></div></div>
<div class="ttc" id="a00303_html_ga6904d09b62141d09712b76983892f95b"><div class="ttname"><a href="a00303.html#ga6904d09b62141d09712b76983892f95b">glm::packed_highp_mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, float, packed_highp &gt; packed_highp_mat2x4</div><div class="ttdoc">2 by 4 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00621">gtc/type_aligned.hpp:621</a></div></div>
<div class="ttc" id="a00303_html_ga50c9af5aa3a848956d625fc64dc8488e"><div class="ttname"><a href="a00303.html#ga50c9af5aa3a848956d625fc64dc8488e">glm::aligned_highp_mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, float, aligned_highp &gt; aligned_highp_mat2x3</div><div class="ttdoc">2 by 3 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00565">gtc/type_aligned.hpp:565</a></div></div>
<div class="ttc" id="a00303_html_gaaa9cea174d342dd9650e3436823cab23"><div class="ttname"><a href="a00303.html#gaaa9cea174d342dd9650e3436823cab23">glm::packed_mediump_mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, float, packed_mediump &gt; packed_mediump_mat3x3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00700">gtc/type_aligned.hpp:700</a></div></div>
<div class="ttc" id="a00303_html_ga4015f36bf5a5adb6ac5d45beed959867"><div class="ttname"><a href="a00303.html#ga4015f36bf5a5adb6ac5d45beed959867">glm::packed_highp_vec4</a></div><div class="ttdeci">vec&lt; 4, float, packed_highp &gt; packed_highp_vec4</div><div class="ttdoc">4 components vector tightly packed in memory of single-precision floating-point numbers using high pr...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00366">gtc/type_aligned.hpp:366</a></div></div>
<div class="ttc" id="a00303_html_ga1ff8ed402c93d280ff0597c1c5e7c548"><div class="ttname"><a href="a00303.html#ga1ff8ed402c93d280ff0597c1c5e7c548">glm::aligned_uvec1</a></div><div class="ttdeci">aligned_highp_uvec1 aligned_uvec1</div><div class="ttdoc">1 component vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01254">gtc/type_aligned.hpp:1254</a></div></div>
<div class="ttc" id="a00303_html_ga40d49648083a0498a12a4bb41ae6ece8"><div class="ttname"><a href="a00303.html#ga40d49648083a0498a12a4bb41ae6ece8">glm::aligned_highp_mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, aligned_highp &gt; aligned_highp_mat4x4</div><div class="ttdoc">4 by 4 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00831">gtc/type_aligned.hpp:831</a></div></div>
<div class="ttc" id="a00303_html_gaa458f9d9e0934bae3097e2a373b24707"><div class="ttname"><a href="a00303.html#gaa458f9d9e0934bae3097e2a373b24707">glm::packed_mediump_mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, float, packed_mediump &gt; packed_mediump_mat4x2</div><div class="ttdoc">4 by 2 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00776">gtc/type_aligned.hpp:776</a></div></div>
<div class="ttc" id="a00303_html_ga2d6639f0bd777bae1ee0eba71cd7bfdc"><div class="ttname"><a href="a00303.html#ga2d6639f0bd777bae1ee0eba71cd7bfdc">glm::aligned_lowp_mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, float, aligned_lowp &gt; aligned_lowp_mat3x2</div><div class="ttdoc">3 by 2 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00647">gtc/type_aligned.hpp:647</a></div></div>
<div class="ttc" id="a00303_html_ga0d22400969dd223465b2900fecfb4f53"><div class="ttname"><a href="a00303.html#ga0d22400969dd223465b2900fecfb4f53">glm::packed_lowp_mat3</a></div><div class="ttdeci">mat&lt; 3, 3, float, packed_lowp &gt; packed_lowp_mat3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00475">gtc/type_aligned.hpp:475</a></div></div>
<div class="ttc" id="a00303_html_ga09f517d88b996ef1b2f42fd54222b82d"><div class="ttname"><a href="a00303.html#ga09f517d88b996ef1b2f42fd54222b82d">glm::packed_highp_bvec4</a></div><div class="ttdeci">vec&lt; 4, bool, packed_highp &gt; packed_highp_bvec4</div><div class="ttdoc">4 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00402">gtc/type_aligned.hpp:402</a></div></div>
<div class="ttc" id="a00303_html_ga05e6d4c908965d04191c2070a8d0a65e"><div class="ttname"><a href="a00303.html#ga05e6d4c908965d04191c2070a8d0a65e">glm::aligned_vec1</a></div><div class="ttdeci">aligned_highp_vec1 aligned_vec1</div><div class="ttdoc">1 component vector aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00940">gtc/type_aligned.hpp:940</a></div></div>
<div class="ttc" id="a00303_html_gaccccd090e185450caa28b5b63ad4e8f0"><div class="ttname"><a href="a00303.html#gaccccd090e185450caa28b5b63ad4e8f0">glm::packed_vec3</a></div><div class="ttdeci">packed_highp_vec3 packed_vec3</div><div class="ttdoc">3 components vector tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00958">gtc/type_aligned.hpp:958</a></div></div>
<div class="ttc" id="a00303_html_ga301b76a89b8a9625501ca58815017f20"><div class="ttname"><a href="a00303.html#ga301b76a89b8a9625501ca58815017f20">glm::packed_mat2x3</a></div><div class="ttdeci">packed_highp_mat2x3 packed_mat2x3</div><div class="ttdoc">2 by 3 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01012">gtc/type_aligned.hpp:1012</a></div></div>
<div class="ttc" id="a00303_html_gacfa4a542f1b20a5b63ad702dfb6fd587"><div class="ttname"><a href="a00303.html#gacfa4a542f1b20a5b63ad702dfb6fd587">glm::aligned_mediump_bvec3</a></div><div class="ttdeci">vec&lt; 3, bool, aligned_mediump &gt; aligned_mediump_bvec3</div><div class="ttdoc">3 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00268">gtc/type_aligned.hpp:268</a></div></div>
<div class="ttc" id="a00303_html_gacb78126ea2eb779b41c7511128ff1283"><div class="ttname"><a href="a00303.html#gacb78126ea2eb779b41c7511128ff1283">glm::aligned_mediump_uvec1</a></div><div class="ttdeci">vec&lt; 1, uint, aligned_mediump &gt; aligned_mediump_uvec1</div><div class="ttdoc">1 component vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00075">gtc/type_aligned.hpp:75</a></div></div>
<div class="ttc" id="a00303_html_gae766b317c5afec852bfb3d74a3c54bc8"><div class="ttname"><a href="a00303.html#gae766b317c5afec852bfb3d74a3c54bc8">glm::aligned_bvec2</a></div><div class="ttdeci">aligned_highp_bvec2 aligned_bvec2</div><div class="ttdoc">2 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01293">gtc/type_aligned.hpp:1293</a></div></div>
<div class="ttc" id="a00303_html_gaaa33d8e06657a777efb0c72c44ce87a9"><div class="ttname"><a href="a00303.html#gaaa33d8e06657a777efb0c72c44ce87a9">glm::packed_dmat2x2</a></div><div class="ttdeci">packed_highp_dmat2x2 packed_dmat2x2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01177">gtc/type_aligned.hpp:1177</a></div></div>
<div class="ttc" id="a00303_html_ga8f22607dcd090cd280071ccc689f4079"><div class="ttname"><a href="a00303.html#ga8f22607dcd090cd280071ccc689f4079">glm::packed_lowp_mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, float, packed_lowp &gt; packed_lowp_mat4x2</div><div class="ttdoc">4 by 2 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00779">gtc/type_aligned.hpp:779</a></div></div>
<div class="ttc" id="a00303_html_gae398fc3156f51d3684b08f62c1a5a6d4"><div class="ttname"><a href="a00303.html#gae398fc3156f51d3684b08f62c1a5a6d4">glm::packed_dmat2x4</a></div><div class="ttdeci">packed_highp_dmat2x4 packed_dmat2x4</div><div class="ttdoc">2 by 4 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01183">gtc/type_aligned.hpp:1183</a></div></div>
<div class="ttc" id="a00303_html_ga073fd6e8b241afade6d8afbd676b2667"><div class="ttname"><a href="a00303.html#ga073fd6e8b241afade6d8afbd676b2667">glm::aligned_highp_uvec3</a></div><div class="ttdeci">vec&lt; 3, uint, aligned_highp &gt; aligned_highp_uvec3</div><div class="ttdoc">3 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00256">gtc/type_aligned.hpp:256</a></div></div>
<div class="ttc" id="a00303_html_gab4c6414a59539e66a242ad4cf4b476b4"><div class="ttname"><a href="a00303.html#gab4c6414a59539e66a242ad4cf4b476b4">glm::packed_mediump_bvec2</a></div><div class="ttdeci">vec&lt; 2, bool, packed_mediump &gt; packed_mediump_bvec2</div><div class="ttdoc">2 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00221">gtc/type_aligned.hpp:221</a></div></div>
<div class="ttc" id="a00303_html_ga780a35f764020f553a9601a3fcdcd059"><div class="ttname"><a href="a00303.html#ga780a35f764020f553a9601a3fcdcd059">glm::aligned_bvec1</a></div><div class="ttdeci">aligned_highp_bvec1 aligned_bvec1</div><div class="ttdoc">1 component vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01290">gtc/type_aligned.hpp:1290</a></div></div>
<div class="ttc" id="a00303_html_ga5c0df24ba85eafafc0eb0c90690510ed"><div class="ttname"><a href="a00303.html#ga5c0df24ba85eafafc0eb0c90690510ed">glm::aligned_mat3x2</a></div><div class="ttdeci">aligned_highp_mat3x2 aligned_mat3x2</div><div class="ttdoc">3 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00991">gtc/type_aligned.hpp:991</a></div></div>
<div class="ttc" id="a00303_html_ga1101d3a82b2e3f5f8828bd8f3adab3e1"><div class="ttname"><a href="a00303.html#ga1101d3a82b2e3f5f8828bd8f3adab3e1">glm::aligned_lowp_ivec1</a></div><div class="ttdeci">vec&lt; 1, int, aligned_lowp &gt; aligned_lowp_ivec1</div><div class="ttdoc">1 component vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00069">gtc/type_aligned.hpp:69</a></div></div>
<div class="ttc" id="a00303_html_ga649d0acf01054b17e679cf00e150e025"><div class="ttname"><a href="a00303.html#ga649d0acf01054b17e679cf00e150e025">glm::aligned_mediump_dmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, aligned_mediump &gt; aligned_mediump_dmat3x3</div><div class="ttdoc">3 by 3 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00691">gtc/type_aligned.hpp:691</a></div></div>
<div class="ttc" id="a00303_html_ga128cd52649621861635fab746df91735"><div class="ttname"><a href="a00303.html#ga128cd52649621861635fab746df91735">glm::packed_lowp_mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, float, packed_lowp &gt; packed_lowp_mat3x2</div><div class="ttdoc">3 by 2 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00665">gtc/type_aligned.hpp:665</a></div></div>
<div class="ttc" id="a00303_html_ga069cc8892aadae144c00f35297617d44"><div class="ttname"><a href="a00303.html#ga069cc8892aadae144c00f35297617d44">glm::packed_highp_mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, float, packed_highp &gt; packed_highp_mat2x3</div><div class="ttdoc">2 by 3 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00583">gtc/type_aligned.hpp:583</a></div></div>
<div class="ttc" id="a00303_html_ga776f18d1a6e7d399f05d386167dc60f5"><div class="ttname"><a href="a00303.html#ga776f18d1a6e7d399f05d386167dc60f5">glm::packed_lowp_mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, packed_lowp &gt; packed_lowp_mat4x4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00855">gtc/type_aligned.hpp:855</a></div></div>
<div class="ttc" id="a00303_html_gabf842c45eea186170c267a328e3f3b7d"><div class="ttname"><a href="a00303.html#gabf842c45eea186170c267a328e3f3b7d">glm::aligned_uvec4</a></div><div class="ttdeci">aligned_highp_uvec4 aligned_uvec4</div><div class="ttdoc">4 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01263">gtc/type_aligned.hpp:1263</a></div></div>
<div class="ttc" id="a00303_html_gab85245913eaa40ab82adabcae37086cb"><div class="ttname"><a href="a00303.html#gab85245913eaa40ab82adabcae37086cb">glm::packed_bvec2</a></div><div class="ttdeci">packed_highp_bvec2 packed_bvec2</div><div class="ttdoc">2 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01305">gtc/type_aligned.hpp:1305</a></div></div>
<div class="ttc" id="a00303_html_ga2f7b8c99ba6f2d07c73a195a8143c259"><div class="ttname"><a href="a00303.html#ga2f7b8c99ba6f2d07c73a195a8143c259">glm::aligned_highp_mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, float, aligned_highp &gt; aligned_highp_mat3x3</div><div class="ttdoc">3 by 3 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00679">gtc/type_aligned.hpp:679</a></div></div>
<div class="ttc" id="a00303_html_ga3180d7db84a74c402157df3bbc0ae3ed"><div class="ttname"><a href="a00303.html#ga3180d7db84a74c402157df3bbc0ae3ed">glm::packed_bvec4</a></div><div class="ttdeci">packed_highp_bvec4 packed_bvec4</div><div class="ttdoc">4 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01311">gtc/type_aligned.hpp:1311</a></div></div>
<div class="ttc" id="a00303_html_ga7f79eae5927c9033d84617e49f6f34e4"><div class="ttname"><a href="a00303.html#ga7f79eae5927c9033d84617e49f6f34e4">glm::aligned_ivec4</a></div><div class="ttdeci">aligned_highp_ivec4 aligned_ivec4</div><div class="ttdoc">4 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01225">gtc/type_aligned.hpp:1225</a></div></div>
<div class="ttc" id="a00303_html_gaf2e07527d678440bf0c20adbeb9177c5"><div class="ttname"><a href="a00303.html#gaf2e07527d678440bf0c20adbeb9177c5">glm::packed_highp_mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, float, packed_highp &gt; packed_highp_mat3x3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00697">gtc/type_aligned.hpp:697</a></div></div>
<div class="ttc" id="a00303_html_gad6f1b4e3a51c2c051814b60d5d1b8895"><div class="ttname"><a href="a00303.html#gad6f1b4e3a51c2c051814b60d5d1b8895">glm::packed_highp_ivec4</a></div><div class="ttdeci">vec&lt; 4, int, packed_highp &gt; packed_highp_ivec4</div><div class="ttdoc">4 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00384">gtc/type_aligned.hpp:384</a></div></div>
<div class="ttc" id="a00303_html_ga134f0d99fbd2459c13cd9ebd056509fa"><div class="ttname"><a href="a00303.html#ga134f0d99fbd2459c13cd9ebd056509fa">glm::packed_mat3x2</a></div><div class="ttdeci">packed_highp_mat3x2 packed_mat3x2</div><div class="ttdoc">3 by 2 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01018">gtc/type_aligned.hpp:1018</a></div></div>
<div class="ttc" id="a00303_html_ga04db692662a4908beeaf5a5ba6e19483"><div class="ttname"><a href="a00303.html#ga04db692662a4908beeaf5a5ba6e19483">glm::aligned_highp_uvec2</a></div><div class="ttdeci">vec&lt; 2, uint, aligned_highp &gt; aligned_highp_uvec2</div><div class="ttdoc">2 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00164">gtc/type_aligned.hpp:164</a></div></div>
<div class="ttc" id="a00303_html_ga19aa695ffdb45ce29f7ea0b5029627de"><div class="ttname"><a href="a00303.html#ga19aa695ffdb45ce29f7ea0b5029627de">glm::aligned_dmat3</a></div><div class="ttdeci">aligned_highp_dmat3 aligned_dmat3</div><div class="ttdoc">3 by 3 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01135">gtc/type_aligned.hpp:1135</a></div></div>
<div class="ttc" id="a00303_html_ga6eca5170bb35eac90b4972590fd31a06"><div class="ttname"><a href="a00303.html#ga6eca5170bb35eac90b4972590fd31a06">glm::aligned_highp_ivec3</a></div><div class="ttdeci">vec&lt; 3, int, aligned_highp &gt; aligned_highp_ivec3</div><div class="ttdoc">3 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00247">gtc/type_aligned.hpp:247</a></div></div>
<div class="ttc" id="a00303_html_ga07cb8e846666cbf56045b064fb553d2e"><div class="ttname"><a href="a00303.html#ga07cb8e846666cbf56045b064fb553d2e">glm::aligned_lowp_dmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, double, aligned_lowp &gt; aligned_lowp_dmat2x2</div><div class="ttdoc">2 by 2 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00542">gtc/type_aligned.hpp:542</a></div></div>
<div class="ttc" id="a00303_html_ga9fc2167b47c9be9295f2d8eea7f0ca75"><div class="ttname"><a href="a00303.html#ga9fc2167b47c9be9295f2d8eea7f0ca75">glm::aligned_highp_mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, float, aligned_highp &gt; aligned_highp_mat3x2</div><div class="ttdoc">3 by 2 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00641">gtc/type_aligned.hpp:641</a></div></div>
<div class="ttc" id="a00303_html_ga5b80e28396c6ef7d32c6fd18df498451"><div class="ttname"><a href="a00303.html#ga5b80e28396c6ef7d32c6fd18df498451">glm::aligned_highp_uvec1</a></div><div class="ttdeci">vec&lt; 1, uint, aligned_highp &gt; aligned_highp_uvec1</div><div class="ttdoc">1 component vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00072">gtc/type_aligned.hpp:72</a></div></div>
<div class="ttc" id="a00303_html_gadab11a7504430825b648ff7c7e36b725"><div class="ttname"><a href="a00303.html#gadab11a7504430825b648ff7c7e36b725">glm::aligned_mat2x4</a></div><div class="ttdeci">aligned_highp_mat2x4 aligned_mat2x4</div><div class="ttdoc">2 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00988">gtc/type_aligned.hpp:988</a></div></div>
<div class="ttc" id="a00303_html_gaeefee8317192174596852ce19b602720"><div class="ttname"><a href="a00303.html#gaeefee8317192174596852ce19b602720">glm::aligned_mediump_mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, aligned_mediump &gt; aligned_mediump_mat4</div><div class="ttdoc">4 by 4 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00492">gtc/type_aligned.hpp:492</a></div></div>
<div class="ttc" id="a00303_html_ga532f0c940649b1ee303acd572fc35531"><div class="ttname"><a href="a00303.html#ga532f0c940649b1ee303acd572fc35531">glm::packed_dvec1</a></div><div class="ttdeci">packed_highp_dvec1 packed_dvec1</div><div class="ttdoc">1 component vector tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01120">gtc/type_aligned.hpp:1120</a></div></div>
<div class="ttc" id="a00303_html_ga5ccb2baeb0ab57b818c24e0d486c59d0"><div class="ttname"><a href="a00303.html#ga5ccb2baeb0ab57b818c24e0d486c59d0">glm::aligned_dmat2x4</a></div><div class="ttdeci">aligned_highp_dmat2x4 aligned_dmat2x4</div><div class="ttdoc">2 by 4 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01156">gtc/type_aligned.hpp:1156</a></div></div>
<div class="ttc" id="a00303_html_ga39e8bb9b70e5694964e8266a21ba534e"><div class="ttname"><a href="a00303.html#ga39e8bb9b70e5694964e8266a21ba534e">glm::packed_mediump_dmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, double, packed_mediump &gt; packed_mediump_dmat2x2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00557">gtc/type_aligned.hpp:557</a></div></div>
<div class="ttc" id="a00303_html_ga9189210cabd6651a5e14a4c46fb20598"><div class="ttname"><a href="a00303.html#ga9189210cabd6651a5e14a4c46fb20598">glm::packed_lowp_dvec3</a></div><div class="ttdeci">vec&lt; 3, double, packed_lowp &gt; packed_lowp_dvec3</div><div class="ttdoc">3 components vector tightly packed in memory of double-precision floating-point numbers using low pre...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00289">gtc/type_aligned.hpp:289</a></div></div>
<div class="ttc" id="a00303_html_ga46b1b0b9eb8625a5d69137bd66cd13dc"><div class="ttname"><a href="a00303.html#ga46b1b0b9eb8625a5d69137bd66cd13dc">glm::aligned_lowp_uvec4</a></div><div class="ttdeci">vec&lt; 4, uint, aligned_lowp &gt; aligned_lowp_uvec4</div><div class="ttdoc">4 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00354">gtc/type_aligned.hpp:354</a></div></div>
<div class="ttc" id="a00303_html_gaa582f38c82aef61dea7aaedf15bb06a6"><div class="ttname"><a href="a00303.html#gaa582f38c82aef61dea7aaedf15bb06a6">glm::packed_highp_uvec4</a></div><div class="ttdeci">vec&lt; 4, uint, packed_highp &gt; packed_highp_uvec4</div><div class="ttdoc">4 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00393">gtc/type_aligned.hpp:393</a></div></div>
<div class="ttc" id="a00303_html_gaea9c555e669dc56c45d95dcc75d59bf3"><div class="ttname"><a href="a00303.html#gaea9c555e669dc56c45d95dcc75d59bf3">glm::packed_lowp_mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, float, packed_lowp &gt; packed_lowp_mat2x4</div><div class="ttdoc">2 by 4 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00627">gtc/type_aligned.hpp:627</a></div></div>
<div class="ttc" id="a00303_html_ga0682462f8096a226773e20fac993cde5"><div class="ttname"><a href="a00303.html#ga0682462f8096a226773e20fac993cde5">glm::aligned_vec2</a></div><div class="ttdeci">aligned_highp_vec2 aligned_vec2</div><div class="ttdoc">2 components vector aligned in memory of single-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00943">gtc/type_aligned.hpp:943</a></div></div>
<div class="ttc" id="a00303_html_gabb04f459d81d753d278b2072e2375e8e"><div class="ttname"><a href="a00303.html#gabb04f459d81d753d278b2072e2375e8e">glm::aligned_mat2x2</a></div><div class="ttdeci">aligned_highp_mat2x2 aligned_mat2x2</div><div class="ttdoc">2 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00982">gtc/type_aligned.hpp:982</a></div></div>
<div class="ttc" id="a00303_html_ga3894a059eeaacec8791c25de398d9955"><div class="ttname"><a href="a00303.html#ga3894a059eeaacec8791c25de398d9955">glm::packed_lowp_dmat3</a></div><div class="ttdeci">mat&lt; 3, 3, double, packed_lowp &gt; packed_lowp_dmat3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00484">gtc/type_aligned.hpp:484</a></div></div>
<div class="ttc" id="a00303_html_ga635bf3732281a2c2ca54d8f9d33d178f"><div class="ttname"><a href="a00303.html#ga635bf3732281a2c2ca54d8f9d33d178f">glm::aligned_dmat3x3</a></div><div class="ttdeci">aligned_highp_dmat3x3 aligned_dmat3x3</div><div class="ttdoc">3 by 3 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01162">gtc/type_aligned.hpp:1162</a></div></div>
<div class="ttc" id="a00303_html_ga5b2dc48fa19b684d207d69c6b145eb63"><div class="ttname"><a href="a00303.html#ga5b2dc48fa19b684d207d69c6b145eb63">glm::packed_highp_dvec2</a></div><div class="ttdeci">vec&lt; 2, double, packed_highp &gt; packed_highp_dvec2</div><div class="ttdoc">2 components vector tightly packed in memory of double-precision floating-point numbers using high pr...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00191">gtc/type_aligned.hpp:191</a></div></div>
<div class="ttc" id="a00303_html_ga9b7feec247d378dd407ba81f56ea96c8"><div class="ttname"><a href="a00303.html#ga9b7feec247d378dd407ba81f56ea96c8">glm::aligned_mediump_dmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, double, aligned_mediump &gt; aligned_mediump_dmat2x2</div><div class="ttdoc">2 by 2 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00539">gtc/type_aligned.hpp:539</a></div></div>
<div class="ttc" id="a00303_html_gaf111fed760ecce16cb1988807569bee5"><div class="ttname"><a href="a00303.html#gaf111fed760ecce16cb1988807569bee5">glm::packed_lowp_uvec1</a></div><div class="ttdeci">vec&lt; 1, uint, packed_lowp &gt; packed_lowp_uvec1</div><div class="ttdoc">1 component vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00123">gtc/type_aligned.hpp:123</a></div></div>
<div class="ttc" id="a00303_html_ga958210fe245a75b058325d367c951132"><div class="ttname"><a href="a00303.html#ga958210fe245a75b058325d367c951132">glm::packed_lowp_uvec2</a></div><div class="ttdeci">vec&lt; 2, uint, packed_lowp &gt; packed_lowp_uvec2</div><div class="ttdoc">2 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00215">gtc/type_aligned.hpp:215</a></div></div>
<div class="ttc" id="a00303_html_gadf202aaa9ed71c09f9bbe347e43f8764"><div class="ttname"><a href="a00303.html#gadf202aaa9ed71c09f9bbe347e43f8764">glm::packed_dmat4x3</a></div><div class="ttdeci">packed_highp_dmat4x3 packed_dmat4x3</div><div class="ttdoc">4 by 3 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01198">gtc/type_aligned.hpp:1198</a></div></div>
<div class="ttc" id="a00303_html_ga7f3148a72355e39932d6855baca42ebc"><div class="ttname"><a href="a00303.html#ga7f3148a72355e39932d6855baca42ebc">glm::aligned_lowp_dmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, aligned_lowp &gt; aligned_lowp_dmat3x3</div><div class="ttdoc">3 by 3 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00694">gtc/type_aligned.hpp:694</a></div></div>
<div class="ttc" id="a00303_html_gabdd5fbffe8b8b8a7b33523f25b120dbe"><div class="ttname"><a href="a00303.html#gabdd5fbffe8b8b8a7b33523f25b120dbe">glm::packed_highp_mat3</a></div><div class="ttdeci">mat&lt; 3, 3, float, packed_highp &gt; packed_highp_mat3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00469">gtc/type_aligned.hpp:469</a></div></div>
<div class="ttc" id="a00303_html_ga53d519a7b1bfb69076b3ec206a6b3bd1"><div class="ttname"><a href="a00303.html#ga53d519a7b1bfb69076b3ec206a6b3bd1">glm::aligned_dmat2x3</a></div><div class="ttdeci">aligned_highp_dmat2x3 aligned_dmat2x3</div><div class="ttdoc">2 by 3 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01153">gtc/type_aligned.hpp:1153</a></div></div>
<div class="ttc" id="a00303_html_ga04bf49316ee777d42fcfe681ee37d7be"><div class="ttname"><a href="a00303.html#ga04bf49316ee777d42fcfe681ee37d7be">glm::aligned_mediump_mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, float, aligned_mediump &gt; aligned_mediump_mat2x2</div><div class="ttdoc">2 by 2 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00530">gtc/type_aligned.hpp:530</a></div></div>
<div class="ttc" id="a00303_html_ga0e44aeb930a47f9cbf2db15b56433b0f"><div class="ttname"><a href="a00303.html#ga0e44aeb930a47f9cbf2db15b56433b0f">glm::aligned_lowp_mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, float, aligned_lowp &gt; aligned_lowp_mat2x2</div><div class="ttdoc">2 by 2 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00533">gtc/type_aligned.hpp:533</a></div></div>
<div class="ttc" id="a00303_html_ga5546d828d63010a8f9cf81161ad0275a"><div class="ttname"><a href="a00303.html#ga5546d828d63010a8f9cf81161ad0275a">glm::packed_mediump_bvec1</a></div><div class="ttdeci">vec&lt; 1, bool, packed_mediump &gt; packed_mediump_bvec1</div><div class="ttdoc">1 component vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00129">gtc/type_aligned.hpp:129</a></div></div>
<div class="ttc" id="a00303_html_ga03e1edf5666c40affe39aee35c87956f"><div class="ttname"><a href="a00303.html#ga03e1edf5666c40affe39aee35c87956f">glm::packed_lowp_dmat4</a></div><div class="ttdeci">mat&lt; 4, 4, double, packed_lowp &gt; packed_lowp_dmat4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00522">gtc/type_aligned.hpp:522</a></div></div>
<div class="ttc" id="a00303_html_ga11581a06fc7bf941fa4d4b6aca29812c"><div class="ttname"><a href="a00303.html#ga11581a06fc7bf941fa4d4b6aca29812c">glm::packed_ivec1</a></div><div class="ttdeci">packed_highp_ivec1 packed_ivec1</div><div class="ttdoc">1 component vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01228">gtc/type_aligned.hpp:1228</a></div></div>
<div class="ttc" id="a00303_html_gae3c8750f53259ece334d3aa3b3649a40"><div class="ttname"><a href="a00303.html#gae3c8750f53259ece334d3aa3b3649a40">glm::packed_lowp_bvec1</a></div><div class="ttdeci">vec&lt; 1, bool, packed_lowp &gt; packed_lowp_bvec1</div><div class="ttdoc">1 component vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00132">gtc/type_aligned.hpp:132</a></div></div>
<div class="ttc" id="a00303_html_ga5f5123d834bd1170edf8c386834e112c"><div class="ttname"><a href="a00303.html#ga5f5123d834bd1170edf8c386834e112c">glm::aligned_dmat3x2</a></div><div class="ttdeci">aligned_highp_dmat3x2 aligned_dmat3x2</div><div class="ttdoc">3 by 2 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01159">gtc/type_aligned.hpp:1159</a></div></div>
<div class="ttc" id="a00303_html_gab462744977beb85fb5c782bc2eea7b15"><div class="ttname"><a href="a00303.html#gab462744977beb85fb5c782bc2eea7b15">glm::packed_highp_dmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, double, packed_highp &gt; packed_highp_dmat3x2</div><div class="ttdoc">3 by 2 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00668">gtc/type_aligned.hpp:668</a></div></div>
<div class="ttc" id="a00303_html_gae4f38fd2c86cee6940986197777b3ca4"><div class="ttname"><a href="a00303.html#gae4f38fd2c86cee6940986197777b3ca4">glm::aligned_ivec2</a></div><div class="ttdeci">aligned_highp_ivec2 aligned_ivec2</div><div class="ttdoc">2 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01219">gtc/type_aligned.hpp:1219</a></div></div>
<div class="ttc" id="a00303_html_gac9bda778d0b7ad82f656dab99b71857a"><div class="ttname"><a href="a00303.html#gac9bda778d0b7ad82f656dab99b71857a">glm::aligned_dmat4x4</a></div><div class="ttdeci">aligned_highp_dmat4x4 aligned_dmat4x4</div><div class="ttdoc">4 by 4 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01174">gtc/type_aligned.hpp:1174</a></div></div>
<div class="ttc" id="a00303_html_ga49e5a709d098523823b2f824e48672a6"><div class="ttname"><a href="a00303.html#ga49e5a709d098523823b2f824e48672a6">glm::packed_highp_dmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, double, packed_highp &gt; packed_highp_dmat3x3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00706">gtc/type_aligned.hpp:706</a></div></div>
<div class="ttc" id="a00303_html_ga9df1d0c425852cf63a57e533b7a83f4f"><div class="ttname"><a href="a00303.html#ga9df1d0c425852cf63a57e533b7a83f4f">glm::aligned_highp_bvec4</a></div><div class="ttdeci">vec&lt; 4, bool, aligned_highp &gt; aligned_highp_bvec4</div><div class="ttdoc">4 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00357">gtc/type_aligned.hpp:357</a></div></div>
<div class="ttc" id="a00303_html_ga7b1620f259595b9da47a6374fc44588a"><div class="ttname"><a href="a00303.html#ga7b1620f259595b9da47a6374fc44588a">glm::packed_mediump_bvec4</a></div><div class="ttdeci">vec&lt; 4, bool, packed_mediump &gt; packed_mediump_bvec4</div><div class="ttdoc">4 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00405">gtc/type_aligned.hpp:405</a></div></div>
<div class="ttc" id="a00303_html_ga2f2d913d8cca2f935b2522964408c0b2"><div class="ttname"><a href="a00303.html#ga2f2d913d8cca2f935b2522964408c0b2">glm::packed_highp_mat2</a></div><div class="ttdeci">mat&lt; 2, 2, float, packed_highp &gt; packed_highp_mat2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00431">gtc/type_aligned.hpp:431</a></div></div>
<div class="ttc" id="a00303_html_gac401da1dd9177ad81d7618a2a5541e23"><div class="ttname"><a href="a00303.html#gac401da1dd9177ad81d7618a2a5541e23">glm::packed_mat2x4</a></div><div class="ttdeci">packed_highp_mat2x4 packed_mat2x4</div><div class="ttdoc">2 by 4 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01015">gtc/type_aligned.hpp:1015</a></div></div>
<div class="ttc" id="a00303_html_ga2cb16c3fdfb15e0719d942ee3b548bc4"><div class="ttname"><a href="a00303.html#ga2cb16c3fdfb15e0719d942ee3b548bc4">glm::aligned_lowp_mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, float, aligned_lowp &gt; aligned_lowp_mat4x2</div><div class="ttdoc">4 by 2 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00761">gtc/type_aligned.hpp:761</a></div></div>
<div class="ttc" id="a00303_html_gadd3b8bd71a758f7fb0da8e525156f34e"><div class="ttname"><a href="a00303.html#gadd3b8bd71a758f7fb0da8e525156f34e">glm::aligned_mediump_bvec1</a></div><div class="ttdeci">vec&lt; 1, bool, aligned_mediump &gt; aligned_mediump_bvec1</div><div class="ttdoc">1 component vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00084">gtc/type_aligned.hpp:84</a></div></div>
<div class="ttc" id="a00303_html_gaa0d2b8a75f1908dcf32c27f8524bdced"><div class="ttname"><a href="a00303.html#gaa0d2b8a75f1908dcf32c27f8524bdced">glm::aligned_highp_dmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, double, aligned_highp &gt; aligned_highp_dmat2x4</div><div class="ttdoc">2 by 4 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00612">gtc/type_aligned.hpp:612</a></div></div>
<div class="ttc" id="a00303_html_ga721f5404caf40d68962dcc0529de71d9"><div class="ttname"><a href="a00303.html#ga721f5404caf40d68962dcc0529de71d9">glm::aligned_mediump_mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, float, aligned_mediump &gt; aligned_mediump_mat3x3</div><div class="ttdoc">3 by 3 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00682">gtc/type_aligned.hpp:682</a></div></div>
<div class="ttc" id="a00303_html_gae89d72ffc149147f61df701bbc8755bf"><div class="ttname"><a href="a00303.html#gae89d72ffc149147f61df701bbc8755bf">glm::packed_mediump_mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, packed_mediump &gt; packed_mediump_mat4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00510">gtc/type_aligned.hpp:510</a></div></div>
<div class="ttc" id="a00303_html_ga71d63cead1e113fca0bcdaaa33aad050"><div class="ttname"><a href="a00303.html#ga71d63cead1e113fca0bcdaaa33aad050">glm::packed_mediump_vec1</a></div><div class="ttdeci">vec&lt; 1, float, packed_mediump &gt; packed_mediump_vec1</div><div class="ttdoc">1 component vector tightly packed in memory of single-precision floating-point numbers using medium p...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00093">gtc/type_aligned.hpp:93</a></div></div>
<div class="ttc" id="a00303_html_ga8b8fb86973a0b768c5bd802c92fac1a1"><div class="ttname"><a href="a00303.html#ga8b8fb86973a0b768c5bd802c92fac1a1">glm::aligned_mat4x4</a></div><div class="ttdeci">aligned_highp_mat4x4 aligned_mat4x4</div><div class="ttdoc">4 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01006">gtc/type_aligned.hpp:1006</a></div></div>
<div class="ttc" id="a00303_html_gac9a2d0fb815fd5c2bd58b869c55e32d3"><div class="ttname"><a href="a00303.html#gac9a2d0fb815fd5c2bd58b869c55e32d3">glm::aligned_mat4x2</a></div><div class="ttdeci">aligned_highp_mat4x2 aligned_mat4x2</div><div class="ttdoc">4 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01000">gtc/type_aligned.hpp:1000</a></div></div>
<div class="ttc" id="a00303_html_ga3815661df0e2de79beff8168c09adf1e"><div class="ttname"><a href="a00303.html#ga3815661df0e2de79beff8168c09adf1e">glm::packed_highp_vec3</a></div><div class="ttdeci">vec&lt; 3, float, packed_highp &gt; packed_highp_vec3</div><div class="ttdoc">3 components vector tightly packed in memory of single-precision floating-point numbers using high pr...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00274">gtc/type_aligned.hpp:274</a></div></div>
<div class="ttc" id="a00303_html_ga8a9f0a4795ccc442fa9901845026f9f5"><div class="ttname"><a href="a00303.html#ga8a9f0a4795ccc442fa9901845026f9f5">glm::aligned_dvec4</a></div><div class="ttdeci">aligned_highp_dvec4 aligned_dvec4</div><div class="ttdoc">4 components vector aligned in memory of double-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01117">gtc/type_aligned.hpp:1117</a></div></div>
<div class="ttc" id="a00303_html_gad63b8c5b4dc0500d54d7414ef555178f"><div class="ttname"><a href="a00303.html#gad63b8c5b4dc0500d54d7414ef555178f">glm::aligned_highp_ivec1</a></div><div class="ttdeci">vec&lt; 1, int, aligned_highp &gt; aligned_highp_ivec1</div><div class="ttdoc">1 component vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00063">gtc/type_aligned.hpp:63</a></div></div>
<div class="ttc" id="a00303_html_ga1eb9076cc28ead5020fd3029fd0472c5"><div class="ttname"><a href="a00303.html#ga1eb9076cc28ead5020fd3029fd0472c5">glm::aligned_lowp_mat3</a></div><div class="ttdeci">mat&lt; 3, 3, float, aligned_lowp &gt; aligned_lowp_mat3</div><div class="ttdoc">3 by 3 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00457">gtc/type_aligned.hpp:457</a></div></div>
<div class="ttc" id="a00303_html_ga3a7eeae43cb7673e14cc89bf02f7dd45"><div class="ttname"><a href="a00303.html#ga3a7eeae43cb7673e14cc89bf02f7dd45">glm::aligned_highp_dmat2</a></div><div class="ttdeci">mat&lt; 2, 2, double, aligned_highp &gt; aligned_highp_dmat2</div><div class="ttdoc">2 by 2 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00422">gtc/type_aligned.hpp:422</a></div></div>
<div class="ttc" id="a00303_html_gaf488c6ad88c185054595d4d5c7ba5b9d"><div class="ttname"><a href="a00303.html#gaf488c6ad88c185054595d4d5c7ba5b9d">glm::aligned_dmat3x4</a></div><div class="ttdeci">aligned_highp_dmat3x4 aligned_dmat3x4</div><div class="ttdoc">3 by 4 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01165">gtc/type_aligned.hpp:1165</a></div></div>
<div class="ttc" id="a00303_html_ga0c48f9417f649e27f3fb0c9f733a18bd"><div class="ttname"><a href="a00303.html#ga0c48f9417f649e27f3fb0c9f733a18bd">glm::packed_bvec3</a></div><div class="ttdeci">packed_highp_bvec3 packed_bvec3</div><div class="ttdoc">3 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01308">gtc/type_aligned.hpp:1308</a></div></div>
<div class="ttc" id="a00303_html_ga0149fe15668925147e07c94fd2c2d6ae"><div class="ttname"><a href="a00303.html#ga0149fe15668925147e07c94fd2c2d6ae">glm::packed_highp_mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, packed_highp &gt; packed_highp_mat4x4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00849">gtc/type_aligned.hpp:849</a></div></div>
<div class="ttc" id="a00303_html_gab0eb771b708c5b2205d9b14dd1434fd8"><div class="ttname"><a href="a00303.html#gab0eb771b708c5b2205d9b14dd1434fd8">glm::packed_mediump_vec3</a></div><div class="ttdeci">vec&lt; 3, float, packed_mediump &gt; packed_mediump_vec3</div><div class="ttdoc">3 components vector tightly packed in memory of single-precision floating-point numbers using medium ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00277">gtc/type_aligned.hpp:277</a></div></div>
<div class="ttc" id="a00303_html_ga6f94fcd28dde906fc6cad5f742b55c1a"><div class="ttname"><a href="a00303.html#ga6f94fcd28dde906fc6cad5f742b55c1a">glm::aligned_lowp_uvec2</a></div><div class="ttdeci">vec&lt; 2, uint, aligned_lowp &gt; aligned_lowp_uvec2</div><div class="ttdoc">2 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00170">gtc/type_aligned.hpp:170</a></div></div>
<div class="ttc" id="a00303_html_ga862843a45b01c35ffe4d44c47ea774ad"><div class="ttname"><a href="a00303.html#ga862843a45b01c35ffe4d44c47ea774ad">glm::aligned_highp_bvec1</a></div><div class="ttdeci">vec&lt; 1, bool, aligned_highp &gt; aligned_highp_bvec1</div><div class="ttdoc">1 component vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00081">gtc/type_aligned.hpp:81</a></div></div>
<div class="ttc" id="a00303_html_gad791d671f4fcf1ed1ea41f752916b70a"><div class="ttname"><a href="a00303.html#gad791d671f4fcf1ed1ea41f752916b70a">glm::packed_highp_bvec2</a></div><div class="ttdeci">vec&lt; 2, bool, packed_highp &gt; packed_highp_bvec2</div><div class="ttdoc">2 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00218">gtc/type_aligned.hpp:218</a></div></div>
<div class="ttc" id="a00303_html_ga7245acc887a5438f46fd85fdf076bb3b"><div class="ttname"><a href="a00303.html#ga7245acc887a5438f46fd85fdf076bb3b">glm::packed_highp_ivec1</a></div><div class="ttdeci">vec&lt; 1, int, packed_highp &gt; packed_highp_ivec1</div><div class="ttdoc">1 component vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00108">gtc/type_aligned.hpp:108</a></div></div>
<div class="ttc" id="a00303_html_ga0edcfdd179f8a158342eead48a4d0c2a"><div class="ttname"><a href="a00303.html#ga0edcfdd179f8a158342eead48a4d0c2a">glm::aligned_highp_mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, float, aligned_highp &gt; aligned_highp_mat2x4</div><div class="ttdoc">2 by 4 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00603">gtc/type_aligned.hpp:603</a></div></div>
<div class="ttc" id="a00303_html_ga52635859f5ef660ab999d22c11b7867f"><div class="ttname"><a href="a00303.html#ga52635859f5ef660ab999d22c11b7867f">glm::packed_lowp_ivec2</a></div><div class="ttdeci">vec&lt; 2, int, packed_lowp &gt; packed_lowp_ivec2</div><div class="ttdoc">2 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00206">gtc/type_aligned.hpp:206</a></div></div>
<div class="ttc" id="a00303_html_gaaac6b356ef00154da41aaae7d1549193"><div class="ttname"><a href="a00303.html#gaaac6b356ef00154da41aaae7d1549193">glm::packed_highp_dvec3</a></div><div class="ttdeci">vec&lt; 3, double, packed_highp &gt; packed_highp_dvec3</div><div class="ttdoc">3 components vector tightly packed in memory of double-precision floating-point numbers using high pr...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00283">gtc/type_aligned.hpp:283</a></div></div>
<div class="ttc" id="a00303_html_ga41563650f36cb7f479e080de21e08418"><div class="ttname"><a href="a00303.html#ga41563650f36cb7f479e080de21e08418">glm::aligned_highp_ivec2</a></div><div class="ttdeci">vec&lt; 2, int, aligned_highp &gt; aligned_highp_ivec2</div><div class="ttdoc">2 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00155">gtc/type_aligned.hpp:155</a></div></div>
<div class="ttc" id="a00303_html_ga6783859382677d35fcd5dac7dcbefdbd"><div class="ttname"><a href="a00303.html#ga6783859382677d35fcd5dac7dcbefdbd">glm::aligned_dmat2</a></div><div class="ttdeci">aligned_highp_dmat2 aligned_dmat2</div><div class="ttdoc">2 by 2 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01132">gtc/type_aligned.hpp:1132</a></div></div>
<div class="ttc" id="a00303_html_ga86efe91cdaa2864c828a5d6d46356c6a"><div class="ttname"><a href="a00303.html#ga86efe91cdaa2864c828a5d6d46356c6a">glm::packed_mediump_dmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, double, packed_mediump &gt; packed_mediump_dmat3x2</div><div class="ttdoc">3 by 2 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00671">gtc/type_aligned.hpp:671</a></div></div>
<div class="ttc" id="a00303_html_gad6fc921bdde2bdbc7e09b028e1e9b379"><div class="ttname"><a href="a00303.html#gad6fc921bdde2bdbc7e09b028e1e9b379">glm::aligned_mediump_uvec3</a></div><div class="ttdeci">vec&lt; 3, uint, aligned_mediump &gt; aligned_mediump_uvec3</div><div class="ttdoc">3 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00259">gtc/type_aligned.hpp:259</a></div></div>
<div class="ttc" id="a00303_html_ga262dafd0c001c3a38d1cc91d024ca738"><div class="ttname"><a href="a00303.html#ga262dafd0c001c3a38d1cc91d024ca738">glm::packed_lowp_dvec4</a></div><div class="ttdeci">vec&lt; 4, double, packed_lowp &gt; packed_lowp_dvec4</div><div class="ttdoc">4 components vector tightly packed in memory of double-precision floating-point numbers using low pre...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00381">gtc/type_aligned.hpp:381</a></div></div>
<div class="ttc" id="a00303_html_ga1f18ada6f7cdd8c46db33ba987280fc4"><div class="ttname"><a href="a00303.html#ga1f18ada6f7cdd8c46db33ba987280fc4">glm::packed_mediump_dvec3</a></div><div class="ttdeci">vec&lt; 3, double, packed_mediump &gt; packed_mediump_dvec3</div><div class="ttdoc">3 components vector tightly packed in memory of double-precision floating-point numbers using medium ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00286">gtc/type_aligned.hpp:286</a></div></div>
<div class="ttc" id="a00303_html_gafcb189f4f93648fe7ca802ca4aca2eb8"><div class="ttname"><a href="a00303.html#gafcb189f4f93648fe7ca802ca4aca2eb8">glm::aligned_mediump_dmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, double, aligned_mediump &gt; aligned_mediump_dmat2x3</div><div class="ttdoc">2 by 3 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00577">gtc/type_aligned.hpp:577</a></div></div>
<div class="ttc" id="a00303_html_ga65663f10a02e52cedcddbcfe36ddf38d"><div class="ttname"><a href="a00303.html#ga65663f10a02e52cedcddbcfe36ddf38d">glm::aligned_lowp_ivec3</a></div><div class="ttdeci">vec&lt; 3, int, aligned_lowp &gt; aligned_lowp_ivec3</div><div class="ttdoc">3 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00253">gtc/type_aligned.hpp:253</a></div></div>
<div class="ttc" id="a00303_html_gab559d943abf92bc588bcd3f4c0e4664b"><div class="ttname"><a href="a00303.html#gab559d943abf92bc588bcd3f4c0e4664b">glm::aligned_highp_mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, float, aligned_highp &gt; aligned_highp_mat2x2</div><div class="ttdoc">2 by 2 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00527">gtc/type_aligned.hpp:527</a></div></div>
<div class="ttc" id="a00303_html_gafff1684c4ff19b4a818138ccacc1e78d"><div class="ttname"><a href="a00303.html#gafff1684c4ff19b4a818138ccacc1e78d">glm::aligned_highp_mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, float, aligned_highp &gt; aligned_highp_mat4x3</div><div class="ttdoc">4 by 3 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00793">gtc/type_aligned.hpp:793</a></div></div>
<div class="ttc" id="a00303_html_ga0913bdf048d0cb74af1d2512aec675bc"><div class="ttname"><a href="a00303.html#ga0913bdf048d0cb74af1d2512aec675bc">glm::aligned_highp_bvec3</a></div><div class="ttdeci">vec&lt; 3, bool, aligned_highp &gt; aligned_highp_bvec3</div><div class="ttdoc">3 components vector aligned in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00265">gtc/type_aligned.hpp:265</a></div></div>
<div class="ttc" id="a00303_html_ga3a30c137c1f8cce478c28eab0427a570"><div class="ttname"><a href="a00303.html#ga3a30c137c1f8cce478c28eab0427a570">glm::packed_lowp_vec3</a></div><div class="ttdeci">vec&lt; 3, float, packed_lowp &gt; packed_lowp_vec3</div><div class="ttdoc">3 components vector tightly packed in memory of single-precision floating-point numbers using low pre...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00280">gtc/type_aligned.hpp:280</a></div></div>
<div class="ttc" id="a00303_html_ga081d53e0a71443d0b68ea61c870f9adc"><div class="ttname"><a href="a00303.html#ga081d53e0a71443d0b68ea61c870f9adc">glm::aligned_mediump_uvec2</a></div><div class="ttdeci">vec&lt; 2, uint, aligned_mediump &gt; aligned_mediump_uvec2</div><div class="ttdoc">2 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00167">gtc/type_aligned.hpp:167</a></div></div>
<div class="ttc" id="a00303_html_ga09507ef020a49517a7bcd50438f05056"><div class="ttname"><a href="a00303.html#ga09507ef020a49517a7bcd50438f05056">glm::packed_mediump_ivec1</a></div><div class="ttdeci">vec&lt; 1, int, packed_mediump &gt; packed_mediump_ivec1</div><div class="ttdoc">1 component vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00111">gtc/type_aligned.hpp:111</a></div></div>
<div class="ttc" id="a00303_html_gad8f6abb2c9994850b5d5c04a5f979ed8"><div class="ttname"><a href="a00303.html#gad8f6abb2c9994850b5d5c04a5f979ed8">glm::aligned_highp_dmat3</a></div><div class="ttdeci">mat&lt; 3, 3, double, aligned_highp &gt; aligned_highp_dmat3</div><div class="ttdoc">3 by 3 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00460">gtc/type_aligned.hpp:460</a></div></div>
<div class="ttc" id="a00303_html_ga63a73be86a4f07ea7a7499ab0bfebe45"><div class="ttname"><a href="a00303.html#ga63a73be86a4f07ea7a7499ab0bfebe45">glm::packed_mediump_uvec4</a></div><div class="ttdeci">vec&lt; 4, uint, packed_mediump &gt; packed_mediump_uvec4</div><div class="ttdoc">4 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00396">gtc/type_aligned.hpp:396</a></div></div>
<div class="ttc" id="a00303_html_ga5a97f0355d801de3444d42c1d5b40438"><div class="ttname"><a href="a00303.html#ga5a97f0355d801de3444d42c1d5b40438">glm::aligned_mediump_dmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, double, aligned_mediump &gt; aligned_mediump_dmat3x2</div><div class="ttdoc">3 by 2 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00653">gtc/type_aligned.hpp:653</a></div></div>
<div class="ttc" id="a00303_html_ga45e155a4840f69b2fa4ed8047a676860"><div class="ttname"><a href="a00303.html#ga45e155a4840f69b2fa4ed8047a676860">glm::aligned_mediump_dmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, double, aligned_mediump &gt; aligned_mediump_dmat3x4</div><div class="ttdoc">3 by 4 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00729">gtc/type_aligned.hpp:729</a></div></div>
<div class="ttc" id="a00303_html_gab5b36cc9caee1bb1c5178fe191bf5713"><div class="ttname"><a href="a00303.html#gab5b36cc9caee1bb1c5178fe191bf5713">glm::packed_mediump_dmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, double, packed_mediump &gt; packed_mediump_dmat4x3</div><div class="ttdoc">4 by 3 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00823">gtc/type_aligned.hpp:823</a></div></div>
<div class="ttc" id="a00303_html_gab704d4fb15f6f96d70e363d5db7060cd"><div class="ttname"><a href="a00303.html#gab704d4fb15f6f96d70e363d5db7060cd">glm::packed_highp_uvec2</a></div><div class="ttdeci">vec&lt; 2, uint, packed_highp &gt; packed_highp_uvec2</div><div class="ttdoc">2 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00209">gtc/type_aligned.hpp:209</a></div></div>
<div class="ttc" id="a00303_html_ga73ea0c1ba31580e107d21270883f51fc"><div class="ttname"><a href="a00303.html#ga73ea0c1ba31580e107d21270883f51fc">glm::aligned_mediump_uvec4</a></div><div class="ttdeci">vec&lt; 4, uint, aligned_mediump &gt; aligned_mediump_uvec4</div><div class="ttdoc">4 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00351">gtc/type_aligned.hpp:351</a></div></div>
<div class="ttc" id="a00303_html_ga568b850f1116b667043533cf77826968"><div class="ttname"><a href="a00303.html#ga568b850f1116b667043533cf77826968">glm::packed_mediump_dvec4</a></div><div class="ttdeci">vec&lt; 4, double, packed_mediump &gt; packed_mediump_dvec4</div><div class="ttdoc">4 components vector tightly packed in memory of double-precision floating-point numbers using medium ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00378">gtc/type_aligned.hpp:378</a></div></div>
<div class="ttc" id="a00303_html_ga85d89e83cb8137e1be1446de8c3b643a"><div class="ttname"><a href="a00303.html#ga85d89e83cb8137e1be1446de8c3b643a">glm::aligned_vec4</a></div><div class="ttdeci">aligned_highp_vec4 aligned_vec4</div><div class="ttdoc">4 components vector aligned in memory of single-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00949">gtc/type_aligned.hpp:949</a></div></div>
<div class="ttc" id="a00303_html_ga31bfa801e1579fdba752ec3f7a45ec91"><div class="ttname"><a href="a00303.html#ga31bfa801e1579fdba752ec3f7a45ec91">glm::aligned_highp_ivec4</a></div><div class="ttdeci">vec&lt; 4, int, aligned_highp &gt; aligned_highp_ivec4</div><div class="ttdoc">4 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00339">gtc/type_aligned.hpp:339</a></div></div>
<div class="ttc" id="a00303_html_ga0e37cff4a43cca866101f0a35f01db6d"><div class="ttname"><a href="a00303.html#ga0e37cff4a43cca866101f0a35f01db6d">glm::aligned_lowp_dvec2</a></div><div class="ttdeci">vec&lt; 2, double, aligned_lowp &gt; aligned_lowp_dvec2</div><div class="ttdoc">2 components vector aligned in memory of double-precision floating-point numbers using low precision ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00152">gtc/type_aligned.hpp:152</a></div></div>
<div class="ttc" id="a00303_html_ga88632cea9008ac0ac1388e94e804a53c"><div class="ttname"><a href="a00303.html#ga88632cea9008ac0ac1388e94e804a53c">glm::packed_bvec1</a></div><div class="ttdeci">packed_highp_bvec1 packed_bvec1</div><div class="ttdoc">1 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01302">gtc/type_aligned.hpp:1302</a></div></div>
<div class="ttc" id="a00303_html_ga3e7df5a11e1be27bc29a4c0d3956f234"><div class="ttname"><a href="a00303.html#ga3e7df5a11e1be27bc29a4c0d3956f234">glm::packed_lowp_mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, float, packed_lowp &gt; packed_lowp_mat2x3</div><div class="ttdoc">2 by 3 matrix tightly packed in memory of single-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00589">gtc/type_aligned.hpp:589</a></div></div>
<div class="ttc" id="a00303_html_gab34aee3d5e121c543fea11d2c50ecc43"><div class="ttname"><a href="a00303.html#gab34aee3d5e121c543fea11d2c50ecc43">glm::aligned_lowp_vec1</a></div><div class="ttdeci">vec&lt; 1, float, aligned_lowp &gt; aligned_lowp_vec1</div><div class="ttdoc">1 component vector aligned in memory of single-precision floating-point numbers using low precision a...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00051">gtc/type_aligned.hpp:51</a></div></div>
<div class="ttc" id="a00303_html_gafbf1c2cce307c5594b165819ed83bf5d"><div class="ttname"><a href="a00303.html#gafbf1c2cce307c5594b165819ed83bf5d">glm::packed_lowp_vec2</a></div><div class="ttdeci">vec&lt; 2, float, packed_lowp &gt; packed_lowp_vec2</div><div class="ttdoc">2 components vector tightly packed in memory of single-precision floating-point numbers using low pre...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00188">gtc/type_aligned.hpp:188</a></div></div>
<div class="ttc" id="a00303_html_gaf0448b0f7ceb8273f7eda3a92205eefc"><div class="ttname"><a href="a00303.html#gaf0448b0f7ceb8273f7eda3a92205eefc">glm::aligned_highp_dvec1</a></div><div class="ttdeci">vec&lt; 1, double, aligned_highp &gt; aligned_highp_dvec1</div><div class="ttdoc">1 component vector aligned in memory of double-precision floating-point numbers using high precision ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00054">gtc/type_aligned.hpp:54</a></div></div>
<div class="ttc" id="a00303_html_ga99049db01faf1e95ed9fb875a47dffe2"><div class="ttname"><a href="a00303.html#ga99049db01faf1e95ed9fb875a47dffe2">glm::packed_mediump_mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, float, packed_mediump &gt; packed_mediump_mat2x3</div><div class="ttdoc">2 by 3 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00586">gtc/type_aligned.hpp:586</a></div></div>
<div class="ttc" id="a00303_html_ga04cfaf1421284df3c24ea0985dab24e7"><div class="ttname"><a href="a00303.html#ga04cfaf1421284df3c24ea0985dab24e7">glm::packed_mediump_mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, float, packed_mediump &gt; packed_mediump_mat3x2</div><div class="ttdoc">3 by 2 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00662">gtc/type_aligned.hpp:662</a></div></div>
<div class="ttc" id="a00303_html_ga320d1c661cff2ef214eb50241f2928b2"><div class="ttname"><a href="a00303.html#ga320d1c661cff2ef214eb50241f2928b2">glm::aligned_mediump_vec4</a></div><div class="ttdeci">vec&lt; 4, float, aligned_mediump &gt; aligned_mediump_vec4</div><div class="ttdoc">4 components vector aligned in memory of single-precision floating-point numbers using medium precisi...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00324">gtc/type_aligned.hpp:324</a></div></div>
<div class="ttc" id="a00303_html_gae8a9b181f9dc813ad6e125a52b14b935"><div class="ttname"><a href="a00303.html#gae8a9b181f9dc813ad6e125a52b14b935">glm::packed_dvec4</a></div><div class="ttdeci">packed_highp_dvec4 packed_dvec4</div><div class="ttdoc">4 components vector tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01129">gtc/type_aligned.hpp:1129</a></div></div>
<div class="ttc" id="a00303_html_gaffd747ea2aea1e69c2ecb04e68521b21"><div class="ttname"><a href="a00303.html#gaffd747ea2aea1e69c2ecb04e68521b21">glm::aligned_mediump_dvec4</a></div><div class="ttdeci">vec&lt; 4, double, aligned_mediump &gt; aligned_mediump_dvec4</div><div class="ttdoc">4 components vector aligned in memory of double-precision floating-point numbers using medium precisi...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00333">gtc/type_aligned.hpp:333</a></div></div>
<div class="ttc" id="a00303_html_ga21e86cf2f6c126bacf31b8985db06bd4"><div class="ttname"><a href="a00303.html#ga21e86cf2f6c126bacf31b8985db06bd4">glm::packed_mediump_dmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, double, packed_mediump &gt; packed_mediump_dmat4x4</div><div class="ttdoc">4 by 4 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00861">gtc/type_aligned.hpp:861</a></div></div>
<div class="ttc" id="a00303_html_gad5dcaf93df267bc3029174e430e0907f"><div class="ttname"><a href="a00303.html#gad5dcaf93df267bc3029174e430e0907f">glm::packed_mediump_dmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, double, packed_mediump &gt; packed_mediump_dmat3x4</div><div class="ttdoc">3 by 4 matrix tightly packed in memory of double-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00747">gtc/type_aligned.hpp:747</a></div></div>
<div class="ttc" id="a00303_html_ga52e00afd0eb181e6738f40cf41787049"><div class="ttname"><a href="a00303.html#ga52e00afd0eb181e6738f40cf41787049">glm::aligned_highp_mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, float, aligned_highp &gt; aligned_highp_mat3x4</div><div class="ttdoc">3 by 4 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00717">gtc/type_aligned.hpp:717</a></div></div>
<div class="ttc" id="a00303_html_ga058ae939bfdbcbb80521dd4a3b01afba"><div class="ttname"><a href="a00303.html#ga058ae939bfdbcbb80521dd4a3b01afba">glm::aligned_highp_mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, aligned_highp &gt; aligned_highp_mat4</div><div class="ttdoc">4 by 4 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00489">gtc/type_aligned.hpp:489</a></div></div>
<div class="ttc" id="a00303_html_ga2ccdcd4846775cbe4f9d12e71d55b5d2"><div class="ttname"><a href="a00303.html#ga2ccdcd4846775cbe4f9d12e71d55b5d2">glm::packed_lowp_dmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, double, packed_lowp &gt; packed_lowp_dmat2x3</div><div class="ttdoc">2 by 3 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00598">gtc/type_aligned.hpp:598</a></div></div>
<div class="ttc" id="a00303_html_ga98f4dc6722a2541a990918c074075359"><div class="ttname"><a href="a00303.html#ga98f4dc6722a2541a990918c074075359">glm::aligned_mediump_mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, float, aligned_mediump &gt; aligned_mediump_mat3x4</div><div class="ttdoc">3 by 4 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00720">gtc/type_aligned.hpp:720</a></div></div>
<div class="ttc" id="a00303_html_gab9b909f1392d86854334350efcae85f5"><div class="ttname"><a href="a00303.html#gab9b909f1392d86854334350efcae85f5">glm::packed_dmat3x3</a></div><div class="ttdeci">packed_highp_dmat3x3 packed_dmat3x3</div><div class="ttdoc">3 by 3 matrix tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01189">gtc/type_aligned.hpp:1189</a></div></div>
<div class="ttc" id="a00303_html_gadb065dbe5c11271fef8cf2ea8608f187"><div class="ttname"><a href="a00303.html#gadb065dbe5c11271fef8cf2ea8608f187">glm::aligned_mat3x3</a></div><div class="ttdeci">aligned_highp_mat3x3 aligned_mat3x3</div><div class="ttdoc">3 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00994">gtc/type_aligned.hpp:994</a></div></div>
<div class="ttc" id="a00303_html_ga98c9d122a959e9f3ce10a5623c310f5d"><div class="ttname"><a href="a00303.html#ga98c9d122a959e9f3ce10a5623c310f5d">glm::packed_lowp_ivec3</a></div><div class="ttdeci">vec&lt; 3, int, packed_lowp &gt; packed_lowp_ivec3</div><div class="ttdoc">3 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00298">gtc/type_aligned.hpp:298</a></div></div>
<div class="ttc" id="a00303_html_ga001bb387ae8192fa94dbd8b23b600439"><div class="ttname"><a href="a00303.html#ga001bb387ae8192fa94dbd8b23b600439">glm::aligned_dmat4</a></div><div class="ttdeci">aligned_highp_dmat4 aligned_dmat4</div><div class="ttdoc">4 by 4 matrix tightly aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01138">gtc/type_aligned.hpp:1138</a></div></div>
<div class="ttc" id="a00303_html_ga84e1f5e0718952a079b748825c03f956"><div class="ttname"><a href="a00303.html#ga84e1f5e0718952a079b748825c03f956">glm::aligned_highp_mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, float, aligned_highp &gt; aligned_highp_mat4x2</div><div class="ttdoc">4 by 2 matrix aligned in memory of single-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00755">gtc/type_aligned.hpp:755</a></div></div>
<div class="ttc" id="a00303_html_ga6b8b9475e7c3b16aed13edbc460bbc4d"><div class="ttname"><a href="a00303.html#ga6b8b9475e7c3b16aed13edbc460bbc4d">glm::packed_highp_vec2</a></div><div class="ttdeci">vec&lt; 2, float, packed_highp &gt; packed_highp_vec2</div><div class="ttdoc">2 components vector tightly packed in memory of single-precision floating-point numbers using high pr...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00182">gtc/type_aligned.hpp:182</a></div></div>
<div class="ttc" id="a00303_html_ga79a90173d8faa9816dc852ce447d66ca"><div class="ttname"><a href="a00303.html#ga79a90173d8faa9816dc852ce447d66ca">glm::aligned_lowp_dmat2</a></div><div class="ttdeci">mat&lt; 2, 2, double, aligned_lowp &gt; aligned_lowp_dmat2</div><div class="ttdoc">2 by 2 matrix aligned in memory of double-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00428">gtc/type_aligned.hpp:428</a></div></div>
<div class="ttc" id="a00303_html_gafdd97922b4a2a42cd0c99a13877ff4da"><div class="ttname"><a href="a00303.html#gafdd97922b4a2a42cd0c99a13877ff4da">glm::packed_lowp_uvec4</a></div><div class="ttdeci">vec&lt; 4, uint, packed_lowp &gt; packed_lowp_uvec4</div><div class="ttdoc">4 components vector tightly packed in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00399">gtc/type_aligned.hpp:399</a></div></div>
<div class="ttc" id="a00303_html_gac228b70372abd561340d5f926a7c1778"><div class="ttname"><a href="a00303.html#gac228b70372abd561340d5f926a7c1778">glm::packed_ivec4</a></div><div class="ttdeci">packed_highp_ivec4 packed_ivec4</div><div class="ttdoc">4 components vector tightly packed in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01237">gtc/type_aligned.hpp:1237</a></div></div>
<div class="ttc" id="a00303_html_ga5c194b11fbda636f2ab20c3bd0079196"><div class="ttname"><a href="a00303.html#ga5c194b11fbda636f2ab20c3bd0079196">glm::packed_dvec2</a></div><div class="ttdeci">packed_highp_dvec2 packed_dvec2</div><div class="ttdoc">2 components vector tightly packed in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01123">gtc/type_aligned.hpp:1123</a></div></div>
<div class="ttc" id="a00303_html_gaeaab04e378a90956eec8d68a99d777ed"><div class="ttname"><a href="a00303.html#gaeaab04e378a90956eec8d68a99d777ed">glm::aligned_lowp_mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, float, aligned_lowp &gt; aligned_lowp_mat3x3</div><div class="ttdoc">3 by 3 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00685">gtc/type_aligned.hpp:685</a></div></div>
<div class="ttc" id="a00303_html_ga23ec236950f5859f59197663266b535d"><div class="ttname"><a href="a00303.html#ga23ec236950f5859f59197663266b535d">glm::packed_lowp_dmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, double, packed_lowp &gt; packed_lowp_dmat3x2</div><div class="ttdoc">3 by 2 matrix tightly packed in memory of double-precision floating-point numbers using low precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00674">gtc/type_aligned.hpp:674</a></div></div>
<div class="ttc" id="a00303_html_ga2624719cb251d8de8cad1beaefc3a3f9"><div class="ttname"><a href="a00303.html#ga2624719cb251d8de8cad1beaefc3a3f9">glm::packed_highp_mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, float, packed_highp &gt; packed_highp_mat3x2</div><div class="ttdoc">3 by 2 matrix tightly packed in memory of single-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00659">gtc/type_aligned.hpp:659</a></div></div>
<div class="ttc" id="a00303_html_ga6dc2832b747c00e0a0df621aba196960"><div class="ttname"><a href="a00303.html#ga6dc2832b747c00e0a0df621aba196960">glm::aligned_mediump_dmat3</a></div><div class="ttdeci">mat&lt; 3, 3, double, aligned_mediump &gt; aligned_mediump_dmat3</div><div class="ttdoc">3 by 3 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00463">gtc/type_aligned.hpp:463</a></div></div>
<div class="ttc" id="a00303_html_ga7dec6d96bc61312b1e56d137c9c74030"><div class="ttname"><a href="a00303.html#ga7dec6d96bc61312b1e56d137c9c74030">glm::aligned_lowp_mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, float, aligned_lowp &gt; aligned_lowp_mat2x3</div><div class="ttdoc">2 by 3 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00571">gtc/type_aligned.hpp:571</a></div></div>
<div class="ttc" id="a00303_html_ga20e63dd980b81af10cadbbe219316650"><div class="ttname"><a href="a00303.html#ga20e63dd980b81af10cadbbe219316650">glm::aligned_mediump_ivec1</a></div><div class="ttdeci">vec&lt; 1, int, aligned_mediump &gt; aligned_mediump_ivec1</div><div class="ttdoc">1 component vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00066">gtc/type_aligned.hpp:66</a></div></div>
<div class="ttc" id="a00303_html_gae29686632fd05efac0675d9a6370d77b"><div class="ttname"><a href="a00303.html#gae29686632fd05efac0675d9a6370d77b">glm::packed_highp_dmat2</a></div><div class="ttdeci">mat&lt; 2, 2, double, packed_highp &gt; packed_highp_dmat2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of double-precision floating-point numbers using high precisio...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00440">gtc/type_aligned.hpp:440</a></div></div>
<div class="ttc" id="a00303_html_ga6844c6f4691d1bf67673240850430948"><div class="ttname"><a href="a00303.html#ga6844c6f4691d1bf67673240850430948">glm::packed_mediump_vec2</a></div><div class="ttdeci">vec&lt; 2, float, packed_mediump &gt; packed_mediump_vec2</div><div class="ttdoc">2 components vector tightly packed in memory of single-precision floating-point numbers using medium ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00185">gtc/type_aligned.hpp:185</a></div></div>
<div class="ttc" id="a00303_html_ga43a92a24ca863e0e0f3b65834b3cf714"><div class="ttname"><a href="a00303.html#ga43a92a24ca863e0e0f3b65834b3cf714">glm::aligned_mat3</a></div><div class="ttdeci">aligned_highp_mat3 aligned_mat3</div><div class="ttdoc">3 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00967">gtc/type_aligned.hpp:967</a></div></div>
<div class="ttc" id="a00303_html_gae84f4763ecdc7457ecb7930bad12057c"><div class="ttname"><a href="a00303.html#gae84f4763ecdc7457ecb7930bad12057c">glm::aligned_mediump_dmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, double, aligned_mediump &gt; aligned_mediump_dmat4x3</div><div class="ttdoc">4 by 3 matrix aligned in memory of double-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00805">gtc/type_aligned.hpp:805</a></div></div>
<div class="ttc" id="a00303_html_ga2d2a73e662759e301c22b8931ff6a526"><div class="ttname"><a href="a00303.html#ga2d2a73e662759e301c22b8931ff6a526">glm::packed_mediump_mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, float, packed_mediump &gt; packed_mediump_mat2x2</div><div class="ttdoc">2 by 2 matrix tightly packed in memory of single-precision floating-point numbers using medium precis...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00548">gtc/type_aligned.hpp:548</a></div></div>
<div class="ttc" id="a00303_html_gaa7c9d4ab7ab651cdf8001fe7843e238b"><div class="ttname"><a href="a00303.html#gaa7c9d4ab7ab651cdf8001fe7843e238b">glm::aligned_highp_dmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, double, aligned_highp &gt; aligned_highp_dmat2x3</div><div class="ttdoc">2 by 3 matrix aligned in memory of double-precision floating-point numbers using high precision arith...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00574">gtc/type_aligned.hpp:574</a></div></div>
<div class="ttc" id="a00303_html_ga18d859f87122b2b3b2992ffe86dbebc0"><div class="ttname"><a href="a00303.html#ga18d859f87122b2b3b2992ffe86dbebc0">glm::aligned_dvec2</a></div><div class="ttdeci">aligned_highp_dvec2 aligned_dvec2</div><div class="ttdoc">2 components vector aligned in memory of double-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01111">gtc/type_aligned.hpp:1111</a></div></div>
<div class="ttc" id="a00303_html_ga1f03696ab066572c6c044e63edf635a2"><div class="ttname"><a href="a00303.html#ga1f03696ab066572c6c044e63edf635a2">glm::aligned_lowp_mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, float, aligned_lowp &gt; aligned_lowp_mat3x4</div><div class="ttdoc">3 by 4 matrix aligned in memory of single-precision floating-point numbers using low precision arithm...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00723">gtc/type_aligned.hpp:723</a></div></div>
<div class="ttc" id="a00303_html_ga379c1153f1339bdeaefd592bebf538e8"><div class="ttname"><a href="a00303.html#ga379c1153f1339bdeaefd592bebf538e8">glm::packed_mat4x2</a></div><div class="ttdeci">packed_highp_mat4x2 packed_mat4x2</div><div class="ttdoc">4 by 2 matrix tightly packed in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01027">gtc/type_aligned.hpp:1027</a></div></div>
<div class="ttc" id="a00303_html_ga82f7275d6102593a69ce38cdad680409"><div class="ttname"><a href="a00303.html#ga82f7275d6102593a69ce38cdad680409">glm::aligned_lowp_vec4</a></div><div class="ttdeci">vec&lt; 4, float, aligned_lowp &gt; aligned_lowp_vec4</div><div class="ttdoc">4 components vector aligned in memory of single-precision floating-point numbers using low precision ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00327">gtc/type_aligned.hpp:327</a></div></div>
<div class="ttc" id="a00303_html_ga6a5a3250b57dfadc66735bc72911437f"><div class="ttname"><a href="a00303.html#ga6a5a3250b57dfadc66735bc72911437f">glm::packed_highp_bvec3</a></div><div class="ttdeci">vec&lt; 3, bool, packed_highp &gt; packed_highp_bvec3</div><div class="ttdoc">3 components vector tightly packed in memory of bool values. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00310">gtc/type_aligned.hpp:310</a></div></div>
<div class="ttc" id="a00303_html_gadc19938ddb204bfcb4d9ef35b1e2bf93"><div class="ttname"><a href="a00303.html#gadc19938ddb204bfcb4d9ef35b1e2bf93">glm::packed_lowp_dvec2</a></div><div class="ttdeci">vec&lt; 2, double, packed_lowp &gt; packed_lowp_dvec2</div><div class="ttdoc">2 components vector tightly packed in memory of double-precision floating-point numbers using low pre...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00197">gtc/type_aligned.hpp:197</a></div></div>
<div class="ttc" id="a00303_html_ga058e7ddab1428e47f2197bdd3a5a6953"><div class="ttname"><a href="a00303.html#ga058e7ddab1428e47f2197bdd3a5a6953">glm::aligned_mediump_dvec3</a></div><div class="ttdeci">vec&lt; 3, double, aligned_mediump &gt; aligned_mediump_dvec3</div><div class="ttdoc">3 components vector aligned in memory of double-precision floating-point numbers using medium precisi...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00241">gtc/type_aligned.hpp:241</a></div></div>
<div class="ttc" id="a00303_html_ga3a25e494173f6a64637b08a1b50a2132"><div class="ttname"><a href="a00303.html#ga3a25e494173f6a64637b08a1b50a2132">glm::aligned_mediump_vec3</a></div><div class="ttdeci">vec&lt; 3, float, aligned_mediump &gt; aligned_mediump_vec3</div><div class="ttdoc">3 components vector aligned in memory of single-precision floating-point numbers using medium precisi...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00232">gtc/type_aligned.hpp:232</a></div></div>
<div class="ttc" id="a00303_html_ga163facc9ed2692ea1300ed57c5d12b17"><div class="ttname"><a href="a00303.html#ga163facc9ed2692ea1300ed57c5d12b17">glm::aligned_mediump_mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, float, aligned_mediump &gt; aligned_mediump_mat2x4</div><div class="ttdoc">2 by 4 matrix aligned in memory of single-precision floating-point numbers using medium precision ari...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00606">gtc/type_aligned.hpp:606</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
<div class="ttc" id="a00303_html_gae94ef61edfa047d05bc69b6065fc42ba"><div class="ttname"><a href="a00303.html#gae94ef61edfa047d05bc69b6065fc42ba">glm::aligned_highp_dvec3</a></div><div class="ttdeci">vec&lt; 3, double, aligned_highp &gt; aligned_highp_dvec3</div><div class="ttdoc">3 components vector aligned in memory of double-precision floating-point numbers using high precision...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00238">gtc/type_aligned.hpp:238</a></div></div>
<div class="ttc" id="a00303_html_ga4d0bd70d5fac49b800546d608b707513"><div class="ttname"><a href="a00303.html#ga4d0bd70d5fac49b800546d608b707513">glm::aligned_highp_vec1</a></div><div class="ttdeci">vec&lt; 1, float, aligned_highp &gt; aligned_highp_vec1</div><div class="ttdoc">1 component vector aligned in memory of single-precision floating-point numbers using high precision ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00045">gtc/type_aligned.hpp:45</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
