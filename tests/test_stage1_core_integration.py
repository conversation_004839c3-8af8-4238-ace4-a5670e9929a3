#!/usr/bin/env python3
"""
🔥 Stage 1 核心集成测试

测试新实现的核心功能：
1. concept_id 在 gaussian_model.py 中的集成
2. 2D→3D concept_id 映射机制
3. train.py 中的 concept_id 训练集成

作者: <PERSON> 4 (Anthropic 最先进模型)
"""

import os
import sys
import torch
import argparse
import logging
from pathlib import Path

# 添加路径
sys.path.append('./gaussian_splatting')
sys.path.append('.')

def test_gaussian_model_concept_integration():
    """测试 GaussianModel 的 concept_id 集成"""
    print("🔥 测试 1: GaussianModel concept_id 集成")
    
    try:
        from scene import GaussianModel
        
        # 创建高斯模型
        gaussians = GaussianModel(sh_degree=3)
        
        # 检查 concept_id 属性
        assert hasattr(gaussians, '_concept_id'), "❌ 缺少 _concept_id 属性"
        assert hasattr(gaussians, '_concept_confidence'), "❌ 缺少 _concept_confidence 属性"
        assert hasattr(gaussians, 'get_concept_id'), "❌ 缺少 get_concept_id 属性访问器"
        assert hasattr(gaussians, 'get_concept_confidence'), "❌ 缺少 get_concept_confidence 属性访问器"
        
        # 检查映射方法
        assert hasattr(gaussians, 'initialize_concept_ids_from_masks'), "❌ 缺少 initialize_concept_ids_from_masks 方法"
        
        print("✅ GaussianModel concept_id 集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GaussianModel concept_id 集成测试失败: {e}")
        return False

def test_concept_initializer():
    """测试 ConceptIDInitializer"""
    print("🔥 测试 2: ConceptIDInitializer 功能")
    
    try:
        from stage1_core_concept_initializer import ConceptIDInitializer
        
        # 创建初始化器（使用虚拟路径）
        initializer = ConceptIDInitializer(
            model_path="test_model",
            concept_masks_path="test_masks",
            output_path="test_output"
        )
        
        # 检查方法存在
        assert hasattr(initializer, 'load_concept_masks'), "❌ 缺少 load_concept_masks 方法"
        assert hasattr(initializer, 'load_gaussian_model'), "❌ 缺少 load_gaussian_model 方法"
        assert hasattr(initializer, 'initialize_concept_ids'), "❌ 缺少 initialize_concept_ids 方法"
        
        print("✅ ConceptIDInitializer 功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ ConceptIDInitializer 功能测试失败: {e}")
        return False

def test_train_integration():
    """测试 train.py 集成"""
    print("🔥 测试 3: train.py concept_id 集成")
    
    try:
        # 检查训练函数签名
        import inspect
        from gaussian_splatting.train import training
        
        sig = inspect.signature(training)
        params = list(sig.parameters.keys())
        
        assert 'concept_training' in params, "❌ training 函数缺少 concept_training 参数"
        assert 'concept_masks_path' in params, "❌ training 函数缺少 concept_masks_path 参数"
        
        print("✅ train.py concept_id 集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ train.py concept_id 集成测试失败: {e}")
        return False

def test_concept_id_data_flow():
    """测试 concept_id 数据流"""
    print("🔥 测试 4: concept_id 数据流")
    
    try:
        from scene import GaussianModel
        import torch
        
        # 创建高斯模型
        gaussians = GaussianModel(sh_degree=3)
        
        # 模拟点云数据
        num_points = 100
        xyz = torch.randn(num_points, 3, device="cuda")
        colors = torch.randn(num_points, 3, device="cuda")
        
        # 模拟 create_from_pcd 的部分逻辑
        gaussians._xyz = torch.nn.Parameter(xyz.requires_grad_(True))
        gaussians._concept_id = torch.zeros((num_points,), dtype=torch.long, device="cuda")
        gaussians._concept_confidence = torch.nn.Parameter(torch.ones((num_points,), dtype=torch.float, device="cuda") * 0.1)
        
        # 测试 concept_id 访问
        concept_ids = gaussians.get_concept_id
        concept_confidences = gaussians.get_concept_confidence
        
        assert concept_ids.shape[0] == num_points, "❌ concept_id 形状不正确"
        assert concept_confidences.shape[0] == num_points, "❌ concept_confidence 形状不正确"
        assert concept_ids.dtype == torch.long, "❌ concept_id 数据类型不正确"
        assert concept_confidences.dtype == torch.float, "❌ concept_confidence 数据类型不正确"
        
        print("✅ concept_id 数据流测试通过")
        return True
        
    except Exception as e:
        print(f"❌ concept_id 数据流测试失败: {e}")
        return False

def test_concept_id_persistence():
    """测试 concept_id 持久化"""
    print("🔥 测试 5: concept_id 持久化")
    
    try:
        from scene import GaussianModel
        import torch
        import tempfile
        import os
        
        # 创建高斯模型
        gaussians = GaussianModel(sh_degree=3)
        
        # 模拟数据
        num_points = 50
        xyz = torch.randn(num_points, 3, device="cuda")
        gaussians._xyz = torch.nn.Parameter(xyz.requires_grad_(True))
        gaussians._concept_id = torch.randint(0, 3, (num_points,), dtype=torch.long, device="cuda")
        gaussians._concept_confidence = torch.nn.Parameter(torch.rand((num_points,), dtype=torch.float, device="cuda"))
        
        # 测试保存和加载
        with tempfile.TemporaryDirectory() as temp_dir:
            ply_path = os.path.join(temp_dir, "test_gaussians.ply")
            
            # 保存
            gaussians.save_ply(ply_path)
            assert os.path.exists(ply_path), "❌ PLY 文件保存失败"
            
            print("✅ concept_id 持久化测试通过")
            return True
        
    except Exception as e:
        print(f"❌ concept_id 持久化测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始 Stage 1 核心集成测试...")
    print("=" * 60)
    
    tests = [
        test_gaussian_model_concept_integration,
        test_concept_initializer,
        test_train_integration,
        test_concept_id_data_flow,
        test_concept_id_persistence
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print("-" * 40)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print("-" * 40)
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心集成测试通过！Stage 1 核心功能已就绪")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复核心功能")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Stage 1 核心集成测试")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.basicConfig(level=logging.DEBUG)
    
    success = run_all_tests()
    
    if success:
        print("\n🔥 下一步: 运行实际的 concept_id 初始化测试")
        print("命令: python stage1_core_concept_initializer.py --model_path <your_model_path>")
    else:
        print("\n❌ 请先修复失败的测试")
        sys.exit(1)

if __name__ == "__main__":
    main()
