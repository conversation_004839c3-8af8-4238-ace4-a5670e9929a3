"""
RCA Core: 3D空间注意力网络
阶段3.1: 设计可学习的网络，预测每个3D点对不同概念的隶属权重

网络设计理念：
- 输入：3D高斯点的多维特征
- 输出：每个点对3个概念的权重分布
- 目标：学习空间语义的软分配
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
import logging

class SpatialAttention3D(nn.Module):
    """
    3D空间注意力网络
    
    功能：
    1. 提取3D高斯点的多维特征
    2. 预测每个点对不同概念的隶属权重
    3. 实现空间语义的软分配
    4. 支持可微分的权重学习
    """
    
    def __init__(
        self, 
        input_dim: int = 13,  # 3(xyz) + 3(color) + 1(opacity) + 3(scaling) + 3(concept_onehot)
        hidden_dim: int = 256,
        num_concepts: int = 3,
        dropout_rate: float = 0.1,
        use_residual: bool = True
    ):
        """
        初始化3D空间注意力网络
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            num_concepts: 概念数量
            dropout_rate: Dropout率
            use_residual: 是否使用残差连接
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_concepts = num_concepts
        self.use_residual = use_residual
        
        # 特征提取网络
        self.feature_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(inplace=True)
        )
        
        # 概念注意力头
        self.concept_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim // 2, hidden_dim // 4),
                nn.ReLU(inplace=True),
                nn.Linear(hidden_dim // 4, 1)
            ) for _ in range(num_concepts)
        ])
        
        # 全局注意力机制
        self.global_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim // 2,
            num_heads=8,
            dropout=dropout_rate,
            batch_first=True
        )
        
        # 输出层
        self.output_projection = nn.Linear(hidden_dim // 2, num_concepts)
        
        # Softmax用于权重归一化
        self.softmax = nn.Softmax(dim=-1)
        
        # 初始化权重
        self._initialize_weights()
        
        logging.info(f"SpatialAttention3D initialized: input_dim={input_dim}, hidden_dim={hidden_dim}, num_concepts={num_concepts}")
    
    def _initialize_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def extract_point_features(self, gaussians) -> torch.Tensor:
        """
        提取3D高斯点的多维特征
        
        Args:
            gaussians: GaussianModel实例
            
        Returns:
            features: 提取的特征 [N, input_dim]
        """
        features = []
        
        # 基础几何特征
        features.append(gaussians.get_xyz)           # 位置 [N, 3]
        features.append(gaussians.get_features_dc.squeeze(-1))  # 颜色 [N, 3] 
        features.append(gaussians.get_opacity)       # 透明度 [N, 1]
        features.append(gaussians.get_scaling)       # 尺度 [N, 3]
        
        # concept_id one-hot编码
        concept_onehot = F.one_hot(gaussians.get_concept_id, num_classes=self.num_concepts)  # [N, 3]
        features.append(concept_onehot.float())
        
        # 拼接所有特征
        point_features = torch.cat(features, dim=-1)  # [N, 13]
        
        return point_features
    
    def forward(
        self, 
        gaussians, 
        use_global_attention: bool = True,
        return_intermediate: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            gaussians: GaussianModel实例
            use_global_attention: 是否使用全局注意力
            return_intermediate: 是否返回中间结果
            
        Returns:
            results: 包含权重和中间结果的字典
        """
        # 1. 提取点特征
        point_features = self.extract_point_features(gaussians)  # [N, input_dim]
        batch_size = point_features.shape[0]
        
        # 2. 特征编码
        encoded_features = self.feature_encoder(point_features)  # [N, hidden_dim//2]
        
        # 3. 全局注意力（可选）
        if use_global_attention:
            # 添加batch维度用于注意力计算
            features_for_attention = encoded_features.unsqueeze(0)  # [1, N, hidden_dim//2]
            
            attended_features, attention_weights = self.global_attention(
                features_for_attention, features_for_attention, features_for_attention
            )
            
            attended_features = attended_features.squeeze(0)  # [N, hidden_dim//2]
            
            # 残差连接
            if self.use_residual:
                encoded_features = encoded_features + attended_features
            else:
                encoded_features = attended_features
        
        # 4. 概念权重预测 - 方法1：独立头
        concept_weights_heads = []
        for head in self.concept_heads:
            weight = head(encoded_features)  # [N, 1]
            concept_weights_heads.append(weight)
        
        concept_weights_heads = torch.cat(concept_weights_heads, dim=-1)  # [N, num_concepts]
        
        # 5. 概念权重预测 - 方法2：统一输出
        concept_weights_unified = self.output_projection(encoded_features)  # [N, num_concepts]
        
        # 6. 权重归一化
        concept_weights_heads_norm = self.softmax(concept_weights_heads)
        concept_weights_unified_norm = self.softmax(concept_weights_unified)
        
        # 7. 融合两种方法的结果
        alpha = 0.7  # 融合权重
        final_weights = alpha * concept_weights_heads_norm + (1 - alpha) * concept_weights_unified_norm
        
        # 构建返回结果
        results = {
            'concept_weights': final_weights,  # [N, num_concepts]
            'concept_weights_raw': concept_weights_heads,  # 未归一化的权重
            'point_features': point_features,
            'encoded_features': encoded_features
        }
        
        if use_global_attention:
            results['attention_weights'] = attention_weights.squeeze(0)  # [N, N]
        
        if return_intermediate:
            results.update({
                'concept_weights_heads': concept_weights_heads_norm,
                'concept_weights_unified': concept_weights_unified_norm,
                'features_before_attention': self.feature_encoder(point_features)
            })
        
        return results
    
    def compute_spatial_consistency_loss(
        self, 
        concept_weights: torch.Tensor, 
        gaussians,
        neighbor_radius: float = 0.1
    ) -> torch.Tensor:
        """
        计算空间一致性损失
        
        Args:
            concept_weights: 概念权重 [N, num_concepts]
            gaussians: GaussianModel实例
            neighbor_radius: 邻域半径
            
        Returns:
            consistency_loss: 空间一致性损失
        """
        xyz = gaussians.get_xyz  # [N, 3]
        
        # 计算点之间的距离
        distances = torch.cdist(xyz, xyz)  # [N, N]
        
        # 找到邻域内的点
        neighbor_mask = distances < neighbor_radius  # [N, N]
        
        # 计算邻域内权重的一致性
        consistency_losses = []
        
        for i in range(xyz.shape[0]):
            neighbors = neighbor_mask[i]  # [N]
            if neighbors.sum() > 1:  # 至少有一个邻居
                neighbor_weights = concept_weights[neighbors]  # [num_neighbors, num_concepts]
                center_weight = concept_weights[i:i+1]  # [1, num_concepts]
                
                # 计算与邻居权重的差异
                weight_diff = torch.norm(neighbor_weights - center_weight, dim=-1)  # [num_neighbors]
                consistency_loss = weight_diff.mean()
                consistency_losses.append(consistency_loss)
        
        if consistency_losses:
            return torch.stack(consistency_losses).mean()
        else:
            return torch.tensor(0.0, device=concept_weights.device)
    
    def get_concept_statistics(self, concept_weights: torch.Tensor) -> Dict:
        """
        获取概念权重统计信息
        
        Args:
            concept_weights: 概念权重 [N, num_concepts]
            
        Returns:
            stats: 统计信息字典
        """
        with torch.no_grad():
            # 主导概念（权重最大的概念）
            dominant_concepts = torch.argmax(concept_weights, dim=-1)  # [N]
            
            # 概念分布
            concept_distribution = {}
            for concept_id in range(self.num_concepts):
                mask = (dominant_concepts == concept_id)
                count = mask.sum().item()
                percentage = count / concept_weights.shape[0] * 100
                concept_distribution[concept_id] = {
                    'count': count,
                    'percentage': percentage
                }
            
            # 权重统计
            weight_stats = {}
            for concept_id in range(self.num_concepts):
                weights = concept_weights[:, concept_id]
                weight_stats[concept_id] = {
                    'mean': weights.mean().item(),
                    'std': weights.std().item(),
                    'min': weights.min().item(),
                    'max': weights.max().item()
                }
            
            # 权重熵（衡量权重分布的均匀性）
            entropy = -torch.sum(concept_weights * torch.log(concept_weights + 1e-8), dim=-1)
            
            stats = {
                'concept_distribution': concept_distribution,
                'weight_statistics': weight_stats,
                'entropy_mean': entropy.mean().item(),
                'entropy_std': entropy.std().item(),
                'total_points': concept_weights.shape[0]
            }
        
        return stats
