import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np

@dataclass
class WeightRendererConfig:
    """权重渲染器配置"""
    # 渲染参数
    image_height: int = 512
    image_width: int = 512
    num_concepts: int = 3
    
    # 光栅化参数
    alpha_threshold: float = 1e-4
    max_gaussians_per_pixel: int = 100
    depth_range: Tuple[float, float] = (0.1, 100.0)
    
    # 权重混合参数
    weight_normalization: str = "softmax"  # "softmax", "sum_to_one", "none"
    spatial_smoothing: bool = True
    smoothing_kernel_size: int = 3
    smoothing_sigma: float = 1.0
    
    # 批处理参数
    max_batch_size: int = 4
    enable_batch_processing: bool = True
    
    # 数值稳定性
    epsilon: float = 1e-8
    gradient_checkpointing: bool = False
    
    # 优化选项
    use_fast_rasterization: bool = True
    enable_culling: bool = True
    culling_threshold: float = 0.01

class GaussianWeightRasterizer(nn.Module):
    """
    🔥 高斯权重光栅化器
    
    功能:
    - 将3D高斯点的概念权重渲染为2D权重图
    - 支持多通道权重渲染
    - 实现可微的权重光栅化
    """
    
    def __init__(self, config: WeightRendererConfig = None):
        super().__init__()
        self.config = config or WeightRendererConfig()
        
        # 预计算高斯核
        self._setup_gaussian_kernel()
        
        print(f"🎯 高斯权重光栅化器初始化完成")
        print(f"   渲染分辨率: {self.config.image_height}x{self.config.image_width}")
        print(f"   概念通道数: {self.config.num_concepts}")
    
    def _setup_gaussian_kernel(self):
        """预计算高斯核"""
        if self.config.spatial_smoothing:
            kernel_size = self.config.smoothing_kernel_size
            sigma = self.config.smoothing_sigma
            
            # 创建2D高斯核
            x = torch.arange(kernel_size, dtype=torch.float32) - kernel_size // 2
            y = torch.arange(kernel_size, dtype=torch.float32) - kernel_size // 2
            xx, yy = torch.meshgrid(x, y, indexing='ij')
            
            kernel = torch.exp(-(xx**2 + yy**2) / (2 * sigma**2))
            kernel = kernel / kernel.sum()
            
            # 扩展为多通道
            kernel = kernel.unsqueeze(0).unsqueeze(0)  # [1, 1, K, K]
            self.register_buffer('smoothing_kernel', kernel)
    
    def forward(self, 
                gaussian_positions: torch.Tensor,
                gaussian_scales: torch.Tensor,
                gaussian_rotations: torch.Tensor,
                gaussian_opacity: torch.Tensor,
                concept_weights: torch.Tensor,
                camera_params: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        🔥 渲染概念权重图
        
        Args:
            gaussian_positions: [N, 3] 3D高斯位置
            gaussian_scales: [N, 3] 3D高斯尺度
            gaussian_rotations: [N, 4] 3D高斯旋转(四元数)
            gaussian_opacity: [N, 1] 高斯透明度
            concept_weights: [N, num_concepts] 概念权重
            camera_params: 相机参数字典
                - 'view_matrix': [4, 4] 视图矩阵
                - 'projection_matrix': [4, 4] 投影矩阵
                - 'camera_center': [3] 相机中心
                
        Returns:
            render_result: 渲染结果字典
                - 'weight_maps': [num_concepts, H, W] 概念权重图
                - 'alpha_map': [H, W] Alpha通道
                - 'depth_map': [H, W] 深度图
                - 'gaussian_ids': [H, W] 高斯点ID图
        """
        batch_size = 1  # 暂时支持单张图像
        device = gaussian_positions.device
        N = gaussian_positions.shape[0]
        
        # 1. 🔥 3D到2D投影
        projected_points, depths, valid_mask = self._project_gaussians_to_2d(
            gaussian_positions, camera_params
        )
        
        # 2. 🔥 筛选有效的高斯点
        if self.config.enable_culling:
            valid_gaussians = self._cull_gaussians(
                projected_points, depths, gaussian_opacity, valid_mask
            )
        else:
            valid_gaussians = valid_mask
        
        # 3. 🔥 计算2D高斯参数
        gaussian_2d_params = self._compute_2d_gaussian_params(
            gaussian_positions[valid_gaussians],
            gaussian_scales[valid_gaussians],
            gaussian_rotations[valid_gaussians],
            projected_points[valid_gaussians],
            camera_params
        )
        
        # 4. 🔥 权重光栅化
        weight_maps, alpha_map, depth_map, gaussian_ids = self._rasterize_weights(
            gaussian_2d_params,
            gaussian_opacity[valid_gaussians],
            concept_weights[valid_gaussians],
            depths[valid_gaussians],
            torch.arange(N, device=device)[valid_gaussians]
        )
        
        # 5. 🔥 后处理
        if self.config.spatial_smoothing:
            weight_maps = self._apply_spatial_smoothing(weight_maps)
        
        if self.config.weight_normalization != "none":
            weight_maps = self._normalize_weights(weight_maps)
        
        # 6. 🔥 组装结果
        result = {
            'weight_maps': weight_maps,           # [num_concepts, H, W]
            'alpha_map': alpha_map,               # [H, W]
            'depth_map': depth_map,               # [H, W]
            'gaussian_ids': gaussian_ids,         # [H, W]
            'valid_gaussians_count': valid_gaussians.sum().item(),
            'total_gaussians': N
        }
        
        return result
    
    def _project_gaussians_to_2d(self, 
                                positions: torch.Tensor,
                                camera_params: Dict[str, torch.Tensor]) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """将3D高斯投影到2D"""
        N = positions.shape[0]
        device = positions.device
        
        # 齐次坐标
        positions_homo = torch.cat([positions, torch.ones(N, 1, device=device)], dim=1)  # [N, 4]
        
        # 视图变换
        view_matrix = camera_params['view_matrix']  # [4, 4]
        positions_view = torch.matmul(positions_homo, view_matrix.t())  # [N, 4]
        
        # 投影变换
        projection_matrix = camera_params['projection_matrix']  # [4, 4]
        positions_clip = torch.matmul(positions_view, projection_matrix.t())  # [N, 4]
        
        # 透视除法
        depths = positions_view[:, 2]  # [N] Z深度
        positions_ndc = positions_clip[:, :3] / (positions_clip[:, 3:4] + self.config.epsilon)  # [N, 3]
        
        # NDC到像素坐标
        x_pixel = (positions_ndc[:, 0] + 1.0) * 0.5 * self.config.image_width   # [N]
        y_pixel = (1.0 - positions_ndc[:, 1]) * 0.5 * self.config.image_height  # [N]
        
        projected_points = torch.stack([x_pixel, y_pixel], dim=1)  # [N, 2]
        
        # 有效性检查
        valid_mask = (
            (depths > self.config.depth_range[0]) &
            (depths < self.config.depth_range[1]) &
            (x_pixel >= 0) & (x_pixel < self.config.image_width) &
            (y_pixel >= 0) & (y_pixel < self.config.image_height) &
            (positions_clip[:, 3] > self.config.epsilon)
        )
        
        return projected_points, depths, valid_mask
    
    def _cull_gaussians(self, 
                       projected_points: torch.Tensor,
                       depths: torch.Tensor,
                       opacity: torch.Tensor,
                       valid_mask: torch.Tensor) -> torch.Tensor:
        """剔除不需要的高斯点"""
        # 透明度过滤
        opacity_mask = opacity.squeeze() > self.config.culling_threshold
        
        # 深度过滤
        depth_mask = (depths > self.config.depth_range[0]) & (depths < self.config.depth_range[1])
        
        # 组合掩码
        culling_mask = valid_mask & opacity_mask & depth_mask
        
        return culling_mask
    
    def _compute_2d_gaussian_params(self,
                                   positions_3d: torch.Tensor,
                                   scales_3d: torch.Tensor,
                                   rotations_3d: torch.Tensor,
                                   projected_points: torch.Tensor,
                                   camera_params: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """计算2D高斯参数"""
        N = positions_3d.shape[0]
        device = positions_3d.device
        
        # 简化版本：使用投影的尺度作为2D高斯参数
        # 在实际实现中，这里应该进行完整的3D到2D变换
        
        # 计算投影尺度 (简化)
        view_matrix = camera_params['view_matrix']
        focal_length = camera_params.get('focal_length', torch.tensor(500.0, device=device))
        
        # 距离相机的距离
        positions_homo = torch.cat([positions_3d, torch.ones(N, 1, device=device)], dim=1)
        positions_view = torch.matmul(positions_homo, view_matrix.t())
        distance_to_camera = torch.norm(positions_view[:, :3], dim=1)  # [N]
        
        # 投影尺度
        projected_scales = scales_3d[:, :2] * focal_length / (distance_to_camera.unsqueeze(1) + self.config.epsilon)
        
        # 2D协方差矩阵 (简化为对角阵)
        cov_2d = torch.zeros(N, 2, 2, device=device)
        cov_2d[:, 0, 0] = projected_scales[:, 0] ** 2
        cov_2d[:, 1, 1] = projected_scales[:, 1] ** 2
        
        return {
            'centers': projected_points,     # [N, 2]
            'covariances': cov_2d,          # [N, 2, 2]
            'scales': projected_scales       # [N, 2]
        }
    
    def _rasterize_weights(self,
                          gaussian_2d_params: Dict[str, torch.Tensor],
                          opacity: torch.Tensor,
                          concept_weights: torch.Tensor,
                          depths: torch.Tensor,
                          gaussian_ids: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """权重光栅化"""
        device = opacity.device
        N = opacity.shape[0]
        H, W = self.config.image_height, self.config.image_width
        num_concepts = self.config.num_concepts
        
        # 初始化输出
        weight_maps = torch.zeros(num_concepts, H, W, device=device)
        alpha_map = torch.zeros(H, W, device=device)
        depth_map = torch.full((H, W), float('inf'), device=device)
        id_map = torch.full((H, W), -1, dtype=torch.long, device=device)
        
        # 创建像素网格
        y_coords, x_coords = torch.meshgrid(
            torch.arange(H, device=device),
            torch.arange(W, device=device),
            indexing='ij'
        )
        pixel_coords = torch.stack([x_coords.flatten(), y_coords.flatten()], dim=1).float()  # [H*W, 2]
        
        # 为每个高斯点计算贡献
        for i in range(N):
            center = gaussian_2d_params['centers'][i]      # [2]
            cov = gaussian_2d_params['covariances'][i]     # [2, 2]
            alpha = opacity[i, 0]                          # scalar
            weights = concept_weights[i]                   # [num_concepts]
            depth = depths[i]                              # scalar
            gauss_id = gaussian_ids[i]                     # scalar
            
            # 计算像素到高斯中心的距离
            diff = pixel_coords - center.unsqueeze(0)     # [H*W, 2]
            
            # 计算高斯权重
            # 简化版本：使用欧式距离
            distances_sq = torch.sum(diff ** 2, dim=1)    # [H*W]
            scale = torch.diag(cov).mean()                 # 平均尺度
            gaussian_values = torch.exp(-distances_sq / (2 * scale + self.config.epsilon))  # [H*W]
            
            # 应用alpha和阈值
            contributions = alpha * gaussian_values        # [H*W]
            valid_contributions = contributions > self.config.alpha_threshold
            
            if valid_contributions.any():
                # 转换回2D索引
                valid_indices = torch.where(valid_contributions)[0]
                valid_y = valid_indices // W
                valid_x = valid_indices % W
                valid_contribs = contributions[valid_contributions]
                
                # 深度测试
                for j, (y, x, contrib) in enumerate(zip(valid_y, valid_x, valid_contribs)):
                    if depth < depth_map[y, x]:
                        # 更新深度和ID
                        depth_map[y, x] = depth
                        id_map[y, x] = gauss_id
                        
                        # 累积权重
                        for k in range(num_concepts):
                            weight_maps[k, y, x] += contrib * weights[k]
                        
                        alpha_map[y, x] += contrib
        
        # Alpha混合后处理
        alpha_map = torch.clamp(alpha_map, 0.0, 1.0)
        
        # 无穷深度设为0
        depth_map[depth_map == float('inf')] = 0.0
        
        return weight_maps, alpha_map, depth_map, id_map
    
    def _apply_spatial_smoothing(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """应用空间平滑"""
        if not hasattr(self, 'smoothing_kernel'):
            return weight_maps
        
        num_concepts = weight_maps.shape[0]
        smoothed_maps = torch.zeros_like(weight_maps)
        
        for i in range(num_concepts):
            # 对每个概念通道独立平滑
            weight_channel = weight_maps[i:i+1].unsqueeze(0)  # [1, 1, H, W]
            smoothed_channel = F.conv2d(
                weight_channel, 
                self.smoothing_kernel, 
                padding=self.config.smoothing_kernel_size//2
            )
            smoothed_maps[i] = smoothed_channel.squeeze()
        
        return smoothed_maps
    
    def _normalize_weights(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """权重归一化"""
        if self.config.weight_normalization == "softmax":
            # 跨概念维度的softmax
            weight_maps = F.softmax(weight_maps, dim=0)
        elif self.config.weight_normalization == "sum_to_one":
            # 简单的和归一化
            weight_sum = weight_maps.sum(dim=0, keepdim=True) + self.config.epsilon
            weight_maps = weight_maps / weight_sum
        
        return weight_maps

class RCAWeightRenderer(nn.Module):
    """
    🔥 RCA权重渲染器主控制器
    
    功能:
    - 管理多视角权重渲染
    - 整合权重光栅化和后处理
    - 提供批处理和优化功能
    """
    
    def __init__(self, config: WeightRendererConfig = None):
        super().__init__()
        self.config = config or WeightRendererConfig()
        
        # 核心光栅化器
        self.rasterizer = GaussianWeightRasterizer(self.config)
        
        # 统计信息
        self.render_stats = {
            'total_renders': 0,
            'total_gaussians_processed': 0,
            'average_valid_gaussians_ratio': 0.0,
            'rendering_times': []
        }
        
        print(f"🎯 RCA权重渲染器初始化完成")
    
    def render_concept_weights(self,
                              gaussian_data: Dict[str, torch.Tensor],
                              concept_weights: torch.Tensor,
                              camera_params: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        🔥 渲染概念权重图
        
        Args:
            gaussian_data: 3D高斯数据字典
                - 'xyz': [N, 3] 位置
                - 'scales': [N, 3] 尺度  
                - 'rotations': [N, 4] 旋转
                - 'opacity': [N, 1] 透明度
            concept_weights: [N, num_concepts] 概念权重
            camera_params: 相机参数
            
        Returns:
            render_result: 渲染结果
        """
        import time
        start_time = time.time()
        
        # 验证输入
        self._validate_inputs(gaussian_data, concept_weights, camera_params)
        
        # 执行渲染
        result = self.rasterizer(
            gaussian_positions=gaussian_data['xyz'],
            gaussian_scales=gaussian_data['scales'],
            gaussian_rotations=gaussian_data['rotations'],
            gaussian_opacity=gaussian_data['opacity'],
            concept_weights=concept_weights,
            camera_params=camera_params
        )
        
        # 更新统计
        render_time = time.time() - start_time
        self._update_render_stats(gaussian_data['xyz'].shape[0], result['valid_gaussians_count'], render_time)
        
        return result
    
    def render_multi_view_weights(self,
                                 gaussian_data: Dict[str, torch.Tensor],
                                 concept_weights: torch.Tensor,
                                 multi_camera_params: List[Dict[str, torch.Tensor]]) -> List[Dict[str, torch.Tensor]]:
        """
        🔥 多视角权重渲染
        
        Args:
            gaussian_data: 3D高斯数据
            concept_weights: 概念权重
            multi_camera_params: 多个相机参数
            
        Returns:
            multi_view_results: 多视角渲染结果列表
        """
        results = []
        
        for i, camera_params in enumerate(multi_camera_params):
            print(f"   渲染视角 {i+1}/{len(multi_camera_params)}")
            
            result = self.render_concept_weights(gaussian_data, concept_weights, camera_params)
            result['view_index'] = i
            results.append(result)
        
        return results
    
    def _validate_inputs(self, 
                        gaussian_data: Dict[str, torch.Tensor],
                        concept_weights: torch.Tensor,
                        camera_params: Dict[str, torch.Tensor]):
        """验证输入数据"""
        required_gaussian_keys = ['xyz', 'scales', 'rotations', 'opacity']
        for key in required_gaussian_keys:
            if key not in gaussian_data:
                raise ValueError(f"缺少高斯数据键: {key}")
        
        N = gaussian_data['xyz'].shape[0]
        
        # 检查形状一致性
        if concept_weights.shape[0] != N:
            raise ValueError(f"概念权重数量({concept_weights.shape[0]})与高斯点数量({N})不匹配")
        
        if concept_weights.shape[1] != self.config.num_concepts:
            raise ValueError(f"概念权重维度({concept_weights.shape[1]})与配置({self.config.num_concepts})不匹配")
        
        # 检查相机参数
        required_camera_keys = ['view_matrix', 'projection_matrix']
        for key in required_camera_keys:
            if key not in camera_params:
                raise ValueError(f"缺少相机参数: {key}")
    
    def _update_render_stats(self, total_gaussians: int, valid_gaussians: int, render_time: float):
        """更新渲染统计"""
        self.render_stats['total_renders'] += 1
        self.render_stats['total_gaussians_processed'] += total_gaussians
        
        # 更新有效高斯比例
        current_ratio = valid_gaussians / total_gaussians if total_gaussians > 0 else 0.0
        old_avg = self.render_stats['average_valid_gaussians_ratio']
        total_renders = self.render_stats['total_renders']
        self.render_stats['average_valid_gaussians_ratio'] = (old_avg * (total_renders - 1) + current_ratio) / total_renders
        
        # 记录渲染时间
        self.render_stats['rendering_times'].append(render_time)
        if len(self.render_stats['rendering_times']) > 100:
            self.render_stats['rendering_times'] = self.render_stats['rendering_times'][-100:]
    
    def get_render_statistics(self) -> Dict[str, Any]:
        """获取渲染统计信息"""
        stats = self.render_stats.copy()
        
        if stats['rendering_times']:
            stats['average_render_time'] = sum(stats['rendering_times']) / len(stats['rendering_times'])
            stats['min_render_time'] = min(stats['rendering_times'])
            stats['max_render_time'] = max(stats['rendering_times'])
        
        return stats
    
    def visualize_weight_maps(self, render_result: Dict[str, torch.Tensor]) -> Dict[str, np.ndarray]:
        """
        🔥 可视化权重图
        
        Args:
            render_result: 渲染结果
            
        Returns:
            visualization: 可视化结果字典
        """
        weight_maps = render_result['weight_maps'].cpu().numpy()  # [num_concepts, H, W]
        alpha_map = render_result['alpha_map'].cpu().numpy()      # [H, W]
        depth_map = render_result['depth_map'].cpu().numpy()      # [H, W]
        
        num_concepts, H, W = weight_maps.shape
        
        # 创建彩色权重可视化
        concept_colors = [
            [1.0, 0.0, 0.0],  # concept 0: 红色 (背景)
            [0.0, 1.0, 0.0],  # concept 1: 绿色 (修复)
            [0.0, 0.0, 1.0],  # concept 2: 蓝色 (边界)
        ]
        
        # 如果概念数超过3，生成更多颜色
        while len(concept_colors) < num_concepts:
            # 随机生成颜色
            color = np.random.rand(3).tolist()
            concept_colors.append(color)
        
        # 加权颜色混合
        colored_weight_map = np.zeros((H, W, 3))
        for i in range(num_concepts):
            weight = weight_maps[i]  # [H, W]
            color = np.array(concept_colors[i])  # [3]
            
            for c in range(3):
                colored_weight_map[:, :, c] += weight * color[c]
        
        # 归一化到[0, 1]
        colored_weight_map = np.clip(colored_weight_map, 0.0, 1.0)
        
        # 深度图归一化
        if depth_map.max() > 0:
            normalized_depth = depth_map / depth_map.max()
        else:
            normalized_depth = depth_map
        
        visualization = {
            'colored_weight_map': colored_weight_map,      # [H, W, 3] 彩色权重图
            'individual_weight_maps': weight_maps,          # [num_concepts, H, W] 单独权重图
            'alpha_map': alpha_map,                        # [H, W] Alpha图
            'depth_map': normalized_depth,                 # [H, W] 归一化深度图
            'concept_colors': concept_colors[:num_concepts] # 概念颜色
        }
        
        return visualization

# 使用示例和测试
if __name__ == "__main__":
    # 创建配置
    config = WeightRendererConfig(
        image_height=512,
        image_width=512,
        num_concepts=3
    )
    
    # 创建渲染器
    renderer = RCAWeightRenderer(config)
    
    # 模拟数据
    N = 1000
    gaussian_data = {
        'xyz': torch.randn(N, 3),
        'scales': torch.rand(N, 3) * 0.1 + 0.01,
        'rotations': torch.randn(N, 4),
        'opacity': torch.rand(N, 1)
    }
    
    # 归一化旋转四元数
    gaussian_data['rotations'] = F.normalize(gaussian_data['rotations'], dim=1)
    
    # 模拟概念权重
    concept_weights = torch.rand(N, 3)
    concept_weights = F.softmax(concept_weights, dim=1)
    
    # 模拟相机参数
    camera_params = {
        'view_matrix': torch.eye(4),
        'projection_matrix': torch.tensor([
            [2.0, 0.0, 0.0, 0.0],
            [0.0, 2.0, 0.0, 0.0],
            [0.0, 0.0, -1.0, -1.0],
            [0.0, 0.0, -2.0, 0.0]
        ]),
        'focal_length': torch.tensor(500.0)
    }
    
    # 测试渲染
    print("🧪 测试权重渲染...")
    result = renderer.render_concept_weights(gaussian_data, concept_weights, camera_params)
    
    print(f"   权重图形状: {result['weight_maps'].shape}")
    print(f"   Alpha图形状: {result['alpha_map'].shape}")
    print(f"   深度图形状: {result['depth_map'].shape}")
    print(f"   有效高斯点: {result['valid_gaussians_count']}/{result['total_gaussians']}")
    
    # 测试可视化
    print("🧪 测试权重可视化...")
    visualization = renderer.visualize_weight_maps(result)
    print(f"   彩色权重图形状: {visualization['colored_weight_map'].shape}")
    print(f"   概念颜色: {visualization['concept_colors']}")
    
    # 测试统计
    stats = renderer.get_render_statistics()
    print(f"📊 渲染统计: {stats}")
    
    print("🎉 RCA权重渲染器测试完成")