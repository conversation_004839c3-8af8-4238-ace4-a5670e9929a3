import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import os
import time
import json
import numpy as np

from .rca_spatial_attention import RCASpatialAttentionNetwork, RCASpatialAttentionConfig
from .rca_weight_renderer import RCAWeight<PERSON>ender<PERSON>, WeightRendererConfig
from .rca_cism_integration import RCACISMIntegrator, RCACISMIntegrationConfig

@dataclass
class Stage3ControllerConfig:
    """阶段3控制器配置"""
    # 子组件配置
    rca_config: RCASpatialAttentionConfig = None
    renderer_config: WeightRendererConfig = None
    integration_config: RCACISMIntegrationConfig = None
    
    # 训练配置
    max_training_epochs: int = 100
    learning_rate: float = 1e-4
    weight_decay: float = 1e-5
    batch_size: int = 1
    
    # 损失权重
    classification_loss_weight: float = 1.0
    spatial_consistency_weight: float = 0.1
    temporal_consistency_weight: float = 0.05
    
    # 验证配置
    validation_interval: int = 10
    save_interval: int = 50
    log_interval: int = 5
    
    # 早停配置
    early_stopping_patience: int = 20
    early_stopping_threshold: float = 1e-5
    
    # 输出配置
    save_visualizations: bool = True
    save_intermediate_results: bool = True

class Stage3MainController:
    """
    🔥 阶段3主控制器
    
    功能:
    - 管理RCA网络训练和优化
    - 整合RCA与CISM的协同工作
    - 实现端到端的空间语义控制
    """
    
    def __init__(self, config: Stage3ControllerConfig = None):
        self.config = config or Stage3ControllerConfig()
        
        # 初始化默认子配置
        self._init_default_configs()
        
        # 核心组件
        self.rca_integrator = RCACISMIntegrator(self.config.integration_config)
        
        # 优化器
        self.optimizer = None
        self.scheduler = None
        
        # 训练状态
        self.current_epoch = 0
        self.best_loss = float('inf')
        self.patience_counter = 0
        
        # 训练日志
        self.training_log = {
            'epoch_losses': [],
            'validation_losses': [],
            'rca_network_stats': [],
            'integration_stats': []
        }
        
        print(f"🎯 阶段3主控制器初始化完成")
    
    def _init_default_configs(self):
        """初始化默认配置"""
        if self.config.rca_config is None:
            self.config.rca_config = RCASpatialAttentionConfig(
                hidden_dims=[256, 512, 256],
                num_attention_heads=8,
                num_concepts=3
            )
        
        if self.config.renderer_config is None:
            self.config.renderer_config = WeightRendererConfig(
                num_concepts=3,
                image_height=512,
                image_width=512
            )
        
        if self.config.integration_config is None:
            self.config.integration_config = RCACISMIntegrationConfig(
                rca_config=self.config.rca_config,
                renderer_config=self.config.renderer_config
            )
    
    def setup_training(self, gaussians_data: Dict[str, torch.Tensor]):
        """
        🔥 设置训练配置
        
        Args:
            gaussians_data: 3D高斯数据样本 (用于确定参数)
        """
        print("🚀 设置阶段3训练配置...")
        
        # 1. 🔥 设置优化器
        parameters = list(self.rca_integrator.parameters())
        
        self.optimizer = optim.AdamW(
            parameters,
            lr=self.config.learning_rate,
            weight_decay=self.config.weight_decay
        )
        
        # 2. 🔥 设置学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=self.config.max_training_epochs,
            eta_min=self.config.learning_rate * 0.01
        )
        
        # 3. 🔥 验证数据格式
        self._validate_data_format(gaussians_data)
        
        print(f"   优化器: AdamW (lr={self.config.learning_rate})")
        print(f"   调度器: CosineAnnealingLR")
        print(f"   可训练参数: {sum(p.numel() for p in parameters if p.requires_grad):,}")
    
    def _validate_data_format(self, gaussians_data: Dict[str, torch.Tensor]):
        """验证数据格式"""
        required_keys = ['xyz', 'rgb', 'opacity', 'scales', 'concept_id']
        for key in required_keys:
            if key not in gaussians_data:
                raise ValueError(f"缺少必需的数据键: {key}")
        
        N = gaussians_data['xyz'].shape[0]
        print(f"   验证数据格式: {N} 个高斯点")
    
            # 在前面代码基础上继续...

    def train_rca_network(self, num_epochs=1000):
        """训练RCA网络"""
        print(f"开始训练RCA网络，epochs: {num_epochs}")
        
        self.rca_network.train()
        
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            num_batches = 0
            
            # 随机选择训练视角
            for view_idx in range(0, len(self.camera_params), 2):  # 每隔一个视角训练
                try:
                    # 获取当前视角参数
                    camera_param = self.camera_params[view_idx]
                    
                    # 渲染当前视角的高斯点云
                    rendered_image = self._render_view(view_idx)
                    if rendered_image is None:
                        continue
                    
                    # 获取RCA权重
                    rca_weights = self.rca_network(self.gaussians_3d)
                    
                    # 渲染权重图
                    weight_maps = self.weight_renderer.render_weights(
                        self.gaussians_3d, rca_weights, camera_param
                    )
                    
                    # 计算RCA训练损失
                    rca_loss = self._compute_rca_loss(
                        weight_maps, rendered_image, view_idx
                    )
                    
                    # 反向传播
                    self.rca_optimizer.zero_grad()
                    rca_loss.backward()
                    torch.nn.utils.clip_grad_norm_(
                        self.rca_network.parameters(), max_norm=1.0
                    )
                    self.rca_optimizer.step()
                    
                    epoch_loss += rca_loss.item()
                    num_batches += 1
                    
                except Exception as e:
                    print(f"视角{view_idx}训练出错: {str(e)}")
                    continue
            
            # 学习率调度
            if hasattr(self, 'rca_scheduler'):
                self.rca_scheduler.step()
            
            # 记录训练进度
            if num_batches > 0:
                avg_loss = epoch_loss / num_batches
                self.training_stats['rca_losses'].append(avg_loss)
                
                if epoch % 100 == 0:
                    print(f"RCA训练 Epoch {epoch}/{num_epochs}, 平均损失: {avg_loss:.6f}")
                    
                    # 保存中间结果
                    if epoch % 500 == 0:
                        self._save_rca_checkpoint(epoch, avg_loss)
    
    def optimize_with_rca_cism(self, num_iterations=5000):
        """使用RCA增强的CISM进行优化"""
        print(f"开始RCA-CISM联合优化，迭代次数: {num_iterations}")
        
        self.rca_network.eval()  # RCA网络进入评估模式
        
        for iteration in range(num_iterations):
            try:
                # 随机选择优化视角
                view_idx = torch.randint(0, len(self.camera_params), (1,)).item()
                camera_param = self.camera_params[view_idx]
                
                # 渲染当前视角
                rendered_image = self._render_view(view_idx)
                if rendered_image is None:
                    continue
                
                # 获取RCA权重（不计算梯度，因为RCA网络已训练完成）
                with torch.no_grad():
                    rca_weights = self.rca_network(self.gaussians_3d)
                    weight_maps = self.weight_renderer.render_weights(
                        self.gaussians_3d, rca_weights, camera_param
                    )
                
                # 使用RCA权重调制CISM引导
                modulated_guidance = self.rca_cism_integrator.modulate_guidance(
                    rendered_image, weight_maps, view_idx
                )
                
                # 计算总损失
                total_loss = self._compute_total_loss(
                    rendered_image, modulated_guidance, weight_maps, view_idx
                )
                
                # 优化3D高斯参数
                self.gaussians_optimizer.zero_grad()
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(
                    self.gaussians_3d.parameters(), max_norm=2.0
                )
                self.gaussians_optimizer.step()
                
                # 记录优化进度
                self.training_stats['optimization_losses'].append(total_loss.item())
                
                if iteration % 100 == 0:
                    print(f"RCA-CISM优化 迭代 {iteration}/{num_iterations}, "
                          f"损失: {total_loss.item():.6f}")
                
                # 定期保存和验证
                if iteration % 1000 == 0:
                    self._save_optimization_checkpoint(iteration, total_loss.item())
                    self._validate_optimization_progress(iteration)
                
            except Exception as e:
                print(f"优化迭代{iteration}出错: {str(e)}")
                continue
    
    def _compute_rca_loss(self, weight_maps, rendered_image, view_idx):
        """计算RCA训练损失"""
        total_loss = 0.0
        
        # 1. 概念分离损失 - 不同概念的权重应该空间分离
        concept_separation_loss = 0.0
        for i in range(self.config['num_concepts']):
            for j in range(i+1, self.config['num_concepts']):
                weight_i = weight_maps[:, :, i]
                weight_j = weight_maps[:, :, j]
                # 计算重叠惩罚
                overlap = torch.sum(weight_i * weight_j)
                concept_separation_loss += overlap
        
        total_loss += self.config['concept_separation_weight'] * concept_separation_loss
        
        # 2. 空间连续性损失 - 权重应该空间平滑
        spatial_smoothness_loss = 0.0
        for c in range(self.config['num_concepts']):
            weight_c = weight_maps[:, :, c]
            # 计算梯度惩罚
            grad_x = torch.abs(weight_c[1:, :] - weight_c[:-1, :])
            grad_y = torch.abs(weight_c[:, 1:] - weight_c[:, :-1])
            spatial_smoothness_loss += torch.mean(grad_x) + torch.mean(grad_y)
        
        total_loss += self.config['spatial_smoothness_weight'] * spatial_smoothness_loss
        
        # 3. 概念一致性损失 - 权重应该与concept_id一致
        consistency_loss = 0.0
        if hasattr(self.gaussians_3d, 'concept_ids'):
            # 投影concept_id到2D空间
            projected_concepts = self._project_concept_ids(view_idx)
            if projected_concepts is not None:
                for c in range(self.config['num_concepts']):
                    target_mask = (projected_concepts == c).float()
                    predicted_weight = weight_maps[:, :, c]
                    consistency_loss += torch.nn.functional.mse_loss(
                        predicted_weight, target_mask
                    )
        
        total_loss += self.config['consistency_weight'] * consistency_loss
        
        return total_loss
    
    def _compute_total_loss(self, rendered_image, modulated_guidance, weight_maps, view_idx):
        """计算总优化损失"""
        total_loss = 0.0
        
        # 1. 调制后的CISM损失
        cism_loss = self.rca_cism_integrator.compute_modulated_sds_loss(
            rendered_image, modulated_guidance
        )
        total_loss += self.config['cism_weight'] * cism_loss
        
        # 2. 空间一致性损失
        spatial_consistency_loss = self.rca_cism_integrator.compute_spatial_consistency_loss(
            weight_maps
        )
        total_loss += self.config['spatial_consistency_weight'] * spatial_consistency_loss
        
        # 3. 高斯正则化损失
        if hasattr(self.gaussians_3d, 'scales'):
            scale_reg = torch.mean(torch.exp(self.gaussians_3d.scales))
            total_loss += self.config['scale_regularization'] * scale_reg
        
        if hasattr(self.gaussians_3d, 'rotations'):
            rotation_reg = torch.mean(torch.abs(self.gaussians_3d.rotations))
            total_loss += self.config['rotation_regularization'] * rotation_reg
        
        return total_loss
    
    def _project_concept_ids(self, view_idx):
        """将3D concept_id投影到2D空间"""
        try:
            if not hasattr(self.gaussians_3d, 'concept_ids'):
                return None
            
            camera_param = self.camera_params[view_idx]
            
            # 使用投影系统进行投影
            if hasattr(self, 'projection_system'):
                projected_ids = self.projection_system.project_concept_ids(
                    self.gaussians_3d, camera_param
                )
                return projected_ids
            else:
                # 简化投影实现
                return None
                
        except Exception as e:
            print(f"concept_id投影出错: {str(e)}")
            return None
    
    def _save_rca_checkpoint(self, epoch, loss):
        """保存RCA训练检查点"""
        try:
            checkpoint_path = os.path.join(
                self.config['output_dir'], 
                f"rca_checkpoint_epoch_{epoch}.pth"
            )
            
            torch.save({
                'epoch': epoch,
                'rca_network_state_dict': self.rca_network.state_dict(),
                'rca_optimizer_state_dict': self.rca_optimizer.state_dict(),
                'loss': loss,
                'training_stats': self.training_stats
            }, checkpoint_path)
            
            print(f"RCA检查点已保存: {checkpoint_path}")
            
        except Exception as e:
            print(f"保存RCA检查点失败: {str(e)}")
    
    def _save_optimization_checkpoint(self, iteration, loss):
        """保存优化检查点"""
        try:
            checkpoint_path = os.path.join(
                self.config['output_dir'], 
                f"optimization_checkpoint_iter_{iteration}.pth"
            )
            
            torch.save({
                'iteration': iteration,
                'gaussians_state_dict': self.gaussians_3d.state_dict(),
                'gaussians_optimizer_state_dict': self.gaussians_optimizer.state_dict(),
                'loss': loss,
                'training_stats': self.training_stats
            }, checkpoint_path)
            
            print(f"优化检查点已保存: {checkpoint_path}")
            
        except Exception as e:
            print(f"保存优化检查点失败: {str(e)}")
    
    def _validate_optimization_progress(self, iteration):
        """验证优化进度"""
        try:
            print(f"验证优化进度 - 迭代 {iteration}")
            
            # 渲染几个测试视角
            test_views = [0, len(self.camera_params)//4, len(self.camera_params)//2]
            
            for view_idx in test_views:
                if view_idx < len(self.camera_params):
                    rendered_image = self._render_view(view_idx)
                    if rendered_image is not None:
                        # 保存测试渲染结果
                        save_path = os.path.join(
                            self.config['output_dir'],
                            f"test_render_iter_{iteration}_view_{view_idx}.png"
                        )
                        self._save_image_tensor(rendered_image, save_path)
            
            print(f"优化进度验证完成 - 迭代 {iteration}")
            
        except Exception as e:
            print(f"验证优化进度失败: {str(e)}")
    
    def _save_image_tensor(self, image_tensor, save_path):
        """保存图像张量为文件"""
        try:
            # 确保图像在正确范围内
            if image_tensor.max() <= 1.0:
                image_tensor = image_tensor * 255.0
            
            image_tensor = torch.clamp(image_tensor, 0, 255).byte()
            
            # 转换为PIL图像并保存
            if len(image_tensor.shape) == 3:
                if image_tensor.shape[0] == 3:  # CHW格式
                    image_tensor = image_tensor.permute(1, 2, 0)  # 转为HWC
                
                image_np = image_tensor.cpu().numpy()
                from PIL import Image
                image = Image.fromarray(image_np)
                image.save(save_path)
                
        except Exception as e:
            print(f"保存图像失败: {str(e)}")
    
    def save_final_results(self):
        """保存最终结果"""
        try:
            print("保存最终RCA优化结果...")
            
            # 1. 保存优化后的3D高斯点云
            final_gaussians_path = os.path.join(
                self.config['output_dir'], 
                "stage3_final_gaussians.ply"
            )
            self._save_gaussians_ply(final_gaussians_path)
            
            # 2. 保存RCA网络
            rca_model_path = os.path.join(
                self.config['output_dir'], 
                "stage3_rca_network.pth"
            )
            torch.save(self.rca_network.state_dict(), rca_model_path)
            
            # 3. 保存训练统计
            stats_path = os.path.join(
                self.config['output_dir'], 
                "stage3_training_stats.json"
            )
            with open(stats_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(self.training_stats, f, indent=2, ensure_ascii=False)
            
            # 4. 渲染所有视角的最终结果
            self._render_all_final_views()
            
            print("RCA阶段3结果保存完成!")
            
        except Exception as e:
            print(f"保存最终结果失败: {str(e)}")
    
    def _save_gaussians_ply(self, save_path):
        """保存3D高斯点云为PLY文件"""
        try:
            # 这里需要将高斯参数转换为PLY格式
            # 具体实现取决于高斯点云的数据结构
            print(f"保存高斯点云到: {save_path}")
            
            # 简化实现 - 实际需要根据具体的高斯点云格式来实现
            if hasattr(self.gaussians_3d, 'save_ply'):
                self.gaussians_3d.save_ply(save_path)
            else:
                print("警告: 高斯点云对象没有save_ply方法")
                
        except Exception as e:
            print(f"保存高斯点云PLY文件失败: {str(e)}")
    
    def _render_all_final_views(self):
        """渲染所有视角的最终结果"""
        try:
            print("渲染所有视角的最终结果...")
            
            final_renders_dir = os.path.join(
                self.config['output_dir'], 
                "final_renders"
            )
            os.makedirs(final_renders_dir, exist_ok=True)
            
            for view_idx in range(len(self.camera_params)):
                try:
                    rendered_image = self._render_view(view_idx)
                    if rendered_image is not None:
                        save_path = os.path.join(
                            final_renders_dir,
                            f"final_render_{view_idx:04d}.png"
                        )
                        self._save_image_tensor(rendered_image, save_path)
                        
                except Exception as e:
                    print(f"渲染视角{view_idx}失败: {str(e)}")
                    continue
            
            print("所有视角最终渲染完成!")
            
        except Exception as e:
            print(f"渲染所有最终视角失败: {str(e)}")

def main():
    """阶段3主函数"""
    try:
        print("=== InFusion 阶段3: RCA区域控制系统 ===")
        
        # 配置参数
        config = {
            'stage2_output_dir': './output/stage2',
            'output_dir': './output/stage3',
            'num_concepts': 3,
            'hidden_dim': 256,
            'num_attention_heads': 8,
            'learning_rate': 0.001,
            'rca_training_epochs': 1000,
            'optimization_iterations': 5000,
            'concept_separation_weight': 1.0,
            'spatial_smoothness_weight': 0.5,
            'consistency_weight': 2.0,
            'cism_weight': 1.0,
            'spatial_consistency_weight': 0.3,
            'scale_regularization': 0.01,
            'rotation_regularization': 0.01,
            'device': 'cuda' if torch.cuda.is_available() else 'cpu'
        }
        
        # 创建输出目录
        os.makedirs(config['output_dir'], exist_ok=True)
        
        # 初始化阶段3控制器
        controller = Stage3MainController(config)
        
        # 加载阶段2结果
        if not controller.load_stage2_results():
            print("错误: 无法加载阶段2结果")
            return False
        
        # 初始化RCA系统
        if not controller.initialize_rca_system():
            print("错误: RCA系统初始化失败")
            return False
        
        print("阶段3系统初始化完成!")
        
        # 训练RCA网络
        controller.train_rca_network(config['rca_training_epochs'])
        
        # RCA-CISM联合优化
        controller.optimize_with_rca_cism(config['optimization_iterations'])
        
        # 保存最终结果
        controller.save_final_results()
        
        print("=== 阶段3: RCA区域控制系统完成! ===")
        return True
        
    except Exception as e:
        print(f"阶段3主函数执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("InFusion 阶段3执行成功!")
    else:
        print("InFusion 阶段3执行失败!")