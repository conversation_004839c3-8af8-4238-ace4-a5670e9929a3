<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_matrix_interpolation</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_matrix_interpolation<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00101.html" title="GLM_GTX_matrix_interpolation ">glm/gtx/matrix_interpolation.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gafefe32ce5a90a135287ba34fac3623bc"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafefe32ce5a90a135287ba34fac3623bc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00337.html#gafefe32ce5a90a135287ba34fac3623bc">axisAngle</a> (mat&lt; 4, 4, T, Q &gt; const &amp;Mat, vec&lt; 3, T, Q &gt; &amp;Axis, T &amp;Angle)</td></tr>
<tr class="memdesc:gafefe32ce5a90a135287ba34fac3623bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the axis and angle of the rotation from a matrix.  <a href="a00337.html#gafefe32ce5a90a135287ba34fac3623bc">More...</a><br /></td></tr>
<tr class="separator:gafefe32ce5a90a135287ba34fac3623bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a788e2f5223397df5c426413ecc2f6b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3a788e2f5223397df5c426413ecc2f6b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00337.html#ga3a788e2f5223397df5c426413ecc2f6b">axisAngleMatrix</a> (vec&lt; 3, T, Q &gt; const &amp;Axis, T const Angle)</td></tr>
<tr class="memdesc:ga3a788e2f5223397df5c426413ecc2f6b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a matrix from axis and angle.  <a href="a00337.html#ga3a788e2f5223397df5c426413ecc2f6b">More...</a><br /></td></tr>
<tr class="separator:ga3a788e2f5223397df5c426413ecc2f6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabbc1c7385a145f04b5c54228965df145"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabbc1c7385a145f04b5c54228965df145"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00337.html#gabbc1c7385a145f04b5c54228965df145">extractMatrixRotation</a> (mat&lt; 4, 4, T, Q &gt; const &amp;Mat)</td></tr>
<tr class="memdesc:gabbc1c7385a145f04b5c54228965df145"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the rotation part of a matrix.  <a href="a00337.html#gabbc1c7385a145f04b5c54228965df145">More...</a><br /></td></tr>
<tr class="separator:gabbc1c7385a145f04b5c54228965df145"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4e67863d150724b10c1ac00972dc958c"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4e67863d150724b10c1ac00972dc958c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00337.html#ga4e67863d150724b10c1ac00972dc958c">interpolate</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m1, mat&lt; 4, 4, T, Q &gt; const &amp;m2, T const Delta)</td></tr>
<tr class="memdesc:ga4e67863d150724b10c1ac00972dc958c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a interpolation of 4 * 4 matrixes.  <a href="a00337.html#ga4e67863d150724b10c1ac00972dc958c">More...</a><br /></td></tr>
<tr class="separator:ga4e67863d150724b10c1ac00972dc958c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00101.html" title="GLM_GTX_matrix_interpolation ">glm/gtx/matrix_interpolation.hpp</a>&gt; to use the features of this extension. </p>
<p>Allows to directly interpolate two matrices. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gafefe32ce5a90a135287ba34fac3623bc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::axisAngle </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Mat</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; &amp;&#160;</td>
          <td class="paramname"><em>Axis</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>Angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the axis and angle of the rotation from a matrix. </p>
<p>From GLM_GTX_matrix_interpolation extension. </p>

</div>
</div>
<a class="anchor" id="ga3a788e2f5223397df5c426413ecc2f6b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::axisAngleMatrix </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Axis</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const&#160;</td>
          <td class="paramname"><em>Angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a matrix from axis and angle. </p>
<p>From GLM_GTX_matrix_interpolation extension. </p>

</div>
</div>
<a class="anchor" id="gabbc1c7385a145f04b5c54228965df145"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::extractMatrixRotation </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Mat</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the rotation part of a matrix. </p>
<p>From GLM_GTX_matrix_interpolation extension. </p>

</div>
</div>
<a class="anchor" id="ga4e67863d150724b10c1ac00972dc958c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::interpolate </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const&#160;</td>
          <td class="paramname"><em>Delta</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a interpolation of 4 * 4 matrixes. </p>
<p>From GLM_GTX_matrix_interpolation extension. Warning! works only with rotation and/or translation matrixes, scale will generate unexpected results. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
