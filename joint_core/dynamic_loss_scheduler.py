"""
Claude Sonnet 4 - 动态损失调度器
基于用户建议实现的智能损失权重调度系统

用户建议核心要点:
1. RCA监督信号动态调整: 早期高权重拟合初始concept_id，后期低权重让CISM主导
2. 损失权重细致调整: 三阶段权重调度
3. 平滑过渡机制: 新损失预热避免训练震荡
"""

import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Optional
import numpy as np
import logging

class DynamicLossScheduler:
    """
    🔥 动态损失调度器
    
    基于用户建议实现的核心功能:
    - 动态RCA监督权重调整
    - 三阶段渐进式训练调度
    - 平滑过渡和预热机制
    """
    
    def __init__(
        self,
        total_iterations: int = 25000,
        stage_a_end: int = 5000,    # 仅3DGS阶段
        stage_b_end: int = 15000,   # 3DGS + CISM阶段
        stage_c_end: int = 25000,   # 完整联合优化阶段
        warmup_iterations: int = 500,
        device: str = "cuda"
    ):
        self.total_iterations = total_iterations
        self.stage_a_end = stage_a_end
        self.stage_b_end = stage_b_end
        self.stage_c_end = stage_c_end
        self.warmup_iterations = warmup_iterations
        self.device = device
        
        # 基础权重配置
        self.base_weights = {
            "stage_a": {"base": 1.0, "cism": 0.0, "rca": 0.0},
            "stage_b": {"base": 1.0, "cism": 0.1, "rca": 0.0},
            "stage_c": {"base": 1.0, "cism": 0.1, "rca": 0.05}
        }
        
        # RCA监督权重调度参数
        self.rca_supervision_start_weight = 1.0  # 早期高权重
        self.rca_supervision_end_weight = 0.1    # 后期低权重
        
        # 当前状态
        self.current_iteration = 0
        self.current_stage = "stage_a"
        self.stage_transition_history = []
        
        logging.info("DynamicLossScheduler initialized with user-suggested strategy")
    
    def get_current_stage(self, iteration: int) -> str:
        """获取当前训练阶段"""
        if iteration <= self.stage_a_end:
            return "stage_a"
        elif iteration <= self.stage_b_end:
            return "stage_b"
        else:
            return "stage_c"
    
    def get_loss_weights(self, iteration: int) -> Dict[str, float]:
        """
        🔥 获取当前迭代的损失权重
        
        实现用户建议的核心策略:
        1. 三阶段渐进式权重调度
        2. 平滑过渡机制
        3. 动态RCA监督调整
        """
        self.current_iteration = iteration
        current_stage = self.get_current_stage(iteration)
        
        # 检查阶段转换
        if current_stage != self.current_stage:
            self._handle_stage_transition(self.current_stage, current_stage, iteration)
            self.current_stage = current_stage
        
        # 获取基础权重
        base_weights = self.base_weights[current_stage].copy()
        
        # 应用平滑过渡
        if self._is_in_transition_period(iteration):
            base_weights = self._apply_smooth_transition(base_weights, iteration)
        
        # 应用RCA监督动态调整
        if current_stage in ["stage_b", "stage_c"]:
            rca_supervision_weight = self._get_rca_supervision_weight(iteration)
            base_weights["rca_supervision"] = rca_supervision_weight
        else:
            base_weights["rca_supervision"] = 0.0
        
        # 应用预热机制
        if self._is_in_warmup_period(iteration):
            base_weights = self._apply_warmup(base_weights, iteration)
        
        return base_weights
    
    def _get_rca_supervision_weight(self, iteration: int) -> float:
        """
        🔥 实现用户建议的RCA监督权重动态调整
        
        用户建议: "早期迭代中，RCA损失的权重可以高一些，让RCA网络先学习大致的区域划分。
        在CISM开始稳定贡献语义损失后，可以适当降低RCA直接拟合初始concept_id的损失权重，
        允许RCA权重更多地受整体任务（语义修复和一致性）的驱动而调整。"
        """
        if iteration <= self.stage_a_end:
            return 0.0  # 阶段A不使用RCA
        
        # 计算从CISM开始到结束的进度
        cism_start = self.stage_a_end
        cism_progress = (iteration - cism_start) / (self.stage_c_end - cism_start)
        cism_progress = np.clip(cism_progress, 0.0, 1.0)
        
        # 动态权重: 早期高，后期低
        dynamic_weight = self.rca_supervision_start_weight * (1 - cism_progress) + \
                        self.rca_supervision_end_weight * cism_progress
        
        return dynamic_weight
    
    def _handle_stage_transition(self, old_stage: str, new_stage: str, iteration: int):
        """处理阶段转换"""
        transition_info = {
            'from_stage': old_stage,
            'to_stage': new_stage,
            'iteration': iteration,
            'timestamp': torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        }
        
        self.stage_transition_history.append(transition_info)
        
        logging.info(f"Stage transition: {old_stage} -> {new_stage} at iteration {iteration}")
        
        # 特殊处理
        if new_stage == "stage_b":
            logging.info("🔥 Entering CISM stage - applying warmup for CISM loss")
        elif new_stage == "stage_c":
            logging.info("🔥 Entering joint optimization stage - applying warmup for RCA loss")
    
    def _is_in_transition_period(self, iteration: int) -> bool:
        """检查是否在阶段转换期"""
        transition_window = self.warmup_iterations
        
        # 检查是否在任何阶段边界附近
        stage_boundaries = [self.stage_a_end, self.stage_b_end]
        
        for boundary in stage_boundaries:
            if abs(iteration - boundary) <= transition_window:
                return True
        
        return False
    
    def _apply_smooth_transition(self, weights: Dict[str, float], iteration: int) -> Dict[str, float]:
        """
        🔥 应用平滑过渡机制
        
        用户建议: "从一个阶段过渡到下一个阶段时，需要注意学习率的调整和新引入损失的预热，
        以避免训练震荡。例如，新引入的损失项可以从小权重开始，逐渐增加。"
        """
        # 找到最近的阶段边界
        if abs(iteration - self.stage_a_end) <= self.warmup_iterations:
            # A -> B 转换: CISM预热
            progress = (iteration - (self.stage_a_end - self.warmup_iterations)) / (2 * self.warmup_iterations)
            progress = np.clip(progress, 0.0, 1.0)
            
            # 平滑引入CISM
            target_cism_weight = self.base_weights["stage_b"]["cism"]
            weights["cism"] = target_cism_weight * progress
            
        elif abs(iteration - self.stage_b_end) <= self.warmup_iterations:
            # B -> C 转换: RCA预热
            progress = (iteration - (self.stage_b_end - self.warmup_iterations)) / (2 * self.warmup_iterations)
            progress = np.clip(progress, 0.0, 1.0)
            
            # 平滑引入RCA
            target_rca_weight = self.base_weights["stage_c"]["rca"]
            weights["rca"] = target_rca_weight * progress
        
        return weights
    
    def _is_in_warmup_period(self, iteration: int) -> bool:
        """检查是否在预热期"""
        return iteration <= self.warmup_iterations
    
    def _apply_warmup(self, weights: Dict[str, float], iteration: int) -> Dict[str, float]:
        """应用预热机制"""
        warmup_factor = iteration / self.warmup_iterations
        
        # 对所有非基础损失应用预热
        for key in weights:
            if key != "base":
                weights[key] *= warmup_factor
        
        return weights
    
    def get_scheduler_state(self) -> Dict:
        """获取调度器状态"""
        return {
            'current_iteration': self.current_iteration,
            'current_stage': self.current_stage,
            'stage_transition_history': self.stage_transition_history,
            'rca_supervision_weight': self._get_rca_supervision_weight(self.current_iteration)
        }
    
    def visualize_weight_schedule(self, save_path: Optional[str] = None) -> Dict[str, List[float]]:
        """可视化权重调度曲线"""
        iterations = list(range(0, self.total_iterations, 100))
        weight_curves = {
            'base': [],
            'cism': [],
            'rca': [],
            'rca_supervision': []
        }
        
        for iteration in iterations:
            weights = self.get_loss_weights(iteration)
            for key in weight_curves:
                weight_curves[key].append(weights.get(key, 0.0))
        
        # 如果需要保存图表
        if save_path:
            try:
                import matplotlib.pyplot as plt
                
                plt.figure(figsize=(12, 8))
                for key, values in weight_curves.items():
                    plt.plot(iterations, values, label=key, linewidth=2)
                
                plt.axvline(x=self.stage_a_end, color='red', linestyle='--', alpha=0.7, label='Stage A->B')
                plt.axvline(x=self.stage_b_end, color='blue', linestyle='--', alpha=0.7, label='Stage B->C')
                
                plt.xlabel('Iteration')
                plt.ylabel('Loss Weight')
                plt.title('Dynamic Loss Weight Schedule (User-Suggested Strategy)')
                plt.legend()
                plt.grid(True, alpha=0.3)
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                plt.close()
                
                logging.info(f"Weight schedule visualization saved to {save_path}")
            except ImportError:
                logging.warning("matplotlib not available for visualization")
        
        return weight_curves

# 使用示例
if __name__ == "__main__":
    # 创建调度器
    scheduler = DynamicLossScheduler(
        total_iterations=25000,
        stage_a_end=5000,
        stage_b_end=15000,
        stage_c_end=25000
    )
    
    # 测试不同迭代的权重
    test_iterations = [0, 1000, 5000, 5500, 10000, 15000, 15500, 20000, 25000]
    
    print("🧪 Testing Dynamic Loss Scheduler:")
    print("=" * 60)
    
    for iteration in test_iterations:
        weights = scheduler.get_loss_weights(iteration)
        stage = scheduler.get_current_stage(iteration)
        
        print(f"Iteration {iteration:5d} | Stage: {stage} | Weights: {weights}")
    
    # 生成权重调度可视化
    weight_curves = scheduler.visualize_weight_schedule("weight_schedule.png")
    
    print("\n📊 Weight Schedule Generated Successfully!")
    print(f"Scheduler state: {scheduler.get_scheduler_state()}")
