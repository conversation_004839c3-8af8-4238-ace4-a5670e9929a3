<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Modules</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Modules</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here is a list of all modules:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9658;</span><a class="el" href="a00280.html" target="_self">Core features</a></td><td class="desc">Features that implement in C++ the GLSL specification as closely as possible </td></tr>
<tr id="row_0_0_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00241.html" target="_self">Common functions</a></td><td class="desc">Provides GLSL common functions </td></tr>
<tr id="row_0_1_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00242.html" target="_self">Exponential functions</a></td><td class="desc">Provides GLSL exponential functions </td></tr>
<tr id="row_0_2_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00279.html" target="_self">Geometric functions</a></td><td class="desc">These operate on vectors as vectors, not component-wise </td></tr>
<tr id="row_0_3_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00281.html" target="_self">Vector types</a></td><td class="desc">Vector types of two to four components with an exhaustive set of operators </td></tr>
<tr id="row_0_4_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00282.html" target="_self">Vector types with precision qualifiers</a></td><td class="desc">Vector types with precision qualifiers which may result in various precision in term of ULPs </td></tr>
<tr id="row_0_5_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00283.html" target="_self">Matrix types</a></td><td class="desc">Matrix types of with C columns and R rows where C and R are values between 2 to 4 included </td></tr>
<tr id="row_0_6_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00284.html" target="_self">Matrix types with precision qualifiers</a></td><td class="desc">Matrix types with precision qualifiers which may result in various precision in term of ULPs </td></tr>
<tr id="row_0_7_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00370.html" target="_self">Integer functions</a></td><td class="desc">Provides GLSL functions on integer types </td></tr>
<tr id="row_0_8_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00371.html" target="_self">Matrix functions</a></td><td class="desc">Provides GLSL matrix functions </td></tr>
<tr id="row_0_9_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00372.html" target="_self">Floating-Point Pack and Unpack Functions</a></td><td class="desc">Provides GLSL functions to pack and unpack half, single and double-precision floating point values into more compact integer types </td></tr>
<tr id="row_0_10_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00373.html" target="_self">Angle and Trigonometry Functions</a></td><td class="desc">Function parameters specified as angle are assumed to be in units of radians </td></tr>
<tr id="row_0_11_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00374.html" target="_self">Vector Relational Functions</a></td><td class="desc">Relational and equality operators (&lt;, &lt;=, &gt;, &gt;=, ==, !=) are defined to operate on scalars and produce scalar Boolean results </td></tr>
<tr id="row_1_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_1_" class="arrow" onclick="toggleFolder('1_')">&#9658;</span><a class="el" href="a00285.html" target="_self">Stable extensions</a></td><td class="desc">Additional features not specified by GLSL specification </td></tr>
<tr id="row_1_0_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00243.html" target="_self">GLM_EXT_matrix_clip_space</a></td><td class="desc">Defines functions that generate clip space transformation matrices </td></tr>
<tr id="row_1_1_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00244.html" target="_self">GLM_EXT_matrix_common</a></td><td class="desc">Defines functions for common matrix operations </td></tr>
<tr id="row_1_2_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00245.html" target="_self">GLM_EXT_matrix_projection</a></td><td class="desc">Functions that generate common projection transformation matrices </td></tr>
<tr id="row_1_3_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00246.html" target="_self">GLM_EXT_matrix_relational</a></td><td class="desc">Exposes comparison functions for matrix types that take a user defined epsilon values </td></tr>
<tr id="row_1_4_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00247.html" target="_self">GLM_EXT_matrix_transform</a></td><td class="desc">Defines functions that generate common transformation matrices </td></tr>
<tr id="row_1_5_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00248.html" target="_self">GLM_EXT_quaternion_common</a></td><td class="desc">Provides common functions for quaternion types </td></tr>
<tr id="row_1_6_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00249.html" target="_self">GLM_EXT_quaternion_double</a></td><td class="desc">Exposes double-precision floating point quaternion type </td></tr>
<tr id="row_1_7_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00250.html" target="_self">GLM_EXT_quaternion_double_precision</a></td><td class="desc">Exposes double-precision floating point quaternion type with various precision in term of ULPs </td></tr>
<tr id="row_1_8_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00251.html" target="_self">GLM_EXT_quaternion_exponential</a></td><td class="desc">Provides exponential functions for quaternion types </td></tr>
<tr id="row_1_9_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00252.html" target="_self">GLM_EXT_quaternion_float</a></td><td class="desc">Exposes single-precision floating point quaternion type </td></tr>
<tr id="row_1_10_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00253.html" target="_self">GLM_EXT_quaternion_float_precision</a></td><td class="desc">Exposes single-precision floating point quaternion type with various precision in term of ULPs </td></tr>
<tr id="row_1_11_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00254.html" target="_self">GLM_EXT_quaternion_geometric</a></td><td class="desc">Provides geometric functions for quaternion types </td></tr>
<tr id="row_1_12_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00255.html" target="_self">GLM_EXT_quaternion_relational</a></td><td class="desc">Exposes comparison functions for quaternion types that take a user defined epsilon values </td></tr>
<tr id="row_1_13_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00256.html" target="_self">GLM_EXT_quaternion_transform</a></td><td class="desc">Provides transformation functions for quaternion types </td></tr>
<tr id="row_1_14_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00257.html" target="_self">GLM_EXT_quaternion_trigonometric</a></td><td class="desc">Provides trigonometric functions for quaternion types </td></tr>
<tr id="row_1_15_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00258.html" target="_self">GLM_EXT_scalar_common</a></td><td class="desc">Exposes min and max functions for 3 to 4 scalar parameters </td></tr>
<tr id="row_1_16_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00259.html" target="_self">GLM_EXT_scalar_constants</a></td><td class="desc">Provides a list of constants and precomputed useful values </td></tr>
<tr id="row_1_17_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00260.html" target="_self">GLM_EXT_scalar_int_sized</a></td><td class="desc">Exposes sized signed integer scalar types </td></tr>
<tr id="row_1_18_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00261.html" target="_self">GLM_EXT_scalar_integer</a></td><td class="desc">Include &lt;<a class="el" href="a00147.html" title="GLM_EXT_scalar_integer ">glm/ext/scalar_integer.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_1_19_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00262.html" target="_self">GLM_EXT_scalar_relational</a></td><td class="desc">Exposes comparison functions for scalar types that take a user defined epsilon values </td></tr>
<tr id="row_1_20_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00263.html" target="_self">GLM_EXT_scalar_uint_sized</a></td><td class="desc">Exposes sized unsigned integer scalar types </td></tr>
<tr id="row_1_21_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00264.html" target="_self">GLM_EXT_scalar_ulp</a></td><td class="desc">Allow the measurement of the accuracy of a function against a reference implementation </td></tr>
<tr id="row_1_22_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00265.html" target="_self">GLM_EXT_vector_bool1</a></td><td class="desc">Exposes bvec1 vector type </td></tr>
<tr id="row_1_23_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00266.html" target="_self">GLM_EXT_vector_bool1_precision</a></td><td class="desc">Exposes highp_bvec1, mediump_bvec1 and lowp_bvec1 types </td></tr>
<tr id="row_1_24_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00267.html" target="_self">GLM_EXT_vector_common</a></td><td class="desc">Exposes min and max functions for 3 to 4 vector parameters </td></tr>
<tr id="row_1_25_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00268.html" target="_self">GLM_EXT_vector_double1</a></td><td class="desc">Exposes double-precision floating point vector type with one component </td></tr>
<tr id="row_1_26_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00269.html" target="_self">GLM_EXT_vector_double1_precision</a></td><td class="desc">Exposes highp_dvec1, mediump_dvec1 and lowp_dvec1 types </td></tr>
<tr id="row_1_27_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00270.html" target="_self">GLM_EXT_vector_float1</a></td><td class="desc">Exposes single-precision floating point vector type with one component </td></tr>
<tr id="row_1_28_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00271.html" target="_self">GLM_EXT_vector_float1_precision</a></td><td class="desc">Exposes highp_vec1, mediump_vec1 and lowp_vec1 types </td></tr>
<tr id="row_1_29_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00272.html" target="_self">GLM_EXT_vector_int1</a></td><td class="desc">Exposes ivec1 vector type </td></tr>
<tr id="row_1_30_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00273.html" target="_self">GLM_EXT_vector_int1_precision</a></td><td class="desc">Exposes highp_ivec1, mediump_ivec1 and lowp_ivec1 types </td></tr>
<tr id="row_1_31_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00274.html" target="_self">GLM_EXT_vector_integer</a></td><td class="desc">Include &lt;<a class="el" href="a00222.html" title="GLM_EXT_vector_integer ">glm/ext/vector_integer.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_1_32_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00275.html" target="_self">GLM_EXT_vector_relational</a></td><td class="desc">Exposes comparison functions for vector types that take a user defined epsilon values </td></tr>
<tr id="row_1_33_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00276.html" target="_self">GLM_EXT_vector_uint1</a></td><td class="desc">Exposes uvec1 vector type </td></tr>
<tr id="row_1_34_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00277.html" target="_self">GLM_EXT_vector_uint1_precision</a></td><td class="desc">Exposes highp_uvec1, mediump_uvec1 and lowp_uvec1 types </td></tr>
<tr id="row_1_35_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00278.html" target="_self">GLM_EXT_vector_ulp</a></td><td class="desc">Allow the measurement of the accuracy of a function against a reference implementation </td></tr>
<tr id="row_2_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_2_" class="arrow" onclick="toggleFolder('2_')">&#9658;</span><a class="el" href="a00286.html" target="_self">Recommended extensions</a></td><td class="desc">Additional features not specified by GLSL specification </td></tr>
<tr id="row_2_0_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00288.html" target="_self">GLM_GTC_bitfield</a></td><td class="desc">Include &lt;<a class="el" href="a00009.html" title="GLM_GTC_bitfield ">glm/gtc/bitfield.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_1_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00289.html" target="_self">GLM_GTC_color_space</a></td><td class="desc">Include &lt;<a class="el" href="a00012.html" title="GLM_GTC_color_space ">glm/gtc/color_space.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_2_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00290.html" target="_self">GLM_GTC_constants</a></td><td class="desc">Include &lt;<a class="el" href="a00021.html" title="GLM_GTC_constants ">glm/gtc/constants.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_3_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00291.html" target="_self">GLM_GTC_epsilon</a></td><td class="desc">Include &lt;<a class="el" href="a00024.html" title="GLM_GTC_epsilon ">glm/gtc/epsilon.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_4_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00292.html" target="_self">GLM_GTC_integer</a></td><td class="desc">Include &lt;<a class="el" href="a00041.html" title="GLM_GTC_integer ">glm/gtc/integer.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_5_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00293.html" target="_self">GLM_GTC_matrix_access</a></td><td class="desc">Include &lt;<a class="el" href="a00058.html" title="GLM_GTC_matrix_access ">glm/gtc/matrix_access.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_6_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00294.html" target="_self">GLM_GTC_matrix_integer</a></td><td class="desc">Include &lt;<a class="el" href="a00100.html" title="GLM_GTC_matrix_integer ">glm/gtc/matrix_integer.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_7_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00295.html" target="_self">GLM_GTC_matrix_inverse</a></td><td class="desc">Include &lt;<a class="el" href="a00100.html" title="GLM_GTC_matrix_integer ">glm/gtc/matrix_integer.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_8_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00296.html" target="_self">GLM_GTC_matrix_transform</a></td><td class="desc">Include &lt;<a class="el" href="a00109.html" title="GLM_GTC_matrix_transform ">glm/gtc/matrix_transform.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_9_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00297.html" target="_self">GLM_GTC_noise</a></td><td class="desc">Include &lt;<a class="el" href="a00112.html" title="GLM_GTC_noise ">glm/gtc/noise.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_10_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00298.html" target="_self">GLM_GTC_packing</a></td><td class="desc">Include &lt;<a class="el" href="a00119.html" title="GLM_GTC_packing ">glm/gtc/packing.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_11_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00299.html" target="_self">GLM_GTC_quaternion</a></td><td class="desc">Include &lt;<a class="el" href="a00125.html" title="GLM_GTC_quaternion ">glm/gtc/quaternion.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_12_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00300.html" target="_self">GLM_GTC_random</a></td><td class="desc">Include &lt;<a class="el" href="a00137.html" title="GLM_GTC_random ">glm/gtc/random.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_13_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00301.html" target="_self">GLM_GTC_reciprocal</a></td><td class="desc">Include &lt;<a class="el" href="a00140.html" title="GLM_GTC_reciprocal ">glm/gtc/reciprocal.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_14_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00302.html" target="_self">GLM_GTC_round</a></td><td class="desc">Include &lt;<a class="el" href="a00143.html" title="GLM_GTC_round ">glm/gtc/round.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_15_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00303.html" target="_self">GLM_GTC_type_aligned</a></td><td class="desc">Include &lt;<a class="el" href="a00161.html" title="GLM_GTC_type_aligned ">glm/gtc/type_aligned.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_16_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00304.html" target="_self">GLM_GTC_type_precision</a></td><td class="desc">Include &lt;<a class="el" href="a00174.html" title="GLM_GTC_type_precision ">glm/gtc/type_precision.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_17_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00305.html" target="_self">GLM_GTC_type_ptr</a></td><td class="desc">Include &lt;<a class="el" href="a00175.html" title="GLM_GTC_type_ptr ">glm/gtc/type_ptr.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_18_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00306.html" target="_self">GLM_GTC_ulp</a></td><td class="desc">Include &lt;<a class="el" href="a00182.html" title="GLM_GTC_ulp ">glm/gtc/ulp.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_2_19_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00307.html" target="_self">GLM_GTC_vec1</a></td><td class="desc">Include &lt;<a class="el" href="a00183.html" title="GLM_GTC_vec1 ">glm/gtc/vec1.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_3_" class="arrow" onclick="toggleFolder('3_')">&#9658;</span><a class="el" href="a00287.html" target="_self">Experimental extensions</a></td><td class="desc">Experimental features not specified by GLSL specification </td></tr>
<tr id="row_3_0_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00308.html" target="_self">GLM_GTX_associated_min_max</a></td><td class="desc">Include &lt;<a class="el" href="a00007.html" title="GLM_GTX_associated_min_max ">glm/gtx/associated_min_max.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_1_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00309.html" target="_self">GLM_GTX_bit</a></td><td class="desc">Include &lt;<a class="el" href="a00008.html" title="GLM_GTX_bit ">glm/gtx/bit.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_2_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00310.html" target="_self">GLM_GTX_closest_point</a></td><td class="desc">Include &lt;<a class="el" href="a00010.html" title="GLM_GTX_closest_point ">glm/gtx/closest_point.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_3_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00311.html" target="_self">GLM_GTX_color_encoding</a></td><td class="desc">Include &lt;<a class="el" href="a00011.html" title="GLM_GTX_color_encoding ">glm/gtx/color_encoding.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_4_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00312.html" target="_self">GLM_GTX_color_space</a></td><td class="desc">Include &lt;<a class="el" href="a00013.html" title="GLM_GTX_color_space ">glm/gtx/color_space.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_5_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00313.html" target="_self">GLM_GTX_color_space_YCoCg</a></td><td class="desc">Include &lt;<a class="el" href="a00014.html" title="GLM_GTX_color_space_YCoCg ">glm/gtx/color_space_YCoCg.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_6_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00314.html" target="_self">GLM_GTX_common</a></td><td class="desc">Include &lt;<a class="el" href="a00016.html" title="GLM_GTX_common ">glm/gtx/common.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_7_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00315.html" target="_self">GLM_GTX_compatibility</a></td><td class="desc">Include &lt;<a class="el" href="a00017.html" title="GLM_GTX_compatibility ">glm/gtx/compatibility.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_8_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00316.html" target="_self">GLM_GTX_component_wise</a></td><td class="desc">Include &lt;<a class="el" href="a00018.html" title="GLM_GTX_component_wise ">glm/gtx/component_wise.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_9_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00317.html" target="_self">GLM_GTX_dual_quaternion</a></td><td class="desc">Include &lt;<a class="el" href="a00022.html" title="GLM_GTX_dual_quaternion ">glm/gtx/dual_quaternion.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_10_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00318.html" target="_self">GLM_GTX_easing</a></td><td class="desc">Include &lt;<a class="el" href="a00023.html" title="GLM_GTX_easing ">glm/gtx/easing.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_11_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00319.html" target="_self">GLM_GTX_euler_angles</a></td><td class="desc">Include &lt;<a class="el" href="a00025.html" title="GLM_GTX_euler_angles ">glm/gtx/euler_angles.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_12_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00320.html" target="_self">GLM_GTX_extend</a></td><td class="desc">Include &lt;<a class="el" href="a00028.html" title="GLM_GTX_extend ">glm/gtx/extend.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_13_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00321.html" target="_self">GLM_GTX_extented_min_max</a></td><td class="desc">Include &lt;glm/gtx/extented_min_max.hpp&gt; to use the features of this extension </td></tr>
<tr id="row_3_14_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00322.html" target="_self">GLM_GTX_exterior_product</a></td><td class="desc">Include &lt;<a class="el" href="a00030.html" title="GLM_GTX_exterior_product ">glm/gtx/exterior_product.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_15_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00323.html" target="_self">GLM_GTX_fast_exponential</a></td><td class="desc">Include &lt;<a class="el" href="a00031.html" title="GLM_GTX_fast_exponential ">glm/gtx/fast_exponential.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_16_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00324.html" target="_self">GLM_GTX_fast_square_root</a></td><td class="desc">Include &lt;<a class="el" href="a00032.html" title="GLM_GTX_fast_square_root ">glm/gtx/fast_square_root.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_17_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00325.html" target="_self">GLM_GTX_fast_trigonometry</a></td><td class="desc">Include &lt;<a class="el" href="a00033.html" title="GLM_GTX_fast_trigonometry ">glm/gtx/fast_trigonometry.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_18_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00326.html" target="_self">GLM_GTX_functions</a></td><td class="desc">Include &lt;<a class="el" href="a00034.html" title="GLM_GTX_functions ">glm/gtx/functions.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_19_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00327.html" target="_self">GLM_GTX_gradient_paint</a></td><td class="desc">Include &lt;<a class="el" href="a00038.html" title="GLM_GTX_gradient_paint ">glm/gtx/gradient_paint.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_20_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00328.html" target="_self">GLM_GTX_handed_coordinate_space</a></td><td class="desc">Include &lt;glm/gtx/handed_coordinate_system.hpp&gt; to use the features of this extension </td></tr>
<tr id="row_3_21_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00329.html" target="_self">GLM_GTX_hash</a></td><td class="desc">Include &lt;<a class="el" href="a00040.html" title="GLM_GTX_hash ">glm/gtx/hash.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_22_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00330.html" target="_self">GLM_GTX_integer</a></td><td class="desc">Include &lt;<a class="el" href="a00042.html" title="GLM_GTX_integer ">glm/gtx/integer.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_23_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00331.html" target="_self">GLM_GTX_intersect</a></td><td class="desc">Include &lt;<a class="el" href="a00044.html" title="GLM_GTX_intersect ">glm/gtx/intersect.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_24_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00332.html" target="_self">GLM_GTX_io</a></td><td class="desc">Include &lt;<a class="el" href="a00045.html" title="GLM_GTX_io ">glm/gtx/io.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_25_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00333.html" target="_self">GLM_GTX_log_base</a></td><td class="desc">Include &lt;<a class="el" href="a00046.html" title="GLM_GTX_log_base ">glm/gtx/log_base.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_26_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00334.html" target="_self">GLM_GTX_matrix_cross_product</a></td><td class="desc">Include &lt;<a class="el" href="a00061.html" title="GLM_GTX_matrix_cross_product ">glm/gtx/matrix_cross_product.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_27_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00335.html" target="_self">GLM_GTX_matrix_decompose</a></td><td class="desc">Include &lt;<a class="el" href="a00062.html" title="GLM_GTX_matrix_decompose ">glm/gtx/matrix_decompose.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_28_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00336.html" target="_self">GLM_GTX_matrix_factorisation</a></td><td class="desc">Include &lt;<a class="el" href="a00081.html" title="GLM_GTX_matrix_factorisation ">glm/gtx/matrix_factorisation.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_29_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00337.html" target="_self">GLM_GTX_matrix_interpolation</a></td><td class="desc">Include &lt;<a class="el" href="a00101.html" title="GLM_GTX_matrix_interpolation ">glm/gtx/matrix_interpolation.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_30_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00338.html" target="_self">GLM_GTX_matrix_major_storage</a></td><td class="desc">Include &lt;<a class="el" href="a00103.html" title="GLM_GTX_matrix_major_storage ">glm/gtx/matrix_major_storage.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_31_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00339.html" target="_self">GLM_GTX_matrix_operation</a></td><td class="desc">Include &lt;<a class="el" href="a00104.html" title="GLM_GTX_matrix_operation ">glm/gtx/matrix_operation.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_32_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00340.html" target="_self">GLM_GTX_matrix_query</a></td><td class="desc">Include &lt;<a class="el" href="a00106.html" title="GLM_GTX_matrix_query ">glm/gtx/matrix_query.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_33_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00341.html" target="_self">GLM_GTX_matrix_transform_2d</a></td><td class="desc">Include &lt;<a class="el" href="a00110.html" title="GLM_GTX_matrix_transform_2d ">glm/gtx/matrix_transform_2d.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_34_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00342.html" target="_self">GLM_GTX_mixed_producte</a></td><td class="desc">Include &lt;<a class="el" href="a00111.html" title="GLM_GTX_mixed_producte ">glm/gtx/mixed_product.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_35_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00343.html" target="_self">GLM_GTX_norm</a></td><td class="desc">Include &lt;<a class="el" href="a00113.html" title="GLM_GTX_norm ">glm/gtx/norm.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_36_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00344.html" target="_self">GLM_GTX_normal</a></td><td class="desc">Include &lt;<a class="el" href="a00114.html" title="GLM_GTX_normal ">glm/gtx/normal.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_37_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00345.html" target="_self">GLM_GTX_normalize_dot</a></td><td class="desc">Include &lt;glm/gtx/normalized_dot.hpp&gt; to use the features of this extension </td></tr>
<tr id="row_3_38_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00346.html" target="_self">GLM_GTX_number_precision</a></td><td class="desc">Include &lt;<a class="el" href="a00116.html" title="GLM_GTX_number_precision ">glm/gtx/number_precision.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_39_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00347.html" target="_self">GLM_GTX_optimum_pow</a></td><td class="desc">Include &lt;<a class="el" href="a00117.html" title="GLM_GTX_optimum_pow ">glm/gtx/optimum_pow.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_40_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00348.html" target="_self">GLM_GTX_orthonormalize</a></td><td class="desc">Include &lt;<a class="el" href="a00118.html" title="GLM_GTX_orthonormalize ">glm/gtx/orthonormalize.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_41_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00349.html" target="_self">GLM_GTX_perpendicular</a></td><td class="desc">Include &lt;<a class="el" href="a00121.html" title="GLM_GTX_perpendicular ">glm/gtx/perpendicular.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_42_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00350.html" target="_self">GLM_GTX_polar_coordinates</a></td><td class="desc">Include &lt;<a class="el" href="a00122.html" title="GLM_GTX_polar_coordinates ">glm/gtx/polar_coordinates.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_43_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00351.html" target="_self">GLM_GTX_projection</a></td><td class="desc">Include &lt;<a class="el" href="a00123.html" title="GLM_GTX_projection ">glm/gtx/projection.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_44_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00352.html" target="_self">GLM_GTX_quaternion</a></td><td class="desc">Include &lt;<a class="el" href="a00126.html" title="GLM_GTX_quaternion ">glm/gtx/quaternion.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_45_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00353.html" target="_self">GLM_GTX_range</a></td><td class="desc">Include &lt;<a class="el" href="a00138.html" title="GLM_GTX_range ">glm/gtx/range.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_46_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00354.html" target="_self">GLM_GTX_raw_data</a></td><td class="desc">Include &lt;<a class="el" href="a00139.html" title="GLM_GTX_raw_data ">glm/gtx/raw_data.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_47_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00355.html" target="_self">GLM_GTX_rotate_normalized_axis</a></td><td class="desc">Include &lt;<a class="el" href="a00141.html" title="GLM_GTX_rotate_normalized_axis ">glm/gtx/rotate_normalized_axis.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_48_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00356.html" target="_self">GLM_GTX_rotate_vector</a></td><td class="desc">Include &lt;<a class="el" href="a00142.html" title="GLM_GTX_rotate_vector ">glm/gtx/rotate_vector.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_49_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00357.html" target="_self">GLM_GTX_scalar_relational</a></td><td class="desc">Include &lt;<a class="el" href="a00150.html" title="GLM_GTX_scalar_relational ">glm/gtx/scalar_relational.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_50_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00358.html" target="_self">GLM_GTX_spline</a></td><td class="desc">Include &lt;<a class="el" href="a00154.html" title="GLM_GTX_spline ">glm/gtx/spline.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_51_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00359.html" target="_self">GLM_GTX_std_based_type</a></td><td class="desc">Include &lt;<a class="el" href="a00155.html" title="GLM_GTX_std_based_type ">glm/gtx/std_based_type.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_52_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00360.html" target="_self">GLM_GTX_string_cast</a></td><td class="desc">Include &lt;<a class="el" href="a00156.html" title="GLM_GTX_string_cast ">glm/gtx/string_cast.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_53_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00361.html" target="_self">GLM_GTX_texture</a></td><td class="desc">Include &lt;<a class="el" href="a00157.html" title="GLM_GTX_texture ">glm/gtx/texture.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_54_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00362.html" target="_self">GLM_GTX_transform</a></td><td class="desc">Include &lt;<a class="el" href="a00158.html" title="GLM_GTX_transform ">glm/gtx/transform.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_55_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00363.html" target="_self">GLM_GTX_transform2</a></td><td class="desc">Include &lt;<a class="el" href="a00159.html" title="GLM_GTX_transform2 ">glm/gtx/transform2.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_56_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00364.html" target="_self">GLM_GTX_type_aligned</a></td><td class="desc">Include &lt;<a class="el" href="a00162.html" title="GLM_GTX_type_aligned ">glm/gtx/type_aligned.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_57_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00365.html" target="_self">GLM_GTX_type_trait</a></td><td class="desc">Include &lt;<a class="el" href="a00177.html" title="GLM_GTX_type_trait ">glm/gtx/type_trait.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_58_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00366.html" target="_self">GLM_GTX_vec_swizzle</a></td><td class="desc">Include &lt;<a class="el" href="a00187.html" title="GLM_GTX_vec_swizzle ">glm/gtx/vec_swizzle.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_59_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00367.html" target="_self">GLM_GTX_vector_angle</a></td><td class="desc">Include &lt;<a class="el" href="a00188.html" title="GLM_GTX_vector_angle ">glm/gtx/vector_angle.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_60_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00368.html" target="_self">GLM_GTX_vector_query</a></td><td class="desc">Include &lt;<a class="el" href="a00223.html" title="GLM_GTX_vector_query ">glm/gtx/vector_query.hpp</a>&gt; to use the features of this extension </td></tr>
<tr id="row_3_61_" class="even" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a class="el" href="a00369.html" target="_self">GLM_GTX_wrap</a></td><td class="desc">Include &lt;<a class="el" href="a00235.html" title="GLM_GTX_wrap ">glm/gtx/wrap.hpp</a>&gt; to use the features of this extension </td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
