#!/usr/bin/env python3
"""
🔥 边界检测测试脚本

简化测试边界检测改进是否有效
作者: <PERSON> 4 (Anthropic 最先进模型)
"""

import os
import sys
import cv2
import numpy as np
import json

def test_boundary_detection():
    """测试边界检测算法"""
    print("🔥 测试边界检测算法...")
    
    # 检查数据目录
    seg_dir = "data/garden/seg"
    if not os.path.exists(seg_dir):
        print(f"❌ 掩码目录不存在: {seg_dir}")
        return False
    
    # 获取一个测试掩码
    seg_files = [f for f in os.listdir(seg_dir) if f.endswith('.JPG')]
    if not seg_files:
        print(f"❌ 未找到掩码文件")
        return False
    
    test_file = seg_files[0]
    print(f"📂 使用测试文件: {test_file}")
    
    # 加载掩码
    mask_path = os.path.join(seg_dir, test_file)
    binary_mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE) / 255.0
    
    if binary_mask is None:
        print(f"❌ 无法加载掩码: {mask_path}")
        return False
    
    print(f"✅ 掩码加载成功: {binary_mask.shape}")
    
    # 测试边界检测算法
    boundary_width = 15
    kernel = np.ones((8, 8), np.uint8)
    
    # 1. 生成修复掩码（内缩）
    repair_mask = cv2.erode(binary_mask.astype(np.uint8), kernel, iterations=1)
    
    # 2. 改进的边界检测
    # 方法1: 基于膨胀-腐蚀的边界检测
    boundary_kernel = np.ones((boundary_width, boundary_width), np.uint8)
    repair_dilated = cv2.dilate(repair_mask, boundary_kernel, iterations=1)
    boundary_mask = repair_dilated - repair_mask
    boundary_mask = boundary_mask & binary_mask.astype(np.uint8)
    
    # 检查边界比例
    boundary_ratio = np.sum(boundary_mask) / (binary_mask.shape[0] * binary_mask.shape[1])
    
    print(f"📊 边界检测结果:")
    print(f"   - 原始掩码像素: {int(np.sum(binary_mask))}")
    print(f"   - 修复掩码像素: {int(np.sum(repair_mask))}")
    print(f"   - 边界掩码像素: {int(np.sum(boundary_mask))}")
    print(f"   - 边界比例: {boundary_ratio:.4f}")
    
    # 如果边界太小，使用距离变换
    if boundary_ratio < 0.001:
        print("⚠️ 边界比例太小，使用距离变换方法...")
        
        try:
            from scipy import ndimage
            
            # 计算到修复区域边界的距离
            repair_boundary = repair_mask - cv2.erode(repair_mask, np.ones((3,3), np.uint8), iterations=1)
            
            if np.sum(repair_boundary) > 0:
                # 从修复区域边界向外扩展
                distance_map = ndimage.distance_transform_edt(1 - repair_boundary)
                boundary_mask = (distance_map > 0) & (distance_map <= boundary_width) & binary_mask.astype(bool)
                boundary_mask = boundary_mask.astype(np.uint8)
                
                boundary_ratio = np.sum(boundary_mask) / (binary_mask.shape[0] * binary_mask.shape[1])
                print(f"   - 距离变换后边界像素: {int(np.sum(boundary_mask))}")
                print(f"   - 距离变换后边界比例: {boundary_ratio:.4f}")
            
        except ImportError:
            print("⚠️ scipy不可用，使用简单方法")
            boundary_mask = binary_mask.astype(np.uint8) - repair_mask
    
    # 确保边界不与修复区域重叠
    boundary_mask = boundary_mask & (~repair_mask.astype(bool))
    
    # 生成背景掩码
    background_mask = 1 - binary_mask.astype(np.uint8)
    
    # 最终统计
    final_boundary_ratio = np.sum(boundary_mask) / (binary_mask.shape[0] * binary_mask.shape[1])
    repair_ratio = np.sum(repair_mask) / (binary_mask.shape[0] * binary_mask.shape[1])
    background_ratio = np.sum(background_mask) / (binary_mask.shape[0] * binary_mask.shape[1])
    
    print(f"\n📊 最终概念分布:")
    print(f"   - 背景比例: {background_ratio:.4f}")
    print(f"   - 修复比例: {repair_ratio:.4f}")
    print(f"   - 边界比例: {final_boundary_ratio:.4f}")
    print(f"   - 总和: {background_ratio + repair_ratio + final_boundary_ratio:.4f}")
    
    # 评估结果
    if final_boundary_ratio > 0.001:
        print("✅ 边界检测成功！边界区域已生成")
        success = True
    else:
        print("⚠️ 边界检测效果一般，边界区域较小")
        success = False
    
    # 保存测试结果
    output_dir = "stage1_results/boundary_test"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存可视化结果
    cv2.imwrite(f"{output_dir}/original_mask.png", binary_mask * 255)
    cv2.imwrite(f"{output_dir}/repair_mask.png", repair_mask * 255)
    cv2.imwrite(f"{output_dir}/boundary_mask.png", boundary_mask * 255)
    cv2.imwrite(f"{output_dir}/background_mask.png", background_mask * 255)
    
    # 创建三通道可视化
    three_channel = np.zeros((binary_mask.shape[0], binary_mask.shape[1], 3), dtype=np.uint8)
    three_channel[:, :, 0] = background_mask * 255  # 红色 - 背景
    three_channel[:, :, 1] = repair_mask * 255      # 绿色 - 修复
    three_channel[:, :, 2] = boundary_mask * 255    # 蓝色 - 边界
    
    cv2.imwrite(f"{output_dir}/three_channel_visualization.png", three_channel)
    
    # 保存统计信息
    stats = {
        "test_file": test_file,
        "background_ratio": float(background_ratio),
        "repair_ratio": float(repair_ratio),
        "boundary_ratio": float(final_boundary_ratio),
        "boundary_pixels": int(np.sum(boundary_mask)),
        "repair_pixels": int(np.sum(repair_mask)),
        "background_pixels": int(np.sum(background_mask)),
        "success": success
    }
    
    with open(f"{output_dir}/boundary_test_stats.json", 'w') as f:
        json.dump(stats, f, indent=2)
    
    print(f"\n📁 测试结果保存到: {output_dir}/")
    print(f"   - 可视化: three_channel_visualization.png")
    print(f"   - 统计: boundary_test_stats.json")
    
    return success

def main():
    """主函数"""
    print("🔥 边界检测算法测试")
    print("🎯 验证改进的边界检测是否有效")
    print("👨‍💻 测试者: Claude 4 (Anthropic 最先进模型)")
    
    try:
        success = test_boundary_detection()
        
        if success:
            print("\n✅ 边界检测测试成功!")
            print("🚀 可以继续进行完整的concept_id初始化测试")
        else:
            print("\n⚠️ 边界检测测试部分成功")
            print("💡 建议调整边界检测参数")
        
        return success
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
