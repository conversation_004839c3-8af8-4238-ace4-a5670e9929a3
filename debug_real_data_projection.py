#!/usr/bin/env python3
"""
调试真实COLMAP数据的投影问题
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
import argparse

# 添加gaussian_splatting路径
sys.path.append('./gaussian_splatting')
from scene import Scene, GaussianModel
from arguments import ModelParams

def debug_real_projection():
    """调试真实数据的投影问题"""
    print("🔍 调试真实COLMAP数据的投影问题...")
    
    # 1. 加载真实模型
    print("📦 加载真实3DGS模型...")
    parser = argparse.ArgumentParser()
    model_params = ModelParams(parser)
    
    args = argparse.Namespace()
    args.source_path = "data/garden"
    args.model_path = "output/garden_incomplete"
    args.images = "images"
    args.resolution = 4
    args.white_background = False
    args.data_device = "cuda"
    args.eval = True
    args.sh_degree = 0
    args.unique_image = "nothing"
    
    gaussians = GaussianModel(args.sh_degree)
    scene = Scene(model_params.extract(args), gaussians, load_iteration=-1)
    
    print(f"✅ 模型加载完成，高斯点数量: {gaussians.get_xyz.shape[0]}")
    
    # 2. 获取相机
    cameras = scene.getTrainCameras()
    print(f"📷 找到 {len(cameras)} 个训练相机")
    
    # 3. 分析前几个相机
    for i in range(min(3, len(cameras))):
        camera = cameras[i]
        print(f"\n🎯 分析相机 {i+1}: {camera.image_name}")
        print(f"   📍 相机分辨率: {camera.image_height} x {camera.image_width}")
        print(f"   📍 znear: {camera.znear}, zfar: {camera.zfar}")
        print(f"   📍 FoVx: {camera.FoVx:.3f}, FoVy: {camera.FoVy:.3f}")
        
        # 检查相机中心和朝向
        camera_center = camera.camera_center
        print(f"   📍 相机中心: [{camera_center[0]:.2f}, {camera_center[1]:.2f}, {camera_center[2]:.2f}]")
        
        # 检查相机矩阵
        print(f"   📍 world_view_transform shape: {camera.world_view_transform.shape}")
        print(f"   📍 projection_matrix shape: {camera.projection_matrix.shape}")
        
        # 检查3D点分布
        xyz = gaussians.get_xyz
        print(f"   📊 3D点分布:")
        print(f"      X: [{xyz[:, 0].min():.2f}, {xyz[:, 0].max():.2f}]")
        print(f"      Y: [{xyz[:, 1].min():.2f}, {xyz[:, 1].max():.2f}]")
        print(f"      Z: [{xyz[:, 2].min():.2f}, {xyz[:, 2].max():.2f}]")
        
        # 手动投影测试
        print(f"   🔧 手动投影测试...")
        with torch.no_grad():
            # 使用高斯模型的投影函数
            pixel_coords, visible_mask = gaussians._project_points_to_camera_view(xyz, camera)
            print(f"      📊 投影结果: {visible_mask.sum()}/{len(visible_mask)} 点可见")
            
            if visible_mask.sum() > 0:
                visible_pixels = pixel_coords[visible_mask]
                print(f"      📍 可见点像素坐标范围:")
                print(f"         X: [{visible_pixels[:, 0].min():.1f}, {visible_pixels[:, 0].max():.1f}]")
                print(f"         Y: [{visible_pixels[:, 1].min():.1f}, {visible_pixels[:, 1].max():.1f}]")
            else:
                print("      ❌ 没有可见点！进行详细分析...")
                
                # 详细分析投影步骤
                analyze_projection_steps(xyz, camera)

def analyze_projection_steps(xyz, camera):
    """详细分析投影的每个步骤"""
    print("      🔍 详细分析投影步骤:")
    
    N = xyz.shape[0]
    device = xyz.device
    dtype = xyz.dtype
    
    # 步骤1: 转换为齐次坐标
    ones = torch.ones(N, 1, device=device, dtype=dtype)
    xyz_world_homo = torch.cat([xyz, ones], dim=1)
    
    # 步骤2: 世界坐标 -> 相机坐标
    xyz_camera_homo = xyz_world_homo @ camera.world_view_transform.T
    xyz_camera = xyz_camera_homo[:, :3]
    
    print(f"         📍 相机坐标范围:")
    print(f"            X: [{xyz_camera[:, 0].min():.2f}, {xyz_camera[:, 0].max():.2f}]")
    print(f"            Y: [{xyz_camera[:, 1].min():.2f}, {xyz_camera[:, 1].max():.2f}]")
    print(f"            Z: [{xyz_camera[:, 2].min():.2f}, {xyz_camera[:, 2].max():.2f}]")
    
    # 步骤3: 深度剔除
    znear, zfar = camera.znear, camera.zfar
    depth_valid_mask = (xyz_camera[:, 2] < -znear) & (xyz_camera[:, 2] > -zfar)
    print(f"         ✂️ 深度剔除: {depth_valid_mask.sum()}/{N} 点在深度范围内")
    print(f"            深度范围要求: Z ∈ ({-zfar:.2f}, {-znear:.2f})")
    
    if depth_valid_mask.sum() == 0:
        print("         ❌ 深度剔除失败！所有点都在深度范围外")
        # 分析深度分布
        z_values = xyz_camera[:, 2]
        print(f"            实际Z值分布: [{z_values.min():.2f}, {z_values.max():.2f}]")
        print(f"            需要的Z值范围: ({-zfar:.2f}, {-znear:.2f})")
        
        # 检查有多少点在前方（Z < 0）
        in_front = (z_values < 0).sum()
        print(f"            在相机前方的点: {in_front}/{N}")
        return
    
    # 步骤4: 相机坐标 -> 裁剪空间
    clip_space_homo = xyz_camera_homo @ camera.projection_matrix.T
    
    print(f"         📍 裁剪空间坐标范围:")
    print(f"            X: [{clip_space_homo[:, 0].min():.2f}, {clip_space_homo[:, 0].max():.2f}]")
    print(f"            Y: [{clip_space_homo[:, 1].min():.2f}, {clip_space_homo[:, 1].max():.2f}]")
    print(f"            Z: [{clip_space_homo[:, 2].min():.2f}, {clip_space_homo[:, 2].max():.2f}]")
    print(f"            W: [{clip_space_homo[:, 3].min():.2f}, {clip_space_homo[:, 3].max():.2f}]")
    
    # 步骤5: 透视除法
    w = clip_space_homo[:, 3:4]
    w_safe = torch.where(torch.abs(w) < 1e-6, torch.sign(w) * 1e-6, w)
    ndc_coords = clip_space_homo[:, :3] / w_safe
    
    print(f"         📍 NDC坐标范围:")
    print(f"            X: [{ndc_coords[:, 0].min():.2f}, {ndc_coords[:, 0].max():.2f}]")
    print(f"            Y: [{ndc_coords[:, 1].min():.2f}, {ndc_coords[:, 1].max():.2f}]")
    print(f"            Z: [{ndc_coords[:, 2].min():.2f}, {ndc_coords[:, 2].max():.2f}]")
    
    # 步骤6: NDC剔除
    ndc_valid_mask = (
        (ndc_coords[:, 0] >= -1.0) & (ndc_coords[:, 0] <= 1.0) &
        (ndc_coords[:, 1] >= -1.0) & (ndc_coords[:, 1] <= 1.0) &
        (ndc_coords[:, 2] >= -1.0) & (ndc_coords[:, 2] <= 1.0)
    )
    print(f"         ✂️ NDC剔除: {ndc_valid_mask.sum()}/{N} 点在NDC范围内")
    
    # 步骤7: 像素坐标转换
    H, W = camera.image_height, camera.image_width
    pixel_x = (ndc_coords[:, 0] + 1.0) * 0.5 * W
    pixel_y = (1.0 - ndc_coords[:, 1]) * 0.5 * H
    pixel_coords = torch.stack([pixel_x, pixel_y], dim=1)
    
    # 步骤8: 屏幕边界剔除
    screen_valid_mask = (
        (pixel_coords[:, 0] >= 0) & (pixel_coords[:, 0] < W) &
        (pixel_coords[:, 1] >= 0) & (pixel_coords[:, 1] < H)
    )
    print(f"         ✂️ 屏幕剔除: {screen_valid_mask.sum()}/{N} 点在屏幕内")
    
    # 综合可见性
    final_valid = depth_valid_mask & ndc_valid_mask & screen_valid_mask
    print(f"         👁️ 最终可见: {final_valid.sum()}/{N} 点")
    
    if final_valid.sum() > 0:
        visible_pixels = pixel_coords[final_valid]
        print(f"         📍 可见点像素范围:")
        print(f"            X: [{visible_pixels[:, 0].min():.1f}, {visible_pixels[:, 0].max():.1f}]")
        print(f"            Y: [{visible_pixels[:, 1].min():.1f}, {visible_pixels[:, 1].max():.1f}]")

if __name__ == "__main__":
    debug_real_projection()
