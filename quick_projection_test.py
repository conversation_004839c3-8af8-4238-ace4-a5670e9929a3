#!/usr/bin/env python3
"""
快速投影测试 - 只测试一个相机
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
import argparse

# 添加gaussian_splatting路径
sys.path.append('./gaussian_splatting')
from scene import Scene, GaussianModel
from arguments import ModelParams

def quick_test():
    """快速测试投影"""
    print("🔍 快速投影测试...")
    
    # 1. 加载模型
    print("📦 加载3DGS模型...")
    parser = argparse.ArgumentParser()
    model_params = ModelParams(parser)
    
    args = argparse.Namespace()
    args.source_path = "data/garden"
    args.model_path = "output/garden_incomplete"
    args.images = "images"
    args.resolution = 4
    args.white_background = False
    args.data_device = "cuda"
    args.eval = True
    args.sh_degree = 0
    args.unique_image = "nothing"
    
    gaussians = GaussianModel(args.sh_degree)
    scene = Scene(model_params.extract(args), gaussians, load_iteration=-1)
    
    print(f"✅ 模型加载完成，高斯点数量: {gaussians.get_xyz.shape[0]}")
    
    # 2. 获取第一个相机
    cameras = scene.getTrainCameras()
    camera = cameras[0]
    print(f"📷 测试相机: {camera.image_name}")
    print(f"   分辨率: {camera.image_height} x {camera.image_width}")
    
    # 3. 获取3D点
    xyz = gaussians.get_xyz
    print(f"📊 3D点数量: {xyz.shape[0]}")
    print(f"   X范围: [{xyz[:, 0].min():.2f}, {xyz[:, 0].max():.2f}]")
    print(f"   Y范围: [{xyz[:, 1].min():.2f}, {xyz[:, 1].max():.2f}]")
    print(f"   Z范围: [{xyz[:, 2].min():.2f}, {xyz[:, 2].max():.2f}]")
    
    # 4. 手动投影测试
    print(f"🔧 手动投影测试...")
    
    N = xyz.shape[0]
    device = xyz.device
    dtype = xyz.dtype
    
    # 步骤1: 转换为齐次坐标
    ones = torch.ones(N, 1, device=device, dtype=dtype)
    xyz_world_homo = torch.cat([xyz, ones], dim=1)
    
    # 步骤2: 世界坐标 -> 相机坐标
    xyz_camera_homo = xyz_world_homo @ camera.world_view_transform.T
    xyz_camera = xyz_camera_homo[:, :3]
    
    print(f"   相机坐标范围:")
    print(f"      X: [{xyz_camera[:, 0].min():.2f}, {xyz_camera[:, 0].max():.2f}]")
    print(f"      Y: [{xyz_camera[:, 1].min():.2f}, {xyz_camera[:, 1].max():.2f}]")
    print(f"      Z: [{xyz_camera[:, 2].min():.2f}, {xyz_camera[:, 2].max():.2f}]")
    
    # 步骤3: 深度剔除 - 使用非常宽松的范围
    depth_valid_mask = (xyz_camera[:, 2] < -0.001) & (xyz_camera[:, 2] > -10000.0)
    print(f"   深度剔除: {depth_valid_mask.sum()}/{N} 点在深度范围内")
    
    if depth_valid_mask.sum() == 0:
        print("   ❌ 深度剔除失败！")
        z_values = xyz_camera[:, 2]
        in_front = (z_values < 0).sum()
        print(f"      在相机前方的点: {in_front}/{N}")
        print(f"      Z值分布: [{z_values.min():.2f}, {z_values.max():.2f}]")
        
        # 尝试更宽松的深度范围
        print("   🔧 尝试更宽松的深度范围...")
        depth_valid_mask = (xyz_camera[:, 2] < 0) & (xyz_camera[:, 2] > -100000.0)
        print(f"   新深度剔除: {depth_valid_mask.sum()}/{N} 点在深度范围内")
        
        if depth_valid_mask.sum() == 0:
            print("   ❌ 仍然失败！所有点都在相机后方或太远")
            return
    
    # 步骤4: 相机坐标 -> 裁剪空间
    clip_space_homo = xyz_camera_homo @ camera.projection_matrix.T
    
    print(f"   裁剪空间坐标范围:")
    print(f"      X: [{clip_space_homo[:, 0].min():.2f}, {clip_space_homo[:, 0].max():.2f}]")
    print(f"      Y: [{clip_space_homo[:, 1].min():.2f}, {clip_space_homo[:, 1].max():.2f}]")
    print(f"      Z: [{clip_space_homo[:, 2].min():.2f}, {clip_space_homo[:, 2].max():.2f}]")
    print(f"      W: [{clip_space_homo[:, 3].min():.2f}, {clip_space_homo[:, 3].max():.2f}]")
    
    # 步骤5: 透视除法
    w = clip_space_homo[:, 3:4]
    w_safe = torch.where(torch.abs(w) < 1e-6, torch.sign(w) * 1e-6, w)
    ndc_coords = clip_space_homo[:, :3] / w_safe
    
    print(f"   NDC坐标范围:")
    print(f"      X: [{ndc_coords[:, 0].min():.2f}, {ndc_coords[:, 0].max():.2f}]")
    print(f"      Y: [{ndc_coords[:, 1].min():.2f}, {ndc_coords[:, 1].max():.2f}]")
    print(f"      Z: [{ndc_coords[:, 2].min():.2f}, {ndc_coords[:, 2].max():.2f}]")
    
    # 步骤6: NDC剔除
    ndc_valid_mask = (
        (ndc_coords[:, 0] >= -1.0) & (ndc_coords[:, 0] <= 1.0) &
        (ndc_coords[:, 1] >= -1.0) & (ndc_coords[:, 1] <= 1.0) &
        (ndc_coords[:, 2] >= -1.0) & (ndc_coords[:, 2] <= 1.0)
    )
    print(f"   NDC剔除: {ndc_valid_mask.sum()}/{N} 点在NDC范围内")
    
    # 综合可见性
    final_valid = depth_valid_mask & ndc_valid_mask
    print(f"   最终可见: {final_valid.sum()}/{N} 点")
    
    if final_valid.sum() > 0:
        print("🎉 投影成功！")
        
        # 计算像素坐标
        H, W = camera.image_height, camera.image_width
        pixel_x = (ndc_coords[:, 0] + 1.0) * 0.5 * W
        pixel_y = (1.0 - ndc_coords[:, 1]) * 0.5 * H
        pixel_coords = torch.stack([pixel_x, pixel_y], dim=1)
        
        visible_pixels = pixel_coords[final_valid]
        print(f"   可见点像素坐标范围:")
        print(f"      X: [{visible_pixels[:, 0].min():.1f}, {visible_pixels[:, 0].max():.1f}]")
        print(f"      Y: [{visible_pixels[:, 1].min():.1f}, {visible_pixels[:, 1].max():.1f}]")
    else:
        print("❌ 投影失败！")

if __name__ == "__main__":
    quick_test()
