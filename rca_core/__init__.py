"""
RCA Core: 区域概念注意力核心模块
阶段3: RCA机制移植与适配

本模块实现了InFusion-Enhanced项目阶段3的RCA（区域概念注意力）核心功能，
基于MultiDreamer3D的参考实现，提供完整的概念感知注意力机制。

主要组件：
- ConceptAwareAttnProcessor: 概念感知注意力处理器
- RCAIntegrator: RCA机制集成器
- 原有的空间控制组件（向后兼容）

技术特性：
- 基于2D概念权重图的空间注意力调制
- 多概念独立注意力计算和聚合
- LoRA增强支持（可选）
- 完整的向后兼容性
- 详细的调试信息和错误处理

参考实现：
- MultiDreamer3D/guidance/sd_utils_casd.py
- concept_forward, mod_forward, base_forward函数

作者: Claude Sonnet 4 (Anthropic 最先进模型)
版本: 阶段3 - RCA机制移植与适配
"""

# 原有组件（向后兼容）
from .spatial_attention_3d import SpatialAttention3D
from .concept_weight_renderer import ConceptWeightRenderer
from .spatial_modulation import SpatialModulation
from .rca_trainer import RCATrainer

# 新增RCA组件
from .concept_aware_attention import ConceptAwareAttnProcessor
from .rca_integration import (
    RCAIntegrator,
    create_rca_integrator,
    integrate_rca_to_diffusion_engine
)
from .rca_data_preparation import RCADataPreparator

# 版本信息
__version__ = "3.0.0"
__stage__ = "阶段3 - RCA机制移植与适配"

# 导出的主要类和函数
__all__ = [
    # 原有组件（向后兼容）
    'SpatialAttention3D',
    'ConceptWeightRenderer',
    'SpatialModulation',
    'RCATrainer',

    # 新增RCA组件
    "ConceptAwareAttnProcessor",
    "RCAIntegrator",
    "RCADataPreparator",

    # 便捷函数
    "create_rca_integrator",
    "integrate_rca_to_diffusion_engine",

    # 版本信息
    "__version__",
    "__stage__"
]

# 模块级别的日志配置
import logging
logging.getLogger(__name__).setLevel(logging.INFO)

def get_rca_info():
    """
    获取RCA模块信息

    Returns:
        info: RCA模块信息字典
    """
    return {
        "module": "rca_core",
        "version": __version__,
        "stage": __stage__,
        "components": [
            "ConceptAwareAttnProcessor - 概念感知注意力处理器",
            "RCAIntegrator - RCA机制集成器",
            "RCADataPreparator - RCA数据准备器",
            "SpatialAttention3D - 3D空间注意力（原有）",
            "ConceptWeightRenderer - 概念权重渲染器（原有）",
            "SpatialModulation - 空间调制（原有）",
            "RCATrainer - RCA训练器（原有）"
        ],
        "features": [
            "基于2D概念权重图的空间注意力调制",
            "多概念独立注意力计算和聚合",
            "LoRA增强支持（可选）",
            "完整的向后兼容性",
            "详细的调试信息和错误处理"
        ],
        "reference": "MultiDreamer3D/guidance/sd_utils_casd.py"
    }

def print_rca_info():
    """
    打印RCA模块信息
    """
    info = get_rca_info()
    print(f"\n🚀 {info['module']} - {info['stage']}")
    print(f"📦 版本: {info['version']}")
    print(f"🔧 组件:")
    for component in info['components']:
        print(f"   - {component}")
    print(f"✨ 特性:")
    for feature in info['features']:
        print(f"   - {feature}")
    print(f"📚 参考: {info['reference']}")
    print()

# 模块导入时的欢迎信息
if __name__ != "__main__":
    logging.info(f"RCA Core模块已加载 - {__stage__} v{__version__}")
