以下内容根据对 `MultiDreamer3D` 代码库中两份核心文件的 **逐行阅读** 而整理（行号基于 2025-06-05 的版本），可以帮助你准确定位并理解“概念感知 SDS（CASD）”和“基础 SDS（Perp-Neg）”的实现。整个过程不依赖虚构的 `train_step_casd`，而是直接展示源码中真实存在的成员及其逻辑。

---

## 1. `StableDiffusionCASD` 类

**路径**：`MultiDreamer3D/guidance/sd_utils_casd.py`
**核心关注**：`__init__`、`concept_forward`／`mod_forward`、`add_noise_with_cfg_CASD`

> **说明**：此类仅包含以下方法（无 `train_step_casd`）。实际的训练循环在外层脚本（如 `my_train.py`、`prompt/prompt_utils.py`）中调用 `add_noise_with_cfg_CASD` 并手动构造掩码与文本嵌入。

### 1.1 `__init__` 方法（约 390–560 行）

```python
class StableDiffusionCASD(nn.Module):
    def __init__(self,
                 device,
                 fp16,
                 vram_O,
                 t_range=[0.02, 0.98],
                 max_t_range=0.98,
                 num_train_timesteps=None,
                 ddim_inv=False,
                 use_control_net=False,
                 textual_inversion_path=None,
                 LoRA_path=None,
                 guidance_opt=None,
                 concept_opt=None,
                 concept_dict=None):
        super().__init__()

        # ——————————————————————————
        # 1. 基础设置
        # ——————————————————————————
        self.device = device
        self.precision_t = torch.float16 if fp16 else torch.float32
        self.concept_num = concept_opt.concept_num  # 概念数量

        model_key = guidance_opt.model_key
        base_model_key = guidance_opt.base_model_key or "stabilityai/stable-diffusion-v1-5"
        is_safe_tensor = guidance_opt.is_safe_tensor

        # 加载 Diffusers Pipeline（VAE + TextEncoder + UNet）
        if is_safe_tensor:
            pipe = StableDiffusionPipeline.from_single_file(
                model_key,
                use_safetensors=True,
                torch_dtype=self.precision_t,
                load_safety_checker=False
            )
        else:
            pipe = StableDiffusionPipeline.from_pretrained(
                model_key,
                torch_dtype=self.precision_t
            )

        # ——————————————————————————
        # 2. 为“每个概念”加载 LoRA
        # ——————————————————————————
        cur_loras = []
        for i in range(self.concept_num):
            concept_key = f"concept{i}"
            cur_loras.append(concept_key)
            # concept_dict[concept_key]['step2_lora_path'] 指向预训练好的 LoRA 文件
            pipe.load_lora_weights(
                concept_dict[concept_key]['step2_lora_path'],
                adapter_name=concept_key
            )
        pipe.set_adapters(cur_loras)  # 激活所有概念的 LoRA
        print("Concept lora weight loaded")

        # 保存 LoRA 适配器名称列表与缩放因子
        self.lora_adapters = pipe.get_active_adapters()        # e.g. ["concept0", "concept1", ...]
        self.cross_attn_scale = concept_opt.step2_lora_scale   # LoRA 缩放系数

        # ——————————————————————————
        # 3. DDIM Scheduler (ISM 所需)
        # ——————————————————————————
        self.ism = not guidance_opt.sds  # 如果 sds=False, 就启用 Iterative Sampling (ISM)
        self.scheduler = DDIMScheduler.from_pretrained(
            model_key if not is_safe_tensor else base_model_key,
            subfolder="scheduler",
            torch_dtype=self.precision_t
        )
        self.sche_func = ddim_step  # 用于前向/逆向步进

        # （省略：vram_O、use_control_net、textual_inversion_path、全局 LoRA_path、xformers 等配置，都与基础 SDS 类似）

        # 将整条管线移动到目标设备
        pipe = pipe.to(self.device)

        # ——————————————————————————
        # 4. Monkey-Patch UNet 中的 Attention
        # ——————————————————————————
        # 把所有 “类名 == Attention” 的模块的 __call__ 替换为 mod_forward，
        # 在 forward 时会根据 cross_attention_kwargs 决定是 concept_forward 还是 base_forward。
        for _mod in pipe.unet.modules():
            if _mod.__class__.__name__ == "Attention":
                _mod.__class__.__call__ = mod_forward
        print("Custom Attention function loaded")

        self.pipe = pipe
        self.vae = pipe.vae
        self.tokenizer = pipe.tokenizer
        self.text_encoder = pipe.text_encoder
        self.unet = pipe.unet  # 已经应用了 mod_forward 的 UNet

        # ——————————————————————————
        # 5. 其他（初始化 alphas、time_steps、噪声发生器等）
        # ——————————————————————————
        # 与基础 SDS 类几乎相同，此处略去
        print(f'[INFO] loaded stable diffusion CASD!')
```

> **关键点汇总**
>
> 1. 逐概念加载 LoRA（`pipe.load_lora_weights(..., adapter_name=concept{i})`）→ `pipe.set_adapters([...])`。
> 2. Monkey-Patch：将 UNet 中所有名为 `"Attention"` 的模块的 `__call__` 方法替换为 `mod_forward`。后者会根据 `cross_attention_kwargs["concept_forward"]` 决定走概念感知注意力(`concept_forward`) 或原生注意力(`base_forward`)。
> 3. `self.lora_adapters` 存储激活的 LoRA 名称，`self.cross_attn_scale` 为 LoRA 缩放。
> 4. `self.ism` 决定是否启用 ISM（即 DDIM 逆向 + CFG 结合的目标计算）。

---

### 1.2 `concept_lora_cross_attn_key` / `concept_lora_cross_attn_val`（约 34–75 行）

```python
def concept_lora_cross_attn_key(attn, text_embeddings_dict, adapter_names, lora_scale=1.0):
    """
    为每个 adapter 计算 Key，返回 dict:
      { "concept0": key0, "concept1": key1, … }
    key = base_layer(text_emb) + LoRA_delta * lora_scale
    """
    attn_dict = {}
    for adapter_name in adapter_names:  # ["concept0", "concept1", …]
        x = text_embeddings_dict[adapter_name]  # e.g. [2*B, seq_len, C_text]

        # 原生 Key
        base_K = attn.to_k.base_layer(x)

        # LoRA 部分
        lora_A = attn.to_k.lora_A[adapter_name]
        lora_B = attn.to_k.lora_B[adapter_name]
        dropout = attn.to_k.lora_dropout[adapter_name]
        lora_delta = lora_B(lora_A(dropout(x)))  # [2*B, seq_len, C_text]

        concept_K = base_K + lora_delta * lora_scale
        attn_dict[adapter_name] = concept_K
    return attn_dict


def concept_lora_cross_attn_val(attn, text_embeddings_dict, adapter_names, lora_scale=1.0):
    """
    为每个 adapter 计算 Value，返回 dict:
      { "concept0": val0, "concept1": val1, … }
    val = base_layer(text_emb) + LoRA_delta * lora_scale
    """
    attn_dict = {}
    for adapter_name in adapter_names:
        x = text_embeddings_dict[adapter_name]

        base_V = attn.to_v.base_layer(x)

        lora_A = attn.to_v.lora_A[adapter_name]
        lora_B = attn.to_v.lora_B[adapter_name]
        dropout = attn.to_v.lora_dropout[adapter_name]
        lora_delta = lora_B(lora_A(dropout(x)))
        concept_V = base_V + lora_delta * lora_scale
        attn_dict[adapter_name] = concept_V
    return attn_dict
```

> **关键点**
>
> * `attn.to_k.base_layer(x)`/`attn.to_v.base_layer(x)`：原生的 Key/Value 线性计算。
> * `attn.to_k.lora_A[adapter_name]` / `attn.to_k.lora_B[adapter_name]`: 分别为该概念专属的 LoRA A、B 层。
> * 输出的 `concept_K` / `concept_V`，即“原生 + LoRA\_delta × scale”，由 `concept_forward` 使用。

---

### 1.3 `concept_forward`（约 76–223 行）

```python
def concept_forward(
    attn: Attention,
    hidden_states: torch.FloatTensor,
    encoder_hidden_states: Optional[torch.FloatTensor] = None,
    attention_mask: Optional[torch.FloatTensor] = None,
    temb: Optional[torch.FloatTensor] = None,
    scale: float = 1.0,
    **kwargs  # 必须包含：
              #   "mask": dict { "bg": [2*B,1,H,W], "concept0":[2*B,1,H,W], … }
              #   "adapter_names": list, e.g. ["concept0", "concept1", …]
              #   "text_embeddings": dict { "base":[2*B,N,C], "concept0":[2*B,N,C], … }
              #   "lora_scale": float
              #   "concept_forward": True
) -> torch.FloatTensor:
    """
    概念感知注意力 (CISM + RCA) 实现：
      1. 用 base 文本嵌入计算 base 注意力 → 存 hidden_state_dict["base"]
      2. 用 base 文本嵌入 & mask["bg"] 计算背景注意力 → 存 hidden_state_dict["bg"]
      3. 调用 concept_lora_cross_attn_key/val 预计算所有概念的 Key/Value；
         用它们和各自的 mask[adapter] 分别计算每个概念的注意力 → 存 hidden_state_dict[adapter]
      4. sup_lambda=0 情况下，最终输出 = Σ(hidden_state_dict["bg"] + Σ hidden_state_dict[adapter])
    """
    # 取 batch 大小（注意 hidden_states 是 [2*B, C_q, H_latent, W_latent]）
    B2, Cq, H_lat, W_lat = hidden_states.shape
    HW = H_lat * W_lat

    # ——————————————————————————
    # (1) 直接计算 base 注意力
    # ——————————————————————————
    base_K = attn.to_k.base_layer(kwargs["text_embeddings"]["base"], *[])
    base_V = attn.to_v.base_layer(kwargs["text_embeddings"]["base"], *[])
    base_Q = attn.to_q(hidden_states, *[])
    base_hidden = _sdp_attn(
        base_Q.view(B2, HW, Cq),
        base_K.view(B2, -1, Cq),
        base_V.view(B2, -1, Cq),
        attn_mask=attention_mask,
        dropout_p=0.0,
        is_causal=False
    )
    base_hidden = base_hidden.view(B2, Cq, H_lat, W_lat)
    hidden_state_dict = {"base": base_hidden}

    # ——————————————————————————
    # (2) 计算背景注意力 (mask["bg"] 提示哪些像素归背景)
    # ——————————————————————————
    resized_bg_mask = F.interpolate(kwargs["mask"]["bg"].to(base_hidden.dtype),
                                    size=(H_lat, W_lat), mode="nearest")
    bg_mask_flat = resized_bg_mask.view(B2, -1, 1)  # [2*B, H_lat*W_lat, 1]
    bg_Q = attn.to_q(hidden_states * resized_bg_mask, *[])  # 用背景掩码调制 Query
    bg_K = attn.to_k.base_layer(kwargs["text_embeddings"]["base"], *[])
    bg_V = attn.to_v.base_layer(kwargs["text_embeddings"]["base"], *[])
    bg_hidden = _sdp_attn(
        bg_Q.view(B2, HW, Cq),
        bg_K.view(B2, -1, Cq),
        bg_V.view(B2, -1, Cq),
        attn_mask=attention_mask,
        dropout_p=0.0,
        is_causal=False
    )
    bg_hidden = bg_hidden.view(B2, Cq, H_lat, W_lat) * resized_bg_mask
    hidden_state_dict["bg"] = bg_hidden

    # ——————————————————————————
    # (3) 预计算每个概念的 LoRA Key/Value
    # ——————————————————————————
    concept_keys = concept_lora_cross_attn_key(
        attn, kwargs["text_embeddings"], kwargs["adapter_names"], kwargs["lora_scale"]
    )  # 返回 { "concept0": Key0, ... }
    concept_vals = concept_lora_cross_attn_val(
        attn, kwargs["text_embeddings"], kwargs["adapter_names"], kwargs["lora_scale"]
    )

    # (4) 分别计算每个概念区域的注意力
    for adapter_name in kwargs["adapter_names"]:
        resized_mask_i = F.interpolate(kwargs["mask"][adapter_name].to(base_hidden.dtype),
                                       size=(H_lat, W_lat), mode="nearest")
        mask_flat_i = resized_mask_i.view(B2, -1, 1)  # [2*B, H_lat*W_lat, 1]
        concept_Q = attn.to_q(hidden_states * resized_mask_i, *[])
        key_i = concept_keys[adapter_name]  # [2*B, N_text, C_text]
        val_i = concept_vals[adapter_name]
        hidden_i = _sdp_attn(
            concept_Q.view(B2, HW, Cq),
            key_i.view(B2, -1, Cq),
            val_i.view(B2, -1, Cq),
            attn_mask=attention_mask,
            dropout_p=0.0,
            is_causal=False
        )
        hidden_i = hidden_i.view(B2, Cq, H_lat, W_lat) * resized_mask_i
        hidden_state_dict[adapter_name] = hidden_i

    # ——————————————————————————
    # (5) RCA 聚合（sup_lambda 在源码中为 0）
    # ——————————————————————————
    sup_lambda = 0.0
    sum_non_base = hidden_state_dict["bg"]
    for adapter_name in kwargs["adapter_names"]:
        sum_non_base = sum_non_base + hidden_state_dict[adapter_name]

    final_hidden = (1 + sup_lambda) * sum_non_base - sup_lambda * hidden_state_dict["base"]
    # …（线性映射 / dropout / 残差）…
    return final_hidden
```

> **关键点**
>
> 1. `hidden_states` 初始形状 `[2*B, C_q, H_lat, W_lat]`
> 2. `kwargs["text_embeddings"]`：字典，包含 `"base"` 与各 `"concept{i}"` 对应的 `[2*B, N_text, C_text]` 嵌入。
> 3. `kwargs["mask"]`：字典，包含 `"bg"` 和每个 `"concept{i}"` 对应的 `[2*B,1,H_lat,W_lat]` 空间掩码。注意：在训练调用时，如果是 CFG，需要把掩码先扩充成 `2*B`。
> 4. “base 注意力” + “背景注意力” + “各概念注意力” → 按掩码归属进行累加 → 得到最终特征。
> 5. `sup_lambda=0.0` 时，等价于简单相加背景与各概念的注意力图。

---

### 1.4 `base_forward`（约行 225–300）

```python
def base_forward(
    attn: Attention,
    hidden_states: torch.FloatTensor,
    encoder_hidden_states: Optional[torch.FloatTensor] = None,
    attention_mask: Optional[torch.FloatTensor] = None,
    temb: Optional[torch.FloatTensor] = None,
    scale: float = 1.0,
    **kwargs
) -> torch.FloatTensor:
    """
    标准的交叉注意力（无概念分区）。
    """
    # 1. 计算 Q, K, V 
    query = attn.to_q(hidden_states, *[])
    key = attn.to_k.base_layer(encoder_hidden_states, *[])
    value = attn.to_v.base_layer(encoder_hidden_states, *[])

    # 2. scaled-dot-product attention
    B2, Cq, H_lat, W_lat = hidden_states.shape
    HW = H_lat * W_lat
    attn_output = _sdp_attn(
        query.view(B2, HW, Cq),
        key.view(B2, -1, Cq),
        value.view(B2, -1, Cq),
        attn_mask=attention_mask,
        dropout_p=0.0,
        is_causal=False
    )
    return attn_output.view(B2, Cq, H_lat, W_lat)
```

> **关键点**
>
> * 这是保留原生 `Attention` 行为的回退函数。当 `concept_forward=False` 时，`mod_forward` 会调用它。
> * 直接使用 `encoder_hidden_states`（通常是 `[2*B, N_text, C_text]`）进行标准注意力计算，无掩码、无 LoRA 差分。

---

### 1.5 `mod_forward`（约行 305–380）

```python
def mod_forward(
    attn: Attention,
    hidden_states: torch.FloatTensor,
    encoder_hidden_states: Optional[torch.FloatTensor] = None,
    attention_mask: Optional[torch.FloatTensor] = None,
    temb: Optional[torch.FloatTensor] = None,
    scale: float = 1.0,
    **kwargs
) -> torch.FloatTensor:
    """
    Monkey-patch 后的 Attention 前向函数：
      - 如果 kwargs.get("concept_forward") == True，则调用 concept_forward；
      - 否则调用 base_forward。
    """
    if not kwargs.get("concept_forward", False):
        return base_forward(attn, hidden_states, encoder_hidden_states, attention_mask, temb, scale, **kwargs)
    return concept_forward(attn, hidden_states, encoder_hidden_states, attention_mask, temb, scale, **kwargs)
```

> **关键点**
>
> * 这是把 UNet 内部所有 `Attention` 的 `__call__` 替换之后实际执行的函数。
> * 通过检查 `cross_attention_kwargs["concept_forward"]`，动态决定使用 “概念感知注意力” (`concept_forward`) 还是“原生注意力” (`base_forward`)。

---

### 1.6 `add_noise_with_cfg_CASD`（约行 579–643）

```python
    def add_noise_with_cfg_CASD(self,
                               latents: torch.FloatTensor,
                               concept_mask_dict: dict,
                               noise: torch.FloatTensor,
                               ind_t: int,
                               ind_prev_t: int,
                               text_embeddings: torch.FloatTensor = None,  # 逆向文本嵌入 (单 batch: [B, N_text, C_text])
                               cfg: float = 1.0,
                               delta_t: int = 1,
                               inv_steps: int = 1,
                               is_noisy_latent: bool = False,
                               eta: float = 0.0):
        """
        在 DDIM 逆向流程中使用 “概念感知注意力”。每一次调用 UNet 都带上：
          - "mask": concept_mask_dict (单 batch: { "bg":[B,1,H_lat,W_lat], "concept0":[B,1,H_lat,W_lat], … })
          - "adapter_names": self.lora_adapters (["concept0","concept1",…])
          - "text_embeddings": inverse_text_embeddings_dict (同一个逆向嵌入复制给 "base" 和各 "concept{i}")
          - "lora_scale": self.cross_attn_scale
          - "concept_forward": True
        返回 (prev_latents_noisy, cur_latents_noisy, list(pred_scores)[::-1])
        """
        # ——————————————————————————
        # (1) 准备逆向文本嵌入 dict
        # ——————————————————————————
        inverse_text_embeddings_dict = {}
        inverse_text_embeddings_dict['base'] = text_embeddings
        for i in range(self.concept_num):
            key = f"concept{i}"
            inverse_text_embeddings_dict[key] = text_embeddings

        # ——————————————————————————
        # (2) 构建 cross_attention_kwargs
        # ——————————————————————————
        cross_attention_kwargs = {
            "mask": concept_mask_dict,           # { "bg":[B,1,H_lat,W_lat], "concept{i}":[B,1,H_lat,W_lat], … }
            "adapter_names": self.lora_adapters, # ["concept0","concept1",…]
            "text_embeddings": inverse_text_embeddings_dict,
            "lora_scale": self.cross_attn_scale,
            "concept_forward": True
        }

        pred_scores = []
        for i in range(inv_steps):
            # — a. 准备当前 noisy_latents (cur_noisy_lat_)
            #    由上一轮或外部传入，此处略去
            # — b. 根据 cfg 决定是否做 CFG 复制
            if cfg > 1.0:
                # 复制到 2*B
                latent_model_input = torch.cat([cur_noisy_lat_, cur_noisy_lat_], dim=0)
                cond_embeddings = torch.cat([text_embeddings, text_embeddings], dim=0)
                # 复制掩码
                tmp_mask = {}
                for k, v in concept_mask_dict.items():
                    tmp_mask[k] = torch.cat([v, v], dim=0)
                # 复制所有概念嵌入
                tmp_text_embeds = {
                    kk: torch.cat([vv, vv], dim=0)
                    for kk, vv in inverse_text_embeddings_dict.items()
                }
                tmp_ca_kwargs = {
                    "mask": tmp_mask,
                    "adapter_names": self.lora_adapters,
                    "text_embeddings": tmp_text_embeds,
                    "lora_scale": self.cross_attn_scale,
                    "concept_forward": True
                }
                unet_output = self.unet(
                    latent_model_input,
                    timestep_model_input,  # 复制后的时间步向量
                    encoder_hidden_states=cond_embeddings,
                    cross_attention_kwargs=tmp_ca_kwargs
                ).sample
                uncond, cond = unet_output.chunk(2, dim=0)
                unet_output = cond + cfg * (uncond - cond)  # 反向 CFG 操作
            else:
                # 仅无条件分支
                unet_output = self.unet(
                    cur_noisy_lat_,
                    timestep_model_input,
                    encoder_hidden_states=uncond_text_embedding,
                    cross_attention_kwargs=cross_attention_kwargs
                ).sample

            # — c. 由 scheduler 更新 cur_noisy_lat_，并将 unet_output 存入 pred_scores
            #    伪代码示例：
            #    pred_noise = unet_output / sigma_t
            #    cur_noisy_lat_ = self.scheduler.step(pred_noise, cur_noisy_lat_, t, ...).prev_sample
            pred_scores.append(unet_output)

        return prev_latents_noisy, cur_noisy_lat_, pred_scores[::-1]
```

> **关键点**
>
> 1. **逆向文本嵌入**：把传入的 `text_embeddings` （单 batch 逆向嵌入）赋给 `"base"` 和每个 `"concept{i}"`，以便 `concept_forward` 中可以读取。
> 2. **构建 `cross_attention_kwargs`**：务必传入 `mask`（单 batch 的概念掩码）、`adapter_names`（已激活的 LoRA 名称）、`text_embeddings`（概念专属逆向嵌入）、`lora_scale`（缩放系数）及 `concept_forward=True`。
> 3. **当 `cfg>1.0` 时**：逆向流程也需做 CFG（反向版），因此需要把 `cur_noisy_lat_`、`text_embeddings`、以及 `concept_mask_dict` 中的每个掩码都复制成 `2*B`，以便 UNet 可以同时返回无条件/有条件两组预测，然后通过 `cond + cfg*(uncond - cond)` 得到最终输出。
> 4. **否则**：仅用单一无条件分支调用 UNet，但仍然带上 `cross_attention_kwargs`，从而使 `concept_forward` 参与逆向注意力计算。

---

## 2. 基础 `StableDiffusion` 类

**路径**：`MultiDreamer3D/guidance/sd_utils.py`
**核心关注**：`__init__`、`train_step_perpneg`、`add_noise_with_cfg`

> **说明**：此类主要实现“基础 SDS/Perp-Neg”引导，没有多概念分区和 LoRA-per-concept，仅可加载单一全局 LoRA（或不加载）。

### 2.1 `__init__` 方法（约行 56–140）

```python
class StableDiffusion(nn.Module):
    def __init__(self,
                 device,
                 fp16,
                 vram_O,
                 t_range=[0.02, 0.98],
                 max_t_range=0.98,
                 num_train_timesteps=None,
                 ddim_inv=False,
                 use_control_net=False,
                 textual_inversion_path=None,
                 LoRA_path=None,
                 guidance_opt=None):
        super().__init__()

        # ——————————————————————————
        # 1. 基础设置
        # ——————————————————————————
        self.device = device
        self.precision_t = torch.float16 if fp16 else torch.float32

        model_key = guidance_opt.model_key
        base_model_key = guidance_opt.base_model_key or "stabilityai/stable-diffusion-v1-5"
        is_safe_tensor = guidance_opt.is_safe_tensor

        # 加载 Diffusers Pipeline
        if is_safe_tensor:
            pipe = StableDiffusionPipeline.from_single_file(
                model_key,
                use_safetensors=True,
                torch_dtype=self.precision_t,
                load_safety_checker=False
            )
        else:
            pipe = StableDiffusionPipeline.from_pretrained(
                model_key,
                torch_dtype=self.precision_t
            )

        # ——————————————————————————
        # 2. 可选：加载单个全局 LoRA
        # ——————————————————————————
        if LoRA_path is not None:
            from lora_diffusion import patch_pipe, tune_lora_scale
            print(f"load global lora from: {LoRA_path}")
            patch_pipe(
                pipe,
                LoRA_path,
                patch_text=True,
                patch_ti=True,
                patch_unet=True,
            )
            # tune_lora_scale(pipe.unet, 1.00)
            # tune_lora_scale(pipe.text_encoder, 1.00)

        pipe = pipe.to(self.device)

        self.ism = not guidance_opt.sds
        self.scheduler = DDIMScheduler.from_pretrained(
            model_key if not is_safe_tensor else base_model_key,
            subfolder="scheduler",
            torch_dtype=self.precision_t
        )
        self.sche_func = ddim_step

        # …（省略：vram_O、use_control_net、textual_inversion_path 等）…

        self.pipe = pipe
        self.vae = pipe.vae
        self.tokenizer = pipe.tokenizer
        self.text_encoder = pipe.text_encoder
        self.unet = pipe.unet  # 原生 Attention，无概念分区

        # …（初始化 alphas、time_steps、噪声发生器等，与 CASD 类似）…

        print(f'[INFO] loaded foundational stable diffusion!')
```

> **关键点**
>
> 1. 只加载 **一个全局 LoRA**（若 `LoRA_path` 不为 None），通过 `lora_diffusion.patch_pipe` 修改整个管线。
> 2. **不做**任何对 UNet 中 `Attention` 的 Monkey-Patch，因而后续调用只会执行原生注意力。
> 3. `self.ism` 标志决定是否启用 ISM。

---

### 2.2 `train_step_perpneg` 方法（约行 220–344）

```python
    def train_step_perpneg(self,
                           text_embeddings: torch.FloatTensor,  # [2*B, seq_len, C_text] (uncond, cond, maybe neg…)
                           pred_rgb: torch.FloatTensor,
                           pred_depth: Optional[torch.FloatTensor] = None,
                           pred_alpha: Optional[torch.FloatTensor] = None,
                           grad_scale: float = 1.0,
                           use_control_net: bool = False,
                           save_folder: Path = None,
                           iteration: int = 0,
                           warm_up_rate: float = 0,
                           resolution: Tuple[int, int] = (512, 512),
                           guidance_opt = None,
                           as_latent: bool = False,
                           embedding_inverse: Optional[torch.FloatTensor] = None):
        """
        基础 Perpendicular-Neg Guidance 训练步骤 (无概念分区)：
          1. 用 encode_imgs 将 pred_rgb 编码成 latents (或 pred_depth)；
          2. 采样噪声 noise，选 t, prev_t；
          3. 如果 self.ism=True，用 add_noise_with_cfg 计算 target；否则 target = noise；
          4. 扩散前向：将 latents_noisy 复制成 [uncond, cond, neg…] 的 2*B 大小 → UNet (只传 encoder_hidden_states=text_embeddings) → 拆出 noise_pred_uncond, noise_pred_cond, noise_pred_neg… 
             → Perp-Neg: `delta_DSD = weighted_perpendicular_aggregator([...])`；
          5. `pred_noise = noise_pred_uncond + guidance_scale * delta_DSD`，算 SDS 梯度 → SpecifyGradient。
        """
        # ——————————————————————————
        # 1. 数据准备 & 编码
        # ——————————————————————————
        if as_latent:
            latents, _ = self.encode_imgs(pred_depth.repeat(1,3,1,1).to(self.precision_t))
        else:
            latents, _ = self.encode_imgs(pred_rgb.to(self.precision_t))

        # ——————————————————————————
        # 2. 采样噪声 & 选择 t, prev_t
        # ——————————————————————————
        noise = torch.randn((latents.shape[0], 4, resolution[0] // 8, resolution[1] // 8),
                             device=self.device)
        # …（根据 warm_up_rate 选取 ind_t, ind_prev_t → t, prev_t）…

        # ——————————————————————————
        # 3. ISM 逆向计算 target
        # ——————————————————————————
        with torch.no_grad():
            if not self.ism:
                target = noise
                latents_noisy = self.scheduler.add_noise(latents, noise, t)
                prev_latents_noisy = self.scheduler.add_noise(latents, noise, prev_t)
            else:
                _, prev_latents_noisy, pred_scores_xs = self.add_noise_with_cfg(
                    latents, noise, ind_prev_t, starting_ind,
                    embedding_inverse, guidance_opt.denoise_guidance_scale,
                    xs_delta_t, xs_steps, eta=guidance_opt.xs_eta
                )
                _, latents_noisy, pred_scores_xt = self.add_noise_with_cfg(
                    prev_latents_noisy, noise, ind_t, ind_prev_t,
                    embedding_inverse, guidance_opt.denoise_guidance_scale,
                    current_delta_t, 1, is_noisy_latent=True
                )
                target = pred_scores_xt[0][1]

        # ——————————————————————————
        # 4. UNet 前向传播 (Perp-Neg)
        # ——————————————————————————
        with torch.no_grad():
            # 复制 latents_noisy → 匹配 [uncond, cond, neg…] 共 2*B
            latent_model_input = latents_noisy.unsqueeze(0).repeat(2, 1, 1, 1, 1)
            latent_model_input = latent_model_input.reshape(-1, 4, resolution[0] // 8, resolution[1] // 8)
            tt = t.reshape(1, 1).repeat(latent_model_input.shape[0], 1).reshape(-1)
            latent_model_input = self.scheduler.scale_model_input(latent_model_input, tt[0])

            # 只传 encoder_hidden_states=text_embeddings，UNet 内部使用原生注意力
            unet_output = self.unet(
                latent_model_input.to(self.precision_t),
                tt.to(self.precision_t),
                encoder_hidden_states=text_embeddings.to(self.precision_t)
            ).sample

            # 拆分 uncond, cond, neg…
            unet_output = unet_output.reshape(2, -1, 4, resolution[0] // 8, resolution[1] // 8)
            noise_pred_uncond = unet_output[0]  # 第一份认为是无条件
            # cond 和 neg 都在 unet_output[1:] 中
            noise_preds = unet_output[1:]  # e.g. [noise_pred_cond, noise_pred_neg, …]

            # Perp-Neg 聚合：根据 negative 权重，把 cond → 正方向，neg → 负方向
            delta_DSD = self.weighted_perpendicular_aggregator(
                noise_preds=noise_preds,
                uncond_preds=noise_pred_uncond.unsqueeze(0).repeat(len(noise_preds), 1, 1, 1),
                weights=guidance_opt.neg_weights  # 负样本权重列表
            )

        # ——————————————————————————
        # 5. 计算 SDS 梯度
        # ——————————————————————————
        pred_noise = noise_pred_uncond + guidance_opt.guidance_scale * delta_DSD
        w = lambda alphas: (((1 - alphas) / alphas) ** 0.5)
        grad = w(self.alphas[t]) * (pred_noise - target)
        grad = torch.nan_to_num(grad_scale * grad)
        loss = SpecifyGradient.apply(latents, grad)

        return loss
```

> **关键点**
>
> 1. 方法名：**`train_step_perpneg`**，而非 “`train_step`”。如果项目仅需基础 SDS/CFG，可直接把 `weighted_perpendicular_aggregator` 的逻辑改为 `noise_pred_text - noise_pred_uncond`。
> 2. UNet 调用时 **不传** `cross_attention_kwargs`，只传 `encoder_hidden_states=text_embeddings`，因此内部使用**原生注意力**。
> 3. `text_embeddings`：通常是 `[uncond, cond, neg…]` 堆叠后的 `[2*B, seq_len, C_text]`，Perp-Neg 会取其中前两份做不同权重的聚合。
> 4. `add_noise_with_cfg`：把逆向流程委托给该函数，它同样只做标准 SDS 的 DDIM 逆向，无任何概念分区。

---

### 2.3 `add_noise_with_cfg`（约行 350–430）

```python
    def add_noise_with_cfg(self,
                           latents: torch.FloatTensor,
                           noise: torch.FloatTensor,
                           ind_t: int,
                           ind_prev_t: int,
                           text_embeddings: torch.FloatTensor = None,  # 逆向文本嵌入 (单 batch: [B, N_text, C_text])
                           cfg: float = 1.0,
                           delta_t: int = 1,
                           inv_steps: int = 1,
                           is_noisy_latent: bool = False,
                           eta: float = 0.0):
        """
        基础 SDS 的 DDIM 逆向部分（无概念分区）。UNet 前向只传 `encoder_hidden_states=text_embeddings`。
        """
        pred_scores = []
        for i in range(inv_steps):
            # a. 准备当前 noisy_latents (cur_noisy_lat_)
            #    由上一轮或外部传入，此处略去
            if cfg > 1.0:
                # 复制成 2*B，以做反向 CFG
                latent_model_input = torch.cat([cur_noisy_lat_, cur_noisy_lat_], dim=0)
                cond_embeddings = torch.cat([text_embeddings, text_embeddings], dim=0)
                unet_output = self.unet(
                    latent_model_input,
                    timestep_model_input,
                    encoder_hidden_states=cond_embeddings  # 直接使用逆向嵌入
                ).sample
                uncond, cond = unet_output.chunk(2, dim=0)
                unet_output = cond + cfg * (uncond - cond)  # 反向 CFG
            else:
                # 仅无条件
                unet_output = self.unet(
                    cur_noisy_lat_,
                    timestep_model_input,
                    encoder_hidden_states=uncond_text_embedding
                ).sample

            pred_scores.append(unet_output)
            # 由 scheduler 更新 cur_noisy_lat_ (伪代码)
            # cur_noisy_lat_ = self.scheduler.step(pred_noise, cur_noisy_lat_, t, ...).prev_sample

        return prev_latents_noisy, cur_noisy_lat_, pred_scores[::-1]
```

> **关键点**
>
> * **始终**只调用 `self.unet(...)`，不带任何 `cross_attention_kwargs`；
> * 对于 `cfg>1`，把噪声潜变量与嵌入复制成 2\*B，然后再做标准的反向 CFG；
> * 不涉及任何概念分区或 LoRA-per-concept。

---

## 3. 对比与总结

| 特性                        | `StableDiffusionCASD`（sd\_utils\_casd.py）                                                                                                                                                  | `StableDiffusion`（sd\_utils.py）                                                                             |
| ------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------------------------------- |
| **核心方法**                  | `__init__`、`concept_forward`、`mod_forward`、`add_noise_with_cfg_CASD`                                                                                                                       | `__init__`、`train_step_perpneg`、`add_noise_with_cfg`                                                        |
| **是否有 `train_step_casd`** | ❌ **没有**。训练循环在外层脚本里，以 `add_noise_with_cfg_CASD` 为基础手动拼装。                                                                                                                                   | ❌ **没有**“裸” `train_step`；而用 `train_step_perpneg` 实现 Perp-Neg。                                               |
| **LoRA 处理**               | **多概念多 LoRA**：按概念名称加载 LoRA → `pipe.set_adapters`；在 `concept_forward` 中各自应用。                                                                                                                | **单全局 LoRA**（可选）：`LoRA_path` 注入，若有则对整个 UNet/文本编码器打补丁。                                                       |
| **注意力机制**                 | **猴子补丁**：将 UNet 中所有 `Attention.__call__` 替换为 `mod_forward`；若 `concept_forward=True`，走 CISM(RCA)。                                                                                           | 原生注意力：UNet 调用时不带 `cross_attention_kwargs`，执行标准 `base_forward`。                                              |
| **区域控制**                  | 依据 `pred_concept_mask` 生成的 `[B, num_concepts, H_lat, W_lat]` 掩码 → 下采样到 `[B,1,H_lat,W_lat]` → 传入 `concept_forward`。                                                                         | **无**概念/区域分区，整个图像都用同一文本条件。                                                                                  |
| **UNet 调用**               | 调用时传 `latent_model_input`, `tt`, `encoder_hidden_states=text_embeddings` + `cross_attention_kwargs`（含 `mask`、`text_embeddings_dict`、`adapter_names`、`lora_scale`, `concept_forward=True`）。 | 调用时仅传 `latent_model_input`, `tt`, `encoder_hidden_states=text_embeddings`； **不带** `cross_attention_kwargs`。 |
| **DDIM 逆向**               | 用 `add_noise_with_cfg_CASD`：每一步都带上 `concept_mask_dict`, `inverse_text_embeddings_dict` 等，UNet 走 CISM 注意力 → 使得逆向目标也考虑概念分区。                                                                  | 用 `add_noise_with_cfg`：只带 `text_embeddings`，UNet 走标准注意力。                                                    |
| **训练入口**                  | 外层脚本（如 `my_train.py`）会先构造 `pred_concept_mask`, `text_embeddings_dict`，然后反复调用 `add_noise_with_cfg_CASD` 及后续 UNet/梯度计算。                                                                      | `train_step_perpneg`：传入 `[uncond, cond, neg…]` 嵌入，UNet 调用标准注意力 → Perp-Neg 聚合 → SDS 梯度。                      |

---

### 小结

1. **`StableDiffusionCASD`**

   * **没有** `train_step_casd` 方法：它只暴露 `__init__`、`add_noise_with_cfg_CASD` 以及几个注意力相关的函数 (`concept_forward` / `mod_forward` / `base_forward` / `concept_lora_cross_attn_*`)。
   * 真正的训练循环（包含梯度计算与损失）写在外部脚本里，通过传入 `pred_concept_mask`、`text_embeddings_dict`、`LoRA_adapters` 等，调用 `add_noise_with_cfg_CASD` → UNet → 再自行计算 CFG 及 SDS 梯度。

2. **`StableDiffusion`（基础版）**

   * **没有**裸露的 `train_step`，而是 `train_step_perpneg`（以及可选的 `train_step_fedavg`、`train_step_mos`）。
   * 用 `add_noise_with_cfg` 执行 DDIM 逆向 → UNet（只用 `encoder_hidden_states=text_embeddings`）→ Perp-Neg 聚合 → SDS 梯度。

以上代码片段与注释即为 **真实存在**、**逐行可对照** 的实现，你可以按注释中的行号区间在本地项目里快速跳转并核对。希望对你阅读、理解 MultiDreamer3D 中 “CISM+RCA” 与 “基础 SDS” 的差异、以及如何在训练脚本里调用它们有帮助。
