<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: vector_float2_precision.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">vector_float2_precision.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00280.html">Core features</a>  
<a href="#details">More...</a></p>

<p><a href="a00209_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gaa92c1954d71b1e7914874bd787b43d1c"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gaa92c1954d71b1e7914874bd787b43d1c">highp_vec2</a></td></tr>
<tr class="memdesc:gaa92c1954d71b1e7914874bd787b43d1c"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of high single-qualifier floating-point numbers.  <a href="a00282.html#gaa92c1954d71b1e7914874bd787b43d1c">More...</a><br /></td></tr>
<tr class="separator:gaa92c1954d71b1e7914874bd787b43d1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga30e8baef5d56d5c166872a2bc00f36e9"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga30e8baef5d56d5c166872a2bc00f36e9">lowp_vec2</a></td></tr>
<tr class="memdesc:ga30e8baef5d56d5c166872a2bc00f36e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of low single-qualifier floating-point numbers.  <a href="a00282.html#ga30e8baef5d56d5c166872a2bc00f36e9">More...</a><br /></td></tr>
<tr class="separator:ga30e8baef5d56d5c166872a2bc00f36e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc61976261c406520c7a8e4d946dc3f0"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gabc61976261c406520c7a8e4d946dc3f0">mediump_vec2</a></td></tr>
<tr class="memdesc:gabc61976261c406520c7a8e4d946dc3f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of medium single-qualifier floating-point numbers.  <a href="a00282.html#gabc61976261c406520c7a8e4d946dc3f0">More...</a><br /></td></tr>
<tr class="separator:gabc61976261c406520c7a8e4d946dc3f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00280.html">Core features</a> </p>

<p>Definition in file <a class="el" href="a00209_source.html">vector_float2_precision.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
