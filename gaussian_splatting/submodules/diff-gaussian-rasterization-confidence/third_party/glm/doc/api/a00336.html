<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_matrix_factorisation</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_matrix_factorisation<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00081.html" title="GLM_GTX_matrix_factorisation ">glm/gtx/matrix_factorisation.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaf39f4e5f78eb29c1a90277d45b9b3feb"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf39f4e5f78eb29c1a90277d45b9b3feb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00336.html#gaf39f4e5f78eb29c1a90277d45b9b3feb">fliplr</a> (mat&lt; C, R, T, Q &gt; const &amp;in)</td></tr>
<tr class="memdesc:gaf39f4e5f78eb29c1a90277d45b9b3feb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Flips the matrix columns right and left.  <a href="a00336.html#gaf39f4e5f78eb29c1a90277d45b9b3feb">More...</a><br /></td></tr>
<tr class="separator:gaf39f4e5f78eb29c1a90277d45b9b3feb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga85003371f0ba97380dd25e8905de1870"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga85003371f0ba97380dd25e8905de1870"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00336.html#ga85003371f0ba97380dd25e8905de1870">flipud</a> (mat&lt; C, R, T, Q &gt; const &amp;in)</td></tr>
<tr class="memdesc:ga85003371f0ba97380dd25e8905de1870"><td class="mdescLeft">&#160;</td><td class="mdescRight">Flips the matrix rows up and down.  <a href="a00336.html#ga85003371f0ba97380dd25e8905de1870">More...</a><br /></td></tr>
<tr class="separator:ga85003371f0ba97380dd25e8905de1870"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac62d7bfc8dc661e616620d70552cd566"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac62d7bfc8dc661e616620d70552cd566"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00336.html#gac62d7bfc8dc661e616620d70552cd566">qr_decompose</a> (mat&lt; C, R, T, Q &gt; const &amp;in, mat&lt;(C&lt; R?C:R), R, T, Q &gt; &amp;q, mat&lt; C,(C&lt; R?C:R), T, Q &gt; &amp;r)</td></tr>
<tr class="memdesc:gac62d7bfc8dc661e616620d70552cd566"><td class="mdescLeft">&#160;</td><td class="mdescRight">Performs QR factorisation of a matrix.  <a href="a00336.html#gac62d7bfc8dc661e616620d70552cd566">More...</a><br /></td></tr>
<tr class="separator:gac62d7bfc8dc661e616620d70552cd566"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga82874e2ebe891ba35ac21d9993873758"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga82874e2ebe891ba35ac21d9993873758"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00336.html#ga82874e2ebe891ba35ac21d9993873758">rq_decompose</a> (mat&lt; C, R, T, Q &gt; const &amp;in, mat&lt;(C&lt; R?C:R), R, T, Q &gt; &amp;r, mat&lt; C,(C&lt; R?C:R), T, Q &gt; &amp;q)</td></tr>
<tr class="memdesc:ga82874e2ebe891ba35ac21d9993873758"><td class="mdescLeft">&#160;</td><td class="mdescRight">Performs RQ factorisation of a matrix.  <a href="a00336.html#ga82874e2ebe891ba35ac21d9993873758">More...</a><br /></td></tr>
<tr class="separator:ga82874e2ebe891ba35ac21d9993873758"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00081.html" title="GLM_GTX_matrix_factorisation ">glm/gtx/matrix_factorisation.hpp</a>&gt; to use the features of this extension. </p>
<p>Functions to factor matrices in various forms </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaf39f4e5f78eb29c1a90277d45b9b3feb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;C, R, T, Q&gt; glm::fliplr </td>
          <td>(</td>
          <td class="paramtype">mat&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>in</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Flips the matrix columns right and left. </p>
<p>From GLM_GTX_matrix_factorisation extension. </p>

</div>
</div>
<a class="anchor" id="ga85003371f0ba97380dd25e8905de1870"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;C, R, T, Q&gt; glm::flipud </td>
          <td>(</td>
          <td class="paramtype">mat&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>in</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Flips the matrix rows up and down. </p>
<p>From GLM_GTX_matrix_factorisation extension. </p>

</div>
</div>
<a class="anchor" id="gac62d7bfc8dc661e616620d70552cd566"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::qr_decompose </td>
          <td>(</td>
          <td class="paramtype">mat&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>in</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Performs QR factorisation of a matrix. </p>
<p>Returns 2 matrices, q and r, such that the columns of q are orthonormal and span the same subspace than those of the input matrix, r is an upper triangular matrix, and q*r=in. Given an n-by-m input matrix, q has dimensions min(n,m)-by-m, and r has dimensions n-by-min(n,m).</p>
<p>From GLM_GTX_matrix_factorisation extension. </p>

</div>
</div>
<a class="anchor" id="ga82874e2ebe891ba35ac21d9993873758"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::rq_decompose </td>
          <td>(</td>
          <td class="paramtype">mat&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>in</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Performs RQ factorisation of a matrix. </p>
<p>Returns 2 matrices, r and q, such that r is an upper triangular matrix, the rows of q are orthonormal and span the same subspace than those of the input matrix, and r*q=in. Note that in the context of RQ factorisation, the diagonal is seen as starting in the lower-right corner of the matrix, instead of the usual upper-left. Given an n-by-m input matrix, r has dimensions min(n,m)-by-m, and q has dimensions n-by-min(n,m).</p>
<p>From GLM_GTX_matrix_factorisation extension. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
