<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Experimental extensions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Modules</a>  </div>
  <div class="headertitle">
<div class="title">Experimental extensions</div>  </div>
</div><!--header-->
<div class="contents">

<p>Experimental features not specified by GLSL specification.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Modules</h2></td></tr>
<tr class="memitem:a00308"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00308.html">GLM_GTX_associated_min_max</a></td></tr>
<tr class="memdesc:a00308"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00007.html" title="GLM_GTX_associated_min_max ">glm/gtx/associated_min_max.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00309"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00309.html">GLM_GTX_bit</a></td></tr>
<tr class="memdesc:a00309"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00008.html" title="GLM_GTX_bit ">glm/gtx/bit.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00310"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00310.html">GLM_GTX_closest_point</a></td></tr>
<tr class="memdesc:a00310"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00010.html" title="GLM_GTX_closest_point ">glm/gtx/closest_point.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00311"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00311.html">GLM_GTX_color_encoding</a></td></tr>
<tr class="memdesc:a00311"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00011.html" title="GLM_GTX_color_encoding ">glm/gtx/color_encoding.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00312"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00312.html">GLM_GTX_color_space</a></td></tr>
<tr class="memdesc:a00312"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00013.html" title="GLM_GTX_color_space ">glm/gtx/color_space.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00313"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00313.html">GLM_GTX_color_space_YCoCg</a></td></tr>
<tr class="memdesc:a00313"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00014.html" title="GLM_GTX_color_space_YCoCg ">glm/gtx/color_space_YCoCg.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00314"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00314.html">GLM_GTX_common</a></td></tr>
<tr class="memdesc:a00314"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00016.html" title="GLM_GTX_common ">glm/gtx/common.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00315"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html">GLM_GTX_compatibility</a></td></tr>
<tr class="memdesc:a00315"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00017.html" title="GLM_GTX_compatibility ">glm/gtx/compatibility.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00316"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00316.html">GLM_GTX_component_wise</a></td></tr>
<tr class="memdesc:a00316"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00018.html" title="GLM_GTX_component_wise ">glm/gtx/component_wise.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00317"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00317.html">GLM_GTX_dual_quaternion</a></td></tr>
<tr class="memdesc:a00317"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00022.html" title="GLM_GTX_dual_quaternion ">glm/gtx/dual_quaternion.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00318"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00318.html">GLM_GTX_easing</a></td></tr>
<tr class="memdesc:a00318"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00023.html" title="GLM_GTX_easing ">glm/gtx/easing.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00319"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00319.html">GLM_GTX_euler_angles</a></td></tr>
<tr class="memdesc:a00319"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00025.html" title="GLM_GTX_euler_angles ">glm/gtx/euler_angles.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00320"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00320.html">GLM_GTX_extend</a></td></tr>
<tr class="memdesc:a00320"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00028.html" title="GLM_GTX_extend ">glm/gtx/extend.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00321"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00321.html">GLM_GTX_extented_min_max</a></td></tr>
<tr class="memdesc:a00321"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;glm/gtx/extented_min_max.hpp&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00322"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00322.html">GLM_GTX_exterior_product</a></td></tr>
<tr class="memdesc:a00322"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00030.html" title="GLM_GTX_exterior_product ">glm/gtx/exterior_product.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00323"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00323.html">GLM_GTX_fast_exponential</a></td></tr>
<tr class="memdesc:a00323"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00031.html" title="GLM_GTX_fast_exponential ">glm/gtx/fast_exponential.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00324"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00324.html">GLM_GTX_fast_square_root</a></td></tr>
<tr class="memdesc:a00324"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00032.html" title="GLM_GTX_fast_square_root ">glm/gtx/fast_square_root.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00325"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00325.html">GLM_GTX_fast_trigonometry</a></td></tr>
<tr class="memdesc:a00325"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00033.html" title="GLM_GTX_fast_trigonometry ">glm/gtx/fast_trigonometry.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00326"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00326.html">GLM_GTX_functions</a></td></tr>
<tr class="memdesc:a00326"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00034.html" title="GLM_GTX_functions ">glm/gtx/functions.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00327"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00327.html">GLM_GTX_gradient_paint</a></td></tr>
<tr class="memdesc:a00327"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00038.html" title="GLM_GTX_gradient_paint ">glm/gtx/gradient_paint.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00328"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00328.html">GLM_GTX_handed_coordinate_space</a></td></tr>
<tr class="memdesc:a00328"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;glm/gtx/handed_coordinate_system.hpp&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00329"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00329.html">GLM_GTX_hash</a></td></tr>
<tr class="memdesc:a00329"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00040.html" title="GLM_GTX_hash ">glm/gtx/hash.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00330"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html">GLM_GTX_integer</a></td></tr>
<tr class="memdesc:a00330"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00042.html" title="GLM_GTX_integer ">glm/gtx/integer.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00331"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00331.html">GLM_GTX_intersect</a></td></tr>
<tr class="memdesc:a00331"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00044.html" title="GLM_GTX_intersect ">glm/gtx/intersect.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00332"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00332.html">GLM_GTX_io</a></td></tr>
<tr class="memdesc:a00332"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00045.html" title="GLM_GTX_io ">glm/gtx/io.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00333"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00333.html">GLM_GTX_log_base</a></td></tr>
<tr class="memdesc:a00333"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00046.html" title="GLM_GTX_log_base ">glm/gtx/log_base.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00334"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00334.html">GLM_GTX_matrix_cross_product</a></td></tr>
<tr class="memdesc:a00334"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00061.html" title="GLM_GTX_matrix_cross_product ">glm/gtx/matrix_cross_product.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00335"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00335.html">GLM_GTX_matrix_decompose</a></td></tr>
<tr class="memdesc:a00335"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00062.html" title="GLM_GTX_matrix_decompose ">glm/gtx/matrix_decompose.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00336"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00336.html">GLM_GTX_matrix_factorisation</a></td></tr>
<tr class="memdesc:a00336"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00081.html" title="GLM_GTX_matrix_factorisation ">glm/gtx/matrix_factorisation.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00337"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00337.html">GLM_GTX_matrix_interpolation</a></td></tr>
<tr class="memdesc:a00337"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00101.html" title="GLM_GTX_matrix_interpolation ">glm/gtx/matrix_interpolation.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00338"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00338.html">GLM_GTX_matrix_major_storage</a></td></tr>
<tr class="memdesc:a00338"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00103.html" title="GLM_GTX_matrix_major_storage ">glm/gtx/matrix_major_storage.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00339"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00339.html">GLM_GTX_matrix_operation</a></td></tr>
<tr class="memdesc:a00339"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00104.html" title="GLM_GTX_matrix_operation ">glm/gtx/matrix_operation.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00340"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00340.html">GLM_GTX_matrix_query</a></td></tr>
<tr class="memdesc:a00340"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00106.html" title="GLM_GTX_matrix_query ">glm/gtx/matrix_query.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00341"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00341.html">GLM_GTX_matrix_transform_2d</a></td></tr>
<tr class="memdesc:a00341"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00110.html" title="GLM_GTX_matrix_transform_2d ">glm/gtx/matrix_transform_2d.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00342"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00342.html">GLM_GTX_mixed_producte</a></td></tr>
<tr class="memdesc:a00342"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00111.html" title="GLM_GTX_mixed_producte ">glm/gtx/mixed_product.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00343"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00343.html">GLM_GTX_norm</a></td></tr>
<tr class="memdesc:a00343"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00113.html" title="GLM_GTX_norm ">glm/gtx/norm.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00344"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00344.html">GLM_GTX_normal</a></td></tr>
<tr class="memdesc:a00344"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00114.html" title="GLM_GTX_normal ">glm/gtx/normal.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00345"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00345.html">GLM_GTX_normalize_dot</a></td></tr>
<tr class="memdesc:a00345"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;glm/gtx/normalized_dot.hpp&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00346"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00346.html">GLM_GTX_number_precision</a></td></tr>
<tr class="memdesc:a00346"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00116.html" title="GLM_GTX_number_precision ">glm/gtx/number_precision.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00347"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00347.html">GLM_GTX_optimum_pow</a></td></tr>
<tr class="memdesc:a00347"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00117.html" title="GLM_GTX_optimum_pow ">glm/gtx/optimum_pow.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00348"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00348.html">GLM_GTX_orthonormalize</a></td></tr>
<tr class="memdesc:a00348"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00118.html" title="GLM_GTX_orthonormalize ">glm/gtx/orthonormalize.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00349"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00349.html">GLM_GTX_perpendicular</a></td></tr>
<tr class="memdesc:a00349"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00121.html" title="GLM_GTX_perpendicular ">glm/gtx/perpendicular.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00350"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00350.html">GLM_GTX_polar_coordinates</a></td></tr>
<tr class="memdesc:a00350"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00122.html" title="GLM_GTX_polar_coordinates ">glm/gtx/polar_coordinates.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00351"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00351.html">GLM_GTX_projection</a></td></tr>
<tr class="memdesc:a00351"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00123.html" title="GLM_GTX_projection ">glm/gtx/projection.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00352"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00352.html">GLM_GTX_quaternion</a></td></tr>
<tr class="memdesc:a00352"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00126.html" title="GLM_GTX_quaternion ">glm/gtx/quaternion.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00353"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00353.html">GLM_GTX_range</a></td></tr>
<tr class="memdesc:a00353"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00138.html" title="GLM_GTX_range ">glm/gtx/range.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00354"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00354.html">GLM_GTX_raw_data</a></td></tr>
<tr class="memdesc:a00354"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00139.html" title="GLM_GTX_raw_data ">glm/gtx/raw_data.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00355"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00355.html">GLM_GTX_rotate_normalized_axis</a></td></tr>
<tr class="memdesc:a00355"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00141.html" title="GLM_GTX_rotate_normalized_axis ">glm/gtx/rotate_normalized_axis.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00356"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00356.html">GLM_GTX_rotate_vector</a></td></tr>
<tr class="memdesc:a00356"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00142.html" title="GLM_GTX_rotate_vector ">glm/gtx/rotate_vector.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00357"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00357.html">GLM_GTX_scalar_relational</a></td></tr>
<tr class="memdesc:a00357"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00150.html" title="GLM_GTX_scalar_relational ">glm/gtx/scalar_relational.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00358"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00358.html">GLM_GTX_spline</a></td></tr>
<tr class="memdesc:a00358"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00154.html" title="GLM_GTX_spline ">glm/gtx/spline.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00359"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00359.html">GLM_GTX_std_based_type</a></td></tr>
<tr class="memdesc:a00359"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00155.html" title="GLM_GTX_std_based_type ">glm/gtx/std_based_type.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00360"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00360.html">GLM_GTX_string_cast</a></td></tr>
<tr class="memdesc:a00360"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00156.html" title="GLM_GTX_string_cast ">glm/gtx/string_cast.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00361"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00361.html">GLM_GTX_texture</a></td></tr>
<tr class="memdesc:a00361"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00157.html" title="GLM_GTX_texture ">glm/gtx/texture.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00362"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00362.html">GLM_GTX_transform</a></td></tr>
<tr class="memdesc:a00362"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00158.html" title="GLM_GTX_transform ">glm/gtx/transform.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00363"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00363.html">GLM_GTX_transform2</a></td></tr>
<tr class="memdesc:a00363"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00159.html" title="GLM_GTX_transform2 ">glm/gtx/transform2.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00364"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00364.html">GLM_GTX_type_aligned</a></td></tr>
<tr class="memdesc:a00364"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00162.html" title="GLM_GTX_type_aligned ">glm/gtx/type_aligned.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00365"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00365.html">GLM_GTX_type_trait</a></td></tr>
<tr class="memdesc:a00365"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00177.html" title="GLM_GTX_type_trait ">glm/gtx/type_trait.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00366"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00366.html">GLM_GTX_vec_swizzle</a></td></tr>
<tr class="memdesc:a00366"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00187.html" title="GLM_GTX_vec_swizzle ">glm/gtx/vec_swizzle.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00367"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00367.html">GLM_GTX_vector_angle</a></td></tr>
<tr class="memdesc:a00367"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00188.html" title="GLM_GTX_vector_angle ">glm/gtx/vector_angle.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00368"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00368.html">GLM_GTX_vector_query</a></td></tr>
<tr class="memdesc:a00368"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00223.html" title="GLM_GTX_vector_query ">glm/gtx/vector_query.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00369"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00369.html">GLM_GTX_wrap</a></td></tr>
<tr class="memdesc:a00369"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00235.html" title="GLM_GTX_wrap ">glm/gtx/wrap.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Experimental features not specified by GLSL specification. </p>
<p>Experimental extensions are useful functions and types, but the development of their API and functionality is not necessarily stable. They can change substantially between versions. Backwards compatibility is not much of an issue for them.</p>
<p>Even if it's highly unrecommended, it's possible to include all the extensions at once by including &lt;<a class="el" href="a00027.html" title="Core features (Dependence) ">glm/ext.hpp</a>&gt;. Otherwise, each extension needs to be included a specific file. </p>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
