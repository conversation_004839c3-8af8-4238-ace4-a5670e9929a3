# -*- coding: utf-8 -*-
"""
InFusion 阶段4: 主训练流程
RCA网络训练与优化的完整管理流程
"""

import os
import torch
import time
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import logging
from pathlib import Path

# 导入阶段4组件
from .training_config_manager import TrainingConfigManager, Stage4TrainingConfig
from .rca_training_data_generator import RCATrainingDataGenerator, TrainingDataConfig
from .rca_loss_functions import RCALossFunction, RCALossConfig
from .rca_end_to_end_optimizer import EndToEndOptimizerConfig

# 导入阶段3组件
from ..stage3_rca.rca_spatial_attention import RCASpatialAttentionNetwork, RCASpatialAttentionConfig
from ..stage3_rca.rca_weight_renderer import RCAWeightRenderer, WeightRendererConfig
from ..stage3_rca.rca_cism_integration import RCACISMIntegrator, RCACISMIntegrationConfig

@dataclass
class Stage4PipelineResult:
    """阶段4完整流程结果"""
    trained_rca_network: RCASpatialAttentionNetwork   # 训练好的RCA网络
    final_gaussians: Dict[str, torch.Tensor]         # 最终优化的高斯参数
    training_history: Dict[str, List[float]]         # 训练历史记录
    validation_metrics: Dict[str, float]             # 验证指标
    final_evaluation: Dict[str, Any]                 # 最终评估结果
    pipeline_stats: Dict[str, Any]                   # 流程统计信息

class Stage4MainPipeline:
    """
    🔥 阶段4主训练流程
    
    功能:
    - 统一管理RCA网络训练的完整流程
    - 实现数据准备、模型训练、验证评估
    - 提供端到端的RCA优化管理
    - 支持断点续训和实验管理
    """
    
    def __init__(self, 
                 config_manager: TrainingConfigManager,
                 stage3_output_dir: str):
        """
        Args:
            config_manager: 配置管理器
            stage3_output_dir: 阶段3输出目录
        """
        self.config_manager = config_manager
        self.config = config_manager.get_config()
        self.stage3_output_dir = stage3_output_dir
        
        # 更新数据配置中的阶段3输出路径
        self.config.data.stage3_output_dir = stage3_output_dir
        
        # 核心组件（稍后初始化）
        self.data_generator = None
        self.rca_network = None
        self.loss_function = None
        self.integrator = None
        
        # 训练状态
        self.current_epoch = 0
        self.best_loss = float('inf')
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'classification_accuracy': [],
            'spatial_consistency': [],
            'learning_rates': []
        }
        
        # 设置实验环境
        self._setup_experiment_environment()
        
        print(f"🎯 阶段4主训练流程初始化完成")
    
    def _setup_experiment_environment(self):
        """设置实验环境"""
        print("🔧 设置实验环境...")
        
        # 1. 创建实验目录
        self.experiment_dirs = self.config_manager.create_experiment_dirs()
        
        # 2. 设置日志
        self.config_manager.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 3. 设置随机种子
        if self.config.experiment.deterministic:
            torch.manual_seed(self.config.experiment.seed)
            torch.cuda.manual_seed_all(self.config.experiment.seed)
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
            self.logger.info(f"设置随机种子: {self.config.experiment.seed}")
        
        # 4. 设置设备
        self.device = torch.device(self.config.training.device)
        if self.device.type == 'cuda':
            self.logger.info(f"使用GPU: {torch.cuda.get_device_name()}")
        else:
            self.logger.info("使用CPU训练")
        
        # 5. 保存配置
        config_save_path = os.path.join(
            self.experiment_dirs['output'], 
            'training_config.json'
        )
        self.config_manager.save_config(config_save_path)
        
        print("✅ 实验环境设置完成")
    
    def run_complete_stage4_pipeline(self) -> Stage4PipelineResult:
        """
        🔥 运行完整的阶段4训练流程
        
        Returns:
            Stage4PipelineResult: 完整的训练结果
        """
        self.logger.info("🚀 开始阶段4完整训练流程...")
        print("🚀 开始阶段4完整训练流程...")
        print("=" * 60)
        
        start_time = time.time()
        pipeline_stats = {'start_time': start_time}
        
        try:
            # 🔥 步骤1: 准备训练数据
            print("\n【步骤1/7】准备训练数据")
            print("-" * 40)
            step1_start = time.time()
            
            success = self._step1_prepare_training_data()
            if not success:
                raise RuntimeError("训练数据准备失败")
            
            step1_time = time.time() - step1_start
            pipeline_stats['step1_data_preparation'] = {
                'duration': step1_time,
                'status': 'completed'
            }
            self.logger.info(f"步骤1完成，耗时: {step1_time:.2f}s")
            print(f"✅ 步骤1完成，耗时: {step1_time:.2f}s")
            
            # 🔥 步骤2: 初始化RCA网络
            print("\n【步骤2/7】初始化RCA网络架构")
            print("-" * 40)
            step2_start = time.time()
            
            self._step2_initialize_rca_network()
            
            step2_time = time.time() - step2_start
            pipeline_stats['step2_network_initialization'] = {
                'duration': step2_time,
                'trainable_parameters': sum(p.numel() for p in self.rca_network.parameters() if p.requires_grad),
                'status': 'completed'
            }
            self.logger.info(f"步骤2完成，耗时: {step2_time:.2f}s")
            print(f"✅ 步骤2完成，耗时: {step2_time:.2f}s")
            
            # 🔥 步骤3: 设置损失函数
            print("\n【步骤3/7】设置多目标损失函数")
            print("-" * 40)
            step3_start = time.time()
            
            self._step3_setup_loss_function()
            
            step3_time = time.time() - step3_start
            pipeline_stats['step3_loss_setup'] = {
                'duration': step3_time,
                'status': 'completed'
            }
            self.logger.info(f"步骤3完成，耗时: {step3_time:.2f}s")
            print(f"✅ 步骤3完成，耗时: {step3_time:.2f}s")
            
            # 🔥 步骤4: 初始化RCA-CISM集成器
            print("\n【步骤4/7】初始化RCA-CISM集成器")
            print("-" * 40)
            step4_start = time.time()
            
            self._step4_setup_rca_cism_integration()
            
            step4_time = time.time() - step4_start
            pipeline_stats['step4_integration_setup'] = {
                'duration': step4_time,
                'status': 'completed'
            }
            self.logger.info(f"步骤4完成，耗时: {step4_time:.2f}s")
            print(f"✅ 步骤4完成，耗时: {step4_time:.2f}s")
            
            # 🔥 步骤5: 设置端到端优化器
            print("\n【步骤5/7】设置端到端优化器")
            print("-" * 40)
            step5_start = time.time()
            
            optimizers, schedulers = self._step5_setup_optimizers()
            
            step5_time = time.time() - step5_start
            pipeline_stats['step5_optimizer_setup'] = {
                'duration': step5_time,
                'num_optimizers': len(optimizers),
                'status': 'completed'
            }
            self.logger.info(f"步骤5完成，耗时: {step5_time:.2f}s")
            print(f"✅ 步骤5完成，耗时: {step5_time:.2f}s")
            
            # 🔥 步骤6: 执行训练循环
            print("\n【步骤6/7】执行RCA网络训练")
            print("-" * 40)
            step6_start = time.time()
            
            training_results = self._step6_run_training_loop(optimizers, schedulers)
            
            step6_time = time.time() - step6_start
            pipeline_stats['step6_training'] = {
                'duration': step6_time,
                'epochs_completed': training_results['epochs_completed'],
                'best_loss': training_results['best_loss'],
                'convergence_achieved': training_results['convergence_achieved'],
                'status': 'completed'
            }
            self.logger.info(f"步骤6完成，耗时: {step6_time:.2f}s")
            print(f"✅ 步骤6完成，耗时: {step6_time:.2f}s")
            
            # 🔥 步骤7: 最终验证和保存
            print("\n【步骤7/7】最终验证和保存结果")
            print("-" * 40)
            step7_start = time.time()
            
            final_results = self._step7_final_validation_and_save(training_results)
            
            step7_time = time.time() - step7_start
            pipeline_stats['step7_final_validation'] = {
                'duration': step7_time,
                'validation_accuracy': final_results['validation_metrics'].get('accuracy', 0),
                'status': 'completed'
            }
            self.logger.info(f"步骤7完成，耗时: {step7_time:.2f}s")
            print(f"✅ 步骤7完成，耗时: {step7_time:.2f}s")
            
            # 构建最终结果
            total_time = time.time() - start_time
            pipeline_stats['total_duration'] = total_time
            pipeline_stats['status'] = 'success'
            
            result = Stage4PipelineResult(
                trained_rca_network=self.rca_network,
                final_gaussians=final_results['final_gaussians'],
                training_history=self.training_history,
                validation_metrics=final_results['validation_metrics'],
                final_evaluation=final_results['final_evaluation'],
                pipeline_stats=pipeline_stats
            )
            
            # 生成最终报告
            self._generate_final_report(result)
            
            self.logger.info(f"🎉 阶段4训练流程完成！总耗时: {total_time:.2f}s")
            print(f"\n🎉 阶段4训练流程完成！总耗时: {total_time:.2f}s")
            
            return result
            
        except Exception as e:
            pipeline_stats['status'] = 'failed'
            pipeline_stats['error'] = str(e)
            self.logger.error(f"❌ 阶段4训练流程失败: {str(e)}")
            raise
    
    def _step1_prepare_training_data(self) -> bool:
        """步骤1: 准备训练数据"""
        try:
            print("   初始化训练数据生成器...")
            
            # 创建数据配置
            data_config = TrainingDataConfig(
                points_per_batch=self.config.data.points_per_batch,
                concept_balance_strategy=self.config.data.concept_balance_strategy,
                hard_mining_ratio=self.config.data.hard_mining_ratio,
                geometric_augmentation=self.config.data.geometric_augmentation,
                noise_augmentation=self.config.data.noise_augmentation,
                occlusion_augmentation=self.config.data.occlusion_augmentation,
                knn_neighbors=self.config.data.knn_neighbors,
                context_radius=self.config.data.context_radius,
                cache_dir=self.config.data.cache_dir
            )
            
            # 初始化数据生成器
            self.data_generator = RCATrainingDataGenerator(data_config)
            
            # 加载阶段3数据
            print("   加载阶段3训练数据...")
            success = self.data_generator.load_stage3_training_data(self.stage3_output_dir)
            
            if success:
                # 获取并打印数据统计
                stats = self.data_generator.get_training_statistics()
                print(f"   ✅ 数据加载成功")
                print(f"   总点数: {stats['total_points']}")
                print(f"   概念分布: {stats['concept_distribution']}")
                return True
            else:
                self.logger.error("阶段3数据加载失败")
                return False
            
        except Exception as e:
            self.logger.error(f"训练数据准备失败: {str(e)}")
            return False
    
    def _step2_initialize_rca_network(self):
        """步骤2: 初始化RCA网络"""
        print("   创建RCA空间注意力网络...")
        
        # 创建RCA网络配置
        rca_config = RCASpatialAttentionConfig(
            hidden_dims=self.config.model.hidden_dims,
            num_attention_heads=self.config.model.num_attention_heads,
            num_concepts=self.config.model.num_concepts,
            dropout_rate=self.config.model.dropout_rate,
            activation=self.config.model.activation
        )
        
        # 初始化RCA网络
        self.rca_network = RCASpatialAttentionNetwork(rca_config)
        self.rca_network.to(self.device)
        
        # 初始化网络权重
        self._initialize_network_weights()
        
        # 打印网络信息
        total_params = sum(p.numel() for p in self.rca_network.parameters())
        trainable_params = sum(p.numel() for p in self.rca_network.parameters() if p.requires_grad)
        print(f"   ✅ RCA网络初始化完成")
        print(f"   总参数量: {total_params:,}")
        print(f"   可训练参数: {trainable_params:,}")
    
    def _initialize_network_weights(self):
        """初始化网络权重"""
        init_method = self.config.optimizer.init_method
        init_gain = self.config.optimizer.init_gain
        
        for module in self.rca_network.modules():
            if isinstance(module, torch.nn.Linear):
                if init_method == "xavier_uniform":
                    torch.nn.init.xavier_uniform_(module.weight, gain=init_gain)
                elif init_method == "kaiming_normal":
                    torch.nn.init.kaiming_normal_(module.weight, nonlinearity='relu')
                elif init_method == "normal":
                    torch.nn.init.normal_(module.weight, std=0.02)
                
                if module.bias is not None:
                    torch.nn.init.constant_(module.bias, 0)
            elif isinstance(module, torch.nn.LayerNorm):
                torch.nn.init.constant_(module.bias, 0)
                torch.nn.init.constant_(module.weight, 1.0)
    
    def _step3_setup_loss_function(self):
        """步骤3: 设置损失函数"""
        print("   创建多目标损失函数...")
        
        # 创建损失配置
        loss_config = RCALossConfig(
            classification_weight=self.config.loss.classification_weight,
            use_focal_loss=self.config.loss.use_focal_loss,
            focal_alpha=self.config.loss.focal_alpha,
            focal_gamma=self.config.loss.focal_gamma,
            label_smoothing=self.config.loss.label_smoothing,
            regularization_weight=self.config.loss.regularization_weight,
            tv_loss_weight=self.config.loss.tv_loss_weight,
            entropy_regularization_weight=self.config.loss.entropy_regularization_weight,
            l2_regularization_weight=self.config.loss.l2_regularization_weight,
            consistency_weight=self.config.loss.consistency_weight,
            spatial_consistency_weight=self.config.loss.spatial_consistency_weight,
            temporal_consistency_weight=self.config.loss.temporal_consistency_weight,
            multi_view_consistency_weight=self.config.loss.multi_view_consistency_weight,
            boundary_loss_weight=self.config.loss.boundary_loss_weight,
            adaptive_weighting=self.config.loss.adaptive_weighting,
            weight_adaptation_rate=self.config.loss.weight_adaptation_rate
        )
        
        # 初始化损失函数
        self.loss_function = RCALossFunction(loss_config)
        self.loss_function.to(self.device)
        
        print("   ✅ 损失函数设置完成")
    
    def _step4_setup_rca_cism_integration(self):
        """步骤4: 设置RCA-CISM集成"""
        print("   初始化RCA-CISM集成器...")
        
        # 创建权重渲染器配置
        renderer_config = WeightRendererConfig(
            num_concepts=self.config.model.num_concepts,
            image_height=self.config.model.image_height,
            image_width=self.config.model.image_width,
            render_num_samples=self.config.model.render_num_samples
        )
        
        # 创建集成配置
        integration_config = RCACISMIntegrationConfig(
            rca_config=RCASpatialAttentionConfig(
                hidden_dims=self.config.model.hidden_dims,
                num_attention_heads=self.config.model.num_attention_heads,
                num_concepts=self.config.model.num_concepts
            ),
            renderer_config=renderer_config
        )
        
        # 初始化集成器
        self.integrator = RCACISMIntegrator(integration_config)
        self.integrator.to(self.device)
        
        print("   ✅ RCA-CISM集成器初始化完成")
    
    def _step5_setup_optimizers(self) -> tuple:
        """步骤5: 设置优化器"""
        print("   配置端到端优化器...")
        
        optimizers = {}
        schedulers = {}
        
        # RCA网络优化器
        if self.config.optimizer.optimizer_type == "adamw":
            rca_optimizer = torch.optim.AdamW(
                self.rca_network.parameters(),
                lr=self.config.optimizer.rca_learning_rate,
                weight_decay=self.config.optimizer.weight_decay,
                betas=(self.config.optimizer.beta1, self.config.optimizer.beta2),
                eps=self.config.optimizer.eps
            )
        elif self.config.optimizer.optimizer_type == "adam":
            rca_optimizer = torch.optim.Adam(
                self.rca_network.parameters(),
                lr=self.config.optimizer.rca_learning_rate,
                weight_decay=self.config.optimizer.weight_decay,
                betas=(self.config.optimizer.beta1, self.config.optimizer.beta2),
                eps=self.config.optimizer.eps
            )
        else:  # sgd
            rca_optimizer = torch.optim.SGD(
                self.rca_network.parameters(),
                lr=self.config.optimizer.rca_learning_rate,
                weight_decay=self.config.optimizer.weight_decay,
                momentum=self.config.optimizer.momentum
            )
        
        optimizers['rca'] = rca_optimizer
        
        # 学习率调度器
        if self.config.optimizer.scheduler_type == "cosine":
            rca_scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                rca_optimizer,
                T_max=self.config.training.max_epochs,
                eta_min=self.config.optimizer.rca_learning_rate * 0.01
            )
        elif self.config.optimizer.scheduler_type == "step":
            rca_scheduler = torch.optim.lr_scheduler.StepLR(
                rca_optimizer,
                step_size=self.config.optimizer.step_size,
                gamma=self.config.optimizer.gamma
            )
        else:  # plateau
            rca_scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                rca_optimizer,
                mode='min',
                patience=self.config.optimizer.patience,
                factor=self.config.optimizer.gamma
            )
        
        schedulers['rca'] = rca_scheduler
        
        print(f"   ✅ 优化器配置完成")
        print(f"   RCA优化器: {self.config.optimizer.optimizer_type}")
        print(f"   学习率调度: {self.config.optimizer.scheduler_type}")
        
        return optimizers, schedulers
    
    def _step6_run_training_loop(self, optimizers: Dict, schedulers: Dict) -> Dict[str, Any]:
        """步骤6: 执行训练循环"""
        print("   开始训练循环...")
        
        training_results = {
            'epochs_completed': 0,
            'best_loss': float('inf'),
            'convergence_achieved': False,
            'early_stopping_triggered': False
        }
        
        patience_counter = 0
        
        for epoch in range(self.config.training.max_epochs):
            self.current_epoch = epoch
            
            # 训练一个epoch
            train_metrics = self._train_one_epoch(optimizers)
            
            # 验证
            if (epoch + 1) % self.config.training.validation_interval == 0:
                val_metrics = self._validate_one_epoch()
                
                # 记录历史
                self._update_training_history(train_metrics, val_metrics)
                
                # 检查是否有改进
                current_loss = val_metrics['total_loss']
                if current_loss < training_results['best_loss']:
                    training_results['best_loss'] = current_loss
                    patience_counter = 0
                    
                    # 保存最佳模型
                    self._save_checkpoint(epoch, 'best')
                else:
                    patience_counter += 1
                
                # 早停检查
                if patience_counter >= self.config.training.early_stopping_patience:
                    print(f"   早停触发，在epoch {epoch + 1}")
                    training_results['early_stopping_triggered'] = True
                    break
                
                # 打印进度
                if (epoch + 1) % (self.config.training.validation_interval * 2) == 0:
                    print(f"   Epoch {epoch + 1}/{self.config.training.max_epochs}")
                    print(f"   训练损失: {train_metrics['total_loss']:.6f}")
                    print(f"   验证损失: {val_metrics['total_loss']:.6f}")
                    print(f"   最佳损失: {training_results['best_loss']:.6f}")
                    print(f"   学习率: {optimizers['rca'].param_groups[0]['lr']:.8f}")
            
            # 更新学习率
            if self.config.optimizer.scheduler_type == "plateau":
                schedulers['rca'].step(val_metrics['total_loss'])
            else:
                schedulers['rca'].step()
            
            # 定期保存检查点
            if (epoch + 1) % self.config.training.save_interval == 0:
                self._save_checkpoint(epoch, 'regular')
        
        training_results['epochs_completed'] = epoch + 1
        if training_results['best_loss'] < float('inf'):
            training_results['convergence_achieved'] = True
        
        print(f"   ✅ 训练完成，共{training_results['epochs_completed']}轮")
        return training_results
    
    def _train_one_epoch(self, optimizers: Dict) -> Dict[str, float]:
        """训练一个epoch"""
        self.rca_network.train()
        epoch_metrics = {
            'total_loss': 0.0,
            'classification_loss': 0.0,
            'consistency_loss': 0.0,
            'regularization_loss': 0.0,
            'num_batches': 0
        }
        
        # 生成训练批次
        num_batches = max(1, self.config.data.points_per_batch // self.config.training.batch_size)
        
        for batch_idx in range(num_batches):
            try:
                # 生成训练批次
                batch_data = self.data_generator.generate_training_batch(
                    self.config.training.batch_size
                )
                
                # 移动到设备
                for key in batch_data:
                    if torch.is_tensor(batch_data[key]):
                        batch_data[key] = batch_data[key].to(self.device)
                
                # 前向传播
                rca_predictions = self.rca_network(batch_data['features'])
                
                # 计算损失
                loss_dict = self.loss_function(
                    predictions=rca_predictions,
                    targets=batch_data['labels'],
                    weight_maps=batch_data.get('weight_maps'),
                    positions_3d=batch_data['positions'],
                    network_parameters=list(self.rca_network.parameters())
                )
                
                total_loss = loss_dict['total_loss']
                
                # 反向传播
                optimizers['rca'].zero_grad()
                total_loss.backward()
                
                # 梯度裁剪
                if self.config.optimizer.gradient_clip_norm > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.rca_network.parameters(),
                        self.config.optimizer.gradient_clip_norm
                    )
                
                optimizers['rca'].step()
                
                # 累积指标
                epoch_metrics['total_loss'] += total_loss.item()
                epoch_metrics['classification_loss'] += loss_dict['classification_loss'].item()
                epoch_metrics['consistency_loss'] += loss_dict['consistency_loss'].item()
                epoch_metrics['regularization_loss'] += loss_dict['regularization_loss'].item()
                epoch_metrics['num_batches'] += 1
                
            except Exception as e:
                self.logger.warning(f"批次{batch_idx}训练失败: {str(e)}")
                continue
        
        # 计算平均指标
        if epoch_metrics['num_batches'] > 0:
            for key in epoch_metrics:
                if key != 'num_batches':
                    epoch_metrics[key] /= epoch_metrics['num_batches']
        
        return epoch_metrics
    
    def _validate_one_epoch(self) -> Dict[str, float]:
        """验证一个epoch"""
        self.rca_network.eval()
        val_metrics = {
            'total_loss': 0.0,
            'classification_loss': 0.0,
            'consistency_loss': 0.0,
            'accuracy': 0.0,
            'num_batches': 0
        }
        
        with torch.no_grad():
            # 生成验证批次
            num_val_batches = max(1, int(self.config.data.points_per_batch * self.config.training.validation_split) // self.config.training.batch_size)
            
            for batch_idx in range(num_val_batches):
                try:
                    # 生成验证批次
                    batch_data = self.data_generator.generate_training_batch(
                        self.config.training.batch_size
                    )
                    
                    # 移动到设备
                    for key in batch_data:
                        if torch.is_tensor(batch_data[key]):
                            batch_data[key] = batch_data[key].to(self.device)
                    
                    # 前向传播
                    rca_predictions = self.rca_network(batch_data['features'])
                    
                    # 计算损失
                    loss_dict = self.loss_function(
                        predictions=rca_predictions,
                        targets=batch_data['labels'],
                        weight_maps=batch_data.get('weight_maps'),
                        positions_3d=batch_data['positions']
                    )
                    
                    # 计算准确率
                    predicted_labels = torch.argmax(rca_predictions, dim=1)
                    true_labels = batch_data['labels']['concept_ids']
                    accuracy = (predicted_labels == true_labels).float().mean()
                    
                    # 累积指标
                    val_metrics['total_loss'] += loss_dict['total_loss'].item()
                    val_metrics['classification_loss'] += loss_dict['classification_loss'].item()
                    val_metrics['consistency_loss'] += loss_dict['consistency_loss'].item()
                    val_metrics['accuracy'] += accuracy.item()
                    val_metrics['num_batches'] += 1
                    
                except Exception as e:
                    self.logger.warning(f"验证批次{batch_idx}失败: {str(e)}")
                    continue
        
        # 计算平均指标
        if val_metrics['num_batches'] > 0:
            for key in val_metrics:
                if key != 'num_batches':
                    val_metrics[key] /= val_metrics['num_batches']
        
        return val_metrics
    
    def _update_training_history(self, train_metrics: Dict, val_metrics: Dict):
        """更新训练历史"""
        self.training_history['train_loss'].append(train_metrics['total_loss'])
        self.training_history['val_loss'].append(val_metrics['total_loss'])
        self.training_history['classification_accuracy'].append(val_metrics['accuracy'])
        self.training_history['spatial_consistency'].append(val_metrics['consistency_loss'])
        
        # 记录学习率
        current_lr = self.integrator.rca_network.parameters().__next__().grad.abs().mean().item() if hasattr(self, 'integrator') else 0.0
        self.training_history['learning_rates'].append(current_lr)
    
    def _save_checkpoint(self, epoch: int, checkpoint_type: str):
        """保存检查点"""
        checkpoint_path = os.path.join(
            self.experiment_dirs['checkpoint'],
            f"{checkpoint_type}_epoch_{epoch + 1}.pt"
        )
        
        checkpoint = {
            'epoch': epoch,
            'rca_network_state_dict': self.rca_network.state_dict(),
            'training_history': self.training_history,
            'config': self.config,
            'checkpoint_type': checkpoint_type
        }
        
        torch.save(checkpoint, checkpoint_path)
        self.logger.info(f"保存{checkpoint_type}检查点: {checkpoint_path}")
    
    def _step7_final_validation_and_save(self, training_results: Dict) -> Dict[str, Any]:
        """步骤7: 最终验证和保存"""
        print("   执行最终验证...")
        
        # 加载最佳模型
        best_checkpoint_path = os.path.join(
            self.experiment_dirs['checkpoint'],
            f"best_epoch_{training_results['epochs_completed']}.pt"
        )
        
        if os.path.exists(best_checkpoint_path):
            checkpoint = torch.load(best_checkpoint_path)
            self.rca_network.load_state_dict(checkpoint['rca_network_state_dict'])
            print("   ✅ 已加载最佳模型权重")
        
        # 最终验证
        final_val_metrics = self._validate_one_epoch()
        
        # 计算最终评估指标
        final_evaluation = self._compute_final_evaluation_metrics()
        
        # 获取最终的高斯参数（这里需要从数据生成器获取）
        final_gaussians = self.data_generator.gaussian_data
        
        # 保存最终模型
        final_model_path = os.path.join(
            self.experiment_dirs['output'],
            'final_rca_network.pt'
        )
        torch.save({
            'rca_network_state_dict': self.rca_network.state_dict(),
            'training_history': self.training_history,
            'final_validation_metrics': final_val_metrics,
            'final_evaluation': final_evaluation,
            'config': self.config
        }, final_model_path)
        
        print(f"   ✅ 最终模型保存至: {final_model_path}")
        
        return {
            'validation_metrics': final_val_metrics,
            'final_evaluation': final_evaluation,
            'final_gaussians': final_gaussians
        }
    
    def _compute_final_evaluation_metrics(self) -> Dict[str, Any]:
        """计算最终评估指标"""
        evaluation = {
            'network_analysis': {
                'total_parameters': sum(p.numel() for p in self.rca_network.parameters()),
                'trainable_parameters': sum(p.numel() for p in self.rca_network.parameters() if p.requires_grad),
                'model_size_mb': sum(p.numel() * p.element_size() for p in self.rca_network.parameters()) / (1024 * 1024)
            },
            'training_analysis': {
                'total_epochs': len(self.training_history['train_loss']),
                'best_train_loss': min(self.training_history['train_loss']) if self.training_history['train_loss'] else float('inf'),
                'best_val_loss': min(self.training_history['val_loss']) if self.training_history['val_loss'] else float('inf'),
                'best_accuracy': max(self.training_history['classification_accuracy']) if self.training_history['classification_accuracy'] else 0.0,
                'convergence_epoch': self._find_convergence_epoch()
            },
            'performance_analysis': {
                'final_accuracy': self.training_history['classification_accuracy'][-1] if self.training_history['classification_accuracy'] else 0.0,
                'accuracy_improvement': self._compute_accuracy_improvement(),
                'loss_reduction_ratio': self._compute_loss_reduction_ratio(),
                'training_stability': self._compute_training_stability()
            }
        }
        
        return evaluation
    
    def _find_convergence_epoch(self) -> int:
        """查找收敛epoch"""
        if len(self.training_history['val_loss']) < 10:
            return -1
        
        # 简单的收敛检测：连续10个epoch验证损失变化小于阈值
        threshold = self.config.training.early_stopping_min_delta
        for i in range(len(self.training_history['val_loss']) - 10):
            recent_losses = self.training_history['val_loss'][i:i+10]
            if max(recent_losses) - min(recent_losses) < threshold:
                return i + 10
        
        return -1
    
    def _compute_accuracy_improvement(self) -> float:
        """计算准确率提升"""
        if len(self.training_history['classification_accuracy']) < 2:
            return 0.0
        
        initial_acc = self.training_history['classification_accuracy'][0]
        final_acc = self.training_history['classification_accuracy'][-1]
        return final_acc - initial_acc
    
    def _compute_loss_reduction_ratio(self) -> float:
        """计算损失降低比例"""
        if len(self.training_history['train_loss']) < 2:
            return 0.0
        
        initial_loss = self.training_history['train_loss'][0]
        final_loss = self.training_history['train_loss'][-1]
        if initial_loss > 0:
            return (initial_loss - final_loss) / initial_loss
        return 0.0
    
    def _compute_training_stability(self) -> float:
        """计算训练稳定性（损失方差的倒数）"""
        if len(self.training_history['val_loss']) < 10:
            return 0.0
        
        recent_losses = self.training_history['val_loss'][-10:]
        variance = torch.tensor(recent_losses).var().item()
        return 1.0 / (1.0 + variance)  # 稳定性分数
    
    def _generate_final_report(self, result: Stage4PipelineResult):
        """生成最终报告"""
        report_path = os.path.join(
            self.experiment_dirs['output'],
            'stage4_training_report.json'
        )
        
        report = {
            'experiment_info': {
                'name': self.config.experiment.experiment_name,
                'version': self.config.experiment.experiment_version,
                'description': self.config.experiment.description,
                'completion_time': time.strftime('%Y-%m-%d %H:%M:%S')
            },
            'training_summary': {
                'total_epochs': result.pipeline_stats.get('step6_training', {}).get('epochs_completed', 0),
                'best_loss': result.pipeline_stats.get('step6_training', {}).get('best_loss', float('inf')),
                'convergence_achieved': result.pipeline_stats.get('step6_training', {}).get('convergence_achieved', False),
                'total_duration': result.pipeline_stats.get('total_duration', 0)
            },
            'performance_metrics': result.validation_metrics,
            'final_evaluation': result.final_evaluation,
            'pipeline_statistics': result.pipeline_stats
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📊 最终报告已保存: {report_path}")
        
        # 打印摘要
        self._print_final_summary(result)
    
    def _print_final_summary(self, result: Stage4PipelineResult):
        """打印最终摘要"""
        print("\n" + "=" * 60)
        print("🎯 阶段4训练完成摘要")
        print("=" * 60)
        
        print(f"📊 训练统计:")
        print(f"   完成轮次: {result.pipeline_stats.get('step6_training', {}).get('epochs_completed', 0)}")
        print(f"   最佳损失: {result.pipeline_stats.get('step6_training', {}).get('best_loss', float('inf')):.6f}")
        print(f"   收敛状态: {'✅ 已收敛' if result.pipeline_stats.get('step6_training', {}).get('convergence_achieved', False) else '❌ 未收敛'}")
        
        print(f"\n🎯 性能指标:")
        print(f"   最终准确率: {result.validation_metrics.get('accuracy', 0):.4f}")
        print(f"   分类损失: {result.validation_metrics.get('classification_loss', 0):.6f}")
        print(f"   一致性损失: {result.validation_metrics.get('consistency_loss', 0):.6f}")
        
        print(f"\n⏱️  耗时统计:")
        total_time = result.pipeline_stats.get('total_duration', 0)
        print(f"   总耗时: {total_time:.2f}s ({total_time/60:.1f}分钟)")
        
        steps_time = [
            ('数据准备', result.pipeline_stats.get('step1_data_preparation', {}).get('duration', 0)),
            ('网络初始化', result.pipeline_stats.get('step2_network_initialization', {}).get('duration', 0)),
            ('损失设置', result.pipeline_stats.get('step3_loss_setup', {}).get('duration', 0)),
            ('集成设置', result.pipeline_stats.get('step4_integration_setup', {}).get('duration', 0)),
            ('优化器设置', result.pipeline_stats.get('step5_optimizer_setup', {}).get('duration', 0)),
            ('训练循环', result.pipeline_stats.get('step6_training', {}).get('duration', 0)),
            ('最终验证', result.pipeline_stats.get('step7_final_validation', {}).get('duration', 0))
        ]
        
        for step_name, duration in steps_time:
            print(f"   {step_name}: {duration:.2f}s")
        
        print("=" * 60)

def run_stage4_pipeline(config_path: str = None, 
                       stage3_output_dir: str = None,
                       custom_config: Dict[str, Any] = None) -> Stage4PipelineResult:
    """
    🔥 运行阶段4训练流程的便捷函数
    
    Args:
        config_path: 配置文件路径
        stage3_output_dir: 阶段3输出目录
        custom_config: 自定义配置覆盖
        
    Returns:
        Stage4PipelineResult: 训练结果
    """
    # 加载配置
    if config_path and os.path.exists(config_path):
        config_manager = TrainingConfigManager(config_path)
    else:
        config_manager = TrainingConfigManager()
    
    # 应用自定义配置
    if custom_config:
        for key, value in custom_config.items():
            config_manager.update_config(**{key: value})
    
    # 打印配置摘要
    config_manager.print_config_summary()
    
    # 创建并运行流程
    pipeline = Stage4MainPipeline(config_manager, stage3_output_dir)
    result = pipeline.run_complete_stage4_pipeline()
    
    return result

if __name__ == "__main__":
    # 示例使用
    stage3_dir = "./outputs/stage3_rca"
    
    # 自定义配置示例
    custom_config = {
        "training__max_epochs": 500,
        "data__points_per_batch": 4096,
        "optimizer__rca_learning_rate": 0.002
    }
    
    result = run_stage4_pipeline(
        stage3_output_dir=stage3_dir,
        custom_config=custom_config
    )
    
    print("🎉 阶段4训练完成！")