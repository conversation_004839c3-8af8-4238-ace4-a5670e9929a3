var searchData=
[
  ['l1norm',['l1Norm',['../a00343.html#gae2fc0b2aa967bebfd6a244700bff6997',1,'glm::l1Norm(vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)'],['../a00343.html#ga1a7491e2037ceeb37f83ce41addfc0be',1,'glm::l1Norm(vec&lt; 3, T, Q &gt; const &amp;v)']]],
  ['l2norm',['l2Norm',['../a00343.html#ga41340b2ef40a9307ab0f137181565168',1,'glm::l2Norm(vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)'],['../a00343.html#gae288bde8f0e41fb4ed62e65137b18cba',1,'glm::l2Norm(vec&lt; 3, T, Q &gt; const &amp;x)']]],
  ['ldexp',['ldexp',['../a00241.html#gac3010e0a0c35a1b514540f2fb579c58c',1,'glm']]],
  ['lefthanded',['leftHanded',['../a00328.html#ga6f1bad193b9a3b048543d1935cf04dd3',1,'glm']]],
  ['length',['length',['../a00254.html#gab703732449be6c7199369b3f9a91ed38',1,'glm::length(qua&lt; T, Q &gt; const &amp;q)'],['../a00279.html#ga0cdabbb000834d994a1d6dc56f8f5263',1,'glm::length(vec&lt; L, T, Q &gt; const &amp;x)']]],
  ['length2',['length2',['../a00343.html#ga8d1789651050adb7024917984b41c3de',1,'glm::length2(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00352.html#ga58a609b1b8ab965f5df2702e8ca4e75b',1,'glm::length2(qua&lt; T, Q &gt; const &amp;q)']]],
  ['lerp',['lerp',['../a00248.html#ga6033dc0741051fa463a0a147ba29f293',1,'glm::lerp(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a)'],['../a00315.html#ga5494ba3a95ea6594c86fc75236886864',1,'glm::lerp(T x, T y, T a)'],['../a00315.html#gaa551c0a0e16d2d4608e49f7696df897f',1,'glm::lerp(const vec&lt; 2, T, Q &gt; &amp;x, const vec&lt; 2, T, Q &gt; &amp;y, T a)'],['../a00315.html#ga44a8b5fd776320f1713413dec959b32a',1,'glm::lerp(const vec&lt; 3, T, Q &gt; &amp;x, const vec&lt; 3, T, Q &gt; &amp;y, T a)'],['../a00315.html#ga89ac8e000199292ec7875519d27e214b',1,'glm::lerp(const vec&lt; 4, T, Q &gt; &amp;x, const vec&lt; 4, T, Q &gt; &amp;y, T a)'],['../a00315.html#gaf68de5baf72d16135368b8ef4f841604',1,'glm::lerp(const vec&lt; 2, T, Q &gt; &amp;x, const vec&lt; 2, T, Q &gt; &amp;y, const vec&lt; 2, T, Q &gt; &amp;a)'],['../a00315.html#ga4ae1a616c8540a2649eab8e0cd051bb3',1,'glm::lerp(const vec&lt; 3, T, Q &gt; &amp;x, const vec&lt; 3, T, Q &gt; &amp;y, const vec&lt; 3, T, Q &gt; &amp;a)'],['../a00315.html#gab5477ab69c40de4db5d58d3359529724',1,'glm::lerp(const vec&lt; 4, T, Q &gt; &amp;x, const vec&lt; 4, T, Q &gt; &amp;y, const vec&lt; 4, T, Q &gt; &amp;a)'],['../a00317.html#gace8380112d16d33f520839cb35a4d173',1,'glm::lerp(tdualquat&lt; T, Q &gt; const &amp;x, tdualquat&lt; T, Q &gt; const &amp;y, T const &amp;a)']]],
  ['lessthan',['lessThan',['../a00299.html#gad091a2d22c8acfebfa92bcfca1dfe9c4',1,'glm::lessThan(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)'],['../a00374.html#gae90ed1592c395f93e3f3dfce6b2f39c6',1,'glm::lessThan(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)']]],
  ['lessthanequal',['lessThanEqual',['../a00299.html#gac00012eea281800d2403f4ea8443134d',1,'glm::lessThanEqual(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)'],['../a00374.html#gab0bdafc019d227257ff73fb5bcca1718',1,'glm::lessThanEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)']]],
  ['levels',['levels',['../a00361.html#gaa8c377f4e63486db4fa872d77880da73',1,'glm']]],
  ['lineargradient',['linearGradient',['../a00327.html#ga849241df1e55129b8ce9476200307419',1,'glm']]],
  ['linearinterpolation',['linearInterpolation',['../a00318.html#ga290c3e47cb0a49f2e8abe90b1872b649',1,'glm']]],
  ['linearrand',['linearRand',['../a00300.html#ga04e241ab88374a477a2c2ceadd2fa03d',1,'glm::linearRand(genType Min, genType Max)'],['../a00300.html#ga94731130c298a9ff5e5025fdee6d97a0',1,'glm::linearRand(vec&lt; L, T, Q &gt; const &amp;Min, vec&lt; L, T, Q &gt; const &amp;Max)']]],
  ['lmaxnorm',['lMaxNorm',['../a00343.html#gad58a8231fc32e38104a9e1c4d3c0cb64',1,'glm::lMaxNorm(vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)'],['../a00343.html#ga6968a324837a8e899396d44de23d5aae',1,'glm::lMaxNorm(vec&lt; 3, T, Q &gt; const &amp;x)']]],
  ['ln_5fln_5ftwo',['ln_ln_two',['../a00290.html#gaca94292c839ed31a405ab7a81ae7e850',1,'glm']]],
  ['ln_5ften',['ln_ten',['../a00290.html#gaf97ebc6c059ffd788e6c4946f71ef66c',1,'glm']]],
  ['ln_5ftwo',['ln_two',['../a00290.html#ga24f4d27765678116f41a2f336ab7975c',1,'glm']]],
  ['log',['log',['../a00242.html#ga918c9f3fd086ce20e6760c903bd30fa9',1,'glm::log(vec&lt; L, T, Q &gt; const &amp;v)'],['../a00256.html#gaa5f7b20e296671b16ce25a2ab7ad5473',1,'glm::log(qua&lt; T, Q &gt; const &amp;q)'],['../a00333.html#ga60a7b0a401da660869946b2b77c710c9',1,'glm::log(genType const &amp;x, genType const &amp;base)']]],
  ['log2',['log2',['../a00242.html#ga82831c7d9cca777cebedfe03a19c8d75',1,'glm::log2(vec&lt; L, T, Q &gt; const &amp;v)'],['../a00292.html#ga9bd682e74bfacb005c735305207ec417',1,'glm::log2(genIUType x)']]],
  ['lookat',['lookAt',['../a00247.html#gaa64aa951a0e99136bba9008d2b59c78e',1,'glm']]],
  ['lookatlh',['lookAtLH',['../a00247.html#gab2c09e25b0a16d3a9d89cc85bbae41b0',1,'glm']]],
  ['lookatrh',['lookAtRH',['../a00247.html#gacfa12c8889c754846bc20c65d9b5c701',1,'glm']]],
  ['lowestbitvalue',['lowestBitValue',['../a00309.html#ga2ff6568089f3a9b67f5c30918855fc6f',1,'glm']]],
  ['luminosity',['luminosity',['../a00312.html#gad028e0a4f1a9c812b39439b746295b34',1,'glm']]],
  ['lxnorm',['lxNorm',['../a00343.html#gacad23d30497eb16f67709f2375d1f66a',1,'glm::lxNorm(vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y, unsigned int Depth)'],['../a00343.html#gac61b6d81d796d6eb4d4183396a19ab91',1,'glm::lxNorm(vec&lt; 3, T, Q &gt; const &amp;x, unsigned int Depth)']]]
];
