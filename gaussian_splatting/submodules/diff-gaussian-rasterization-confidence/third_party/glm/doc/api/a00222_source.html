<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: vector_integer.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">vector_integer.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00222.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment">// Dependencies</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &quot;../detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#include &quot;../detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../detail/_vectorize.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../vector_relational.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../common.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_EXT_vector_integer extension included&quot;)</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;{</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        GLM_FUNC_DECL vec&lt;L, bool, Q&gt; <a class="code" href="a00274.html#gabf2b61ded7049bcb13e25164f832a290">isPowerOfTwo</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00274.html#gabba67f8aac9915e10fca727277274502">nextPowerOfTwo</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00274.html#ga759db73f14d79f63612bd2398b577e7a">prevPowerOfTwo</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;        GLM_FUNC_DECL vec&lt;L, bool, Q&gt; <a class="code" href="a00274.html#gabb4360e38c0943d8981ba965dead519d">isMultiple</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, T Multiple);</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        GLM_FUNC_DECL vec&lt;L, bool, Q&gt; <a class="code" href="a00274.html#gabb4360e38c0943d8981ba965dead519d">isMultiple</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Multiple);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00274.html#gacda365edad320c7aff19cc283a3b8ca2">nextMultiple</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, T Multiple);</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00274.html#gacda365edad320c7aff19cc283a3b8ca2">nextMultiple</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Multiple);</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00274.html#ga51e04379e8aebbf83e2e5ab094578ee9">prevMultiple</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, T Multiple);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00274.html#ga51e04379e8aebbf83e2e5ab094578ee9">prevMultiple</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; v, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Multiple);</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        GLM_FUNC_DECL vec&lt;L, int, Q&gt; <a class="code" href="a00274.html#gaff61eca266da315002a3db92ff0dd604">findNSB</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; Source, vec&lt;L, int, Q&gt; SignificantBitCount);</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;} <span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;<span class="preprocessor">#include &quot;vector_integer.inl&quot;</span></div>
<div class="ttc" id="a00274_html_gabf2b61ded7049bcb13e25164f832a290"><div class="ttname"><a href="a00274.html#gabf2b61ded7049bcb13e25164f832a290">glm::isPowerOfTwo</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, bool, Q &gt; isPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Return true if the value is a power of two number. </div></div>
<div class="ttc" id="a00274_html_gabba67f8aac9915e10fca727277274502"><div class="ttname"><a href="a00274.html#gabba67f8aac9915e10fca727277274502">glm::nextPowerOfTwo</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; nextPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Return the power of two number which value is just higher the input value, round up to a power of two...</div></div>
<div class="ttc" id="a00274_html_gacda365edad320c7aff19cc283a3b8ca2"><div class="ttname"><a href="a00274.html#gacda365edad320c7aff19cc283a3b8ca2">glm::nextMultiple</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; nextMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</div><div class="ttdoc">Higher multiple number of Source. </div></div>
<div class="ttc" id="a00274_html_ga759db73f14d79f63612bd2398b577e7a"><div class="ttname"><a href="a00274.html#ga759db73f14d79f63612bd2398b577e7a">glm::prevPowerOfTwo</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; prevPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)</div><div class="ttdoc">Return the power of two number which value is just lower the input value, round down to a power of tw...</div></div>
<div class="ttc" id="a00274_html_gaff61eca266da315002a3db92ff0dd604"><div class="ttname"><a href="a00274.html#gaff61eca266da315002a3db92ff0dd604">glm::findNSB</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, int, Q &gt; findNSB(vec&lt; L, T, Q &gt; const &amp;Source, vec&lt; L, int, Q &gt; SignificantBitCount)</div><div class="ttdoc">Returns the bit number of the Nth significant bit set to 1 in the binary representation of value...</div></div>
<div class="ttc" id="a00274_html_ga51e04379e8aebbf83e2e5ab094578ee9"><div class="ttname"><a href="a00274.html#ga51e04379e8aebbf83e2e5ab094578ee9">glm::prevMultiple</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; prevMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</div><div class="ttdoc">Lower multiple number of Source. </div></div>
<div class="ttc" id="a00274_html_gabb4360e38c0943d8981ba965dead519d"><div class="ttname"><a href="a00274.html#gabb4360e38c0943d8981ba965dead519d">glm::isMultiple</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, bool, Q &gt; isMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</div><div class="ttdoc">Return true if the &#39;Value&#39; is a multiple of &#39;Multiple&#39;. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
