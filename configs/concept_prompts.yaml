# CISM概念提示配置文件
# 阶段2.2: concept_id与文本概念的映射

description: "Concept ID to text prompt mapping for CISM guidance system"
version: "1.0"

# 概念提示映射
concept_prompts:
  # 概念0: 背景区域
  0: "natural garden background, lush vegetation, harmonious landscape, photorealistic, high quality, detailed foliage, serene environment"
  
  # 概念1: 核心修复区域 (用户可自定义)
  1: "USER_DEFINED_CONCEPT"
  
  # 概念2: 边界过渡区域
  2: "smooth natural transition, seamless blending edge, soft boundary, natural gradient, harmonious integration"

# 预定义的用户概念示例
example_user_concepts:
  modern_garden: "modern minimalist garden with geometric patterns, clean lines, contemporary landscape design"
  flower_bed: "colorful flower bed, vibrant blooms, mixed flowers, garden border, natural beauty"
  water_feature: "elegant water fountain, flowing water, garden centerpiece, tranquil water element"
  sculpture: "artistic garden sculpture, decorative element, outdoor art piece, landscape focal point"
  pathway: "stone garden pathway, walkway, natural stepping stones, garden trail"
  seating_area: "outdoor seating area, garden bench, relaxation space, peaceful corner"

# 高级概念配置
advanced_settings:
  # 负面提示（用于改善质量）
  negative_prompts:
    common: "blurry, low quality, distorted, artifacts, noise, oversaturated"
    background: "cluttered, messy, unnatural, artificial"
    repair: "inconsistent, mismatched, obvious editing"
    boundary: "harsh edges, abrupt transitions, visible seams"
  
  # 概念权重建议
  concept_weights:
    background: 0.3    # 背景概念权重
    repair: 1.0        # 修复概念权重（主要）
    boundary: 0.5      # 边界概念权重
  
  # 引导强度建议
  guidance_scales:
    background: 5.0    # 背景较低引导强度
    repair: 7.5        # 修复标准引导强度
    boundary: 6.0      # 边界中等引导强度

# 使用说明
usage_instructions: |
  1. 设置用户概念：
     concept_guidance.set_user_concept(1, "your custom concept description")
  
  2. 加载配置：
     concept_guidance = ConceptGuidance(diffusion_engine, "configs/concept_prompts.yaml")
  
  3. 使用示例概念：
     concept_guidance.set_user_concept(1, example_user_concepts["modern_garden"])
  
  4. 自定义概念：
     concept_guidance.set_user_concept(1, "detailed description of your desired concept")

# 概念提示编写指南
prompt_guidelines:
  best_practices:
    - "使用具体、描述性的词汇"
    - "包含风格和质量修饰符"
    - "避免过于复杂的句子"
    - "考虑与背景的协调性"
  
  recommended_keywords:
    quality: ["photorealistic", "high quality", "detailed", "sharp", "clear"]
    style: ["natural", "harmonious", "elegant", "beautiful", "serene"]
    technical: ["well-lit", "balanced composition", "proper perspective"]
  
  avoid:
    - "过于抽象的概念"
    - "相互矛盾的描述"
    - "过长的提示文本"
    - "与场景不符的元素"

# 🔥 CISM 训练配置 - Stage 2 真正集成版本
# 作者: Claude 4 (Anthropic 最先进模型)
cism_training_config:
  # 模型配置
  model_id: "runwayml/stable-diffusion-v1-5"
  half_precision: true
  attention_slicing: true
  memory_efficient_attention: true

  # 训练调度
  cism_start_iter: 2000
  cism_end_iter: 25000
  cism_interval: 100
  cism_weight: 0.1

  # 引导参数
  guidance_scale: 7.5
  timestep_range: [0.02, 0.98]

  # SDS 损失配置
  min_step: 20
  max_step: 980
  weight_schedule_type: "linear"
  sds_start_weight: 0.1
  sds_end_weight: 1.0
  sds_warmup_iters: 500

  # 多概念训练
  use_multi_concept: true
  active_concepts: [0, 1, 2]
  main_concept_id: 1
