# 阶段2操作指南: CISM语义引导系统

**模型**: Claude Sonnet 4 by Anthropic  
**开发者**: Augment Agent developed by Augment Code

## 🎯 阶段2操作目标
基于阶段1的concept_id标记高斯点云，实现CISM (Concept-aware Importance Sampling Module) 语义引导系统。

## 📋 前置条件检查

### 必需文件确认
```bash
# 检查阶段1输出
ls stage1_results/concept_gaussians/gaussians_with_concept_id.ply
ls stage1_results/concept_masks/concept_0_background/
ls stage1_results/concept_masks/concept_1_repair/
ls stage1_results/concept_masks/concept_2_boundary/

# 检查原始数据
ls data/garden/images/
ls data/garden/seg/
```

### 环境准备
```bash
# 安装CISM依赖
pip install -r stage2_results/requirements_cism.txt

# 检查GPU可用性
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
python -c "import torch; print(f'GPU memory: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB')"
```

## 🚀 阶段2执行步骤

### 步骤1: 配置文件准备
```bash
# 复制配置文件到工作目录
cp stage2_results/cism_training_config.yaml ./
cp stage2_results/concept_prompts.yaml ./

# 根据需要修改用户概念
vim concept_prompts.yaml
# 修改 user_concept: "你的自定义概念描述"
```

### 步骤2: CISM训练执行
```bash
# 启动CISM训练
python stage2_results/train_cism.py \
    --config cism_training_config.yaml \
    --source_path data/garden \
    --model_path stage1_results/concept_gaussians/gaussians_with_concept_id.ply \
    --output_dir stage2_results/training_output \
    --user_concept "modern minimalist garden with geometric patterns"

# 监控训练进度
tail -f stage2_results/training_output/logs/cism_training.log
```

### 步骤3: 训练监控
```bash
# 使用TensorBoard监控
tensorboard --logdir stage2_results/training_output/logs/ --port 6006

# 检查中间结果
ls stage2_results/training_output/checkpoints/
ls stage2_results/training_output/visualizations/
```

### 步骤4: CISM评估
```bash
# 评估训练结果
python stage2_results/evaluate_cism.py \
    --model_path stage2_results/training_output/cism_enhanced_gaussians.ply \
    --source_path data/garden \
    --config cism_training_config.yaml \
    --output_dir stage2_results/evaluation_output
```

## 📊 预期输出结果

### 训练输出
```
stage2_results/training_output/
├── cism_enhanced_gaussians.ply      # CISM增强的高斯模型
├── checkpoints/                     # 训练检查点
│   ├── iteration_1000.ply
│   ├── iteration_5000.ply
│   └── iteration_10000.ply
├── logs/                           # 训练日志
│   ├── cism_training.log
│   └── tensorboard_events/
├── visualizations/                 # 可视化结果
│   ├── concept_guidance_samples/
│   ├── sds_loss_curves.png
│   └── training_progress.mp4
└── config_backup/                  # 配置备份
    ├── cism_training_config.yaml
    └── concept_prompts.yaml
```

### 评估输出
```
stage2_results/evaluation_output/
├── cism_evaluation_report.json     # 评估报告
├── concept_consistency_metrics.json # 概念一致性指标
├── visual_quality_analysis/        # 视觉质量分析
├── comparison_renders/              # 对比渲染结果
└── ablation_study_results/          # 消融研究结果
```

## 🎛️ 关键参数调整

### 训练参数优化
```yaml
# cism_training_config.yaml 关键参数
model_id: "runwayml/stable-diffusion-v1-5"  # 可选其他SD模型
half_precision: true                         # 内存优化
guidance_scale: 7.5                         # 引导强度 (5.0-15.0)
sds_start_weight: 0.05                      # SDS起始权重
sds_end_weight: 0.1                         # SDS结束权重
cism_start_iter: 1000                       # CISM开始迭代
cism_end_iter: 10000                        # CISM结束迭代
cism_interval: 5                            # CISM应用间隔
```

### 概念提示优化
```yaml
# concept_prompts.yaml 优化建议
concept_prompts:
  0: "natural garden background, lush vegetation, harmonious landscape"
  1: "USER_DEFINED_CONCEPT"  # 详细描述修复目标
  2: "smooth natural transition, seamless blending edge"

# 用户概念示例
user_concept: "modern minimalist garden with geometric patterns, clean lines, contemporary landscape design"
```

## 🔧 故障排除

### 常见问题解决

#### 1. GPU内存不足
```bash
# 解决方案1: 启用更多内存优化
# 修改 cism_training_config.yaml
enable_attention_slicing: true
enable_memory_efficient_attention: true
enable_sequential_cpu_offload: true

# 解决方案2: 降低批次大小
batch_size: 1
gradient_accumulation_steps: 4
```

#### 2. 训练不收敛
```bash
# 检查学习率
learning_rate: 0.0001  # 降低学习率

# 检查权重调度
sds_start_weight: 0.01  # 降低起始权重
warmup_iterations: 500  # 增加预热迭代
```

#### 3. 概念引导效果不明显
```bash
# 增强引导强度
guidance_scale: 10.0  # 提高引导强度

# 优化概念描述
user_concept: "具体详细的概念描述，包含视觉特征"
```

## 📈 质量评估指标

### 训练质量检查
```bash
# 检查损失收敛
python -c "
import json
with open('stage2_results/training_output/logs/training_stats.json', 'r') as f:
    stats = json.load(f)
    print(f'Final SDS Loss: {stats[\"final_sds_loss\"]:.4f}')
    print(f'Concept Consistency: {stats[\"concept_consistency\"]:.3f}')
"

# 检查概念分离度
python stage2_results/cism_core/evaluate_concept_separation.py \
    --model_path stage2_results/training_output/cism_enhanced_gaussians.ply
```

### 视觉质量评估
```bash
# 生成对比渲染
python stage2_results/cism_core/generate_comparison_renders.py \
    --original_model stage1_results/concept_gaussians/gaussians_with_concept_id.ply \
    --cism_model stage2_results/training_output/cism_enhanced_gaussians.ply \
    --output_dir stage2_results/evaluation_output/comparison_renders/
```

## 🎯 成功标准

### 技术指标
- **SDS损失收敛**: 最终损失 < 0.1
- **概念一致性**: > 0.8
- **训练稳定性**: 无发散现象
- **内存使用**: < 8GB GPU内存

### 视觉指标
- **概念引导效果**: 明显的视觉差异
- **空间连续性**: 无明显伪影
- **整体质量**: PSNR保持或提升

## 🔗 下一步操作

### 准备阶段3
```bash
# 确认阶段2输出质量
python stage2_results/evaluate_cism.py --quick_check

# 准备阶段3输入
cp stage2_results/training_output/cism_enhanced_gaussians.ply stage3_input/
cp cism_training_config.yaml stage3_input/
```

---

**操作完成**: Claude Sonnet 4 by Anthropic  
**下一阶段**: 准备进入阶段3 RCA区域控制系统实现
