<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_gradient_paint</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_gradient_paint<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00038.html" title="GLM_GTX_gradient_paint ">glm/gtx/gradient_paint.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga849241df1e55129b8ce9476200307419"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga849241df1e55129b8ce9476200307419"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00327.html#ga849241df1e55129b8ce9476200307419">linearGradient</a> (vec&lt; 2, T, Q &gt; const &amp;Point0, vec&lt; 2, T, Q &gt; const &amp;Point1, vec&lt; 2, T, Q &gt; const &amp;Position)</td></tr>
<tr class="memdesc:ga849241df1e55129b8ce9476200307419"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return a color from a linear gradient.  <a href="a00327.html#ga849241df1e55129b8ce9476200307419">More...</a><br /></td></tr>
<tr class="separator:ga849241df1e55129b8ce9476200307419"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaecb1e93de4cbe0758b882812d4da294"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaaecb1e93de4cbe0758b882812d4da294"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00327.html#gaaecb1e93de4cbe0758b882812d4da294">radialGradient</a> (vec&lt; 2, T, Q &gt; const &amp;Center, T const &amp;Radius, vec&lt; 2, T, Q &gt; const &amp;Focal, vec&lt; 2, T, Q &gt; const &amp;Position)</td></tr>
<tr class="memdesc:gaaecb1e93de4cbe0758b882812d4da294"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return a color from a radial gradient.  <a href="a00327.html#gaaecb1e93de4cbe0758b882812d4da294">More...</a><br /></td></tr>
<tr class="separator:gaaecb1e93de4cbe0758b882812d4da294"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00038.html" title="GLM_GTX_gradient_paint ">glm/gtx/gradient_paint.hpp</a>&gt; to use the features of this extension. </p>
<p>Functions that return the color of procedural gradient for specific coordinates. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga849241df1e55129b8ce9476200307419"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::linearGradient </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Point0</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Point1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Position</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return a color from a linear gradient. </p>
<dl class="section see"><dt>See also</dt><dd>- <a class="el" href="a00327.html" title="Include <glm/gtx/gradient_paint.hpp> to use the features of this extension. ">GLM_GTX_gradient_paint</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaaecb1e93de4cbe0758b882812d4da294"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::radialGradient </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Center</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>Radius</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Focal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Position</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return a color from a radial gradient. </p>
<dl class="section see"><dt>See also</dt><dd>- <a class="el" href="a00327.html" title="Include <glm/gtx/gradient_paint.hpp> to use the features of this extension. ">GLM_GTX_gradient_paint</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
