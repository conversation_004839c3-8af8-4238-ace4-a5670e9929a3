"""
CISM Core: Stable Diffusion模型集成
阶段2.1: 搭建扩散模型引擎，为语义引导提供AI能力

本模块实现了InFusion-Enhanced项目阶段2的核心扩散模型引擎，
为CISM（Concept-guided Inpainting with Semantic Masks）提供基础的
Stable Diffusion能力，包括文本编码、噪声预测和CFG引导。

主要特性：
- 标准Stable Diffusion v1.5集成
- 内存优化配置（attention slicing, memory efficient attention）
- 支持半精度推理
- 标准CFG引导（无复杂RCA空间调制）
- 完整的错误处理和日志记录

作者: Claude Sonnet 4 (Anthropic 最先进模型)
版本: 阶段2 - 简化RCA版本
"""

import torch
import torch.nn as nn
from typing import Dict, Optional, Union, Tuple
import logging
import warnings

# 抑制一些不重要的警告
warnings.filterwarnings("ignore", category=UserWarning, module="transformers")

try:
    from diffusers import StableDiffusionPipeline, UNet2DConditionModel, DDIMScheduler
    from diffusers.models import AutoencoderKL
    from transformers import CLIPTextModel, CLIPTokenizer
    DIFFUSERS_AVAILABLE = True
except ImportError:
    DIFFUSERS_AVAILABLE = False
    logging.warning("Diffusers not available. Please install: pip install diffusers transformers accelerate")

class DiffusionEngine:
    """
    Stable Diffusion引擎，为CISM提供语义引导能力
    
    功能：
    1. 加载预训练的Stable Diffusion组件
    2. 文本编码和条件化
    3. 噪声预测和引导计算
    4. 内存优化配置
    """
    
    def __init__(
        self,
        model_id: str = "runwayml/stable-diffusion-v1-5",
        device: str = "cuda",
        half_precision: bool = True,
        enable_attention_slicing: bool = True,
        enable_memory_efficient_attention: bool = True
    ):
        """
        初始化扩散模型引擎
        
        Args:
            model_id: Hugging Face模型ID
            device: 计算设备
            half_precision: 是否使用半精度
            enable_attention_slicing: 是否启用注意力切片
            enable_memory_efficient_attention: 是否启用内存高效注意力
        """
        if not DIFFUSERS_AVAILABLE:
            raise ImportError("Diffusers package is required. Install with: pip install diffusers transformers accelerate")
        
        self.device = device
        self.half_precision = half_precision
        self.model_id = model_id
        
        # 初始化组件
        self._load_components()
        self._setup_memory_optimization(enable_attention_slicing, enable_memory_efficient_attention)
        
        logging.info(f"DiffusionEngine initialized with model: {model_id}")
    
    def _load_components(self):
        """加载Stable Diffusion的核心组件"""
        logging.info("Loading Stable Diffusion components...")
        
        # 加载UNet
        self.unet = UNet2DConditionModel.from_pretrained(
            self.model_id, 
            subfolder="unet",
            torch_dtype=torch.float16 if self.half_precision else torch.float32
        ).to(self.device)
        
        # 加载VAE
        self.vae = AutoencoderKL.from_pretrained(
            self.model_id,
            subfolder="vae", 
            torch_dtype=torch.float16 if self.half_precision else torch.float32
        ).to(self.device)
        
        # 加载文本编码器
        self.text_encoder = CLIPTextModel.from_pretrained(
            self.model_id,
            subfolder="text_encoder",
            torch_dtype=torch.float16 if self.half_precision else torch.float32
        ).to(self.device)
        
        # 加载分词器
        self.tokenizer = CLIPTokenizer.from_pretrained(
            self.model_id,
            subfolder="tokenizer"
        )
        
        # 加载调度器
        self.scheduler = DDIMScheduler.from_pretrained(
            self.model_id,
            subfolder="scheduler"
        )
        
        # 设置为评估模式
        self.unet.eval()
        self.vae.eval()
        self.text_encoder.eval()
        
        logging.info("All components loaded successfully")
    
    def _setup_memory_optimization(self, enable_attention_slicing: bool, enable_memory_efficient_attention: bool):
        """设置内存优化"""
        if enable_attention_slicing:
            print("    🔧 尝试启用 attention slicing...")
            try:
                # 直接尝试调用，捕获所有可能的异常
                self.unet.enable_attention_slicing()
                print("     ✅ Attention slicing 启用成功")
                logging.info("Attention slicing enabled")
            except AttributeError as e:
                print(f"     ⚠️  UNet 模型不支持 enable_attention_slicing()。跳过。错误: {e}")
                logging.warning(f"Attention slicing not available: {e}")
            except Exception as e:
                print(f"     ⚠️  启用 attention slicing 时出错: {e}。跳过。")
                logging.warning(f"Error enabling attention slicing: {e}")

        if enable_memory_efficient_attention:
            print("    🔧 尝试启用 memory efficient attention...")
            try:
                # 检查是否存在该方法
                if hasattr(self.unet, 'enable_xformers_memory_efficient_attention'):
                    self.unet.enable_xformers_memory_efficient_attention()
                    print("     ✅ XFormers memory efficient attention 启用成功")
                    logging.info("XFormers memory efficient attention enabled")
                elif hasattr(self.unet, 'enable_memory_efficient_attention'):
                    self.unet.enable_memory_efficient_attention()
                    print("     ✅ Memory efficient attention 启用成功")
                    logging.info("Memory efficient attention enabled")
                else:
                    print("     ⚠️  UNet 模型不支持 memory efficient attention。跳过。")
                    logging.warning("Memory efficient attention not supported")
            except Exception as e:
                print(f"     ⚠️  启用 memory efficient attention 时出错: {e}。跳过。")
                logging.warning(f"Error enabling memory efficient attention: {e}")
    
    def encode_text(self, prompts: Union[str, list]) -> torch.Tensor:
        """
        编码文本提示为嵌入向量

        Args:
            prompts: 文本提示，可以是单个字符串或字符串列表

        Returns:
            text_embeddings: 文本嵌入 [batch_size, 77, 768]

        Raises:
            ValueError: 当输入为空或无效时
            RuntimeError: 当编码过程失败时
        """
        if not prompts:
            raise ValueError("Prompts cannot be empty")

        if isinstance(prompts, str):
            prompts = [prompts]

        # 验证输入
        if not all(isinstance(p, str) for p in prompts):
            raise ValueError("All prompts must be strings")

        try:
            # 分词
            text_inputs = self.tokenizer(
                prompts,
                padding="max_length",
                max_length=self.tokenizer.model_max_length,
                truncation=True,
                return_tensors="pt"
            )

            # 编码
            with torch.no_grad():
                text_embeddings = self.text_encoder(text_inputs.input_ids.to(self.device))[0]

            # 验证输出形状
            expected_shape = (len(prompts), 77, 768)
            if text_embeddings.shape != expected_shape:
                logging.warning(f"Unexpected embedding shape: {text_embeddings.shape}, expected: {expected_shape}")

            return text_embeddings

        except Exception as e:
            raise RuntimeError(f"Failed to encode text: {e}") from e
    
    def encode_image_to_latent(self, images: torch.Tensor) -> torch.Tensor:
        """
        将图像编码到潜在空间

        Args:
            images: 输入图像 [batch_size, 3, H, W], 范围[0, 1]

        Returns:
            latents: 潜在表示 [batch_size, 4, H//8, W//8]

        Raises:
            ValueError: 当输入图像格式不正确时
            RuntimeError: 当编码过程失败时
        """
        # 输入验证
        if not isinstance(images, torch.Tensor):
            raise ValueError("Images must be a torch.Tensor")

        if images.dim() != 4:
            raise ValueError(f"Images must be 4D tensor [batch_size, 3, H, W], got shape: {images.shape}")

        if images.shape[1] != 3:
            raise ValueError(f"Images must have 3 channels, got: {images.shape[1]}")

        # 检查值域并修正
        if images.min() < 0 or images.max() > 1:
            logging.warning(f"Images should be in range [0,1], got range [{images.min():.3f}, {images.max():.3f}]. Clamping values.")
            images = torch.clamp(images, 0, 1)

        try:
            # 确保图像在正确的设备上
            images = images.to(self.device)

            # 转换到[-1, 1]范围
            images = 2.0 * images - 1.0

            with torch.no_grad():
                latents = self.vae.encode(images).latent_dist.sample()
                latents = latents * self.vae.config.scaling_factor

            # 验证输出形状
            expected_h, expected_w = images.shape[2] // 8, images.shape[3] // 8
            expected_shape = (images.shape[0], 4, expected_h, expected_w)
            if latents.shape != expected_shape:
                logging.warning(f"Unexpected latent shape: {latents.shape}, expected: {expected_shape}")

            return latents

        except Exception as e:
            raise RuntimeError(f"Failed to encode images to latents: {e}") from e
    
    def decode_latent_to_image(self, latents: torch.Tensor) -> torch.Tensor:
        """
        将潜在表示解码为图像
        
        Args:
            latents: 潜在表示 [batch_size, 4, H//8, W//8]
            
        Returns:
            images: 解码图像 [batch_size, 3, H, W], 范围[0, 1]
        """
        latents = latents / self.vae.config.scaling_factor
        
        with torch.no_grad():
            images = self.vae.decode(latents).sample
        
        # 转换到[0, 1]范围
        images = (images + 1.0) / 2.0
        images = torch.clamp(images, 0.0, 1.0)
        
        return images
    
    def predict_noise(
        self,
        latents: torch.Tensor,
        timesteps: torch.Tensor,
        text_embeddings: torch.Tensor,
        guidance_scale: float = 7.5,
        return_separate: bool = False,
        cross_attention_kwargs: Optional[Dict] = None
    ) -> torch.Tensor:
        """
        预测噪声，支持分类器自由引导（标准实现）

        Args:
            latents: 噪声潜在表示 [batch_size, 4, H//8, W//8]
            timesteps: 时间步 [batch_size]
            text_embeddings: 文本嵌入 [2*batch_size, 77, 768] (uncond + cond)
            guidance_scale: 引导强度
            return_separate: 是否返回分离的预测结果

        Returns:
            noise_pred: 预测的噪声 [batch_size, 4, H//8, W//8]
        """
        batch_size = latents.shape[0]

        # 确保text_embeddings的格式正确
        if text_embeddings.shape[0] != 2 * batch_size:
            raise ValueError(f"Expected text_embeddings shape [2*{batch_size}, 77, 768], got {text_embeddings.shape}")

        # 扩展潜在表示用于CFG
        latent_model_input = torch.cat([latents, latents], dim=0)  # [2*batch_size, 4, H//8, W//8]

        # 扩展时间步
        timestep_input = torch.cat([timesteps, timesteps], dim=0)  # [2*batch_size]

        # 预测噪声（支持RCA的cross_attention_kwargs）
        with torch.no_grad():
            unet_kwargs = {
                "encoder_hidden_states": text_embeddings
            }

            # 如果提供了cross_attention_kwargs，则传递给UNet
            # 这支持阶段3的RCA机制
            if cross_attention_kwargs is not None:
                unet_kwargs["cross_attention_kwargs"] = cross_attention_kwargs

            noise_pred = self.unet(
                latent_model_input,
                timestep_input,
                **unet_kwargs
            ).sample

        # 分离条件和无条件预测
        noise_pred_uncond, noise_pred_cond = noise_pred.chunk(2)

        if return_separate:
            return noise_pred_uncond, noise_pred_cond

        # 计算CFG引导
        if guidance_scale == 1.0:
            # 无引导情况
            guided_noise_pred = noise_pred_cond
        else:
            # 标准CFG公式
            guided_noise_pred = noise_pred_uncond + guidance_scale * (noise_pred_cond - noise_pred_uncond)

        return guided_noise_pred
    
    def add_noise(self, latents: torch.Tensor, noise: torch.Tensor, timesteps: torch.Tensor) -> torch.Tensor:
        """
        向潜在表示添加噪声
        
        Args:
            latents: 原始潜在表示
            noise: 噪声
            timesteps: 时间步
            
        Returns:
            noisy_latents: 添加噪声后的潜在表示
        """
        return self.scheduler.add_noise(latents, noise, timesteps)
    
    def get_timesteps(self, num_inference_steps: int = 50) -> torch.Tensor:
        """
        获取推理时间步
        
        Args:
            num_inference_steps: 推理步数
            
        Returns:
            timesteps: 时间步序列
        """
        self.scheduler.set_timesteps(num_inference_steps)
        return self.scheduler.timesteps
    
    def cleanup(self):
        """清理GPU内存"""
        if hasattr(self, 'unet'):
            del self.unet
        if hasattr(self, 'vae'):
            del self.vae
        if hasattr(self, 'text_encoder'):
            del self.text_encoder
        
        torch.cuda.empty_cache()
        logging.info("DiffusionEngine cleaned up")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
