#!/usr/bin/env python3
"""
简单的SpecifyGradient测试
"""

import sys
import torch

# 添加项目路径
sys.path.append('.')

def test_specify_gradient():
    """测试SpecifyGradient类"""
    print("🔥 测试SpecifyGradient类...")
    
    try:
        from cism_core.specify_gradient import SpecifyGradient
        
        # 创建测试张量
        x = torch.randn(2, 3, requires_grad=True)
        custom_grad = torch.randn(2, 3)
        
        print(f"   - x requires_grad: {x.requires_grad}")
        print(f"   - custom_grad requires_grad: {custom_grad.requires_grad}")
        
        # 使用SpecifyGradient
        y = SpecifyGradient.apply(x, custom_grad)
        
        print(f"   - y requires_grad: {y.requires_grad}")
        print(f"   - y shape: {y.shape}")
        
        # 创建一个简单的损失
        loss = y.sum()
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        if x.grad is not None:
            print(f"   ✅ x.grad存在: {x.grad.shape}")
            print(f"   - 梯度值: {x.grad}")
            print(f"   - 自定义梯度: {custom_grad}")
            print(f"   - 梯度匹配: {torch.allclose(x.grad, custom_grad)}")
            return True
        else:
            print("   ❌ x.grad不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sds_loss_function():
    """测试SDS损失函数"""
    print("🔥 测试SDS损失函数...")
    
    try:
        from cism_core.specify_gradient import sds_loss_with_specify_gradient
        
        # 创建测试数据
        latents = torch.randn(1, 4, 8, 8, requires_grad=True)
        pred_noise = torch.randn(1, 4, 8, 8)
        target_noise = torch.randn(1, 4, 8, 8)
        timesteps = torch.tensor([500])
        alphas = torch.linspace(0.99, 0.01, 1000)
        
        print(f"   - latents requires_grad: {latents.requires_grad}")
        
        # 计算SDS损失
        loss = sds_loss_with_specify_gradient(
            latents=latents,
            pred_noise=pred_noise,
            target_noise=target_noise,
            timesteps=timesteps,
            alphas=alphas,
            grad_scale=1.0,
            weight_type="dreamfusion"
        )
        
        print(f"   - 损失值: {loss.item():.6f}")
        print(f"   - 损失requires_grad: {loss.requires_grad}")
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        if latents.grad is not None:
            grad_norm = latents.grad.norm().item()
            print(f"   ✅ latents梯度存在，范数: {grad_norm:.6f}")
            return True
        else:
            print("   ❌ latents没有梯度")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔥 简单SpecifyGradient测试")
    print("=" * 50)
    
    tests = [
        ("SpecifyGradient基础测试", test_specify_gradient),
        ("SDS损失函数测试", test_sds_loss_function)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n🧪 {name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {name} - 通过")
        else:
            print(f"❌ {name} - 失败")
    
    print(f"\n🎯 结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！SpecifyGradient工作正常")
    else:
        print("❌ 部分测试失败")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
