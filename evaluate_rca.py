#!/usr/bin/env python3
"""
RCA增强结果评估脚本
阶段3: 评估RCA区域控制系统的效果

使用方法:
python evaluate_rca.py --model_path output/rca_experiments/rca_enhanced_gaussians.ply --source_path data/garden --config configs/rca_training_config.yaml
"""

import os
import sys
import argparse
import yaml
import logging
from pathlib import Path
import torch
import numpy as np
from typing import Dict, List, Tuple
import json

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入3DGS组件
try:
    from gaussian_splatting.scene import Scene, GaussianModel
    from gaussian_splatting.utils.general_utils import safe_state
    from gaussian_splatting.utils.loss_utils import l1_loss, ssim
    from gaussian_splatting.gaussian_renderer import render
    from gaussian_splatting.arguments import ModelParams, PipelineParams
    from gaussian_splatting.utils.image_utils import psnr
    GAUSSIAN_SPLATTING_AVAILABLE = True
except ImportError:
    GAUSSIAN_SPLATTING_AVAILABLE = False
    logging.warning("Gaussian Splatting modules not available")

# 导入RCA组件
from rca_core import SpatialAttention3D, ConceptWeightRenderer, SpatialModulation

def setup_logging(verbose: bool = False):
    """设置日志系统"""
    log_level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def load_config(config_path: str) -> Dict:
    """加载配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def load_rca_enhanced_gaussians(model_path: str) -> GaussianModel:
    """加载RCA增强后的高斯模型"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    gaussians = GaussianModel(sh_degree=3)
    gaussians.load_ply(model_path)
    
    logging.info(f"Loaded RCA-enhanced Gaussian model from {model_path}")
    logging.info(f"Number of Gaussian points: {gaussians.get_xyz.shape[0]}")
    
    # 检查concept信息
    if hasattr(gaussians, '_concept_id') and hasattr(gaussians, '_concept_confidence'):
        concept_ids = gaussians.get_concept_id
        concept_confidences = gaussians.get_concept_confidence
        
        unique_concepts, counts = torch.unique(concept_ids, return_counts=True)
        logging.info(f"Concept distribution: {dict(zip(unique_concepts.cpu().numpy(), counts.cpu().numpy()))}")
        logging.info(f"Average concept confidence: {concept_confidences.mean().item():.3f}")
    else:
        logging.warning("Model does not contain concept information")
    
    return gaussians

def setup_evaluation_scene(source_path: str, gaussians: GaussianModel, config: Dict):
    """设置评估场景"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    # 创建模型参数
    model_params = ModelParams()
    model_params.source_path = source_path
    model_params.resolution = config['rendering']['image_resolution']
    
    # 创建场景
    scene = Scene(model_params, gaussians, load_iteration=None, shuffle=False)
    
    return scene

def compute_rendering_metrics(
    gaussians: GaussianModel,
    scene: Scene,
    camera_set: str = "test"
) -> Dict:
    """计算渲染质量指标"""
    logging.info(f"Computing rendering metrics on {camera_set} set...")
    
    # 获取相机
    if camera_set == "test":
        cameras = scene.getTestCameras()
    elif camera_set == "train":
        cameras = scene.getTrainCameras()
    else:
        cameras = scene.getTrainCameras() + scene.getTestCameras()
    
    metrics = {
        'psnr_values': [],
        'ssim_values': [],
        'l1_values': []
    }
    
    pipeline_params = PipelineParams()
    background = torch.tensor([1.0, 1.0, 1.0], device="cuda")
    
    for camera in cameras:
        # 渲染
        render_pkg = render(camera, gaussians, pipeline_params, background)
        rendered_image = render_pkg["render"]
        
        # 真实图像
        gt_image = camera.original_image.cuda()
        
        # 计算指标
        psnr_val = psnr(rendered_image, gt_image).mean().item()
        ssim_val = ssim(rendered_image, gt_image).mean().item()
        l1_val = l1_loss(rendered_image, gt_image).mean().item()
        
        metrics['psnr_values'].append(psnr_val)
        metrics['ssim_values'].append(ssim_val)
        metrics['l1_values'].append(l1_val)
    
    # 计算平均值
    avg_metrics = {
        'avg_psnr': np.mean(metrics['psnr_values']),
        'avg_ssim': np.mean(metrics['ssim_values']),
        'avg_l1': np.mean(metrics['l1_values']),
        'std_psnr': np.std(metrics['psnr_values']),
        'std_ssim': np.std(metrics['ssim_values']),
        'std_l1': np.std(metrics['l1_values']),
        'num_views': len(cameras)
    }
    
    logging.info(f"Rendering metrics ({camera_set}):")
    logging.info(f"  PSNR: {avg_metrics['avg_psnr']:.2f} ± {avg_metrics['std_psnr']:.2f}")
    logging.info(f"  SSIM: {avg_metrics['avg_ssim']:.4f} ± {avg_metrics['std_ssim']:.4f}")
    logging.info(f"  L1: {avg_metrics['avg_l1']:.4f} ± {avg_metrics['std_l1']:.4f}")
    
    return avg_metrics

def evaluate_rca_quality(
    gaussians: GaussianModel,
    scene: Scene,
    config: Dict
) -> Dict:
    """评估RCA质量"""
    logging.info("Evaluating RCA quality...")
    
    device = config.get('device', 'cuda')
    
    # 初始化RCA组件
    spatial_attention = SpatialAttention3D(
        input_dim=config.get('spatial_attention', {}).get('input_dim', 13),
        hidden_dim=config.get('spatial_attention', {}).get('hidden_dim', 256),
        num_concepts=config.get('num_concepts', 3)
    ).to(device)
    
    weight_renderer = ConceptWeightRenderer(device=device)
    spatial_modulator = SpatialModulation(device=device)
    
    rca_stats = {
        'total_points': gaussians.get_xyz.shape[0],
        'concept_distribution': {},
        'concept_confidence_stats': {},
        'spatial_consistency_score': 0.0,
        'concept_separation_score': 0.0,
        'weight_quality_metrics': {}
    }
    
    # 检查concept信息
    if hasattr(gaussians, '_concept_id') and hasattr(gaussians, '_concept_confidence'):
        concept_ids = gaussians.get_concept_id
        concept_confidences = gaussians.get_concept_confidence
        
        # 概念分布统计
        unique_concepts, counts = torch.unique(concept_ids, return_counts=True)
        for concept_id, count in zip(unique_concepts.cpu().numpy(), counts.cpu().numpy()):
            rca_stats['concept_distribution'][int(concept_id)] = {
                'count': int(count),
                'percentage': float(count / rca_stats['total_points'] * 100)
            }
        
        # 概念置信度统计
        for concept_id in unique_concepts.cpu().numpy():
            mask = (concept_ids == concept_id)
            confidences = concept_confidences[mask]
            
            rca_stats['concept_confidence_stats'][int(concept_id)] = {
                'mean_confidence': float(confidences.mean().item()),
                'std_confidence': float(confidences.std().item()),
                'min_confidence': float(confidences.min().item()),
                'max_confidence': float(confidences.max().item())
            }
        
        # 使用空间注意力网络评估
        try:
            with torch.no_grad():
                spatial_results = spatial_attention(gaussians, use_global_attention=True)
                predicted_weights = spatial_results['concept_weights']
                
                # 计算空间一致性
                spatial_consistency_loss = spatial_attention.compute_spatial_consistency_loss(
                    predicted_weights, gaussians, neighbor_radius=0.1
                )
                rca_stats['spatial_consistency_score'] = float(1.0 / (1.0 + spatial_consistency_loss.item()))
                
                # 获取概念统计
                concept_stats = spatial_attention.get_concept_statistics(predicted_weights)
                rca_stats['weight_quality_metrics'] = concept_stats
                
                # 计算概念分离度
                entropy = -torch.sum(predicted_weights * torch.log(predicted_weights + 1e-8), dim=-1)
                rca_stats['concept_separation_score'] = float(1.0 / (1.0 + entropy.mean().item()))
                
        except Exception as e:
            logging.warning(f"Failed to evaluate spatial attention: {e}")
        
        # 评估权重渲染质量
        try:
            test_cameras = scene.getTestCameras()
            if test_cameras:
                test_camera = test_cameras[0]
                
                # 渲染概念权重
                weight_results = weight_renderer.render_concept_weights(
                    test_camera, gaussians, None, predicted_weights
                )
                
                # 计算权重一致性
                consistency_loss = weight_renderer.compute_weight_consistency_loss(
                    weight_results['normalized_weights']
                )
                rca_stats['weight_rendering_quality'] = float(1.0 / (1.0 + consistency_loss.item()))
                
        except Exception as e:
            logging.warning(f"Failed to evaluate weight rendering: {e}")
        
        logging.info("RCA concept distribution:")
        for concept_id, stats in rca_stats['concept_distribution'].items():
            logging.info(f"  Concept {concept_id}: {stats['count']} points ({stats['percentage']:.1f}%)")
        
        logging.info("RCA quality scores:")
        logging.info(f"  Spatial consistency: {rca_stats['spatial_consistency_score']:.3f}")
        logging.info(f"  Concept separation: {rca_stats['concept_separation_score']:.3f}")
    
    else:
        logging.warning("No concept information found in the model")
        rca_stats['has_concept_info'] = False
    
    return rca_stats

def compare_with_baselines(
    rca_metrics: Dict,
    baseline_paths: Dict[str, str]
) -> Dict:
    """与基线模型比较"""
    comparisons = {}
    
    for baseline_name, baseline_path in baseline_paths.items():
        if baseline_path and Path(baseline_path).exists():
            logging.info(f"Comparing with {baseline_name}...")
            
            try:
                # 加载基线模型
                baseline_gaussians = load_rca_enhanced_gaussians(baseline_path)
                
                # 这里可以添加具体的比较逻辑
                # 为简化，这里只记录基线存在
                comparisons[baseline_name] = {
                    'available': True,
                    'num_points': baseline_gaussians.get_xyz.shape[0]
                }
                
            except Exception as e:
                logging.warning(f"Failed to load baseline {baseline_name}: {e}")
                comparisons[baseline_name] = {'available': False, 'error': str(e)}
        else:
            comparisons[baseline_name] = {'available': False, 'reason': 'Path not found'}
    
    return comparisons

def generate_evaluation_report(
    rendering_metrics: Dict,
    rca_stats: Dict,
    comparisons: Dict,
    config: Dict,
    output_path: str
):
    """生成评估报告"""
    report = {
        'evaluation_summary': {
            'model_type': 'RCA Enhanced 3D Gaussian Splatting',
            'evaluation_date': str(torch.datetime.now()),
            'total_gaussian_points': rca_stats['total_points']
        },
        'rendering_quality': rendering_metrics,
        'rca_analysis': rca_stats,
        'baseline_comparisons': comparisons,
        'configuration': {
            'spatial_attention_config': config.get('spatial_attention', {}),
            'num_concepts': config.get('num_concepts', 3),
            'training_schedule': config.get('training_schedule', {}),
            'loss_weights': config.get('loss_weights', {})
        }
    }
    
    # 保存报告
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logging.info(f"Evaluation report saved to {output_path}")
    
    return report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RCA Enhanced Model Evaluation")
    parser.add_argument("--model_path", type=str, required=True, help="RCA-enhanced Gaussian model path")
    parser.add_argument("--source_path", type=str, required=True, help="Source data path")
    parser.add_argument("--config", type=str, required=True, help="Training configuration file")
    parser.add_argument("--baseline_original", type=str, help="Original 3DGS model for comparison")
    parser.add_argument("--baseline_cism", type=str, help="CISM-only model for comparison")
    parser.add_argument("--output_dir", type=str, default="output/rca_evaluation", help="Output directory")
    parser.add_argument("--camera_set", type=str, default="test", choices=["train", "test", "all"], help="Camera set for evaluation")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # 检查依赖
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        print("Error: Gaussian Splatting modules not available")
        sys.exit(1)
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logging.info("Starting RCA Enhanced Model Evaluation")
    logging.info(f"Model: {args.model_path}")
    logging.info(f"Source: {args.source_path}")
    logging.info(f"Config: {args.config}")
    
    try:
        # 加载配置
        config = load_config(args.config)
        
        # 加载RCA增强模型
        gaussians = load_rca_enhanced_gaussians(args.model_path)
        
        # 设置评估场景
        scene = setup_evaluation_scene(args.source_path, gaussians, config)
        
        # 计算渲染质量指标
        rendering_metrics = compute_rendering_metrics(gaussians, scene, args.camera_set)
        
        # 评估RCA质量
        rca_stats = evaluate_rca_quality(gaussians, scene, config)
        
        # 与基线比较
        baseline_paths = {
            'original_3dgs': args.baseline_original,
            'cism_only': args.baseline_cism
        }
        comparisons = compare_with_baselines(rendering_metrics, baseline_paths)
        
        # 生成评估报告
        report_path = output_dir / "rca_evaluation_report.json"
        report = generate_evaluation_report(rendering_metrics, rca_stats, comparisons, config, str(report_path))
        
        # 输出总结
        logging.info("\n" + "="*50)
        logging.info("RCA EVALUATION SUMMARY")
        logging.info("="*50)
        logging.info(f"Model: RCA Enhanced 3D Gaussian Splatting")
        logging.info(f"Total Points: {rca_stats['total_points']:,}")
        logging.info(f"PSNR: {rendering_metrics['avg_psnr']:.2f} dB")
        logging.info(f"SSIM: {rendering_metrics['avg_ssim']:.4f}")
        logging.info(f"L1 Loss: {rendering_metrics['avg_l1']:.4f}")
        
        if 'spatial_consistency_score' in rca_stats:
            logging.info(f"Spatial Consistency: {rca_stats['spatial_consistency_score']:.3f}")
            logging.info(f"Concept Separation: {rca_stats['concept_separation_score']:.3f}")
        
        if 'concept_distribution' in rca_stats:
            logging.info("\nConcept Distribution:")
            for concept_id, stats in rca_stats['concept_distribution'].items():
                logging.info(f"  Concept {concept_id}: {stats['percentage']:.1f}%")
        
        logging.info(f"\nDetailed report saved to: {report_path}")
        logging.info("Evaluation completed successfully")
        
    except Exception as e:
        logging.error(f"Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
