<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_raw_data</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_raw_data<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00139.html" title="GLM_GTX_raw_data ">glm/gtx/raw_data.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga3005cb0d839d546c616becfa6602c607"><td class="memItemLeft" align="right" valign="top">typedef detail::uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00354.html#ga3005cb0d839d546c616becfa6602c607">byte</a></td></tr>
<tr class="memdesc:ga3005cb0d839d546c616becfa6602c607"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type for byte numbers.  <a href="a00354.html#ga3005cb0d839d546c616becfa6602c607">More...</a><br /></td></tr>
<tr class="separator:ga3005cb0d839d546c616becfa6602c607"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga86e46fff9f80ae33893d8d697f2ca98a"><td class="memItemLeft" align="right" valign="top">typedef detail::uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00354.html#ga86e46fff9f80ae33893d8d697f2ca98a">dword</a></td></tr>
<tr class="memdesc:ga86e46fff9f80ae33893d8d697f2ca98a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type for dword numbers.  <a href="a00354.html#ga86e46fff9f80ae33893d8d697f2ca98a">More...</a><br /></td></tr>
<tr class="separator:ga86e46fff9f80ae33893d8d697f2ca98a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4021754ffb8e5ef14c75802b15657714"><td class="memItemLeft" align="right" valign="top">typedef detail::uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00354.html#ga4021754ffb8e5ef14c75802b15657714">qword</a></td></tr>
<tr class="memdesc:ga4021754ffb8e5ef14c75802b15657714"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type for qword numbers.  <a href="a00354.html#ga4021754ffb8e5ef14c75802b15657714">More...</a><br /></td></tr>
<tr class="separator:ga4021754ffb8e5ef14c75802b15657714"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga16e9fea0ef1e6c4ef472d3d1731c49a5"><td class="memItemLeft" align="right" valign="top">typedef detail::uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00354.html#ga16e9fea0ef1e6c4ef472d3d1731c49a5">word</a></td></tr>
<tr class="memdesc:ga16e9fea0ef1e6c4ef472d3d1731c49a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type for word numbers.  <a href="a00354.html#ga16e9fea0ef1e6c4ef472d3d1731c49a5">More...</a><br /></td></tr>
<tr class="separator:ga16e9fea0ef1e6c4ef472d3d1731c49a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00139.html" title="GLM_GTX_raw_data ">glm/gtx/raw_data.hpp</a>&gt; to use the features of this extension. </p>
<p>Projection of a vector to other one </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="ga3005cb0d839d546c616becfa6602c607"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef detail::uint8 byte</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Type for byte numbers. </p>
<p>From GLM_GTX_raw_data extension. </p>

<p>Definition at line <a class="el" href="a00139_source.html#l00034">34</a> of file <a class="el" href="a00139_source.html">raw_data.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga86e46fff9f80ae33893d8d697f2ca98a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef detail::uint32 dword</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Type for dword numbers. </p>
<p>From GLM_GTX_raw_data extension. </p>

<p>Definition at line <a class="el" href="a00139_source.html#l00042">42</a> of file <a class="el" href="a00139_source.html">raw_data.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga4021754ffb8e5ef14c75802b15657714"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef detail::uint64 qword</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Type for qword numbers. </p>
<p>From GLM_GTX_raw_data extension. </p>

<p>Definition at line <a class="el" href="a00139_source.html#l00046">46</a> of file <a class="el" href="a00139_source.html">raw_data.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga16e9fea0ef1e6c4ef472d3d1731c49a5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef detail::uint16 word</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Type for word numbers. </p>
<p>From GLM_GTX_raw_data extension. </p>

<p>Definition at line <a class="el" href="a00139_source.html#l00038">38</a> of file <a class="el" href="a00139_source.html">raw_data.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
