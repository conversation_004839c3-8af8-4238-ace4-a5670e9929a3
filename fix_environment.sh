#!/bin/bash
# 修复InFusion环境脚本

echo "🔧 开始修复InFusion环境..."

# 1. 创建新的现代化环境
echo "1. 创建新环境 infusion_fixed..."
conda create -n infusion_fixed python=3.9 -y

# 2. 激活环境
echo "2. 激活环境..."
source activate infusion_fixed

# 3. 安装现代化PyTorch
echo "3. 安装PyTorch 2.0+..."
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y

# 4. 安装基础依赖
echo "4. 安装基础依赖..."
conda install -c conda-forge plyfile tqdm opencv matplotlib -y

# 5. 安装Python包
echo "5. 安装Python包..."
pip install diffusers transformers accelerate
pip install imageio open3d torchmetrics timm
pip install xformers  # 加速attention计算

# 6. 安装3DGS依赖
echo "6. 安装3DGS依赖..."
cd /home/<USER>/Infusion
pip install -e gaussian_splatting/submodules/diff-gaussian-rasterization-confidence
pip install -e gaussian_splatting/submodules/simple-knn

echo "✅ 环境修复完成！"
echo "请运行: conda activate infusion_fixed"
