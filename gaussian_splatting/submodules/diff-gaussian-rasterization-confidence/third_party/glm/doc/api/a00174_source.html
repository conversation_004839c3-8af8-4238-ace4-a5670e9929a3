<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: type_precision.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">type_precision.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00174.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../gtc/quaternion.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../gtc/vec1.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_int_sized.hpp&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#include &quot;../ext/scalar_uint_sized.hpp&quot;</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;../detail/type_vec2.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;../detail/type_vec3.hpp&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;../detail/type_vec4.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat2x2.hpp&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat2x3.hpp&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat2x4.hpp&quot;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat3x2.hpp&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat3x3.hpp&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat3x4.hpp&quot;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat4x2.hpp&quot;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat4x3.hpp&quot;</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;../detail/type_mat4x4.hpp&quot;</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_type_precision extension included&quot;)</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;{</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;        <span class="comment">// Signed int vector types</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#ga760bcf26fdb23a2c3ecad3c928a19ae6">lowp_int8</a>;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#ga698e36b01167fc0f037889334dce8def">lowp_int16</a>;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga864aabca5f3296e176e0c3ed9cc16b02">lowp_int32</a>;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#gaf645b1a60203b39c0207baff5e3d8c3c">lowp_int64</a>;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#ga119c41d73fe9977358174eb3ac1035a3">lowp_int8_t</a>;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#ga8b2cd8d31eb345b2d641d9261c38db1a">lowp_int16_t</a>;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga0350631d35ff800e6133ac6243b13cbc">lowp_int32_t</a>;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#gaebf341fc4a5be233f7dde962c2e33847">lowp_int64_t</a>;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#ga552a6bde5e75984efb0f863278da2e54">lowp_i8</a>;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#ga392b673fd10847bfb78fb808c6cf8ff7">lowp_i16</a>;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga7ff73a45cea9613ebf1a9fad0b9f82ac">lowp_i32</a>;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#ga354736e0c645099cd44c42fb2f87c2b8">lowp_i64</a>;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#ga6fbd69cbdaa44345bff923a2cf63de7e">mediump_int8</a>;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#gadff3608baa4b5bd3ed28f95c1c2c345d">mediump_int16</a>;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga5244cef85d6e870e240c76428a262ae8">mediump_int32</a>;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#ga7b968f2b86a0442a89c7359171e1d866">mediump_int64</a>;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#ga6d7b3789ecb932c26430009478cac7ae">mediump_int8_t</a>;</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#ga80e72fe94c88498537e8158ba7591c54">mediump_int16_t</a>;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga26fc7ced1ad7ca5024f1c973c8dc9180">mediump_int32_t</a>;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#gac3bc41bcac61d1ba8f02a6f68ce23f64">mediump_int64_t</a>;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#gacf1ded173e1e2d049c511d095b259e21">mediump_i8</a>;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#ga62a17cddeb4dffb4e18fe3aea23f051a">mediump_i16</a>;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#gaf5e94bf2a20af7601787c154751dc2e1">mediump_i32</a>;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#ga3ebcb1f6d8d8387253de8bccb058d77f">mediump_i64</a>;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#gad0549c902a96a7164e4ac858d5f39dbf">highp_int8</a>;</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#ga5fde0fa4a3852a9dd5d637a92ee74718">highp_int16</a>;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga84ed04b4e0de18c977e932d617e7c223">highp_int32</a>;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#ga226a8d52b4e3f77aaa6231135e886aac">highp_int64</a>;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#ga1085c50dd8fbeb5e7e609b1c127492a5">highp_int8_t</a>;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#gacaea06d0a79ef3172e887a7a6ba434ff">highp_int16_t</a>;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga2c71c8bd9e2fe7d2e93ca250d8b6157f">highp_int32_t</a>;</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#ga73c6abb280a45feeff60f9accaee91f3">highp_int64_t</a>;</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#gacb88796f2d08ef253d0345aff20c3aee">highp_i8</a>;</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#ga0336abc2604dd2c20c30e036454b64f8">highp_i16</a>;</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga727675ac6b5d2fc699520e0059735e25">highp_i32</a>;</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#gac25db6d2b1e2a0f351b77ba3409ac4cd">highp_i64</a>;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;<span class="preprocessor">#if GLM_HAS_EXTENDED_INTEGER_TYPE</span></div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;        <span class="keyword">using</span> <a class="code" href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">std::int8_t</a>;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;        <span class="keyword">using</span> <a class="code" href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">std::int16_t</a>;</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;        <span class="keyword">using</span> <a class="code" href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">std::int32_t</a>;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;        <span class="keyword">using</span> <a class="code" href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">std::int64_t</a>;</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">int8_t</a>;</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">int16_t</a>;</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">int32_t</a>;</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">int64_t</a>;</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;        <span class="keyword">typedef</span> detail::int8 <a class="code" href="a00304.html#ga302ec977b0c0c3ea245b6c9275495355">i8</a>;</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;        <span class="keyword">typedef</span> detail::int16 <a class="code" href="a00304.html#ga3ab5fe184343d394fb6c2723c3ee3699">i16</a>;</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;        <span class="keyword">typedef</span> detail::int32 <a class="code" href="a00304.html#ga96faea43ac5f875d2d3ffbf8d213e3eb">i32</a>;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">detail::int64</a> <a class="code" href="a00304.html#gadb997e409103d4da18abd837e636a496">i64</a>;</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i8, lowp&gt; <a class="code" href="a00304.html#ga036d6c7ca9fbbdc5f3871bfcb937c85c">lowp_i8vec1</a>;</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i8, lowp&gt; <a class="code" href="a00304.html#gac03e5099d27eeaa74b6016ea435a1df2">lowp_i8vec2</a>;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i8, lowp&gt; <a class="code" href="a00304.html#gae2f43ace6b5b33ab49516d9e40af1845">lowp_i8vec3</a>;</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i8, lowp&gt; <a class="code" href="a00304.html#ga6d388e9b9aa1b389f0672d9c7dfc61c5">lowp_i8vec4</a>;</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i8, mediump&gt; <a class="code" href="a00304.html#ga85e8893f4ae3630065690a9000c0c483">mediump_i8vec1</a>;</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i8, mediump&gt; <a class="code" href="a00304.html#ga2a8bdc32184ea0a522ef7bd90640cf67">mediump_i8vec2</a>;</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i8, mediump&gt; <a class="code" href="a00304.html#ga6dd1c1618378c6f94d522a61c28773c9">mediump_i8vec3</a>;</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i8, mediump&gt; <a class="code" href="a00304.html#gac7bb04fb857ef7b520e49f6c381432be">mediump_i8vec4</a>;</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i8, highp&gt; <a class="code" href="a00304.html#ga1d8c10949691b0fd990253476f47beb3">highp_i8vec1</a>;</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i8, highp&gt; <a class="code" href="a00304.html#ga50542e4cb9b2f9bec213b66e06145d07">highp_i8vec2</a>;</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i8, highp&gt; <a class="code" href="a00304.html#ga8396bfdc081d9113190d0c39c9f67084">highp_i8vec3</a>;</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i8, highp&gt; <a class="code" href="a00304.html#ga4824e3ddf6e608117dfe4809430737b4">highp_i8vec4</a>;</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i8, defaultp&gt; <a class="code" href="a00304.html#ga7e80d927ff0a3861ced68dfff8a4020b">i8vec1</a>;</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i8, defaultp&gt; <a class="code" href="a00304.html#gad06935764d78f43f9d542c784c2212ec">i8vec2</a>;</div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i8, defaultp&gt; <a class="code" href="a00304.html#ga5a08d36cf7917cd19d081a603d0eae3e">i8vec3</a>;</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i8, defaultp&gt; <a class="code" href="a00304.html#ga4177a44206121dabc8c4ff1c0f544574">i8vec4</a>;</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i16, lowp&gt;               <a class="code" href="a00304.html#ga501a2f313f1c220eef4ab02bdabdc3c6">lowp_i16vec1</a>;</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i16, lowp&gt;               <a class="code" href="a00304.html#ga7cac84b520a6b57f2fbd880d3d63c51b">lowp_i16vec2</a>;</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i16, lowp&gt;               <a class="code" href="a00304.html#gab69ef9cbc2a9214bf5596c528c801b72">lowp_i16vec3</a>;</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i16, lowp&gt;               <a class="code" href="a00304.html#ga1d47d94d17c2406abdd1f087a816e387">lowp_i16vec4</a>;</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i16, mediump&gt;            <a class="code" href="a00304.html#gacc44265ed440bf5e6e566782570de842">mediump_i16vec1</a>;</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i16, mediump&gt;            <a class="code" href="a00304.html#ga4b5e2c9aaa5d7717bf71179aefa12e88">mediump_i16vec2</a>;</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i16, mediump&gt;            <a class="code" href="a00304.html#ga3be6c7fc5fe08fa2274bdb001d5f2633">mediump_i16vec3</a>;</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i16, mediump&gt;            <a class="code" href="a00304.html#gaf52982bb23e3a3772649b2c5bb84b107">mediump_i16vec4</a>;</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i16, highp&gt;              <a class="code" href="a00304.html#ga70fdfcc1fd38084bde83c3f06a8b9f19">highp_i16vec1</a>;</div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i16, highp&gt;              <a class="code" href="a00304.html#gaa7db3ad10947cf70cae6474d05ebd227">highp_i16vec2</a>;</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i16, highp&gt;              <a class="code" href="a00304.html#ga5609c8fa2b7eac3dec337d321cb0ca96">highp_i16vec3</a>;</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i16, highp&gt;              <a class="code" href="a00304.html#ga7a18659438828f91ccca28f1a1e067b4">highp_i16vec4</a>;</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;</div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i16, defaultp&gt; <a class="code" href="a00304.html#gafe730798732aa7b0647096a004db1b1c">i16vec1</a>;</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i16, defaultp&gt; <a class="code" href="a00304.html#ga2996630ba7b10535af8e065cf326f761">i16vec2</a>;</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i16, defaultp&gt; <a class="code" href="a00304.html#gae9c90a867a6026b1f6eab00456f3fb8b">i16vec3</a>;</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i16, defaultp&gt; <a class="code" href="a00304.html#ga550831bfc26d1e0101c1cb3d79938c06">i16vec4</a>;</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;</div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;</div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i32, lowp&gt;               <a class="code" href="a00304.html#gae31ac3608cf643ceffd6554874bec4a0">lowp_i32vec1</a>;</div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;</div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i32, lowp&gt;               <a class="code" href="a00304.html#ga867a3c2d99ab369a454167d2c0a24dbd">lowp_i32vec2</a>;</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i32, lowp&gt;               <a class="code" href="a00304.html#ga5fe17c87ede1b1b4d92454cff4da076d">lowp_i32vec3</a>;</div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i32, lowp&gt;               <a class="code" href="a00304.html#gac9b2eb4296ffe50a32eacca9ed932c08">lowp_i32vec4</a>;</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;</div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;</div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i32, mediump&gt;            <a class="code" href="a00304.html#ga46a57f71e430637559097a732b550a7e">mediump_i32vec1</a>;</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i32, mediump&gt;            <a class="code" href="a00304.html#ga20bf224bd4f8a24ecc4ed2004a40c219">mediump_i32vec2</a>;</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;</div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i32, mediump&gt;            <a class="code" href="a00304.html#ga13a221b910aa9eb1b04ca1c86e81015a">mediump_i32vec3</a>;</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;</div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i32, mediump&gt;            <a class="code" href="a00304.html#ga6addd4dfee87fc09ab9525e3d07db4c8">mediump_i32vec4</a>;</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;</div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i32, highp&gt;              <a class="code" href="a00304.html#ga6a9d71cc62745302f70422b7dc98755c">highp_i32vec1</a>;</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i32, highp&gt;              <a class="code" href="a00304.html#gaa9b4579f8e6f3d9b649a965bcb785530">highp_i32vec2</a>;</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i32, highp&gt;              <a class="code" href="a00304.html#ga31e070ea3bdee623e6e18a61ba5718b1">highp_i32vec3</a>;</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i32, highp&gt;              <a class="code" href="a00304.html#gadf70eaaa230aeed5a4c9f4c9c5c55902">highp_i32vec4</a>;</div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;</div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;</div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i32, defaultp&gt; <a class="code" href="a00304.html#ga54b8a4e0f5a7203a821bf8e9c1265bcf">i32vec1</a>;</div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i32, defaultp&gt; <a class="code" href="a00304.html#ga8b44026374982dcd1e52d22bac99247e">i32vec2</a>;</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;</div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i32, defaultp&gt; <a class="code" href="a00304.html#ga7f526b5cccef126a2ebcf9bdd890394e">i32vec3</a>;</div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;</div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i32, defaultp&gt; <a class="code" href="a00304.html#ga866a05905c49912309ed1fa5f5980e61">i32vec4</a>;</div>
<div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;</div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;</div>
<div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;</div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i64, lowp&gt;               <a class="code" href="a00304.html#gab0f7d875db5f3cc9f3168c5a0ed56437">lowp_i64vec1</a>;</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;</div>
<div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i64, lowp&gt;               <a class="code" href="a00304.html#gab485c48f06a4fdd6b8d58d343bb49f3c">lowp_i64vec2</a>;</div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;</div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i64, lowp&gt;               <a class="code" href="a00304.html#ga5cb1dc9e8d300c2cdb0d7ff2308fa36c">lowp_i64vec3</a>;</div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;</div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i64, lowp&gt;               <a class="code" href="a00304.html#gabb4229a4c1488bf063eed0c45355bb9c">lowp_i64vec4</a>;</div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;</div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i64, mediump&gt;            <a class="code" href="a00304.html#ga8343e9d244fb17a5bbf0d94d36b3695e">mediump_i64vec1</a>;</div>
<div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;</div>
<div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i64, mediump&gt;            <a class="code" href="a00304.html#ga2c94aeae3457325944ca1059b0b68330">mediump_i64vec2</a>;</div>
<div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;</div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i64, mediump&gt;            <a class="code" href="a00304.html#ga8089722ffdf868cdfe721dea1fb6a90e">mediump_i64vec3</a>;</div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;</div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i64, mediump&gt;            <a class="code" href="a00304.html#gabf1f16c5ab8cb0484bd1e846ae4368f1">mediump_i64vec4</a>;</div>
<div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;</div>
<div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;</div>
<div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i64, highp&gt;              <a class="code" href="a00304.html#gabd2fda3cd208acf5a370ec9b5b3c58d4">highp_i64vec1</a>;</div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;</div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i64, highp&gt;              <a class="code" href="a00304.html#gad9d1903cb20899966e8ebe0670889a5f">highp_i64vec2</a>;</div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;</div>
<div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i64, highp&gt;              <a class="code" href="a00304.html#ga62324224b9c6cce9c6b4db96bb704a8a">highp_i64vec3</a>;</div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;</div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i64, highp&gt;              <a class="code" href="a00304.html#gad23b1be9b3bf20352089a6b738f0ebba">highp_i64vec4</a>;</div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;</div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;</div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, i64, defaultp&gt; <a class="code" href="a00304.html#ga2b65767f8b5aed1bd1cf86c541662b50">i64vec1</a>;</div>
<div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;</div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, i64, defaultp&gt; <a class="code" href="a00304.html#ga48310188e1d0c616bf8d78c92447523b">i64vec2</a>;</div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;</div>
<div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, i64, defaultp&gt; <a class="code" href="a00304.html#ga667948cfe6fb3d6606c750729ec49f77">i64vec3</a>;</div>
<div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;</div>
<div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, i64, defaultp&gt; <a class="code" href="a00304.html#gaa4e31c3d9de067029efeb161a44b0232">i64vec4</a>;</div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;</div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;</div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;        <span class="comment">// Unsigned int vector types</span></div>
<div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;</div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#gaf49470869e9be2c059629b250619804e">lowp_uint8</a>;</div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;</div>
<div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#gad68bfd9f881856fc863a6ebca0b67f78">lowp_uint16</a>;</div>
<div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;</div>
<div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#gaa6a5b461bbf5fe20982472aa51896d4b">lowp_uint32</a>;</div>
<div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;</div>
<div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#gaa212b805736a759998e312cbdd550fae">lowp_uint64</a>;</div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;</div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#ga667b2ece2b258be898812dc2177995d1">lowp_uint8_t</a>;</div>
<div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;</div>
<div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#ga91c4815f93177eb423362fd296a87e9f">lowp_uint16_t</a>;</div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;</div>
<div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#gaf1b735b4b1145174f4e4167d13778f9b">lowp_uint32_t</a>;</div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;</div>
<div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#ga8dd3a3281ae5c970ffe0c41d538aa153">lowp_uint64_t</a>;</div>
<div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;</div>
<div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#ga1b09f03da7ac43055c68a349d5445083">lowp_u8</a>;</div>
<div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;</div>
<div class="line"><a name="l00554"></a><span class="lineno">  554</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#ga504ce1631cb2ac02fcf1d44d8c2aa126">lowp_u16</a>;</div>
<div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;</div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#ga4f072ada9552e1e480bbb3b1acde5250">lowp_u32</a>;</div>
<div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;</div>
<div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#ga30069d1f02b19599cbfadf98c23ac6ed">lowp_u64</a>;</div>
<div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;</div>
<div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#ga1fa92a233b9110861cdbc8c2ccf0b5a3">mediump_uint8</a>;</div>
<div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;</div>
<div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#ga2885a6c89916911e418c06bb76b9bdbb">mediump_uint16</a>;</div>
<div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;</div>
<div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#ga34dd5ec1988c443bae80f1b20a8ade5f">mediump_uint32</a>;</div>
<div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;</div>
<div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#ga30652709815ad9404272a31957daa59e">mediump_uint64</a>;</div>
<div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;</div>
<div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#gadfe65c78231039e90507770db50c98c7">mediump_uint8_t</a>;</div>
<div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;</div>
<div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#ga3963b1050fc65a383ee28e3f827b6e3e">mediump_uint16_t</a>;</div>
<div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;</div>
<div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#gaf4dae276fd29623950de14a6ca2586b5">mediump_uint32_t</a>;</div>
<div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;</div>
<div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#ga9b170dd4a8f38448a2dc93987c7875e9">mediump_uint64_t</a>;</div>
<div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;</div>
<div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#gad1213a22bbb9e4107f07eaa4956f8281">mediump_u8</a>;</div>
<div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;</div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#ga9df98857be695d5a30cb30f5bfa38a80">mediump_u16</a>;</div>
<div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;</div>
<div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#ga1bd0e914158bf03135f8a317de6debe9">mediump_u32</a>;</div>
<div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;</div>
<div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#ga2af9490085ae3bdf36a544e9dd073610">mediump_u64</a>;</div>
<div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;</div>
<div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#ga97432f9979e73e66567361fd01e4cffb">highp_uint8</a>;</div>
<div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;</div>
<div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#ga746dc6da204f5622e395f492997dbf57">highp_uint16</a>;</div>
<div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;</div>
<div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#ga256b12b650c3f2fb86878fd1c5db8bc3">highp_uint32</a>;</div>
<div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;</div>
<div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#gaa38d732f5d4a7bc42a1b43b9d3c141ce">highp_uint64</a>;</div>
<div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;</div>
<div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#gac4e00a26a2adb5f2c0a7096810df29e5">highp_uint8_t</a>;</div>
<div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;</div>
<div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#gacf54c3330ef60aa3d16cb676c7bcb8c7">highp_uint16_t</a>;</div>
<div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;</div>
<div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#gae978599c9711ac263ba732d4ac225b0e">highp_uint32_t</a>;</div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;</div>
<div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#gaa46172d7dc1c7ffe3e78107ff88adf08">highp_uint64_t</a>;</div>
<div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;</div>
<div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#gacd1259f3a9e8d2a9df5be2d74322ef9c">highp_u8</a>;</div>
<div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;</div>
<div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#ga8e62c883d13f47015f3b70ed88751369">highp_u16</a>;</div>
<div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;</div>
<div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#ga7a6f1929464dcc680b16381a4ee5f2cf">highp_u32</a>;</div>
<div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;</div>
<div class="line"><a name="l00658"></a><span class="lineno">  658</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#ga0c181fdf06a309691999926b6690c969">highp_u64</a>;</div>
<div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;</div>
<div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;<span class="preprocessor">#if GLM_HAS_EXTENDED_INTEGER_TYPE</span></div>
<div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;        <span class="keyword">using</span> <a class="code" href="a00304.html#ga28d97808322d3c92186e4a0c067d7e8e">std::uint8_t</a>;</div>
<div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;        <span class="keyword">using</span> <a class="code" href="a00304.html#ga91f91f411080c37730856ff5887f5bcf">std::uint16_t</a>;</div>
<div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;        <span class="keyword">using</span> <a class="code" href="a00304.html#ga2171d9dc1fefb1c82e2817f45b622eac">std::uint32_t</a>;</div>
<div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;        <span class="keyword">using</span> <a class="code" href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">std::uint64_t</a>;</div>
<div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#ga28d97808322d3c92186e4a0c067d7e8e">uint8_t</a>;</div>
<div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;</div>
<div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#ga91f91f411080c37730856ff5887f5bcf">uint16_t</a>;</div>
<div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160;</div>
<div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#ga2171d9dc1fefb1c82e2817f45b622eac">uint32_t</a>;</div>
<div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;</div>
<div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">uint64_t</a>;</div>
<div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;</div>
<div class="line"><a name="l00685"></a><span class="lineno">  685</span>&#160;        <span class="keyword">typedef</span> detail::uint8 <a class="code" href="a00304.html#gaecc7082561fc9028b844b6cf3d305d36">u8</a>;</div>
<div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;</div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;        <span class="keyword">typedef</span> detail::uint16 <a class="code" href="a00304.html#gaa2d7acc0adb536fab71fe261232a40ff">u16</a>;</div>
<div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;</div>
<div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;        <span class="keyword">typedef</span> detail::uint32 <a class="code" href="a00304.html#ga8165913e068444f7842302d40ba897b9">u32</a>;</div>
<div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;</div>
<div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;        <span class="keyword">typedef</span> <a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">detail::uint64</a> <a class="code" href="a00304.html#gaf3f312156984c365e9f65620354da70b">u64</a>;</div>
<div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;</div>
<div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;</div>
<div class="line"><a name="l00700"></a><span class="lineno">  700</span>&#160;</div>
<div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;</div>
<div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;</div>
<div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;        <span class="comment">// Float vector types</span></div>
<div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;</div>
<div class="line"><a name="l00708"></a><span class="lineno">  708</span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">float</span> <a class="code" href="a00304.html#gaacdc525d6f7bddb3ae95d5c311bd06a1">float32</a>;</div>
<div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;</div>
<div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160;        <span class="keyword">typedef</span> <span class="keywordtype">double</span> <a class="code" href="a00304.html#ga232fad1b0d6dcc7c16aabde98b2e2a80">float64</a>;</div>
<div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;</div>
<div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#ga41b0d390bd8cc827323b1b3816ff4bf8">lowp_float32</a>;</div>
<div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;</div>
<div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga3714dab2c16a6545a405cb0c3b3aaa6f">lowp_float64</a>;</div>
<div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;</div>
<div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gaea881cae4ddc6c0fbf7cc5b08177ca5b">lowp_float32_t</a>;</div>
<div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;</div>
<div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga7286a37076a09da140df18bfa75d4e38">lowp_float64_t</a>;</div>
<div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;</div>
<div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gaeea53879fc327293cf3352a409b7867b">lowp_f32</a>;</div>
<div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;</div>
<div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#gabc7a97c07cbfac8e35eb5e63beb4b679">lowp_f64</a>;</div>
<div class="line"><a name="l00737"></a><span class="lineno">  737</span>&#160;</div>
<div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#ga41b0d390bd8cc827323b1b3816ff4bf8">lowp_float32</a>;</div>
<div class="line"><a name="l00741"></a><span class="lineno">  741</span>&#160;</div>
<div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga3714dab2c16a6545a405cb0c3b3aaa6f">lowp_float64</a>;</div>
<div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;</div>
<div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gaea881cae4ddc6c0fbf7cc5b08177ca5b">lowp_float32_t</a>;</div>
<div class="line"><a name="l00749"></a><span class="lineno">  749</span>&#160;</div>
<div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga7286a37076a09da140df18bfa75d4e38">lowp_float64_t</a>;</div>
<div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;</div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gaeea53879fc327293cf3352a409b7867b">lowp_f32</a>;</div>
<div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;</div>
<div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#gabc7a97c07cbfac8e35eb5e63beb4b679">lowp_f64</a>;</div>
<div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;</div>
<div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;</div>
<div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#ga41b0d390bd8cc827323b1b3816ff4bf8">lowp_float32</a>;</div>
<div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;</div>
<div class="line"><a name="l00769"></a><span class="lineno">  769</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga3714dab2c16a6545a405cb0c3b3aaa6f">lowp_float64</a>;</div>
<div class="line"><a name="l00770"></a><span class="lineno">  770</span>&#160;</div>
<div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gaea881cae4ddc6c0fbf7cc5b08177ca5b">lowp_float32_t</a>;</div>
<div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;</div>
<div class="line"><a name="l00777"></a><span class="lineno">  777</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga7286a37076a09da140df18bfa75d4e38">lowp_float64_t</a>;</div>
<div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;</div>
<div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gaeea53879fc327293cf3352a409b7867b">lowp_f32</a>;</div>
<div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;</div>
<div class="line"><a name="l00785"></a><span class="lineno">  785</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#gabc7a97c07cbfac8e35eb5e63beb4b679">lowp_f64</a>;</div>
<div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;</div>
<div class="line"><a name="l00787"></a><span class="lineno">  787</span>&#160;</div>
<div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#ga7812bf00676fb1a86dcd62cca354d2c7">mediump_float32</a>;</div>
<div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;</div>
<div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#gab83d8aae6e4f115e97a785e8574a115f">mediump_float64</a>;</div>
<div class="line"><a name="l00795"></a><span class="lineno">  795</span>&#160;</div>
<div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gae4dee61f8fe1caccec309fbed02faf12">mediump_float32_t</a>;</div>
<div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;</div>
<div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#gac61843e4fa96c1f4e9d8316454f32a8e">mediump_float64_t</a>;</div>
<div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;</div>
<div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#ga3b27fcd9eaa2757f0aaf6b0ce0d85c80">mediump_f32</a>;</div>
<div class="line"><a name="l00807"></a><span class="lineno">  807</span>&#160;</div>
<div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga6d40381d78472553f878f66e443feeef">mediump_f64</a>;</div>
<div class="line"><a name="l00811"></a><span class="lineno">  811</span>&#160;</div>
<div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160;</div>
<div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gac5a7f21136e0a78d0a1b9f60ef2f8aea">highp_float32</a>;</div>
<div class="line"><a name="l00816"></a><span class="lineno">  816</span>&#160;</div>
<div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#gadbb198a4d7aad82a0f4dc466ef6f6215">highp_float64</a>;</div>
<div class="line"><a name="l00820"></a><span class="lineno">  820</span>&#160;</div>
<div class="line"><a name="l00823"></a><span class="lineno">  823</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#ga5376ef18dca9d248897c3363ef5a06b2">highp_float32_t</a>;</div>
<div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160;</div>
<div class="line"><a name="l00827"></a><span class="lineno">  827</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#gaaeeb0077198cff40e3f48b1108ece139">highp_float64_t</a>;</div>
<div class="line"><a name="l00828"></a><span class="lineno">  828</span>&#160;</div>
<div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#ga6906e1ef0b34064b4b675489c5c38725">highp_f32</a>;</div>
<div class="line"><a name="l00832"></a><span class="lineno">  832</span>&#160;</div>
<div class="line"><a name="l00835"></a><span class="lineno">  835</span>&#160;        <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga51d5266017d88f62737c1973923a7cf4">highp_f64</a>;</div>
<div class="line"><a name="l00836"></a><span class="lineno">  836</span>&#160;</div>
<div class="line"><a name="l00837"></a><span class="lineno">  837</span>&#160;</div>
<div class="line"><a name="l00838"></a><span class="lineno">  838</span>&#160;<span class="preprocessor">#if(defined(GLM_PRECISION_LOWP_FLOAT))</span></div>
<div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160;        <span class="keyword">typedef</span> lowp_float32_t <a class="code" href="a00304.html#gaa4947bc8b47c72fceea9bda730ecf603">float32_t</a>;</div>
<div class="line"><a name="l00842"></a><span class="lineno">  842</span>&#160;</div>
<div class="line"><a name="l00845"></a><span class="lineno">  845</span>&#160;        <span class="keyword">typedef</span> lowp_float64_t <a class="code" href="a00304.html#ga728366fef72cd96f0a5fa6429f05469e">float64_t</a>;</div>
<div class="line"><a name="l00846"></a><span class="lineno">  846</span>&#160;</div>
<div class="line"><a name="l00849"></a><span class="lineno">  849</span>&#160;        <span class="keyword">typedef</span> lowp_f32 <a class="code" href="a00304.html#gabe6a542dd6c1d5ffd847f1b9b4c9c9b7">f32</a>;</div>
<div class="line"><a name="l00850"></a><span class="lineno">  850</span>&#160;</div>
<div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;        <span class="keyword">typedef</span> lowp_f64 <a class="code" href="a00304.html#ga1d794d240091678f602e8de225b8d8c9">f64</a>;</div>
<div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;</div>
<div class="line"><a name="l00855"></a><span class="lineno">  855</span>&#160;<span class="preprocessor">#elif(defined(GLM_PRECISION_MEDIUMP_FLOAT))</span></div>
<div class="line"><a name="l00856"></a><span class="lineno">  856</span>&#160;        <span class="keyword">typedef</span> mediump_float32 <a class="code" href="a00304.html#gaa4947bc8b47c72fceea9bda730ecf603">float32_t</a>;</div>
<div class="line"><a name="l00859"></a><span class="lineno">  859</span>&#160;</div>
<div class="line"><a name="l00862"></a><span class="lineno">  862</span>&#160;        <span class="keyword">typedef</span> mediump_float64 <a class="code" href="a00304.html#ga728366fef72cd96f0a5fa6429f05469e">float64_t</a>;</div>
<div class="line"><a name="l00863"></a><span class="lineno">  863</span>&#160;</div>
<div class="line"><a name="l00866"></a><span class="lineno">  866</span>&#160;        <span class="keyword">typedef</span> mediump_float32 <a class="code" href="a00304.html#gabe6a542dd6c1d5ffd847f1b9b4c9c9b7">f32</a>;</div>
<div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;</div>
<div class="line"><a name="l00870"></a><span class="lineno">  870</span>&#160;        <span class="keyword">typedef</span> mediump_float64 <a class="code" href="a00304.html#ga1d794d240091678f602e8de225b8d8c9">f64</a>;</div>
<div class="line"><a name="l00871"></a><span class="lineno">  871</span>&#160;</div>
<div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;<span class="preprocessor">#else//(defined(GLM_PRECISION_HIGHP_FLOAT))</span></div>
<div class="line"><a name="l00873"></a><span class="lineno">  873</span>&#160;</div>
<div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;        <span class="keyword">typedef</span> highp_float32_t <a class="code" href="a00304.html#gaa4947bc8b47c72fceea9bda730ecf603">float32_t</a>;</div>
<div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160;</div>
<div class="line"><a name="l00880"></a><span class="lineno">  880</span>&#160;        <span class="keyword">typedef</span> highp_float64_t <a class="code" href="a00304.html#ga728366fef72cd96f0a5fa6429f05469e">float64_t</a>;</div>
<div class="line"><a name="l00881"></a><span class="lineno">  881</span>&#160;</div>
<div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;        <span class="keyword">typedef</span> highp_float32_t <a class="code" href="a00304.html#gabe6a542dd6c1d5ffd847f1b9b4c9c9b7">f32</a>;</div>
<div class="line"><a name="l00885"></a><span class="lineno">  885</span>&#160;</div>
<div class="line"><a name="l00888"></a><span class="lineno">  888</span>&#160;        <span class="keyword">typedef</span> highp_float64_t <a class="code" href="a00304.html#ga1d794d240091678f602e8de225b8d8c9">f64</a>;</div>
<div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00890"></a><span class="lineno">  890</span>&#160;</div>
<div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;</div>
<div class="line"><a name="l00894"></a><span class="lineno">  894</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, lowp&gt; <a class="code" href="a00304.html#ga346b2336fff168a7e0df1583aae3e5a5">lowp_fvec1</a>;</div>
<div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;</div>
<div class="line"><a name="l00898"></a><span class="lineno">  898</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, lowp&gt; <a class="code" href="a00304.html#ga62a32c31f4e2e8ca859663b6e3289a2d">lowp_fvec2</a>;</div>
<div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160;</div>
<div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, lowp&gt; <a class="code" href="a00304.html#ga40b5c557efebb5bb99d6b9aa81095afa">lowp_fvec3</a>;</div>
<div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160;</div>
<div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, lowp&gt; <a class="code" href="a00304.html#ga755484ffbe39ae3db2875953ed04e7b7">lowp_fvec4</a>;</div>
<div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;</div>
<div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160;</div>
<div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, mediump&gt; <a class="code" href="a00304.html#ga367964fc2133d3f1b5b3755ff9cf6c9b">mediump_fvec1</a>;</div>
<div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;</div>
<div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, mediump&gt; <a class="code" href="a00304.html#ga44bfa55cda5dbf53f24a1fb7610393d6">mediump_fvec2</a>;</div>
<div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;</div>
<div class="line"><a name="l00919"></a><span class="lineno">  919</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, mediump&gt; <a class="code" href="a00304.html#ga999dc6703ad16e3d3c26b74ea8083f07">mediump_fvec3</a>;</div>
<div class="line"><a name="l00920"></a><span class="lineno">  920</span>&#160;</div>
<div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, mediump&gt; <a class="code" href="a00304.html#ga1bed890513c0f50b7e7ba4f7f359dbfb">mediump_fvec4</a>;</div>
<div class="line"><a name="l00924"></a><span class="lineno">  924</span>&#160;</div>
<div class="line"><a name="l00925"></a><span class="lineno">  925</span>&#160;</div>
<div class="line"><a name="l00928"></a><span class="lineno">  928</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, highp&gt; <a class="code" href="a00304.html#gaa1040342c4efdedc8f90e6267db8d41c">highp_fvec1</a>;</div>
<div class="line"><a name="l00929"></a><span class="lineno">  929</span>&#160;</div>
<div class="line"><a name="l00932"></a><span class="lineno">  932</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, highp&gt; <a class="code" href="a00304.html#ga7c0d196f5fa79f7e892a2f323a0be1ae">highp_fvec2</a>;</div>
<div class="line"><a name="l00933"></a><span class="lineno">  933</span>&#160;</div>
<div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, highp&gt; <a class="code" href="a00304.html#ga6ef77413883f48d6b53b4169b25edbd0">highp_fvec3</a>;</div>
<div class="line"><a name="l00937"></a><span class="lineno">  937</span>&#160;</div>
<div class="line"><a name="l00940"></a><span class="lineno">  940</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, highp&gt; <a class="code" href="a00304.html#ga8b839abbb44f5102609eed89f6ed61f7">highp_fvec4</a>;</div>
<div class="line"><a name="l00941"></a><span class="lineno">  941</span>&#160;</div>
<div class="line"><a name="l00942"></a><span class="lineno">  942</span>&#160;</div>
<div class="line"><a name="l00945"></a><span class="lineno">  945</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, f32, lowp&gt; <a class="code" href="a00304.html#ga43e5b41c834fcaf4db5a831c0e28128e">lowp_f32vec1</a>;</div>
<div class="line"><a name="l00946"></a><span class="lineno">  946</span>&#160;</div>
<div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, f32, lowp&gt; <a class="code" href="a00304.html#gaf3b694b2b8ded7e0b9f07b061917e1a0">lowp_f32vec2</a>;</div>
<div class="line"><a name="l00950"></a><span class="lineno">  950</span>&#160;</div>
<div class="line"><a name="l00953"></a><span class="lineno">  953</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, f32, lowp&gt; <a class="code" href="a00304.html#gaf739a2cd7b81783a43148b53e40d983b">lowp_f32vec3</a>;</div>
<div class="line"><a name="l00954"></a><span class="lineno">  954</span>&#160;</div>
<div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, f32, lowp&gt; <a class="code" href="a00304.html#ga4e2e1debe022074ab224c9faf856d374">lowp_f32vec4</a>;</div>
<div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160;</div>
<div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, f32, mediump&gt; <a class="code" href="a00304.html#gabb33cab7d7c74cc14aa95455d0690865">mediump_f32vec1</a>;</div>
<div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;</div>
<div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, f32, mediump&gt; <a class="code" href="a00304.html#gad6eb11412a3161ca8dc1d63b2a307c4b">mediump_f32vec2</a>;</div>
<div class="line"><a name="l00966"></a><span class="lineno">  966</span>&#160;</div>
<div class="line"><a name="l00969"></a><span class="lineno">  969</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, f32, mediump&gt; <a class="code" href="a00304.html#ga062ffef2973bd8241df993c3b30b327c">mediump_f32vec3</a>;</div>
<div class="line"><a name="l00970"></a><span class="lineno">  970</span>&#160;</div>
<div class="line"><a name="l00973"></a><span class="lineno">  973</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, f32, mediump&gt; <a class="code" href="a00304.html#gad80c84bcd5f585840faa6179f6fd446c">mediump_f32vec4</a>;</div>
<div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;</div>
<div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, f32, highp&gt; <a class="code" href="a00304.html#gab1b1c9e8667902b78b2c330e4d383a61">highp_f32vec1</a>;</div>
<div class="line"><a name="l00978"></a><span class="lineno">  978</span>&#160;</div>
<div class="line"><a name="l00981"></a><span class="lineno">  981</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, f32, highp&gt; <a class="code" href="a00304.html#ga0b8ebd4262331e139ff257d7cf2a4b77">highp_f32vec2</a>;</div>
<div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;</div>
<div class="line"><a name="l00985"></a><span class="lineno">  985</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, f32, highp&gt; <a class="code" href="a00304.html#ga522775dbcc6d96246a1c5cf02344fd8c">highp_f32vec3</a>;</div>
<div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;</div>
<div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, f32, highp&gt; <a class="code" href="a00304.html#ga0f038d4e09862a74f03d102c59eda73e">highp_f32vec4</a>;</div>
<div class="line"><a name="l00990"></a><span class="lineno">  990</span>&#160;</div>
<div class="line"><a name="l00991"></a><span class="lineno">  991</span>&#160;</div>
<div class="line"><a name="l00994"></a><span class="lineno">  994</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, f64, lowp&gt; <a class="code" href="a00304.html#gaf2d02c5f4d59135b9bc524fe317fd26b">lowp_f64vec1</a>;</div>
<div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;</div>
<div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, f64, lowp&gt; <a class="code" href="a00304.html#ga4e641a54d70c81eabf56c25c966d04bd">lowp_f64vec2</a>;</div>
<div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160;</div>
<div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, f64, lowp&gt; <a class="code" href="a00304.html#gae7a4711107b7d078fc5f03ce2227b90b">lowp_f64vec3</a>;</div>
<div class="line"><a name="l01003"></a><span class="lineno"> 1003</span>&#160;</div>
<div class="line"><a name="l01006"></a><span class="lineno"> 1006</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, f64, lowp&gt; <a class="code" href="a00304.html#gaa666bb9e6d204d3bea0b3a39a3a335f4">lowp_f64vec4</a>;</div>
<div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;</div>
<div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, f64, mediump&gt; <a class="code" href="a00304.html#gac30fdf8afa489400053275b6a3350127">mediump_f64vec1</a>;</div>
<div class="line"><a name="l01011"></a><span class="lineno"> 1011</span>&#160;</div>
<div class="line"><a name="l01014"></a><span class="lineno"> 1014</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, f64, mediump&gt; <a class="code" href="a00304.html#ga8ebc04ecf6440c4ee24718a16600ce6b">mediump_f64vec2</a>;</div>
<div class="line"><a name="l01015"></a><span class="lineno"> 1015</span>&#160;</div>
<div class="line"><a name="l01018"></a><span class="lineno"> 1018</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, f64, mediump&gt; <a class="code" href="a00304.html#ga461c4c7d0757404dd0dba931760b25cf">mediump_f64vec3</a>;</div>
<div class="line"><a name="l01019"></a><span class="lineno"> 1019</span>&#160;</div>
<div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, f64, mediump&gt; <a class="code" href="a00304.html#gacfea053bd6bb3eddb996a4f94de22a3e">mediump_f64vec4</a>;</div>
<div class="line"><a name="l01023"></a><span class="lineno"> 1023</span>&#160;</div>
<div class="line"><a name="l01026"></a><span class="lineno"> 1026</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, f64, highp&gt; <a class="code" href="a00304.html#ga62c31b133ceee9984fbee05ac4c434a9">highp_f64vec1</a>;</div>
<div class="line"><a name="l01027"></a><span class="lineno"> 1027</span>&#160;</div>
<div class="line"><a name="l01030"></a><span class="lineno"> 1030</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, f64, highp&gt; <a class="code" href="a00304.html#ga670ea1b0a1172bc73b1d7c1e0c26cce2">highp_f64vec2</a>;</div>
<div class="line"><a name="l01031"></a><span class="lineno"> 1031</span>&#160;</div>
<div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, f64, highp&gt; <a class="code" href="a00304.html#gacd1196090ece7a69fb5c3e43a7d4d851">highp_f64vec3</a>;</div>
<div class="line"><a name="l01035"></a><span class="lineno"> 1035</span>&#160;</div>
<div class="line"><a name="l01038"></a><span class="lineno"> 1038</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, f64, highp&gt; <a class="code" href="a00304.html#ga61185c44c8cc0b25d9a0f67d8a267444">highp_f64vec4</a>;</div>
<div class="line"><a name="l01039"></a><span class="lineno"> 1039</span>&#160;</div>
<div class="line"><a name="l01040"></a><span class="lineno"> 1040</span>&#160;</div>
<div class="line"><a name="l01041"></a><span class="lineno"> 1041</span>&#160;</div>
<div class="line"><a name="l01043"></a><span class="lineno"> 1043</span>&#160;        <span class="comment">// Float matrix types</span></div>
<div class="line"><a name="l01044"></a><span class="lineno"> 1044</span>&#160;</div>
<div class="line"><a name="l01047"></a><span class="lineno"> 1047</span>&#160;        <span class="comment">//typedef lowp_f32 lowp_fmat1x1;</span></div>
<div class="line"><a name="l01048"></a><span class="lineno"> 1048</span>&#160;</div>
<div class="line"><a name="l01051"></a><span class="lineno"> 1051</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, lowp&gt; <a class="code" href="a00304.html#gab0feb11edd0d3ab3e8ed996d349a5066">lowp_fmat2x2</a>;</div>
<div class="line"><a name="l01052"></a><span class="lineno"> 1052</span>&#160;</div>
<div class="line"><a name="l01055"></a><span class="lineno"> 1055</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f32, lowp&gt; <a class="code" href="a00304.html#ga71cdb53801ed4c3aadb3603c04723210">lowp_fmat2x3</a>;</div>
<div class="line"><a name="l01056"></a><span class="lineno"> 1056</span>&#160;</div>
<div class="line"><a name="l01059"></a><span class="lineno"> 1059</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f32, lowp&gt; <a class="code" href="a00304.html#gaab217601c74974a84acbca428123ecf7">lowp_fmat2x4</a>;</div>
<div class="line"><a name="l01060"></a><span class="lineno"> 1060</span>&#160;</div>
<div class="line"><a name="l01063"></a><span class="lineno"> 1063</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f32, lowp&gt; <a class="code" href="a00304.html#ga49b98e7d71804af45d86886a489e633c">lowp_fmat3x2</a>;</div>
<div class="line"><a name="l01064"></a><span class="lineno"> 1064</span>&#160;</div>
<div class="line"><a name="l01067"></a><span class="lineno"> 1067</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, lowp&gt; <a class="code" href="a00304.html#gaba56275dd04a7a61560b0e8fa5d365b4">lowp_fmat3x3</a>;</div>
<div class="line"><a name="l01068"></a><span class="lineno"> 1068</span>&#160;</div>
<div class="line"><a name="l01071"></a><span class="lineno"> 1071</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f32, lowp&gt; <a class="code" href="a00304.html#ga28733aec7288191b314d42154fd0b690">lowp_fmat3x4</a>;</div>
<div class="line"><a name="l01072"></a><span class="lineno"> 1072</span>&#160;</div>
<div class="line"><a name="l01075"></a><span class="lineno"> 1075</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f32, lowp&gt; <a class="code" href="a00304.html#ga5868c2dcce41cc3ea5edcaeae239f62c">lowp_fmat4x2</a>;</div>
<div class="line"><a name="l01076"></a><span class="lineno"> 1076</span>&#160;</div>
<div class="line"><a name="l01079"></a><span class="lineno"> 1079</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f32, lowp&gt; <a class="code" href="a00304.html#ga5e649bbdb135fbcb4bfe950f4c73a444">lowp_fmat4x3</a>;</div>
<div class="line"><a name="l01080"></a><span class="lineno"> 1080</span>&#160;</div>
<div class="line"><a name="l01083"></a><span class="lineno"> 1083</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, lowp&gt; <a class="code" href="a00304.html#gac2f5263708ac847b361a9841e74ddf9f">lowp_fmat4x4</a>;</div>
<div class="line"><a name="l01084"></a><span class="lineno"> 1084</span>&#160;</div>
<div class="line"><a name="l01087"></a><span class="lineno"> 1087</span>&#160;        <span class="comment">//typedef lowp_fmat1x1 lowp_fmat1;</span></div>
<div class="line"><a name="l01088"></a><span class="lineno"> 1088</span>&#160;</div>
<div class="line"><a name="l01091"></a><span class="lineno"> 1091</span>&#160;        <span class="keyword">typedef</span> lowp_fmat2x2 <a class="code" href="a00304.html#ga5bba0ce31210e274f73efacd3364c03f">lowp_fmat2</a>;</div>
<div class="line"><a name="l01092"></a><span class="lineno"> 1092</span>&#160;</div>
<div class="line"><a name="l01095"></a><span class="lineno"> 1095</span>&#160;        <span class="keyword">typedef</span> lowp_fmat3x3 <a class="code" href="a00304.html#ga83079315e230e8f39728f4bf0d2f9a9b">lowp_fmat3</a>;</div>
<div class="line"><a name="l01096"></a><span class="lineno"> 1096</span>&#160;</div>
<div class="line"><a name="l01099"></a><span class="lineno"> 1099</span>&#160;        <span class="keyword">typedef</span> lowp_fmat4x4 <a class="code" href="a00304.html#ga5803cb9ae26399762d8bba9e0b2fc09f">lowp_fmat4</a>;</div>
<div class="line"><a name="l01100"></a><span class="lineno"> 1100</span>&#160;</div>
<div class="line"><a name="l01101"></a><span class="lineno"> 1101</span>&#160;</div>
<div class="line"><a name="l01104"></a><span class="lineno"> 1104</span>&#160;        <span class="comment">//typedef mediump_f32 mediump_fmat1x1;</span></div>
<div class="line"><a name="l01105"></a><span class="lineno"> 1105</span>&#160;</div>
<div class="line"><a name="l01108"></a><span class="lineno"> 1108</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, mediump&gt; <a class="code" href="a00304.html#ga98a687c17b174ea316b5f397b64f44bc">mediump_fmat2x2</a>;</div>
<div class="line"><a name="l01109"></a><span class="lineno"> 1109</span>&#160;</div>
<div class="line"><a name="l01112"></a><span class="lineno"> 1112</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f32, mediump&gt; <a class="code" href="a00304.html#gaa03f939d90d5ef157df957d93f0b9a64">mediump_fmat2x3</a>;</div>
<div class="line"><a name="l01113"></a><span class="lineno"> 1113</span>&#160;</div>
<div class="line"><a name="l01116"></a><span class="lineno"> 1116</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f32, mediump&gt; <a class="code" href="a00304.html#ga35223623e9ccebd8a281873b71b7d213">mediump_fmat2x4</a>;</div>
<div class="line"><a name="l01117"></a><span class="lineno"> 1117</span>&#160;</div>
<div class="line"><a name="l01120"></a><span class="lineno"> 1120</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f32, mediump&gt; <a class="code" href="a00304.html#ga42569e5b92f8635cedeadb1457ee1467">mediump_fmat3x2</a>;</div>
<div class="line"><a name="l01121"></a><span class="lineno"> 1121</span>&#160;</div>
<div class="line"><a name="l01124"></a><span class="lineno"> 1124</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, mediump&gt; <a class="code" href="a00304.html#gaa6f526388c74a66b3d52315a14d434ae">mediump_fmat3x3</a>;</div>
<div class="line"><a name="l01125"></a><span class="lineno"> 1125</span>&#160;</div>
<div class="line"><a name="l01128"></a><span class="lineno"> 1128</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f32, mediump&gt; <a class="code" href="a00304.html#gaefe8ef520c6cb78590ebbefe648da4d4">mediump_fmat3x4</a>;</div>
<div class="line"><a name="l01129"></a><span class="lineno"> 1129</span>&#160;</div>
<div class="line"><a name="l01132"></a><span class="lineno"> 1132</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f32, mediump&gt; <a class="code" href="a00304.html#gacea38a85893e17e6834b6cb09a9ad0cf">mediump_fmat4x2</a>;</div>
<div class="line"><a name="l01133"></a><span class="lineno"> 1133</span>&#160;</div>
<div class="line"><a name="l01136"></a><span class="lineno"> 1136</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f32, mediump&gt; <a class="code" href="a00304.html#ga41ad497f7eae211556aefd783cb02b90">mediump_fmat4x3</a>;</div>
<div class="line"><a name="l01137"></a><span class="lineno"> 1137</span>&#160;</div>
<div class="line"><a name="l01140"></a><span class="lineno"> 1140</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, mediump&gt; <a class="code" href="a00304.html#ga22e27beead07bff4d5ce9d6065a57279">mediump_fmat4x4</a>;</div>
<div class="line"><a name="l01141"></a><span class="lineno"> 1141</span>&#160;</div>
<div class="line"><a name="l01144"></a><span class="lineno"> 1144</span>&#160;        <span class="comment">//typedef mediump_fmat1x1 mediump_fmat1;</span></div>
<div class="line"><a name="l01145"></a><span class="lineno"> 1145</span>&#160;</div>
<div class="line"><a name="l01148"></a><span class="lineno"> 1148</span>&#160;        <span class="keyword">typedef</span> mediump_fmat2x2 <a class="code" href="a00304.html#ga74e9133378fd0b4da8ac0bc0876702ff">mediump_fmat2</a>;</div>
<div class="line"><a name="l01149"></a><span class="lineno"> 1149</span>&#160;</div>
<div class="line"><a name="l01152"></a><span class="lineno"> 1152</span>&#160;        <span class="keyword">typedef</span> mediump_fmat3x3 <a class="code" href="a00304.html#ga80823dfad5dba98512c76af498343847">mediump_fmat3</a>;</div>
<div class="line"><a name="l01153"></a><span class="lineno"> 1153</span>&#160;</div>
<div class="line"><a name="l01156"></a><span class="lineno"> 1156</span>&#160;        <span class="keyword">typedef</span> mediump_fmat4x4 <a class="code" href="a00304.html#gac1c38778c0b5a1263f07753c05a4f7b9">mediump_fmat4</a>;</div>
<div class="line"><a name="l01157"></a><span class="lineno"> 1157</span>&#160;</div>
<div class="line"><a name="l01158"></a><span class="lineno"> 1158</span>&#160;</div>
<div class="line"><a name="l01161"></a><span class="lineno"> 1161</span>&#160;        <span class="comment">//typedef highp_f32 highp_fmat1x1;</span></div>
<div class="line"><a name="l01162"></a><span class="lineno"> 1162</span>&#160;</div>
<div class="line"><a name="l01165"></a><span class="lineno"> 1165</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, highp&gt; <a class="code" href="a00304.html#ga28635abcddb2f3e92c33c3f0fcc682ad">highp_fmat2x2</a>;</div>
<div class="line"><a name="l01166"></a><span class="lineno"> 1166</span>&#160;</div>
<div class="line"><a name="l01169"></a><span class="lineno"> 1169</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f32, highp&gt; <a class="code" href="a00304.html#gacf111095594996fef29067b2454fccad">highp_fmat2x3</a>;</div>
<div class="line"><a name="l01170"></a><span class="lineno"> 1170</span>&#160;</div>
<div class="line"><a name="l01173"></a><span class="lineno"> 1173</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f32, highp&gt; <a class="code" href="a00304.html#ga4920a1536f161f7ded1d6909b7fef0d2">highp_fmat2x4</a>;</div>
<div class="line"><a name="l01174"></a><span class="lineno"> 1174</span>&#160;</div>
<div class="line"><a name="l01177"></a><span class="lineno"> 1177</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f32, highp&gt; <a class="code" href="a00304.html#gae54e4d1aeb5a0f0c64822e6f1b299e19">highp_fmat3x2</a>;</div>
<div class="line"><a name="l01178"></a><span class="lineno"> 1178</span>&#160;</div>
<div class="line"><a name="l01181"></a><span class="lineno"> 1181</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, highp&gt; <a class="code" href="a00304.html#gaa5b44d3ef6efcf33f44876673a7a936e">highp_fmat3x3</a>;</div>
<div class="line"><a name="l01182"></a><span class="lineno"> 1182</span>&#160;</div>
<div class="line"><a name="l01185"></a><span class="lineno"> 1185</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f32, highp&gt; <a class="code" href="a00304.html#ga961fac2a885907ffcf4d40daac6615c5">highp_fmat3x4</a>;</div>
<div class="line"><a name="l01186"></a><span class="lineno"> 1186</span>&#160;</div>
<div class="line"><a name="l01189"></a><span class="lineno"> 1189</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f32, highp&gt; <a class="code" href="a00304.html#ga076961cf2d120c7168b957cb2ed107b3">highp_fmat4x2</a>;</div>
<div class="line"><a name="l01190"></a><span class="lineno"> 1190</span>&#160;</div>
<div class="line"><a name="l01193"></a><span class="lineno"> 1193</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f32, highp&gt; <a class="code" href="a00304.html#gae406ec670f64170a7437b5e302eeb2cb">highp_fmat4x3</a>;</div>
<div class="line"><a name="l01194"></a><span class="lineno"> 1194</span>&#160;</div>
<div class="line"><a name="l01197"></a><span class="lineno"> 1197</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, highp&gt; <a class="code" href="a00304.html#gaee80c7cd3caa0f2635058656755f6f69">highp_fmat4x4</a>;</div>
<div class="line"><a name="l01198"></a><span class="lineno"> 1198</span>&#160;</div>
<div class="line"><a name="l01201"></a><span class="lineno"> 1201</span>&#160;        <span class="comment">//typedef highp_fmat1x1 highp_fmat1;</span></div>
<div class="line"><a name="l01202"></a><span class="lineno"> 1202</span>&#160;</div>
<div class="line"><a name="l01205"></a><span class="lineno"> 1205</span>&#160;        <span class="keyword">typedef</span> highp_fmat2x2 <a class="code" href="a00304.html#gae98c88d9a7befa9b5877f49176225535">highp_fmat2</a>;</div>
<div class="line"><a name="l01206"></a><span class="lineno"> 1206</span>&#160;</div>
<div class="line"><a name="l01209"></a><span class="lineno"> 1209</span>&#160;        <span class="keyword">typedef</span> highp_fmat3x3 <a class="code" href="a00304.html#gaed2dc69e0d507d4191092dbd44b3eb75">highp_fmat3</a>;</div>
<div class="line"><a name="l01210"></a><span class="lineno"> 1210</span>&#160;</div>
<div class="line"><a name="l01213"></a><span class="lineno"> 1213</span>&#160;        <span class="keyword">typedef</span> highp_fmat4x4 <a class="code" href="a00304.html#gabf28443ce0cc0959077ec39b21f32c39">highp_fmat4</a>;</div>
<div class="line"><a name="l01214"></a><span class="lineno"> 1214</span>&#160;</div>
<div class="line"><a name="l01215"></a><span class="lineno"> 1215</span>&#160;</div>
<div class="line"><a name="l01218"></a><span class="lineno"> 1218</span>&#160;        <span class="comment">//typedef f32 lowp_f32mat1x1;</span></div>
<div class="line"><a name="l01219"></a><span class="lineno"> 1219</span>&#160;</div>
<div class="line"><a name="l01222"></a><span class="lineno"> 1222</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, lowp&gt; <a class="code" href="a00304.html#ga1d091b6abfba1772450e1745a06525bc">lowp_f32mat2x2</a>;</div>
<div class="line"><a name="l01223"></a><span class="lineno"> 1223</span>&#160;</div>
<div class="line"><a name="l01226"></a><span class="lineno"> 1226</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f32, lowp&gt; <a class="code" href="a00304.html#ga961ccb34cd1a5654c772c8709e001dc5">lowp_f32mat2x3</a>;</div>
<div class="line"><a name="l01227"></a><span class="lineno"> 1227</span>&#160;</div>
<div class="line"><a name="l01230"></a><span class="lineno"> 1230</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f32, lowp&gt; <a class="code" href="a00304.html#gacc6bf0209dda0c7c14851a646071c974">lowp_f32mat2x4</a>;</div>
<div class="line"><a name="l01231"></a><span class="lineno"> 1231</span>&#160;</div>
<div class="line"><a name="l01234"></a><span class="lineno"> 1234</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f32, lowp&gt; <a class="code" href="a00304.html#gac53f9d7ab04eace67adad026092fb1e8">lowp_f32mat3x2</a>;</div>
<div class="line"><a name="l01235"></a><span class="lineno"> 1235</span>&#160;</div>
<div class="line"><a name="l01238"></a><span class="lineno"> 1238</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, lowp&gt; <a class="code" href="a00304.html#ga841211b641cff1fcf861bdb14e5e4abc">lowp_f32mat3x3</a>;</div>
<div class="line"><a name="l01239"></a><span class="lineno"> 1239</span>&#160;</div>
<div class="line"><a name="l01242"></a><span class="lineno"> 1242</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f32, lowp&gt; <a class="code" href="a00304.html#ga21b1b22dec013a72656e3644baf8a1e1">lowp_f32mat3x4</a>;</div>
<div class="line"><a name="l01243"></a><span class="lineno"> 1243</span>&#160;</div>
<div class="line"><a name="l01246"></a><span class="lineno"> 1246</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f32, lowp&gt; <a class="code" href="a00304.html#gae6f3fcb702a666de07650c149cfa845a">lowp_f32mat4x2</a>;</div>
<div class="line"><a name="l01247"></a><span class="lineno"> 1247</span>&#160;</div>
<div class="line"><a name="l01250"></a><span class="lineno"> 1250</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f32, lowp&gt; <a class="code" href="a00304.html#gac21eda58a1475449a5709b412ebd776c">lowp_f32mat4x3</a>;</div>
<div class="line"><a name="l01251"></a><span class="lineno"> 1251</span>&#160;</div>
<div class="line"><a name="l01254"></a><span class="lineno"> 1254</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, lowp&gt; <a class="code" href="a00304.html#ga4143d129898f91545948c46859adce44">lowp_f32mat4x4</a>;</div>
<div class="line"><a name="l01255"></a><span class="lineno"> 1255</span>&#160;</div>
<div class="line"><a name="l01258"></a><span class="lineno"> 1258</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f32, lowp&gt; lowp_f32mat1;</span></div>
<div class="line"><a name="l01259"></a><span class="lineno"> 1259</span>&#160;</div>
<div class="line"><a name="l01262"></a><span class="lineno"> 1262</span>&#160;        <span class="keyword">typedef</span> lowp_f32mat2x2 <a class="code" href="a00304.html#ga52409bc6d4a2ce3421526c069220d685">lowp_f32mat2</a>;</div>
<div class="line"><a name="l01263"></a><span class="lineno"> 1263</span>&#160;</div>
<div class="line"><a name="l01266"></a><span class="lineno"> 1266</span>&#160;        <span class="keyword">typedef</span> lowp_f32mat3x3 <a class="code" href="a00304.html#ga4187f89f196505b40e63f516139511e5">lowp_f32mat3</a>;</div>
<div class="line"><a name="l01267"></a><span class="lineno"> 1267</span>&#160;</div>
<div class="line"><a name="l01270"></a><span class="lineno"> 1270</span>&#160;        <span class="keyword">typedef</span> lowp_f32mat4x4 <a class="code" href="a00304.html#ga766aed2871e6173a81011a877f398f04">lowp_f32mat4</a>;</div>
<div class="line"><a name="l01271"></a><span class="lineno"> 1271</span>&#160;</div>
<div class="line"><a name="l01272"></a><span class="lineno"> 1272</span>&#160;</div>
<div class="line"><a name="l01275"></a><span class="lineno"> 1275</span>&#160;        <span class="comment">//typedef f32 mediump_f32mat1x1;</span></div>
<div class="line"><a name="l01276"></a><span class="lineno"> 1276</span>&#160;</div>
<div class="line"><a name="l01279"></a><span class="lineno"> 1279</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, mediump&gt; <a class="code" href="a00304.html#gaa3ca74a44102035b3ffb5c9c52dfdd3f">mediump_f32mat2x2</a>;</div>
<div class="line"><a name="l01280"></a><span class="lineno"> 1280</span>&#160;</div>
<div class="line"><a name="l01283"></a><span class="lineno"> 1283</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f32, mediump&gt; <a class="code" href="a00304.html#gad4cc829ab1ad3e05ac0a24828a3c95cf">mediump_f32mat2x3</a>;</div>
<div class="line"><a name="l01284"></a><span class="lineno"> 1284</span>&#160;</div>
<div class="line"><a name="l01287"></a><span class="lineno"> 1287</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f32, mediump&gt; <a class="code" href="a00304.html#gae71445ac6cd0b9fba3e5c905cd030fb1">mediump_f32mat2x4</a>;</div>
<div class="line"><a name="l01288"></a><span class="lineno"> 1288</span>&#160;</div>
<div class="line"><a name="l01291"></a><span class="lineno"> 1291</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f32, mediump&gt; <a class="code" href="a00304.html#gaaab39454f56cf9fc6d940358ce5e6a0f">mediump_f32mat3x2</a>;</div>
<div class="line"><a name="l01292"></a><span class="lineno"> 1292</span>&#160;</div>
<div class="line"><a name="l01295"></a><span class="lineno"> 1295</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, mediump&gt; <a class="code" href="a00304.html#gacd80ad7640e9e32f2edcb8330b1ffe4f">mediump_f32mat3x3</a>;</div>
<div class="line"><a name="l01296"></a><span class="lineno"> 1296</span>&#160;</div>
<div class="line"><a name="l01299"></a><span class="lineno"> 1299</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f32, mediump&gt; <a class="code" href="a00304.html#ga8df705d775b776f5ae6b39e2ab892899">mediump_f32mat3x4</a>;</div>
<div class="line"><a name="l01300"></a><span class="lineno"> 1300</span>&#160;</div>
<div class="line"><a name="l01303"></a><span class="lineno"> 1303</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f32, mediump&gt; <a class="code" href="a00304.html#gab005efe0fa4de1a928e8ddec4bc2c43f">mediump_f32mat4x2</a>;</div>
<div class="line"><a name="l01304"></a><span class="lineno"> 1304</span>&#160;</div>
<div class="line"><a name="l01307"></a><span class="lineno"> 1307</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f32, mediump&gt; <a class="code" href="a00304.html#gade108f16633cf95fa500b5b8c36c8b00">mediump_f32mat4x3</a>;</div>
<div class="line"><a name="l01308"></a><span class="lineno"> 1308</span>&#160;</div>
<div class="line"><a name="l01311"></a><span class="lineno"> 1311</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, mediump&gt; <a class="code" href="a00304.html#ga936e95b881ecd2d109459ca41913fa99">mediump_f32mat4x4</a>;</div>
<div class="line"><a name="l01312"></a><span class="lineno"> 1312</span>&#160;</div>
<div class="line"><a name="l01315"></a><span class="lineno"> 1315</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f32, mediump&gt; f32mat1;</span></div>
<div class="line"><a name="l01316"></a><span class="lineno"> 1316</span>&#160;</div>
<div class="line"><a name="l01319"></a><span class="lineno"> 1319</span>&#160;        <span class="keyword">typedef</span> mediump_f32mat2x2 <a class="code" href="a00304.html#gaf9020c6176a75bc84828ab01ea7dac25">mediump_f32mat2</a>;</div>
<div class="line"><a name="l01320"></a><span class="lineno"> 1320</span>&#160;</div>
<div class="line"><a name="l01323"></a><span class="lineno"> 1323</span>&#160;        <span class="keyword">typedef</span> mediump_f32mat3x3 <a class="code" href="a00304.html#gaaaf878d0d7bfc0aac054fe269a886ca8">mediump_f32mat3</a>;</div>
<div class="line"><a name="l01324"></a><span class="lineno"> 1324</span>&#160;</div>
<div class="line"><a name="l01327"></a><span class="lineno"> 1327</span>&#160;        <span class="keyword">typedef</span> mediump_f32mat4x4 <a class="code" href="a00304.html#ga4491baaebbc46a20f1cb5da985576bf4">mediump_f32mat4</a>;</div>
<div class="line"><a name="l01328"></a><span class="lineno"> 1328</span>&#160;</div>
<div class="line"><a name="l01329"></a><span class="lineno"> 1329</span>&#160;</div>
<div class="line"><a name="l01332"></a><span class="lineno"> 1332</span>&#160;        <span class="comment">//typedef f32 highp_f32mat1x1;</span></div>
<div class="line"><a name="l01333"></a><span class="lineno"> 1333</span>&#160;</div>
<div class="line"><a name="l01336"></a><span class="lineno"> 1336</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, highp&gt; <a class="code" href="a00304.html#gae5eb02d92b7d4605a4b7f37ae5cb2968">highp_f32mat2x2</a>;</div>
<div class="line"><a name="l01337"></a><span class="lineno"> 1337</span>&#160;</div>
<div class="line"><a name="l01340"></a><span class="lineno"> 1340</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f32, highp&gt; <a class="code" href="a00304.html#ga0aeb5cb001473b08c88175012708a379">highp_f32mat2x3</a>;</div>
<div class="line"><a name="l01341"></a><span class="lineno"> 1341</span>&#160;</div>
<div class="line"><a name="l01344"></a><span class="lineno"> 1344</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f32, highp&gt; <a class="code" href="a00304.html#ga88938ee1e7981fa3402e88da6ad74531">highp_f32mat2x4</a>;</div>
<div class="line"><a name="l01345"></a><span class="lineno"> 1345</span>&#160;</div>
<div class="line"><a name="l01348"></a><span class="lineno"> 1348</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f32, highp&gt; <a class="code" href="a00304.html#ga36537e701456f12c20e73f469cac4967">highp_f32mat3x2</a>;</div>
<div class="line"><a name="l01349"></a><span class="lineno"> 1349</span>&#160;</div>
<div class="line"><a name="l01352"></a><span class="lineno"> 1352</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, highp&gt; <a class="code" href="a00304.html#gaab691ae40c37976d268d8cac0096e0e1">highp_f32mat3x3</a>;</div>
<div class="line"><a name="l01353"></a><span class="lineno"> 1353</span>&#160;</div>
<div class="line"><a name="l01356"></a><span class="lineno"> 1356</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f32, highp&gt; <a class="code" href="a00304.html#gaa5086dbd6efb272d13fc88829330861d">highp_f32mat3x4</a>;</div>
<div class="line"><a name="l01357"></a><span class="lineno"> 1357</span>&#160;</div>
<div class="line"><a name="l01360"></a><span class="lineno"> 1360</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f32, highp&gt; <a class="code" href="a00304.html#ga602e119c6b246b4f6edcf66845f2aa0f">highp_f32mat4x2</a>;</div>
<div class="line"><a name="l01361"></a><span class="lineno"> 1361</span>&#160;</div>
<div class="line"><a name="l01364"></a><span class="lineno"> 1364</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f32, highp&gt; <a class="code" href="a00304.html#ga66bffdd8e5c0d3ef9958bbab9ca1ba59">highp_f32mat4x3</a>;</div>
<div class="line"><a name="l01365"></a><span class="lineno"> 1365</span>&#160;</div>
<div class="line"><a name="l01368"></a><span class="lineno"> 1368</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, highp&gt; <a class="code" href="a00304.html#gaf1b712b97b2322685fbbed28febe5f84">highp_f32mat4x4</a>;</div>
<div class="line"><a name="l01369"></a><span class="lineno"> 1369</span>&#160;</div>
<div class="line"><a name="l01372"></a><span class="lineno"> 1372</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f32, highp&gt; f32mat1;</span></div>
<div class="line"><a name="l01373"></a><span class="lineno"> 1373</span>&#160;</div>
<div class="line"><a name="l01376"></a><span class="lineno"> 1376</span>&#160;        <span class="keyword">typedef</span> highp_f32mat2x2 <a class="code" href="a00304.html#ga298f7d4d273678d0282812368da27fda">highp_f32mat2</a>;</div>
<div class="line"><a name="l01377"></a><span class="lineno"> 1377</span>&#160;</div>
<div class="line"><a name="l01380"></a><span class="lineno"> 1380</span>&#160;        <span class="keyword">typedef</span> highp_f32mat3x3 <a class="code" href="a00304.html#ga24f9ef3263b1638564713892cc37981f">highp_f32mat3</a>;</div>
<div class="line"><a name="l01381"></a><span class="lineno"> 1381</span>&#160;</div>
<div class="line"><a name="l01384"></a><span class="lineno"> 1384</span>&#160;        <span class="keyword">typedef</span> highp_f32mat4x4 <a class="code" href="a00304.html#ga14c90ca49885723f51d06e295587236f">highp_f32mat4</a>;</div>
<div class="line"><a name="l01385"></a><span class="lineno"> 1385</span>&#160;</div>
<div class="line"><a name="l01386"></a><span class="lineno"> 1386</span>&#160;</div>
<div class="line"><a name="l01389"></a><span class="lineno"> 1389</span>&#160;        <span class="comment">//typedef f64 lowp_f64mat1x1;</span></div>
<div class="line"><a name="l01390"></a><span class="lineno"> 1390</span>&#160;</div>
<div class="line"><a name="l01393"></a><span class="lineno"> 1393</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f64, lowp&gt; <a class="code" href="a00304.html#ga771fda9109933db34f808d92b9b84d7e">lowp_f64mat2x2</a>;</div>
<div class="line"><a name="l01394"></a><span class="lineno"> 1394</span>&#160;</div>
<div class="line"><a name="l01397"></a><span class="lineno"> 1397</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f64, lowp&gt; <a class="code" href="a00304.html#ga39e90adcffe33264bd608fa9c6bd184b">lowp_f64mat2x3</a>;</div>
<div class="line"><a name="l01398"></a><span class="lineno"> 1398</span>&#160;</div>
<div class="line"><a name="l01401"></a><span class="lineno"> 1401</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f64, lowp&gt; <a class="code" href="a00304.html#ga50265a202fbfe0a25fc70066c31d9336">lowp_f64mat2x4</a>;</div>
<div class="line"><a name="l01402"></a><span class="lineno"> 1402</span>&#160;</div>
<div class="line"><a name="l01405"></a><span class="lineno"> 1405</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f64, lowp&gt; <a class="code" href="a00304.html#gab0eb2d65514ee3e49905aa2caad8c0ad">lowp_f64mat3x2</a>;</div>
<div class="line"><a name="l01406"></a><span class="lineno"> 1406</span>&#160;</div>
<div class="line"><a name="l01409"></a><span class="lineno"> 1409</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f64, lowp&gt; <a class="code" href="a00304.html#gac8f8a12ee03105ef8861dc652434e3b7">lowp_f64mat3x3</a>;</div>
<div class="line"><a name="l01410"></a><span class="lineno"> 1410</span>&#160;</div>
<div class="line"><a name="l01413"></a><span class="lineno"> 1413</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f64, lowp&gt; <a class="code" href="a00304.html#gade8d1edfb23996ab6c622e65e3893271">lowp_f64mat3x4</a>;</div>
<div class="line"><a name="l01414"></a><span class="lineno"> 1414</span>&#160;</div>
<div class="line"><a name="l01417"></a><span class="lineno"> 1417</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f64, lowp&gt; <a class="code" href="a00304.html#gab0cecb80fd106bc369b9e46a165815ce">lowp_f64mat4x2</a>;</div>
<div class="line"><a name="l01418"></a><span class="lineno"> 1418</span>&#160;</div>
<div class="line"><a name="l01421"></a><span class="lineno"> 1421</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f64, lowp&gt; <a class="code" href="a00304.html#gae731613b25db3a5ef5a05d21e57a57d3">lowp_f64mat4x3</a>;</div>
<div class="line"><a name="l01422"></a><span class="lineno"> 1422</span>&#160;</div>
<div class="line"><a name="l01425"></a><span class="lineno"> 1425</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f64, lowp&gt; <a class="code" href="a00304.html#ga8c9cd734e03cd49674f3e287aa4a6f95">lowp_f64mat4x4</a>;</div>
<div class="line"><a name="l01426"></a><span class="lineno"> 1426</span>&#160;</div>
<div class="line"><a name="l01429"></a><span class="lineno"> 1429</span>&#160;        <span class="comment">//typedef lowp_f64mat1x1 lowp_f64mat1;</span></div>
<div class="line"><a name="l01430"></a><span class="lineno"> 1430</span>&#160;</div>
<div class="line"><a name="l01433"></a><span class="lineno"> 1433</span>&#160;        <span class="keyword">typedef</span> lowp_f64mat2x2 <a class="code" href="a00304.html#gafc730f6b4242763b0eda0ffa25150292">lowp_f64mat2</a>;</div>
<div class="line"><a name="l01434"></a><span class="lineno"> 1434</span>&#160;</div>
<div class="line"><a name="l01437"></a><span class="lineno"> 1437</span>&#160;        <span class="keyword">typedef</span> lowp_f64mat3x3 <a class="code" href="a00304.html#ga58119a41d143ebaea0df70fe882e8a40">lowp_f64mat3</a>;</div>
<div class="line"><a name="l01438"></a><span class="lineno"> 1438</span>&#160;</div>
<div class="line"><a name="l01441"></a><span class="lineno"> 1441</span>&#160;        <span class="keyword">typedef</span> lowp_f64mat4x4 <a class="code" href="a00304.html#ga7451266e67794bd1125163502bc4a570">lowp_f64mat4</a>;</div>
<div class="line"><a name="l01442"></a><span class="lineno"> 1442</span>&#160;</div>
<div class="line"><a name="l01443"></a><span class="lineno"> 1443</span>&#160;</div>
<div class="line"><a name="l01446"></a><span class="lineno"> 1446</span>&#160;        <span class="comment">//typedef f64 Highp_f64mat1x1;</span></div>
<div class="line"><a name="l01447"></a><span class="lineno"> 1447</span>&#160;</div>
<div class="line"><a name="l01450"></a><span class="lineno"> 1450</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f64, mediump&gt; <a class="code" href="a00304.html#ga4fd527644cccbca4cb205320eab026f3">mediump_f64mat2x2</a>;</div>
<div class="line"><a name="l01451"></a><span class="lineno"> 1451</span>&#160;</div>
<div class="line"><a name="l01454"></a><span class="lineno"> 1454</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f64, mediump&gt; <a class="code" href="a00304.html#gafd9a6ebc0c7b95f5c581d00d16a17c54">mediump_f64mat2x3</a>;</div>
<div class="line"><a name="l01455"></a><span class="lineno"> 1455</span>&#160;</div>
<div class="line"><a name="l01458"></a><span class="lineno"> 1458</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f64, mediump&gt; <a class="code" href="a00304.html#gaf306dd69e53633636aee38cea79d4cb7">mediump_f64mat2x4</a>;</div>
<div class="line"><a name="l01459"></a><span class="lineno"> 1459</span>&#160;</div>
<div class="line"><a name="l01462"></a><span class="lineno"> 1462</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f64, mediump&gt; <a class="code" href="a00304.html#gacd926d36a72433f6cac51dd60fa13107">mediump_f64mat3x2</a>;</div>
<div class="line"><a name="l01463"></a><span class="lineno"> 1463</span>&#160;</div>
<div class="line"><a name="l01466"></a><span class="lineno"> 1466</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f64, mediump&gt; <a class="code" href="a00304.html#ga84d88a6e3a54ccd2b67e195af4a4c23e">mediump_f64mat3x3</a>;</div>
<div class="line"><a name="l01467"></a><span class="lineno"> 1467</span>&#160;</div>
<div class="line"><a name="l01470"></a><span class="lineno"> 1470</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f64, mediump&gt; <a class="code" href="a00304.html#gad38c544d332b8c4bd0b70b1bd9feccc2">mediump_f64mat3x4</a>;</div>
<div class="line"><a name="l01471"></a><span class="lineno"> 1471</span>&#160;</div>
<div class="line"><a name="l01474"></a><span class="lineno"> 1474</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f64, mediump&gt; <a class="code" href="a00304.html#ga17d36f0ea22314117e1cec9594b33945">mediump_f64mat4x2</a>;</div>
<div class="line"><a name="l01475"></a><span class="lineno"> 1475</span>&#160;</div>
<div class="line"><a name="l01478"></a><span class="lineno"> 1478</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f64, mediump&gt; <a class="code" href="a00304.html#ga54697a78f9a4643af6a57fc2e626ec0d">mediump_f64mat4x3</a>;</div>
<div class="line"><a name="l01479"></a><span class="lineno"> 1479</span>&#160;</div>
<div class="line"><a name="l01482"></a><span class="lineno"> 1482</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f64, mediump&gt; <a class="code" href="a00304.html#ga66edb8de17b9235029472f043ae107e9">mediump_f64mat4x4</a>;</div>
<div class="line"><a name="l01483"></a><span class="lineno"> 1483</span>&#160;</div>
<div class="line"><a name="l01486"></a><span class="lineno"> 1486</span>&#160;        <span class="comment">//typedef mediump_f64mat1x1 mediump_f64mat1;</span></div>
<div class="line"><a name="l01487"></a><span class="lineno"> 1487</span>&#160;</div>
<div class="line"><a name="l01490"></a><span class="lineno"> 1490</span>&#160;        <span class="keyword">typedef</span> mediump_f64mat2x2 <a class="code" href="a00304.html#gac1281da5ded55047e8892b0e1f1ae965">mediump_f64mat2</a>;</div>
<div class="line"><a name="l01491"></a><span class="lineno"> 1491</span>&#160;</div>
<div class="line"><a name="l01494"></a><span class="lineno"> 1494</span>&#160;        <span class="keyword">typedef</span> mediump_f64mat3x3 <a class="code" href="a00304.html#gad35fb67eb1d03c5a514f0bd7aed1c776">mediump_f64mat3</a>;</div>
<div class="line"><a name="l01495"></a><span class="lineno"> 1495</span>&#160;</div>
<div class="line"><a name="l01498"></a><span class="lineno"> 1498</span>&#160;        <span class="keyword">typedef</span> mediump_f64mat4x4 <a class="code" href="a00304.html#gaa805ef691c711dc41e2776cfb67f5cf5">mediump_f64mat4</a>;</div>
<div class="line"><a name="l01499"></a><span class="lineno"> 1499</span>&#160;</div>
<div class="line"><a name="l01502"></a><span class="lineno"> 1502</span>&#160;        <span class="comment">//typedef f64 highp_f64mat1x1;</span></div>
<div class="line"><a name="l01503"></a><span class="lineno"> 1503</span>&#160;</div>
<div class="line"><a name="l01506"></a><span class="lineno"> 1506</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f64, highp&gt; <a class="code" href="a00304.html#ga773ea237a051827cfc20de960bc73ff0">highp_f64mat2x2</a>;</div>
<div class="line"><a name="l01507"></a><span class="lineno"> 1507</span>&#160;</div>
<div class="line"><a name="l01510"></a><span class="lineno"> 1510</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f64, highp&gt; <a class="code" href="a00304.html#ga8342c7469384c6d769cacc9e309278d9">highp_f64mat2x3</a>;</div>
<div class="line"><a name="l01511"></a><span class="lineno"> 1511</span>&#160;</div>
<div class="line"><a name="l01514"></a><span class="lineno"> 1514</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f64, highp&gt; <a class="code" href="a00304.html#ga5a67a7440b9c0d1538533540f99036a5">highp_f64mat2x4</a>;</div>
<div class="line"><a name="l01515"></a><span class="lineno"> 1515</span>&#160;</div>
<div class="line"><a name="l01518"></a><span class="lineno"> 1518</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f64, highp&gt; <a class="code" href="a00304.html#ga5bdbfb4ce7d05ce1e1b663f50be17e8a">highp_f64mat3x2</a>;</div>
<div class="line"><a name="l01519"></a><span class="lineno"> 1519</span>&#160;</div>
<div class="line"><a name="l01522"></a><span class="lineno"> 1522</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f64, highp&gt; <a class="code" href="a00304.html#ga7c2cadb9b85cc7e0d125db21ca19dea4">highp_f64mat3x3</a>;</div>
<div class="line"><a name="l01523"></a><span class="lineno"> 1523</span>&#160;</div>
<div class="line"><a name="l01526"></a><span class="lineno"> 1526</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f64, highp&gt; <a class="code" href="a00304.html#gad310b1dddeec9ec837a104e7db8de580">highp_f64mat3x4</a>;</div>
<div class="line"><a name="l01527"></a><span class="lineno"> 1527</span>&#160;</div>
<div class="line"><a name="l01530"></a><span class="lineno"> 1530</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f64, highp&gt; <a class="code" href="a00304.html#ga58c4631421e323e252fc716b6103e38c">highp_f64mat4x2</a>;</div>
<div class="line"><a name="l01531"></a><span class="lineno"> 1531</span>&#160;</div>
<div class="line"><a name="l01534"></a><span class="lineno"> 1534</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f64, highp&gt; <a class="code" href="a00304.html#gae94823d65648e44d972863c6caa13103">highp_f64mat4x3</a>;</div>
<div class="line"><a name="l01535"></a><span class="lineno"> 1535</span>&#160;</div>
<div class="line"><a name="l01538"></a><span class="lineno"> 1538</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f64, highp&gt; <a class="code" href="a00304.html#ga09a2374b725c4246d263ee36fb66434c">highp_f64mat4x4</a>;</div>
<div class="line"><a name="l01539"></a><span class="lineno"> 1539</span>&#160;</div>
<div class="line"><a name="l01542"></a><span class="lineno"> 1542</span>&#160;        <span class="comment">//typedef highp_f64mat1x1 highp_f64mat1;</span></div>
<div class="line"><a name="l01543"></a><span class="lineno"> 1543</span>&#160;</div>
<div class="line"><a name="l01546"></a><span class="lineno"> 1546</span>&#160;        <span class="keyword">typedef</span> highp_f64mat2x2 <a class="code" href="a00304.html#gaf7adb92ce8de0afaff01436b039fd924">highp_f64mat2</a>;</div>
<div class="line"><a name="l01547"></a><span class="lineno"> 1547</span>&#160;</div>
<div class="line"><a name="l01550"></a><span class="lineno"> 1550</span>&#160;        <span class="keyword">typedef</span> highp_f64mat3x3 <a class="code" href="a00304.html#ga609bf0ace941d6ab1bb2f9522a04e546">highp_f64mat3</a>;</div>
<div class="line"><a name="l01551"></a><span class="lineno"> 1551</span>&#160;</div>
<div class="line"><a name="l01554"></a><span class="lineno"> 1554</span>&#160;        <span class="keyword">typedef</span> highp_f64mat4x4 <a class="code" href="a00304.html#gad308e0ed27d64daa4213fb257fcbd5a5">highp_f64mat4</a>;</div>
<div class="line"><a name="l01555"></a><span class="lineno"> 1555</span>&#160;</div>
<div class="line"><a name="l01556"></a><span class="lineno"> 1556</span>&#160;</div>
<div class="line"><a name="l01557"></a><span class="lineno"> 1557</span>&#160;</div>
<div class="line"><a name="l01558"></a><span class="lineno"> 1558</span>&#160;</div>
<div class="line"><a name="l01561"></a><span class="lineno"> 1561</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u8, lowp&gt; <a class="code" href="a00304.html#ga4b2e0e10d8d154fec9cab50e216588ec">lowp_u8vec1</a>;</div>
<div class="line"><a name="l01562"></a><span class="lineno"> 1562</span>&#160;</div>
<div class="line"><a name="l01565"></a><span class="lineno"> 1565</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u8, lowp&gt; <a class="code" href="a00304.html#gae6f63fa38635431e51a8f2602f15c566">lowp_u8vec2</a>;</div>
<div class="line"><a name="l01566"></a><span class="lineno"> 1566</span>&#160;</div>
<div class="line"><a name="l01569"></a><span class="lineno"> 1569</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u8, lowp&gt; <a class="code" href="a00304.html#ga150dc47e31c6b8cf8461803c8d56f7bd">lowp_u8vec3</a>;</div>
<div class="line"><a name="l01570"></a><span class="lineno"> 1570</span>&#160;</div>
<div class="line"><a name="l01573"></a><span class="lineno"> 1573</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u8, lowp&gt; <a class="code" href="a00304.html#ga9910927f3a4d1addb3da6a82542a8287">lowp_u8vec4</a>;</div>
<div class="line"><a name="l01574"></a><span class="lineno"> 1574</span>&#160;</div>
<div class="line"><a name="l01575"></a><span class="lineno"> 1575</span>&#160;</div>
<div class="line"><a name="l01578"></a><span class="lineno"> 1578</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u8, mediump&gt; <a class="code" href="a00304.html#ga4a43050843b141bdc7e85437faef6f55">mediump_u8vec1</a>;</div>
<div class="line"><a name="l01579"></a><span class="lineno"> 1579</span>&#160;</div>
<div class="line"><a name="l01582"></a><span class="lineno"> 1582</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u8, mediump&gt; <a class="code" href="a00304.html#ga907f85d4a0eac3d8aaf571e5c2647194">mediump_u8vec2</a>;</div>
<div class="line"><a name="l01583"></a><span class="lineno"> 1583</span>&#160;</div>
<div class="line"><a name="l01586"></a><span class="lineno"> 1586</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u8, mediump&gt; <a class="code" href="a00304.html#gaddc6f7748b699254942c5216b68f8f7f">mediump_u8vec3</a>;</div>
<div class="line"><a name="l01587"></a><span class="lineno"> 1587</span>&#160;</div>
<div class="line"><a name="l01590"></a><span class="lineno"> 1590</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u8, mediump&gt; <a class="code" href="a00304.html#gaaf4ee3b76d43d98da02ec399b99bda4b">mediump_u8vec4</a>;</div>
<div class="line"><a name="l01591"></a><span class="lineno"> 1591</span>&#160;</div>
<div class="line"><a name="l01592"></a><span class="lineno"> 1592</span>&#160;</div>
<div class="line"><a name="l01595"></a><span class="lineno"> 1595</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u8, highp&gt; <a class="code" href="a00304.html#ga8408cb76b6550ff01fa0a3024e7b68d2">highp_u8vec1</a>;</div>
<div class="line"><a name="l01596"></a><span class="lineno"> 1596</span>&#160;</div>
<div class="line"><a name="l01599"></a><span class="lineno"> 1599</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u8, highp&gt; <a class="code" href="a00304.html#ga27585b7c3ab300059f11fcba465f6fd2">highp_u8vec2</a>;</div>
<div class="line"><a name="l01600"></a><span class="lineno"> 1600</span>&#160;</div>
<div class="line"><a name="l01603"></a><span class="lineno"> 1603</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u8, highp&gt; <a class="code" href="a00304.html#ga45721c13b956eb691cbd6c6c1429167a">highp_u8vec3</a>;</div>
<div class="line"><a name="l01604"></a><span class="lineno"> 1604</span>&#160;</div>
<div class="line"><a name="l01607"></a><span class="lineno"> 1607</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u8, highp&gt; <a class="code" href="a00304.html#gae0b75ad0fed8c00ddc0b5ce335d31060">highp_u8vec4</a>;</div>
<div class="line"><a name="l01608"></a><span class="lineno"> 1608</span>&#160;</div>
<div class="line"><a name="l01609"></a><span class="lineno"> 1609</span>&#160;</div>
<div class="line"><a name="l01610"></a><span class="lineno"> 1610</span>&#160;</div>
<div class="line"><a name="l01613"></a><span class="lineno"> 1613</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u8, defaultp&gt; <a class="code" href="a00304.html#ga29b349e037f0b24320b4548a143daee2">u8vec1</a>;</div>
<div class="line"><a name="l01614"></a><span class="lineno"> 1614</span>&#160;</div>
<div class="line"><a name="l01617"></a><span class="lineno"> 1617</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u8, defaultp&gt; <a class="code" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a>;</div>
<div class="line"><a name="l01618"></a><span class="lineno"> 1618</span>&#160;</div>
<div class="line"><a name="l01621"></a><span class="lineno"> 1621</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u8, defaultp&gt; <a class="code" href="a00304.html#ga7c5706f6bbe5282e5598acf7e7b377e2">u8vec3</a>;</div>
<div class="line"><a name="l01622"></a><span class="lineno"> 1622</span>&#160;</div>
<div class="line"><a name="l01625"></a><span class="lineno"> 1625</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u8, defaultp&gt; <a class="code" href="a00304.html#ga20779a61de2fd526a17f12fe53ec46b1">u8vec4</a>;</div>
<div class="line"><a name="l01626"></a><span class="lineno"> 1626</span>&#160;</div>
<div class="line"><a name="l01627"></a><span class="lineno"> 1627</span>&#160;</div>
<div class="line"><a name="l01628"></a><span class="lineno"> 1628</span>&#160;</div>
<div class="line"><a name="l01629"></a><span class="lineno"> 1629</span>&#160;</div>
<div class="line"><a name="l01632"></a><span class="lineno"> 1632</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u16, lowp&gt;               <a class="code" href="a00304.html#gaa6aab4ee7189b86716f5d7015d43021d">lowp_u16vec1</a>;</div>
<div class="line"><a name="l01633"></a><span class="lineno"> 1633</span>&#160;</div>
<div class="line"><a name="l01636"></a><span class="lineno"> 1636</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u16, lowp&gt;               <a class="code" href="a00304.html#ga2a7d997da9ac29cb931e35bd399f58df">lowp_u16vec2</a>;</div>
<div class="line"><a name="l01637"></a><span class="lineno"> 1637</span>&#160;</div>
<div class="line"><a name="l01640"></a><span class="lineno"> 1640</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u16, lowp&gt;               <a class="code" href="a00304.html#gac0253db6c3d3bae1f591676307a9dd8c">lowp_u16vec3</a>;</div>
<div class="line"><a name="l01641"></a><span class="lineno"> 1641</span>&#160;</div>
<div class="line"><a name="l01644"></a><span class="lineno"> 1644</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u16, lowp&gt;               <a class="code" href="a00304.html#gaa7f00459b9a2e5b2757e70afc0c189e1">lowp_u16vec4</a>;</div>
<div class="line"><a name="l01645"></a><span class="lineno"> 1645</span>&#160;</div>
<div class="line"><a name="l01646"></a><span class="lineno"> 1646</span>&#160;</div>
<div class="line"><a name="l01649"></a><span class="lineno"> 1649</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u16, mediump&gt;            <a class="code" href="a00304.html#ga400ce8cc566de093a9b28e59e220d6e4">mediump_u16vec1</a>;</div>
<div class="line"><a name="l01650"></a><span class="lineno"> 1650</span>&#160;</div>
<div class="line"><a name="l01653"></a><span class="lineno"> 1653</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u16, mediump&gt;            <a class="code" href="a00304.html#ga429c201b3e92c90b4ef4356f2be52ee1">mediump_u16vec2</a>;</div>
<div class="line"><a name="l01654"></a><span class="lineno"> 1654</span>&#160;</div>
<div class="line"><a name="l01657"></a><span class="lineno"> 1657</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u16, mediump&gt;            <a class="code" href="a00304.html#gac9ba20234b0c3751d45ce575fc71e551">mediump_u16vec3</a>;</div>
<div class="line"><a name="l01658"></a><span class="lineno"> 1658</span>&#160;</div>
<div class="line"><a name="l01661"></a><span class="lineno"> 1661</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u16, mediump&gt;            <a class="code" href="a00304.html#ga5793393686ce5bd2d5968ff9144762b8">mediump_u16vec4</a>;</div>
<div class="line"><a name="l01662"></a><span class="lineno"> 1662</span>&#160;</div>
<div class="line"><a name="l01663"></a><span class="lineno"> 1663</span>&#160;</div>
<div class="line"><a name="l01666"></a><span class="lineno"> 1666</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u16, highp&gt;              <a class="code" href="a00304.html#gad064202b4cf9a2972475c03de657cb39">highp_u16vec1</a>;</div>
<div class="line"><a name="l01667"></a><span class="lineno"> 1667</span>&#160;</div>
<div class="line"><a name="l01670"></a><span class="lineno"> 1670</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u16, highp&gt;              <a class="code" href="a00304.html#ga791b15ceb3f1e09d1a0ec6f3057ca159">highp_u16vec2</a>;</div>
<div class="line"><a name="l01671"></a><span class="lineno"> 1671</span>&#160;</div>
<div class="line"><a name="l01674"></a><span class="lineno"> 1674</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u16, highp&gt;              <a class="code" href="a00304.html#gacfd806749008f0ade6ac4bb9dd91082f">highp_u16vec3</a>;</div>
<div class="line"><a name="l01675"></a><span class="lineno"> 1675</span>&#160;</div>
<div class="line"><a name="l01678"></a><span class="lineno"> 1678</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u16, highp&gt;              <a class="code" href="a00304.html#ga8a85a3d54a8a9e14fe7a1f96196c4f61">highp_u16vec4</a>;</div>
<div class="line"><a name="l01679"></a><span class="lineno"> 1679</span>&#160;</div>
<div class="line"><a name="l01680"></a><span class="lineno"> 1680</span>&#160;</div>
<div class="line"><a name="l01681"></a><span class="lineno"> 1681</span>&#160;</div>
<div class="line"><a name="l01682"></a><span class="lineno"> 1682</span>&#160;</div>
<div class="line"><a name="l01685"></a><span class="lineno"> 1685</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u16, defaultp&gt; <a class="code" href="a00304.html#ga08c05ba8ffb19f5d14ab584e1e9e9ee5">u16vec1</a>;</div>
<div class="line"><a name="l01686"></a><span class="lineno"> 1686</span>&#160;</div>
<div class="line"><a name="l01689"></a><span class="lineno"> 1689</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u16, defaultp&gt; <a class="code" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a>;</div>
<div class="line"><a name="l01690"></a><span class="lineno"> 1690</span>&#160;</div>
<div class="line"><a name="l01693"></a><span class="lineno"> 1693</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u16, defaultp&gt; <a class="code" href="a00304.html#ga1c522ca821c27b862fe51cf4024b064b">u16vec3</a>;</div>
<div class="line"><a name="l01694"></a><span class="lineno"> 1694</span>&#160;</div>
<div class="line"><a name="l01697"></a><span class="lineno"> 1697</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u16, defaultp&gt; <a class="code" href="a00304.html#ga529496d75775fb656a07993ea9af2450">u16vec4</a>;</div>
<div class="line"><a name="l01698"></a><span class="lineno"> 1698</span>&#160;</div>
<div class="line"><a name="l01699"></a><span class="lineno"> 1699</span>&#160;</div>
<div class="line"><a name="l01700"></a><span class="lineno"> 1700</span>&#160;</div>
<div class="line"><a name="l01703"></a><span class="lineno"> 1703</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u32, lowp&gt;               <a class="code" href="a00304.html#gabed3be8dfdc4a0df4bf3271dbd7344c4">lowp_u32vec1</a>;</div>
<div class="line"><a name="l01704"></a><span class="lineno"> 1704</span>&#160;</div>
<div class="line"><a name="l01707"></a><span class="lineno"> 1707</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u32, lowp&gt;               <a class="code" href="a00304.html#gaf7e286e81347011e257ee779524e73b9">lowp_u32vec2</a>;</div>
<div class="line"><a name="l01708"></a><span class="lineno"> 1708</span>&#160;</div>
<div class="line"><a name="l01711"></a><span class="lineno"> 1711</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u32, lowp&gt;               <a class="code" href="a00304.html#gad3ad390560a671b1f676fbf03cd3aa15">lowp_u32vec3</a>;</div>
<div class="line"><a name="l01712"></a><span class="lineno"> 1712</span>&#160;</div>
<div class="line"><a name="l01715"></a><span class="lineno"> 1715</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u32, lowp&gt;               <a class="code" href="a00304.html#ga4502885718742aa238c36a312c3f3f20">lowp_u32vec4</a>;</div>
<div class="line"><a name="l01716"></a><span class="lineno"> 1716</span>&#160;</div>
<div class="line"><a name="l01717"></a><span class="lineno"> 1717</span>&#160;</div>
<div class="line"><a name="l01720"></a><span class="lineno"> 1720</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u32, mediump&gt;            <a class="code" href="a00304.html#ga8a11ccd2e38f674bbf3c2d1afc232aee">mediump_u32vec1</a>;</div>
<div class="line"><a name="l01721"></a><span class="lineno"> 1721</span>&#160;</div>
<div class="line"><a name="l01724"></a><span class="lineno"> 1724</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u32, mediump&gt;            <a class="code" href="a00304.html#ga94f74851fce338549c705b5f0d601c4f">mediump_u32vec2</a>;</div>
<div class="line"><a name="l01725"></a><span class="lineno"> 1725</span>&#160;</div>
<div class="line"><a name="l01728"></a><span class="lineno"> 1728</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u32, mediump&gt;            <a class="code" href="a00304.html#ga012c24c8fc69707b90260474c70275a2">mediump_u32vec3</a>;</div>
<div class="line"><a name="l01729"></a><span class="lineno"> 1729</span>&#160;</div>
<div class="line"><a name="l01732"></a><span class="lineno"> 1732</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u32, mediump&gt;            <a class="code" href="a00304.html#ga5d43ee8b5dbaa06c327b03b83682598a">mediump_u32vec4</a>;</div>
<div class="line"><a name="l01733"></a><span class="lineno"> 1733</span>&#160;</div>
<div class="line"><a name="l01734"></a><span class="lineno"> 1734</span>&#160;</div>
<div class="line"><a name="l01737"></a><span class="lineno"> 1737</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u32, highp&gt;              <a class="code" href="a00304.html#ga0e35a565b9036bfc3989f5e23a0792e3">highp_u32vec1</a>;</div>
<div class="line"><a name="l01738"></a><span class="lineno"> 1738</span>&#160;</div>
<div class="line"><a name="l01741"></a><span class="lineno"> 1741</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u32, highp&gt;              <a class="code" href="a00304.html#ga2f256334f83fba4c2d219e414b51df6c">highp_u32vec2</a>;</div>
<div class="line"><a name="l01742"></a><span class="lineno"> 1742</span>&#160;</div>
<div class="line"><a name="l01745"></a><span class="lineno"> 1745</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u32, highp&gt;              <a class="code" href="a00304.html#gaf14d7a50502464e7cbfa074f24684cb1">highp_u32vec3</a>;</div>
<div class="line"><a name="l01746"></a><span class="lineno"> 1746</span>&#160;</div>
<div class="line"><a name="l01749"></a><span class="lineno"> 1749</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u32, highp&gt;              <a class="code" href="a00304.html#ga22166f0da65038b447f3c5e534fff1c2">highp_u32vec4</a>;</div>
<div class="line"><a name="l01750"></a><span class="lineno"> 1750</span>&#160;</div>
<div class="line"><a name="l01751"></a><span class="lineno"> 1751</span>&#160;</div>
<div class="line"><a name="l01752"></a><span class="lineno"> 1752</span>&#160;</div>
<div class="line"><a name="l01755"></a><span class="lineno"> 1755</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u32, defaultp&gt; <a class="code" href="a00304.html#gae627372cfd5f20dd87db490387b71195">u32vec1</a>;</div>
<div class="line"><a name="l01756"></a><span class="lineno"> 1756</span>&#160;</div>
<div class="line"><a name="l01759"></a><span class="lineno"> 1759</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u32, defaultp&gt; <a class="code" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a>;</div>
<div class="line"><a name="l01760"></a><span class="lineno"> 1760</span>&#160;</div>
<div class="line"><a name="l01763"></a><span class="lineno"> 1763</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u32, defaultp&gt; <a class="code" href="a00304.html#gae267358ff2a41d156d97f5762630235a">u32vec3</a>;</div>
<div class="line"><a name="l01764"></a><span class="lineno"> 1764</span>&#160;</div>
<div class="line"><a name="l01767"></a><span class="lineno"> 1767</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u32, defaultp&gt; <a class="code" href="a00304.html#ga31cef34e4cd04840c54741ff2f7005f0">u32vec4</a>;</div>
<div class="line"><a name="l01768"></a><span class="lineno"> 1768</span>&#160;</div>
<div class="line"><a name="l01769"></a><span class="lineno"> 1769</span>&#160;</div>
<div class="line"><a name="l01770"></a><span class="lineno"> 1770</span>&#160;</div>
<div class="line"><a name="l01771"></a><span class="lineno"> 1771</span>&#160;</div>
<div class="line"><a name="l01774"></a><span class="lineno"> 1774</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u64, lowp&gt;               <a class="code" href="a00304.html#ga859be7b9d3a3765c1cafc14dbcf249a6">lowp_u64vec1</a>;</div>
<div class="line"><a name="l01775"></a><span class="lineno"> 1775</span>&#160;</div>
<div class="line"><a name="l01778"></a><span class="lineno"> 1778</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u64, lowp&gt;               <a class="code" href="a00304.html#ga581485db4ba6ddb501505ee711fd8e42">lowp_u64vec2</a>;</div>
<div class="line"><a name="l01779"></a><span class="lineno"> 1779</span>&#160;</div>
<div class="line"><a name="l01782"></a><span class="lineno"> 1782</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u64, lowp&gt;               <a class="code" href="a00304.html#gaa4a8682bec7ec8af666ef87fae38d5d1">lowp_u64vec3</a>;</div>
<div class="line"><a name="l01783"></a><span class="lineno"> 1783</span>&#160;</div>
<div class="line"><a name="l01786"></a><span class="lineno"> 1786</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u64, lowp&gt;               <a class="code" href="a00304.html#ga6fccc89c34045c86339f6fa781ce96de">lowp_u64vec4</a>;</div>
<div class="line"><a name="l01787"></a><span class="lineno"> 1787</span>&#160;</div>
<div class="line"><a name="l01788"></a><span class="lineno"> 1788</span>&#160;</div>
<div class="line"><a name="l01791"></a><span class="lineno"> 1791</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u64, mediump&gt;            <a class="code" href="a00304.html#ga659f372ccb8307d5db5beca942cde5e8">mediump_u64vec1</a>;</div>
<div class="line"><a name="l01792"></a><span class="lineno"> 1792</span>&#160;</div>
<div class="line"><a name="l01795"></a><span class="lineno"> 1795</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u64, mediump&gt;            <a class="code" href="a00304.html#ga73a08ef5a74798f3a1a99250b5f86a7d">mediump_u64vec2</a>;</div>
<div class="line"><a name="l01796"></a><span class="lineno"> 1796</span>&#160;</div>
<div class="line"><a name="l01799"></a><span class="lineno"> 1799</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u64, mediump&gt;            <a class="code" href="a00304.html#ga1900c6ab74acd392809425953359ef52">mediump_u64vec3</a>;</div>
<div class="line"><a name="l01800"></a><span class="lineno"> 1800</span>&#160;</div>
<div class="line"><a name="l01803"></a><span class="lineno"> 1803</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u64, mediump&gt;            <a class="code" href="a00304.html#gaec7ee455cb379ec2993e81482123e1cc">mediump_u64vec4</a>;</div>
<div class="line"><a name="l01804"></a><span class="lineno"> 1804</span>&#160;</div>
<div class="line"><a name="l01805"></a><span class="lineno"> 1805</span>&#160;</div>
<div class="line"><a name="l01808"></a><span class="lineno"> 1808</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u64, highp&gt;              <a class="code" href="a00304.html#gae4fe774744852c4d7d069be2e05257ab">highp_u64vec1</a>;</div>
<div class="line"><a name="l01809"></a><span class="lineno"> 1809</span>&#160;</div>
<div class="line"><a name="l01812"></a><span class="lineno"> 1812</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u64, highp&gt;              <a class="code" href="a00304.html#ga78f77b8b2d17b431ac5a68c0b5d7050d">highp_u64vec2</a>;</div>
<div class="line"><a name="l01813"></a><span class="lineno"> 1813</span>&#160;</div>
<div class="line"><a name="l01816"></a><span class="lineno"> 1816</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u64, highp&gt;              <a class="code" href="a00304.html#ga41bdabea6e589029659331ba47eb78c1">highp_u64vec3</a>;</div>
<div class="line"><a name="l01817"></a><span class="lineno"> 1817</span>&#160;</div>
<div class="line"><a name="l01820"></a><span class="lineno"> 1820</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u64, highp&gt;              <a class="code" href="a00304.html#ga4f15b41aa24b11cc42ad5798c04a2325">highp_u64vec4</a>;</div>
<div class="line"><a name="l01821"></a><span class="lineno"> 1821</span>&#160;</div>
<div class="line"><a name="l01822"></a><span class="lineno"> 1822</span>&#160;</div>
<div class="line"><a name="l01823"></a><span class="lineno"> 1823</span>&#160;</div>
<div class="line"><a name="l01824"></a><span class="lineno"> 1824</span>&#160;</div>
<div class="line"><a name="l01827"></a><span class="lineno"> 1827</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, u64, defaultp&gt; <a class="code" href="a00304.html#gaf09f3ca4b671a4a4f84505eb4cc865fd">u64vec1</a>;</div>
<div class="line"><a name="l01828"></a><span class="lineno"> 1828</span>&#160;</div>
<div class="line"><a name="l01831"></a><span class="lineno"> 1831</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, u64, defaultp&gt; <a class="code" href="a00304.html#gaef3824ed4fe435a019c5b9dddf53fec5">u64vec2</a>;</div>
<div class="line"><a name="l01832"></a><span class="lineno"> 1832</span>&#160;</div>
<div class="line"><a name="l01835"></a><span class="lineno"> 1835</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, u64, defaultp&gt; <a class="code" href="a00304.html#ga489b89ba93d4f7b3934df78debc52276">u64vec3</a>;</div>
<div class="line"><a name="l01836"></a><span class="lineno"> 1836</span>&#160;</div>
<div class="line"><a name="l01839"></a><span class="lineno"> 1839</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, u64, defaultp&gt; <a class="code" href="a00304.html#ga3945dd6515d4498cb603e65ff867ab03">u64vec4</a>;</div>
<div class="line"><a name="l01840"></a><span class="lineno"> 1840</span>&#160;</div>
<div class="line"><a name="l01841"></a><span class="lineno"> 1841</span>&#160;</div>
<div class="line"><a name="l01843"></a><span class="lineno"> 1843</span>&#160;        <span class="comment">// Float vector types</span></div>
<div class="line"><a name="l01844"></a><span class="lineno"> 1844</span>&#160;</div>
<div class="line"><a name="l01847"></a><span class="lineno"> 1847</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gaa4947bc8b47c72fceea9bda730ecf603">float32_t</a>;</div>
<div class="line"><a name="l01848"></a><span class="lineno"> 1848</span>&#160;</div>
<div class="line"><a name="l01851"></a><span class="lineno"> 1851</span>&#160;        <span class="keyword">typedef</span> float32 <a class="code" href="a00304.html#gabe6a542dd6c1d5ffd847f1b9b4c9c9b7">f32</a>;</div>
<div class="line"><a name="l01852"></a><span class="lineno"> 1852</span>&#160;</div>
<div class="line"><a name="l01853"></a><span class="lineno"> 1853</span>&#160;<span class="preprocessor">#       ifndef GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l01854"></a><span class="lineno"> 1854</span>&#160;</div>
<div class="line"><a name="l01857"></a><span class="lineno"> 1857</span>&#160;                <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga728366fef72cd96f0a5fa6429f05469e">float64_t</a>;</div>
<div class="line"><a name="l01858"></a><span class="lineno"> 1858</span>&#160;</div>
<div class="line"><a name="l01861"></a><span class="lineno"> 1861</span>&#160;                <span class="keyword">typedef</span> float64 <a class="code" href="a00304.html#ga1d794d240091678f602e8de225b8d8c9">f64</a>;</div>
<div class="line"><a name="l01862"></a><span class="lineno"> 1862</span>&#160;<span class="preprocessor">#       endif//GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l01863"></a><span class="lineno"> 1863</span>&#160;</div>
<div class="line"><a name="l01866"></a><span class="lineno"> 1866</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, float, defaultp&gt; <a class="code" href="a00304.html#ga98b9ed43cf8c5cf1d354b23c7df9119f">fvec1</a>;</div>
<div class="line"><a name="l01867"></a><span class="lineno"> 1867</span>&#160;</div>
<div class="line"><a name="l01870"></a><span class="lineno"> 1870</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, float, defaultp&gt; <a class="code" href="a00304.html#ga24273aa02abaecaab7f160bac437a339">fvec2</a>;</div>
<div class="line"><a name="l01871"></a><span class="lineno"> 1871</span>&#160;</div>
<div class="line"><a name="l01874"></a><span class="lineno"> 1874</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, float, defaultp&gt; <a class="code" href="a00304.html#ga89930533646b30d021759298aa6bf04a">fvec3</a>;</div>
<div class="line"><a name="l01875"></a><span class="lineno"> 1875</span>&#160;</div>
<div class="line"><a name="l01878"></a><span class="lineno"> 1878</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, float, defaultp&gt; <a class="code" href="a00304.html#ga713c796c54875cf4092d42ff9d9096b0">fvec4</a>;</div>
<div class="line"><a name="l01879"></a><span class="lineno"> 1879</span>&#160;</div>
<div class="line"><a name="l01880"></a><span class="lineno"> 1880</span>&#160;</div>
<div class="line"><a name="l01883"></a><span class="lineno"> 1883</span>&#160;        <span class="keyword">typedef</span> vec&lt;1, f32, defaultp&gt; <a class="code" href="a00304.html#ga701f32ab5b3fb06996b41f5c0d643805">f32vec1</a>;</div>
<div class="line"><a name="l01884"></a><span class="lineno"> 1884</span>&#160;</div>
<div class="line"><a name="l01887"></a><span class="lineno"> 1887</span>&#160;        <span class="keyword">typedef</span> vec&lt;2, f32, defaultp&gt; <a class="code" href="a00304.html#ga5d6c70e080409a76a257dc55bd8ea2c8">f32vec2</a>;</div>
<div class="line"><a name="l01888"></a><span class="lineno"> 1888</span>&#160;</div>
<div class="line"><a name="l01891"></a><span class="lineno"> 1891</span>&#160;        <span class="keyword">typedef</span> vec&lt;3, f32, defaultp&gt; <a class="code" href="a00304.html#gaea5c4518e175162e306d2c2b5ef5ac79">f32vec3</a>;</div>
<div class="line"><a name="l01892"></a><span class="lineno"> 1892</span>&#160;</div>
<div class="line"><a name="l01895"></a><span class="lineno"> 1895</span>&#160;        <span class="keyword">typedef</span> vec&lt;4, f32, defaultp&gt; <a class="code" href="a00304.html#ga31c6ca0e074a44007f49a9a3720b18c8">f32vec4</a>;</div>
<div class="line"><a name="l01896"></a><span class="lineno"> 1896</span>&#160;</div>
<div class="line"><a name="l01897"></a><span class="lineno"> 1897</span>&#160;<span class="preprocessor">#       ifndef GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l01898"></a><span class="lineno"> 1898</span>&#160;                <span class="keyword">typedef</span> vec&lt;1, f64, defaultp&gt; <a class="code" href="a00304.html#gade502df1ce14f837fae7f60a03ddb9b0">f64vec1</a>;</div>
<div class="line"><a name="l01901"></a><span class="lineno"> 1901</span>&#160;</div>
<div class="line"><a name="l01904"></a><span class="lineno"> 1904</span>&#160;                <span class="keyword">typedef</span> vec&lt;2, f64, defaultp&gt; <a class="code" href="a00304.html#gadc4e1594f9555d919131ee02b17822a2">f64vec2</a>;</div>
<div class="line"><a name="l01905"></a><span class="lineno"> 1905</span>&#160;</div>
<div class="line"><a name="l01908"></a><span class="lineno"> 1908</span>&#160;                <span class="keyword">typedef</span> vec&lt;3, f64, defaultp&gt; <a class="code" href="a00304.html#gaa7a1ddca75c5f629173bf4772db7a635">f64vec3</a>;</div>
<div class="line"><a name="l01909"></a><span class="lineno"> 1909</span>&#160;</div>
<div class="line"><a name="l01912"></a><span class="lineno"> 1912</span>&#160;                <span class="keyword">typedef</span> vec&lt;4, f64, defaultp&gt; <a class="code" href="a00304.html#ga66e92e57260bdb910609b9a56bf83e97">f64vec4</a>;</div>
<div class="line"><a name="l01913"></a><span class="lineno"> 1913</span>&#160;<span class="preprocessor">#       endif//GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l01914"></a><span class="lineno"> 1914</span>&#160;</div>
<div class="line"><a name="l01915"></a><span class="lineno"> 1915</span>&#160;</div>
<div class="line"><a name="l01917"></a><span class="lineno"> 1917</span>&#160;        <span class="comment">// Float matrix types</span></div>
<div class="line"><a name="l01918"></a><span class="lineno"> 1918</span>&#160;</div>
<div class="line"><a name="l01921"></a><span class="lineno"> 1921</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f32&gt; fmat1;</span></div>
<div class="line"><a name="l01922"></a><span class="lineno"> 1922</span>&#160;</div>
<div class="line"><a name="l01925"></a><span class="lineno"> 1925</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, defaultp&gt; <a class="code" href="a00304.html#ga4541dc2feb2a31d6ecb5a303f3dd3280">fmat2</a>;</div>
<div class="line"><a name="l01926"></a><span class="lineno"> 1926</span>&#160;</div>
<div class="line"><a name="l01929"></a><span class="lineno"> 1929</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, defaultp&gt; <a class="code" href="a00304.html#ga253d453c20e037730023fea0215cb6f6">fmat3</a>;</div>
<div class="line"><a name="l01930"></a><span class="lineno"> 1930</span>&#160;</div>
<div class="line"><a name="l01933"></a><span class="lineno"> 1933</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, defaultp&gt; <a class="code" href="a00304.html#ga8cb400c0f4438f2640035d7b9824a0ca">fmat4</a>;</div>
<div class="line"><a name="l01934"></a><span class="lineno"> 1934</span>&#160;</div>
<div class="line"><a name="l01935"></a><span class="lineno"> 1935</span>&#160;</div>
<div class="line"><a name="l01938"></a><span class="lineno"> 1938</span>&#160;        <span class="comment">//typedef f32 fmat1x1;</span></div>
<div class="line"><a name="l01939"></a><span class="lineno"> 1939</span>&#160;</div>
<div class="line"><a name="l01942"></a><span class="lineno"> 1942</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, defaultp&gt; <a class="code" href="a00304.html#ga3350c93c3275298f940a42875388e4b4">fmat2x2</a>;</div>
<div class="line"><a name="l01943"></a><span class="lineno"> 1943</span>&#160;</div>
<div class="line"><a name="l01946"></a><span class="lineno"> 1946</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f32, defaultp&gt; <a class="code" href="a00304.html#ga55a2d2a8eb09b5633668257eb3cad453">fmat2x3</a>;</div>
<div class="line"><a name="l01947"></a><span class="lineno"> 1947</span>&#160;</div>
<div class="line"><a name="l01950"></a><span class="lineno"> 1950</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f32, defaultp&gt; <a class="code" href="a00304.html#ga681381f19f11c9e5ee45cda2c56937ff">fmat2x4</a>;</div>
<div class="line"><a name="l01951"></a><span class="lineno"> 1951</span>&#160;</div>
<div class="line"><a name="l01954"></a><span class="lineno"> 1954</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f32, defaultp&gt; <a class="code" href="a00304.html#ga6af54d70d9beb0a7ef992a879e86b04f">fmat3x2</a>;</div>
<div class="line"><a name="l01955"></a><span class="lineno"> 1955</span>&#160;</div>
<div class="line"><a name="l01958"></a><span class="lineno"> 1958</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, defaultp&gt; <a class="code" href="a00304.html#gaa07c86650253672a19dbfb898f3265b8">fmat3x3</a>;</div>
<div class="line"><a name="l01959"></a><span class="lineno"> 1959</span>&#160;</div>
<div class="line"><a name="l01962"></a><span class="lineno"> 1962</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f32, defaultp&gt; <a class="code" href="a00304.html#ga44e158af77a670ee1b58c03cda9e1619">fmat3x4</a>;</div>
<div class="line"><a name="l01963"></a><span class="lineno"> 1963</span>&#160;</div>
<div class="line"><a name="l01966"></a><span class="lineno"> 1966</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f32, defaultp&gt; <a class="code" href="a00304.html#ga8c8aa45aafcc23238edb1d5aeb801774">fmat4x2</a>;</div>
<div class="line"><a name="l01967"></a><span class="lineno"> 1967</span>&#160;</div>
<div class="line"><a name="l01970"></a><span class="lineno"> 1970</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f32, defaultp&gt; <a class="code" href="a00304.html#ga4295048a78bdf46b8a7de77ec665b497">fmat4x3</a>;</div>
<div class="line"><a name="l01971"></a><span class="lineno"> 1971</span>&#160;</div>
<div class="line"><a name="l01974"></a><span class="lineno"> 1974</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, defaultp&gt; <a class="code" href="a00304.html#gad01cc6479bde1fd1870f13d3ed9530b3">fmat4x4</a>;</div>
<div class="line"><a name="l01975"></a><span class="lineno"> 1975</span>&#160;</div>
<div class="line"><a name="l01976"></a><span class="lineno"> 1976</span>&#160;</div>
<div class="line"><a name="l01979"></a><span class="lineno"> 1979</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f32, defaultp&gt; f32mat1;</span></div>
<div class="line"><a name="l01980"></a><span class="lineno"> 1980</span>&#160;</div>
<div class="line"><a name="l01983"></a><span class="lineno"> 1983</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, defaultp&gt; <a class="code" href="a00304.html#gab12383ed6ac7595ed6fde4d266c58425">f32mat2</a>;</div>
<div class="line"><a name="l01984"></a><span class="lineno"> 1984</span>&#160;</div>
<div class="line"><a name="l01987"></a><span class="lineno"> 1987</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, defaultp&gt; <a class="code" href="a00304.html#ga856f3905ee7cc2e4890a8a1d56c150be">f32mat3</a>;</div>
<div class="line"><a name="l01988"></a><span class="lineno"> 1988</span>&#160;</div>
<div class="line"><a name="l01991"></a><span class="lineno"> 1991</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, defaultp&gt; <a class="code" href="a00304.html#ga99d1b85ff99956b33da7e9992aad129a">f32mat4</a>;</div>
<div class="line"><a name="l01992"></a><span class="lineno"> 1992</span>&#160;</div>
<div class="line"><a name="l01993"></a><span class="lineno"> 1993</span>&#160;</div>
<div class="line"><a name="l01996"></a><span class="lineno"> 1996</span>&#160;        <span class="comment">//typedef f32 f32mat1x1;</span></div>
<div class="line"><a name="l01997"></a><span class="lineno"> 1997</span>&#160;</div>
<div class="line"><a name="l02000"></a><span class="lineno"> 2000</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f32, defaultp&gt; <a class="code" href="a00304.html#ga04100c76f7d55a0dd0983ccf05142bff">f32mat2x2</a>;</div>
<div class="line"><a name="l02001"></a><span class="lineno"> 2001</span>&#160;</div>
<div class="line"><a name="l02004"></a><span class="lineno"> 2004</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f32, defaultp&gt; <a class="code" href="a00304.html#gab256cdab5eb582e426d749ae77b5b566">f32mat2x3</a>;</div>
<div class="line"><a name="l02005"></a><span class="lineno"> 2005</span>&#160;</div>
<div class="line"><a name="l02008"></a><span class="lineno"> 2008</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f32, defaultp&gt; <a class="code" href="a00304.html#gaf512b74c4400b68f9fdf9388b3d6aac8">f32mat2x4</a>;</div>
<div class="line"><a name="l02009"></a><span class="lineno"> 2009</span>&#160;</div>
<div class="line"><a name="l02012"></a><span class="lineno"> 2012</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f32, defaultp&gt; <a class="code" href="a00304.html#ga1320a08e14fdff3821241eefab6947e9">f32mat3x2</a>;</div>
<div class="line"><a name="l02013"></a><span class="lineno"> 2013</span>&#160;</div>
<div class="line"><a name="l02016"></a><span class="lineno"> 2016</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f32, defaultp&gt; <a class="code" href="a00304.html#ga65261fa8a21045c8646ddff114a56174">f32mat3x3</a>;</div>
<div class="line"><a name="l02017"></a><span class="lineno"> 2017</span>&#160;</div>
<div class="line"><a name="l02020"></a><span class="lineno"> 2020</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f32, defaultp&gt; <a class="code" href="a00304.html#gab90ade28222f8b861d5ceaf81a3a7f5d">f32mat3x4</a>;</div>
<div class="line"><a name="l02021"></a><span class="lineno"> 2021</span>&#160;</div>
<div class="line"><a name="l02024"></a><span class="lineno"> 2024</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f32, defaultp&gt; <a class="code" href="a00304.html#ga3b32ca1e57a4ef91babbc3d35a34ea20">f32mat4x2</a>;</div>
<div class="line"><a name="l02025"></a><span class="lineno"> 2025</span>&#160;</div>
<div class="line"><a name="l02028"></a><span class="lineno"> 2028</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f32, defaultp&gt; <a class="code" href="a00304.html#ga239b96198771b7add8eea7e6b59840c0">f32mat4x3</a>;</div>
<div class="line"><a name="l02029"></a><span class="lineno"> 2029</span>&#160;</div>
<div class="line"><a name="l02032"></a><span class="lineno"> 2032</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f32, defaultp&gt; <a class="code" href="a00304.html#gaee4da0e9fbd8cfa2f89cb80889719dc3">f32mat4x4</a>;</div>
<div class="line"><a name="l02033"></a><span class="lineno"> 2033</span>&#160;</div>
<div class="line"><a name="l02034"></a><span class="lineno"> 2034</span>&#160;</div>
<div class="line"><a name="l02035"></a><span class="lineno"> 2035</span>&#160;<span class="preprocessor">#       ifndef GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l02036"></a><span class="lineno"> 2036</span>&#160;</div>
<div class="line"><a name="l02039"></a><span class="lineno"> 2039</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f64, defaultp&gt; f64mat1;</span></div>
<div class="line"><a name="l02040"></a><span class="lineno"> 2040</span>&#160;</div>
<div class="line"><a name="l02043"></a><span class="lineno"> 2043</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f64, defaultp&gt; <a class="code" href="a00304.html#gad9771450a54785d13080cdde0fe20c1d">f64mat2</a>;</div>
<div class="line"><a name="l02044"></a><span class="lineno"> 2044</span>&#160;</div>
<div class="line"><a name="l02047"></a><span class="lineno"> 2047</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f64, defaultp&gt; <a class="code" href="a00304.html#ga9b69181efbf8f37ae934f135137b29c0">f64mat3</a>;</div>
<div class="line"><a name="l02048"></a><span class="lineno"> 2048</span>&#160;</div>
<div class="line"><a name="l02051"></a><span class="lineno"> 2051</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f64, defaultp&gt; <a class="code" href="a00304.html#ga0ecd3f4952536e5ef12702b44d2626fc">f64mat4</a>;</div>
<div class="line"><a name="l02052"></a><span class="lineno"> 2052</span>&#160;</div>
<div class="line"><a name="l02053"></a><span class="lineno"> 2053</span>&#160;</div>
<div class="line"><a name="l02056"></a><span class="lineno"> 2056</span>&#160;        <span class="comment">//typedef f64 f64mat1x1;</span></div>
<div class="line"><a name="l02057"></a><span class="lineno"> 2057</span>&#160;</div>
<div class="line"><a name="l02060"></a><span class="lineno"> 2060</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 2, f64, defaultp&gt; <a class="code" href="a00304.html#ga9ec7c4c79e303c053e30729a95fb2c37">f64mat2x2</a>;</div>
<div class="line"><a name="l02061"></a><span class="lineno"> 2061</span>&#160;</div>
<div class="line"><a name="l02064"></a><span class="lineno"> 2064</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 3, f64, defaultp&gt; <a class="code" href="a00304.html#gae3ab5719fc4c1e966631dbbcba8d412a">f64mat2x3</a>;</div>
<div class="line"><a name="l02065"></a><span class="lineno"> 2065</span>&#160;</div>
<div class="line"><a name="l02068"></a><span class="lineno"> 2068</span>&#160;        <span class="keyword">typedef</span> mat&lt;2, 4, f64, defaultp&gt; <a class="code" href="a00304.html#gac87278e0c702ba8afff76316d4eeb769">f64mat2x4</a>;</div>
<div class="line"><a name="l02069"></a><span class="lineno"> 2069</span>&#160;</div>
<div class="line"><a name="l02072"></a><span class="lineno"> 2072</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 2, f64, defaultp&gt; <a class="code" href="a00304.html#ga2473d8bf3f4abf967c4d0e18175be6f7">f64mat3x2</a>;</div>
<div class="line"><a name="l02073"></a><span class="lineno"> 2073</span>&#160;</div>
<div class="line"><a name="l02076"></a><span class="lineno"> 2076</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 3, f64, defaultp&gt; <a class="code" href="a00304.html#ga916c1aed91cf91f7b41399ebe7c6e185">f64mat3x3</a>;</div>
<div class="line"><a name="l02077"></a><span class="lineno"> 2077</span>&#160;</div>
<div class="line"><a name="l02080"></a><span class="lineno"> 2080</span>&#160;        <span class="keyword">typedef</span> mat&lt;3, 4, f64, defaultp&gt; <a class="code" href="a00304.html#gaab239fa9e35b65a67cbaa6ac082f3675">f64mat3x4</a>;</div>
<div class="line"><a name="l02081"></a><span class="lineno"> 2081</span>&#160;</div>
<div class="line"><a name="l02084"></a><span class="lineno"> 2084</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 2, f64, defaultp&gt; <a class="code" href="a00304.html#gab7daf79d6bc06a68bea1c6f5e11b5512">f64mat4x2</a>;</div>
<div class="line"><a name="l02085"></a><span class="lineno"> 2085</span>&#160;</div>
<div class="line"><a name="l02088"></a><span class="lineno"> 2088</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 3, f64, defaultp&gt; <a class="code" href="a00304.html#ga3e2e66ffbe341a80bc005ba2b9552110">f64mat4x3</a>;</div>
<div class="line"><a name="l02089"></a><span class="lineno"> 2089</span>&#160;</div>
<div class="line"><a name="l02092"></a><span class="lineno"> 2092</span>&#160;        <span class="keyword">typedef</span> mat&lt;4, 4, f64, defaultp&gt; <a class="code" href="a00304.html#gae52e2b7077a9ff928a06ab5ce600b81e">f64mat4x4</a>;</div>
<div class="line"><a name="l02093"></a><span class="lineno"> 2093</span>&#160;</div>
<div class="line"><a name="l02094"></a><span class="lineno"> 2094</span>&#160;<span class="preprocessor">#       endif//GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l02095"></a><span class="lineno"> 2095</span>&#160;</div>
<div class="line"><a name="l02097"></a><span class="lineno"> 2097</span>&#160;        <span class="comment">// Quaternion types</span></div>
<div class="line"><a name="l02098"></a><span class="lineno"> 2098</span>&#160;</div>
<div class="line"><a name="l02101"></a><span class="lineno"> 2101</span>&#160;        <span class="keyword">typedef</span> qua&lt;f32, defaultp&gt; <a class="code" href="a00304.html#ga38e674196ba411d642be40c47bf33939">f32quat</a>;</div>
<div class="line"><a name="l02102"></a><span class="lineno"> 2102</span>&#160;</div>
<div class="line"><a name="l02105"></a><span class="lineno"> 2105</span>&#160;        <span class="keyword">typedef</span> qua&lt;f32, lowp&gt; <a class="code" href="a00304.html#gaa3ba60ef8f69c6aeb1629594eaa95347">lowp_f32quat</a>;</div>
<div class="line"><a name="l02106"></a><span class="lineno"> 2106</span>&#160;</div>
<div class="line"><a name="l02109"></a><span class="lineno"> 2109</span>&#160;        <span class="keyword">typedef</span> qua&lt;f64, lowp&gt; <a class="code" href="a00304.html#gaa3ee2bc4af03cc06578b66b3e3f878ae">lowp_f64quat</a>;</div>
<div class="line"><a name="l02110"></a><span class="lineno"> 2110</span>&#160;</div>
<div class="line"><a name="l02113"></a><span class="lineno"> 2113</span>&#160;        <span class="keyword">typedef</span> qua&lt;f32, mediump&gt; <a class="code" href="a00304.html#gaa40c03d52dbfbfaf03e75773b9606ff3">mediump_f32quat</a>;</div>
<div class="line"><a name="l02114"></a><span class="lineno"> 2114</span>&#160;</div>
<div class="line"><a name="l02115"></a><span class="lineno"> 2115</span>&#160;<span class="preprocessor">#       ifndef GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l02116"></a><span class="lineno"> 2116</span>&#160;</div>
<div class="line"><a name="l02119"></a><span class="lineno"> 2119</span>&#160;        <span class="keyword">typedef</span> qua&lt;f64, mediump&gt; <a class="code" href="a00304.html#ga5e52f485059ce6e3010c590b882602c9">mediump_f64quat</a>;</div>
<div class="line"><a name="l02120"></a><span class="lineno"> 2120</span>&#160;</div>
<div class="line"><a name="l02123"></a><span class="lineno"> 2123</span>&#160;        <span class="keyword">typedef</span> qua&lt;f32, highp&gt; <a class="code" href="a00304.html#ga4252cf7f5b0e3cd47c3d3badf0ef43b3">highp_f32quat</a>;</div>
<div class="line"><a name="l02124"></a><span class="lineno"> 2124</span>&#160;</div>
<div class="line"><a name="l02127"></a><span class="lineno"> 2127</span>&#160;        <span class="keyword">typedef</span> qua&lt;f64, highp&gt; <a class="code" href="a00304.html#gafcfdd74a115163af2ce1093551747352">highp_f64quat</a>;</div>
<div class="line"><a name="l02128"></a><span class="lineno"> 2128</span>&#160;</div>
<div class="line"><a name="l02131"></a><span class="lineno"> 2131</span>&#160;        <span class="keyword">typedef</span> qua&lt;f64, defaultp&gt; <a class="code" href="a00304.html#ga2b114a2f2af0fe1dfeb569c767822940">f64quat</a>;</div>
<div class="line"><a name="l02132"></a><span class="lineno"> 2132</span>&#160;</div>
<div class="line"><a name="l02133"></a><span class="lineno"> 2133</span>&#160;<span class="preprocessor">#       endif//GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l02134"></a><span class="lineno"> 2134</span>&#160;</div>
<div class="line"><a name="l02136"></a><span class="lineno"> 2136</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l02137"></a><span class="lineno"> 2137</span>&#160;</div>
<div class="line"><a name="l02138"></a><span class="lineno"> 2138</span>&#160;<span class="preprocessor">#include &quot;type_precision.inl&quot;</span></div>
<div class="ttc" id="a00304_html_gad064202b4cf9a2972475c03de657cb39"><div class="ttname"><a href="a00304.html#gad064202b4cf9a2972475c03de657cb39">glm::highp_u16vec1</a></div><div class="ttdeci">vec&lt; 1, u16, highp &gt; highp_u16vec1</div><div class="ttdoc">High qualifier 16 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00354">fwd.hpp:354</a></div></div>
<div class="ttc" id="a00304_html_ga602e119c6b246b4f6edcf66845f2aa0f"><div class="ttname"><a href="a00304.html#ga602e119c6b246b4f6edcf66845f2aa0f">glm::highp_f32mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, highp &gt; highp_f32mat4x2</div><div class="ttdoc">High single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00696">fwd.hpp:696</a></div></div>
<div class="ttc" id="a00304_html_ga0c181fdf06a309691999926b6690c969"><div class="ttname"><a href="a00304.html#ga0c181fdf06a309691999926b6690c969">glm::highp_u64</a></div><div class="ttdeci">uint64 highp_u64</div><div class="ttdoc">High qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00133">fwd.hpp:133</a></div></div>
<div class="ttc" id="a00304_html_gac30fdf8afa489400053275b6a3350127"><div class="ttname"><a href="a00304.html#gac30fdf8afa489400053275b6a3350127">glm::mediump_f64vec1</a></div><div class="ttdeci">vec&lt; 1, f64, mediump &gt; mediump_f64vec1</div><div class="ttdoc">Medium double-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00491">fwd.hpp:491</a></div></div>
<div class="ttc" id="a00304_html_gaea5c4518e175162e306d2c2b5ef5ac79"><div class="ttname"><a href="a00304.html#gaea5c4518e175162e306d2c2b5ef5ac79">glm::f32vec3</a></div><div class="ttdeci">vec&lt; 3, f32, defaultp &gt; f32vec3</div><div class="ttdoc">Single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00463">fwd.hpp:463</a></div></div>
<div class="ttc" id="a00304_html_ga74e9133378fd0b4da8ac0bc0876702ff"><div class="ttname"><a href="a00304.html#ga74e9133378fd0b4da8ac0bc0876702ff">glm::mediump_fmat2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, mediump &gt; mediump_fmat2</div><div class="ttdoc">Medium single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00528">fwd.hpp:528</a></div></div>
<div class="ttc" id="a00304_html_gaaeeb0077198cff40e3f48b1108ece139"><div class="ttname"><a href="a00304.html#gaaeeb0077198cff40e3f48b1108ece139">glm::highp_float64_t</a></div><div class="ttdeci">double highp_float64_t</div><div class="ttdoc">High 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00175">fwd.hpp:175</a></div></div>
<div class="ttc" id="a00304_html_ga0ecd3f4952536e5ef12702b44d2626fc"><div class="ttname"><a href="a00304.html#ga0ecd3f4952536e5ef12702b44d2626fc">glm::f64mat4</a></div><div class="ttdeci">mat&lt; 4, 4, f64, defaultp &gt; f64mat4</div><div class="ttdoc">Double-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00586">fwd.hpp:586</a></div></div>
<div class="ttc" id="a00304_html_gad9771450a54785d13080cdde0fe20c1d"><div class="ttname"><a href="a00304.html#gad9771450a54785d13080cdde0fe20c1d">glm::f64mat2</a></div><div class="ttdeci">mat&lt; 2, 2, f64, defaultp &gt; f64mat2</div><div class="ttdoc">Double-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00584">fwd.hpp:584</a></div></div>
<div class="ttc" id="a00304_html_ga41ad497f7eae211556aefd783cb02b90"><div class="ttname"><a href="a00304.html#ga41ad497f7eae211556aefd783cb02b90">glm::mediump_fmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, mediump &gt; mediump_fmat4x3</div><div class="ttdoc">Medium single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00647">fwd.hpp:647</a></div></div>
<div class="ttc" id="a00304_html_gaaaf878d0d7bfc0aac054fe269a886ca8"><div class="ttname"><a href="a00304.html#gaaaf878d0d7bfc0aac054fe269a886ca8">glm::mediump_f32mat3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, mediump &gt; mediump_f32mat3</div><div class="ttdoc">Medium single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00545">fwd.hpp:545</a></div></div>
<div class="ttc" id="a00304_html_gaf4dae276fd29623950de14a6ca2586b5"><div class="ttname"><a href="a00304.html#gaf4dae276fd29623950de14a6ca2586b5">glm::mediump_uint32_t</a></div><div class="ttdeci">uint32 mediump_uint32_t</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00127">fwd.hpp:127</a></div></div>
<div class="ttc" id="a00304_html_gaa212b805736a759998e312cbdd550fae"><div class="ttname"><a href="a00304.html#gaa212b805736a759998e312cbdd550fae">glm::lowp_uint64</a></div><div class="ttdeci">uint64 lowp_uint64</div><div class="ttdoc">Low qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00136">fwd.hpp:136</a></div></div>
<div class="ttc" id="a00304_html_ga98a687c17b174ea316b5f397b64f44bc"><div class="ttname"><a href="a00304.html#ga98a687c17b174ea316b5f397b64f44bc">glm::mediump_fmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, mediump &gt; mediump_fmat2x2</div><div class="ttdoc">Medium single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00640">fwd.hpp:640</a></div></div>
<div class="ttc" id="a00304_html_ga701f32ab5b3fb06996b41f5c0d643805"><div class="ttname"><a href="a00304.html#ga701f32ab5b3fb06996b41f5c0d643805">glm::f32vec1</a></div><div class="ttdeci">vec&lt; 1, f32, defaultp &gt; f32vec1</div><div class="ttdoc">Single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00461">fwd.hpp:461</a></div></div>
<div class="ttc" id="a00304_html_ga14c90ca49885723f51d06e295587236f"><div class="ttname"><a href="a00304.html#ga14c90ca49885723f51d06e295587236f">glm::highp_f32mat4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, highp &gt; highp_f32mat4</div><div class="ttdoc">High single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00550">fwd.hpp:550</a></div></div>
<div class="ttc" id="a00304_html_gadbb198a4d7aad82a0f4dc466ef6f6215"><div class="ttname"><a href="a00304.html#gadbb198a4d7aad82a0f4dc466ef6f6215">glm::highp_float64</a></div><div class="ttdeci">double highp_float64</div><div class="ttdoc">High 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00170">fwd.hpp:170</a></div></div>
<div class="ttc" id="a00304_html_ga1b09f03da7ac43055c68a349d5445083"><div class="ttname"><a href="a00304.html#ga1b09f03da7ac43055c68a349d5445083">glm::lowp_u8</a></div><div class="ttdeci">uint8 lowp_u8</div><div class="ttdoc">Low qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00089">fwd.hpp:89</a></div></div>
<div class="ttc" id="a00304_html_ga8165913e068444f7842302d40ba897b9"><div class="ttname"><a href="a00304.html#ga8165913e068444f7842302d40ba897b9">glm::u32</a></div><div class="ttdeci">uint32 u32</div><div class="ttdoc">Default qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00120">fwd.hpp:120</a></div></div>
<div class="ttc" id="a00304_html_ga9b69181efbf8f37ae934f135137b29c0"><div class="ttname"><a href="a00304.html#ga9b69181efbf8f37ae934f135137b29c0">glm::f64mat3</a></div><div class="ttdeci">mat&lt; 3, 3, f64, defaultp &gt; f64mat3</div><div class="ttdoc">Double-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00585">fwd.hpp:585</a></div></div>
<div class="ttc" id="a00304_html_ga3714dab2c16a6545a405cb0c3b3aaa6f"><div class="ttname"><a href="a00304.html#ga3714dab2c16a6545a405cb0c3b3aaa6f">glm::lowp_float64</a></div><div class="ttdeci">double lowp_float64</div><div class="ttdoc">Low 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00168">fwd.hpp:168</a></div></div>
<div class="ttc" id="a00304_html_ga54b8a4e0f5a7203a821bf8e9c1265bcf"><div class="ttname"><a href="a00304.html#ga54b8a4e0f5a7203a821bf8e9c1265bcf">glm::i32vec1</a></div><div class="ttdeci">vec&lt; 1, i32, defaultp &gt; i32vec1</div><div class="ttdoc">32 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00277">fwd.hpp:277</a></div></div>
<div class="ttc" id="a00304_html_ga746dc6da204f5622e395f492997dbf57"><div class="ttname"><a href="a00304.html#ga746dc6da204f5622e395f492997dbf57">glm::highp_uint16</a></div><div class="ttdeci">uint16 highp_uint16</div><div class="ttdoc">High qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00110">fwd.hpp:110</a></div></div>
<div class="ttc" id="a00304_html_gaf306dd69e53633636aee38cea79d4cb7"><div class="ttname"><a href="a00304.html#gaf306dd69e53633636aee38cea79d4cb7">glm::mediump_f64mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f64, mediump &gt; mediump_f64mat2x4</div><div class="ttdoc">Medium double-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00762">fwd.hpp:762</a></div></div>
<div class="ttc" id="a00304_html_gad23b1be9b3bf20352089a6b738f0ebba"><div class="ttname"><a href="a00304.html#gad23b1be9b3bf20352089a6b738f0ebba">glm::highp_i64vec4</a></div><div class="ttdeci">vec&lt; 4, i64, highp &gt; highp_i64vec4</div><div class="ttdoc">High qualifier 64 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00295">fwd.hpp:295</a></div></div>
<div class="ttc" id="a00304_html_gaab239fa9e35b65a67cbaa6ac082f3675"><div class="ttname"><a href="a00304.html#gaab239fa9e35b65a67cbaa6ac082f3675">glm::f64mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f64, defaultp &gt; f64mat3x4</div><div class="ttdoc">Double-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00787">fwd.hpp:787</a></div></div>
<div class="ttc" id="a00304_html_ga4541dc2feb2a31d6ecb5a303f3dd3280"><div class="ttname"><a href="a00304.html#ga4541dc2feb2a31d6ecb5a303f3dd3280">glm::fmat2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, defaultp &gt; fmat2</div><div class="ttdoc">Single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00536">fwd.hpp:536</a></div></div>
<div class="ttc" id="a00304_html_gae9c90a867a6026b1f6eab00456f3fb8b"><div class="ttname"><a href="a00304.html#gae9c90a867a6026b1f6eab00456f3fb8b">glm::i16vec3</a></div><div class="ttdeci">vec&lt; 3, i16, defaultp &gt; i16vec3</div><div class="ttdoc">16 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00259">fwd.hpp:259</a></div></div>
<div class="ttc" id="a00304_html_gaf1b735b4b1145174f4e4167d13778f9b"><div class="ttname"><a href="a00304.html#gaf1b735b4b1145174f4e4167d13778f9b">glm::lowp_uint32_t</a></div><div class="ttdeci">uint32 lowp_uint32_t</div><div class="ttdoc">Low qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00126">fwd.hpp:126</a></div></div>
<div class="ttc" id="a00304_html_ga62a32c31f4e2e8ca859663b6e3289a2d"><div class="ttname"><a href="a00304.html#ga62a32c31f4e2e8ca859663b6e3289a2d">glm::lowp_fvec2</a></div><div class="ttdeci">vec&lt; 2, float, lowp &gt; lowp_fvec2</div><div class="ttdoc">Low single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00427">fwd.hpp:427</a></div></div>
<div class="ttc" id="a00304_html_ga34dd5ec1988c443bae80f1b20a8ade5f"><div class="ttname"><a href="a00304.html#ga34dd5ec1988c443bae80f1b20a8ade5f">glm::mediump_uint32</a></div><div class="ttdeci">uint32 mediump_uint32</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00123">fwd.hpp:123</a></div></div>
<div class="ttc" id="a00304_html_gac1c38778c0b5a1263f07753c05a4f7b9"><div class="ttname"><a href="a00304.html#gac1c38778c0b5a1263f07753c05a4f7b9">glm::mediump_fmat4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, mediump &gt; mediump_fmat4</div><div class="ttdoc">Medium single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00530">fwd.hpp:530</a></div></div>
<div class="ttc" id="a00304_html_gaa38d732f5d4a7bc42a1b43b9d3c141ce"><div class="ttname"><a href="a00304.html#gaa38d732f5d4a7bc42a1b43b9d3c141ce">glm::highp_uint64</a></div><div class="ttdeci">uint64 highp_uint64</div><div class="ttdoc">High qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00138">fwd.hpp:138</a></div></div>
<div class="ttc" id="a00304_html_ga5bba0ce31210e274f73efacd3364c03f"><div class="ttname"><a href="a00304.html#ga5bba0ce31210e274f73efacd3364c03f">glm::lowp_fmat2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, lowp &gt; lowp_fmat2</div><div class="ttdoc">Low single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00524">fwd.hpp:524</a></div></div>
<div class="ttc" id="a00304_html_gaa6a5b461bbf5fe20982472aa51896d4b"><div class="ttname"><a href="a00304.html#gaa6a5b461bbf5fe20982472aa51896d4b">glm::lowp_uint32</a></div><div class="ttdeci">uint32 lowp_uint32</div><div class="ttdoc">Low qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00122">fwd.hpp:122</a></div></div>
<div class="ttc" id="a00304_html_ga40b5c557efebb5bb99d6b9aa81095afa"><div class="ttname"><a href="a00304.html#ga40b5c557efebb5bb99d6b9aa81095afa">glm::lowp_fvec3</a></div><div class="ttdeci">vec&lt; 3, float, lowp &gt; lowp_fvec3</div><div class="ttdoc">Low single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00428">fwd.hpp:428</a></div></div>
<div class="ttc" id="a00304_html_ga44bfa55cda5dbf53f24a1fb7610393d6"><div class="ttname"><a href="a00304.html#ga44bfa55cda5dbf53f24a1fb7610393d6">glm::mediump_fvec2</a></div><div class="ttdeci">vec&lt; 2, float, mediump &gt; mediump_fvec2</div><div class="ttdoc">Medium Single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00432">fwd.hpp:432</a></div></div>
<div class="ttc" id="a00304_html_ga28733aec7288191b314d42154fd0b690"><div class="ttname"><a href="a00304.html#ga28733aec7288191b314d42154fd0b690">glm::lowp_fmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, lowp &gt; lowp_fmat3x4</div><div class="ttdoc">Low single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00635">fwd.hpp:635</a></div></div>
<div class="ttc" id="a00304_html_ga771fda9109933db34f808d92b9b84d7e"><div class="ttname"><a href="a00304.html#ga771fda9109933db34f808d92b9b84d7e">glm::lowp_f64mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f64, lowp &gt; lowp_f64mat2x2</div><div class="ttdoc">Low double-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00750">fwd.hpp:750</a></div></div>
<div class="ttc" id="a00304_html_gaa4e31c3d9de067029efeb161a44b0232"><div class="ttname"><a href="a00304.html#gaa4e31c3d9de067029efeb161a44b0232">glm::i64vec4</a></div><div class="ttdeci">vec&lt; 4, i64, defaultp &gt; i64vec4</div><div class="ttdoc">64 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00300">fwd.hpp:300</a></div></div>
<div class="ttc" id="a00304_html_ga1c522ca821c27b862fe51cf4024b064b"><div class="ttname"><a href="a00304.html#ga1c522ca821c27b862fe51cf4024b064b">glm::u16vec3</a></div><div class="ttdeci">vec&lt; 3, u16, defaultp &gt; u16vec3</div><div class="ttdoc">Default qualifier 16 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00361">fwd.hpp:361</a></div></div>
<div class="ttc" id="a00304_html_ga859be7b9d3a3765c1cafc14dbcf249a6"><div class="ttname"><a href="a00304.html#ga859be7b9d3a3765c1cafc14dbcf249a6">glm::lowp_u64vec1</a></div><div class="ttdeci">vec&lt; 1, u64, lowp &gt; lowp_u64vec1</div><div class="ttdoc">Low qualifier 64 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00384">fwd.hpp:384</a></div></div>
<div class="ttc" id="a00304_html_ga400ce8cc566de093a9b28e59e220d6e4"><div class="ttname"><a href="a00304.html#ga400ce8cc566de093a9b28e59e220d6e4">glm::mediump_u16vec1</a></div><div class="ttdeci">vec&lt; 1, u16, mediump &gt; mediump_u16vec1</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00349">fwd.hpp:349</a></div></div>
<div class="ttc" id="a00304_html_gad06935764d78f43f9d542c784c2212ec"><div class="ttname"><a href="a00304.html#gad06935764d78f43f9d542c784c2212ec">glm::i8vec2</a></div><div class="ttdeci">vec&lt; 2, i8, defaultp &gt; i8vec2</div><div class="ttdoc">8 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00238">fwd.hpp:238</a></div></div>
<div class="ttc" id="a00304_html_gafd9a6ebc0c7b95f5c581d00d16a17c54"><div class="ttname"><a href="a00304.html#gafd9a6ebc0c7b95f5c581d00d16a17c54">glm::mediump_f64mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f64, mediump &gt; mediump_f64mat2x3</div><div class="ttdoc">Medium double-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00761">fwd.hpp:761</a></div></div>
<div class="ttc" id="a00304_html_ga4502885718742aa238c36a312c3f3f20"><div class="ttname"><a href="a00304.html#ga4502885718742aa238c36a312c3f3f20">glm::lowp_u32vec4</a></div><div class="ttdeci">vec&lt; 4, u32, lowp &gt; lowp_u32vec4</div><div class="ttdoc">Low qualifier 32 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00367">fwd.hpp:367</a></div></div>
<div class="ttc" id="a00304_html_ga0f038d4e09862a74f03d102c59eda73e"><div class="ttname"><a href="a00304.html#ga0f038d4e09862a74f03d102c59eda73e">glm::highp_f32vec4</a></div><div class="ttdeci">vec&lt; 4, f32, highp &gt; highp_f32vec4</div><div class="ttdoc">High single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00459">fwd.hpp:459</a></div></div>
<div class="ttc" id="a00304_html_ga43e5b41c834fcaf4db5a831c0e28128e"><div class="ttname"><a href="a00304.html#ga43e5b41c834fcaf4db5a831c0e28128e">glm::lowp_f32vec1</a></div><div class="ttdeci">vec&lt; 1, f32, lowp &gt; lowp_f32vec1</div><div class="ttdoc">Low single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00446">fwd.hpp:446</a></div></div>
<div class="ttc" id="a00304_html_ga0aeb5cb001473b08c88175012708a379"><div class="ttname"><a href="a00304.html#ga0aeb5cb001473b08c88175012708a379">glm::highp_f32mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, highp &gt; highp_f32mat2x3</div><div class="ttdoc">High single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00691">fwd.hpp:691</a></div></div>
<div class="ttc" id="a00304_html_ga226a8d52b4e3f77aaa6231135e886aac"><div class="ttname"><a href="a00304.html#ga226a8d52b4e3f77aaa6231135e886aac">glm::highp_int64</a></div><div class="ttdeci">int64 highp_int64</div><div class="ttdoc">High qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00080">fwd.hpp:80</a></div></div>
<div class="ttc" id="a00304_html_ga20bf224bd4f8a24ecc4ed2004a40c219"><div class="ttname"><a href="a00304.html#ga20bf224bd4f8a24ecc4ed2004a40c219">glm::mediump_i32vec2</a></div><div class="ttdeci">vec&lt; 2, i32, mediump &gt; mediump_i32vec2</div><div class="ttdoc">Medium qualifier 32 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00268">fwd.hpp:268</a></div></div>
<div class="ttc" id="a00304_html_ga7451266e67794bd1125163502bc4a570"><div class="ttname"><a href="a00304.html#ga7451266e67794bd1125163502bc4a570">glm::lowp_f64mat4</a></div><div class="ttdeci">mat&lt; 4, 4, f64, lowp &gt; lowp_f64mat4</div><div class="ttdoc">Low double-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00574">fwd.hpp:574</a></div></div>
<div class="ttc" id="a00304_html_ga8cb400c0f4438f2640035d7b9824a0ca"><div class="ttname"><a href="a00304.html#ga8cb400c0f4438f2640035d7b9824a0ca">glm::fmat4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, defaultp &gt; fmat4</div><div class="ttdoc">Single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00538">fwd.hpp:538</a></div></div>
<div class="ttc" id="a00304_html_gaefe8ef520c6cb78590ebbefe648da4d4"><div class="ttname"><a href="a00304.html#gaefe8ef520c6cb78590ebbefe648da4d4">glm::mediump_fmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, mediump &gt; mediump_fmat3x4</div><div class="ttdoc">Medium single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00645">fwd.hpp:645</a></div></div>
<div class="ttc" id="a00304_html_ga8b2cd8d31eb345b2d641d9261c38db1a"><div class="ttname"><a href="a00304.html#ga8b2cd8d31eb345b2d641d9261c38db1a">glm::lowp_int16_t</a></div><div class="ttdeci">int16 lowp_int16_t</div><div class="ttdoc">Low qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00054">fwd.hpp:54</a></div></div>
<div class="ttc" id="a00304_html_gadf70eaaa230aeed5a4c9f4c9c5c55902"><div class="ttname"><a href="a00304.html#gadf70eaaa230aeed5a4c9f4c9c5c55902">glm::highp_i32vec4</a></div><div class="ttdeci">vec&lt; 4, i32, highp &gt; highp_i32vec4</div><div class="ttdoc">High qualifier 32 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00275">fwd.hpp:275</a></div></div>
<div class="ttc" id="a00304_html_ga3b32ca1e57a4ef91babbc3d35a34ea20"><div class="ttname"><a href="a00304.html#ga3b32ca1e57a4ef91babbc3d35a34ea20">glm::f32mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, defaultp &gt; f32mat4x2</div><div class="ttdoc">Single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00702">fwd.hpp:702</a></div></div>
<div class="ttc" id="a00304_html_gae54e4d1aeb5a0f0c64822e6f1b299e19"><div class="ttname"><a href="a00304.html#gae54e4d1aeb5a0f0c64822e6f1b299e19">glm::highp_fmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, highp &gt; highp_fmat3x2</div><div class="ttdoc">High single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00653">fwd.hpp:653</a></div></div>
<div class="ttc" id="a00304_html_gaa03f939d90d5ef157df957d93f0b9a64"><div class="ttname"><a href="a00304.html#gaa03f939d90d5ef157df957d93f0b9a64">glm::mediump_fmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, mediump &gt; mediump_fmat2x3</div><div class="ttdoc">Medium single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00641">fwd.hpp:641</a></div></div>
<div class="ttc" id="a00304_html_ga1bd0e914158bf03135f8a317de6debe9"><div class="ttname"><a href="a00304.html#ga1bd0e914158bf03135f8a317de6debe9">glm::mediump_u32</a></div><div class="ttdeci">uint32 mediump_u32</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00118">fwd.hpp:118</a></div></div>
<div class="ttc" id="a00304_html_ga49b98e7d71804af45d86886a489e633c"><div class="ttname"><a href="a00304.html#ga49b98e7d71804af45d86886a489e633c">glm::lowp_fmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, lowp &gt; lowp_fmat3x2</div><div class="ttdoc">Low single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00633">fwd.hpp:633</a></div></div>
<div class="ttc" id="a00304_html_ga17d36f0ea22314117e1cec9594b33945"><div class="ttname"><a href="a00304.html#ga17d36f0ea22314117e1cec9594b33945">glm::mediump_f64mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f64, mediump &gt; mediump_f64mat4x2</div><div class="ttdoc">Medium double-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00766">fwd.hpp:766</a></div></div>
<div class="ttc" id="a00304_html_ga791b15ceb3f1e09d1a0ec6f3057ca159"><div class="ttname"><a href="a00304.html#ga791b15ceb3f1e09d1a0ec6f3057ca159">glm::highp_u16vec2</a></div><div class="ttdeci">vec&lt; 2, u16, highp &gt; highp_u16vec2</div><div class="ttdoc">High qualifier 16 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00355">fwd.hpp:355</a></div></div>
<div class="ttc" id="a00304_html_ga62c31b133ceee9984fbee05ac4c434a9"><div class="ttname"><a href="a00304.html#ga62c31b133ceee9984fbee05ac4c434a9">glm::highp_f64vec1</a></div><div class="ttdeci">vec&lt; 1, f64, highp &gt; highp_f64vec1</div><div class="ttdoc">High double-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00496">fwd.hpp:496</a></div></div>
<div class="ttc" id="a00304_html_ga4b5e2c9aaa5d7717bf71179aefa12e88"><div class="ttname"><a href="a00304.html#ga4b5e2c9aaa5d7717bf71179aefa12e88">glm::mediump_i16vec2</a></div><div class="ttdeci">vec&lt; 2, i16, mediump &gt; mediump_i16vec2</div><div class="ttdoc">Medium qualifier 16 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00248">fwd.hpp:248</a></div></div>
<div class="ttc" id="a00304_html_ga4920a1536f161f7ded1d6909b7fef0d2"><div class="ttname"><a href="a00304.html#ga4920a1536f161f7ded1d6909b7fef0d2">glm::highp_fmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, highp &gt; highp_fmat2x4</div><div class="ttdoc">High single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00652">fwd.hpp:652</a></div></div>
<div class="ttc" id="a00304_html_ga489b89ba93d4f7b3934df78debc52276"><div class="ttname"><a href="a00304.html#ga489b89ba93d4f7b3934df78debc52276">glm::u64vec3</a></div><div class="ttdeci">vec&lt; 3, u64, defaultp &gt; u64vec3</div><div class="ttdoc">Default qualifier 64 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00401">fwd.hpp:401</a></div></div>
<div class="ttc" id="a00304_html_gaf49470869e9be2c059629b250619804e"><div class="ttname"><a href="a00304.html#gaf49470869e9be2c059629b250619804e">glm::lowp_uint8</a></div><div class="ttdeci">uint8 lowp_uint8</div><div class="ttdoc">Low qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00094">fwd.hpp:94</a></div></div>
<div class="ttc" id="a00304_html_gac53f9d7ab04eace67adad026092fb1e8"><div class="ttname"><a href="a00304.html#gac53f9d7ab04eace67adad026092fb1e8">glm::lowp_f32mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, lowp &gt; lowp_f32mat3x2</div><div class="ttdoc">Low single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00673">fwd.hpp:673</a></div></div>
<div class="ttc" id="a00304_html_ga30069d1f02b19599cbfadf98c23ac6ed"><div class="ttname"><a href="a00304.html#ga30069d1f02b19599cbfadf98c23ac6ed">glm::lowp_u64</a></div><div class="ttdeci">uint64 lowp_u64</div><div class="ttdoc">Low qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00131">fwd.hpp:131</a></div></div>
<div class="ttc" id="a00304_html_ga62324224b9c6cce9c6b4db96bb704a8a"><div class="ttname"><a href="a00304.html#ga62324224b9c6cce9c6b4db96bb704a8a">glm::highp_i64vec3</a></div><div class="ttdeci">vec&lt; 3, i64, highp &gt; highp_i64vec3</div><div class="ttdoc">High qualifier 64 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00294">fwd.hpp:294</a></div></div>
<div class="ttc" id="a00304_html_ga6fbd69cbdaa44345bff923a2cf63de7e"><div class="ttname"><a href="a00304.html#ga6fbd69cbdaa44345bff923a2cf63de7e">glm::mediump_int8</a></div><div class="ttdeci">int8 mediump_int8</div><div class="ttdoc">Medium qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00037">fwd.hpp:37</a></div></div>
<div class="ttc" id="a00304_html_gaf645b1a60203b39c0207baff5e3d8c3c"><div class="ttname"><a href="a00304.html#gaf645b1a60203b39c0207baff5e3d8c3c">glm::lowp_int64</a></div><div class="ttdeci">int64 lowp_int64</div><div class="ttdoc">Low qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00078">fwd.hpp:78</a></div></div>
<div class="ttc" id="a00304_html_gab005efe0fa4de1a928e8ddec4bc2c43f"><div class="ttname"><a href="a00304.html#gab005efe0fa4de1a928e8ddec4bc2c43f">glm::mediump_f32mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, mediump &gt; mediump_f32mat4x2</div><div class="ttdoc">Medium single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00686">fwd.hpp:686</a></div></div>
<div class="ttc" id="a00304_html_gae7a4711107b7d078fc5f03ce2227b90b"><div class="ttname"><a href="a00304.html#gae7a4711107b7d078fc5f03ce2227b90b">glm::lowp_f64vec3</a></div><div class="ttdeci">vec&lt; 3, f64, lowp &gt; lowp_f64vec3</div><div class="ttdoc">Low double-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00488">fwd.hpp:488</a></div></div>
<div class="ttc" id="a00304_html_gaef3824ed4fe435a019c5b9dddf53fec5"><div class="ttname"><a href="a00304.html#gaef3824ed4fe435a019c5b9dddf53fec5">glm::u64vec2</a></div><div class="ttdeci">vec&lt; 2, u64, defaultp &gt; u64vec2</div><div class="ttdoc">Default qualifier 64 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00400">fwd.hpp:400</a></div></div>
<div class="ttc" id="a00304_html_ga5cb1dc9e8d300c2cdb0d7ff2308fa36c"><div class="ttname"><a href="a00304.html#ga5cb1dc9e8d300c2cdb0d7ff2308fa36c">glm::lowp_i64vec3</a></div><div class="ttdeci">vec&lt; 3, i64, lowp &gt; lowp_i64vec3</div><div class="ttdoc">Low qualifier 64 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00284">fwd.hpp:284</a></div></div>
<div class="ttc" id="a00304_html_ga2a8bdc32184ea0a522ef7bd90640cf67"><div class="ttname"><a href="a00304.html#ga2a8bdc32184ea0a522ef7bd90640cf67">glm::mediump_i8vec2</a></div><div class="ttdeci">vec&lt; 2, i8, mediump &gt; mediump_i8vec2</div><div class="ttdoc">Medium qualifier 8 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00228">fwd.hpp:228</a></div></div>
<div class="ttc" id="a00304_html_gab90ade28222f8b861d5ceaf81a3a7f5d"><div class="ttname"><a href="a00304.html#gab90ade28222f8b861d5ceaf81a3a7f5d">glm::f32mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, defaultp &gt; f32mat3x4</div><div class="ttdoc">Single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00707">fwd.hpp:707</a></div></div>
<div class="ttc" id="a00304_html_ga5609c8fa2b7eac3dec337d321cb0ca96"><div class="ttname"><a href="a00304.html#ga5609c8fa2b7eac3dec337d321cb0ca96">glm::highp_i16vec3</a></div><div class="ttdeci">vec&lt; 3, i16, highp &gt; highp_i16vec3</div><div class="ttdoc">High qualifier 16 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00254">fwd.hpp:254</a></div></div>
<div class="ttc" id="a00304_html_ga3be6c7fc5fe08fa2274bdb001d5f2633"><div class="ttname"><a href="a00304.html#ga3be6c7fc5fe08fa2274bdb001d5f2633">glm::mediump_i16vec3</a></div><div class="ttdeci">vec&lt; 3, i16, mediump &gt; mediump_i16vec3</div><div class="ttdoc">Medium qualifier 16 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00249">fwd.hpp:249</a></div></div>
<div class="ttc" id="a00304_html_gaf3f312156984c365e9f65620354da70b"><div class="ttname"><a href="a00304.html#gaf3f312156984c365e9f65620354da70b">glm::u64</a></div><div class="ttdeci">uint64 u64</div><div class="ttdoc">Default qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00134">fwd.hpp:134</a></div></div>
<div class="ttc" id="a00304_html_gade502df1ce14f837fae7f60a03ddb9b0"><div class="ttname"><a href="a00304.html#gade502df1ce14f837fae7f60a03ddb9b0">glm::f64vec1</a></div><div class="ttdeci">vec&lt; 1, f64, defaultp &gt; f64vec1</div><div class="ttdoc">Double-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00501">fwd.hpp:501</a></div></div>
<div class="ttc" id="a00304_html_ga42569e5b92f8635cedeadb1457ee1467"><div class="ttname"><a href="a00304.html#ga42569e5b92f8635cedeadb1457ee1467">glm::mediump_fmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, mediump &gt; mediump_fmat3x2</div><div class="ttdoc">Medium single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00643">fwd.hpp:643</a></div></div>
<div class="ttc" id="a00304_html_ga8343e9d244fb17a5bbf0d94d36b3695e"><div class="ttname"><a href="a00304.html#ga8343e9d244fb17a5bbf0d94d36b3695e">glm::mediump_i64vec1</a></div><div class="ttdeci">vec&lt; 1, i64, mediump &gt; mediump_i64vec1</div><div class="ttdoc">Medium qualifier 64 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00287">fwd.hpp:287</a></div></div>
<div class="ttc" id="a00304_html_gafe730798732aa7b0647096a004db1b1c"><div class="ttname"><a href="a00304.html#gafe730798732aa7b0647096a004db1b1c">glm::i16vec1</a></div><div class="ttdeci">vec&lt; 1, i16, defaultp &gt; i16vec1</div><div class="ttdoc">16 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00257">fwd.hpp:257</a></div></div>
<div class="ttc" id="a00304_html_gac8f8a12ee03105ef8861dc652434e3b7"><div class="ttname"><a href="a00304.html#gac8f8a12ee03105ef8861dc652434e3b7">glm::lowp_f64mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f64, lowp &gt; lowp_f64mat3x3</div><div class="ttdoc">Low double-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00754">fwd.hpp:754</a></div></div>
<div class="ttc" id="a00304_html_ga4e641a54d70c81eabf56c25c966d04bd"><div class="ttname"><a href="a00304.html#ga4e641a54d70c81eabf56c25c966d04bd">glm::lowp_f64vec2</a></div><div class="ttdeci">vec&lt; 2, f64, lowp &gt; lowp_f64vec2</div><div class="ttdoc">Low double-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00487">fwd.hpp:487</a></div></div>
<div class="ttc" id="a00304_html_gacf111095594996fef29067b2454fccad"><div class="ttname"><a href="a00304.html#gacf111095594996fef29067b2454fccad">glm::highp_fmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, highp &gt; highp_fmat2x3</div><div class="ttdoc">High single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00651">fwd.hpp:651</a></div></div>
<div class="ttc" id="a00304_html_ga58119a41d143ebaea0df70fe882e8a40"><div class="ttname"><a href="a00304.html#ga58119a41d143ebaea0df70fe882e8a40">glm::lowp_f64mat3</a></div><div class="ttdeci">mat&lt; 3, 3, f64, lowp &gt; lowp_f64mat3</div><div class="ttdoc">Low double-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00573">fwd.hpp:573</a></div></div>
<div class="ttc" id="a00304_html_gac21eda58a1475449a5709b412ebd776c"><div class="ttname"><a href="a00304.html#gac21eda58a1475449a5709b412ebd776c">glm::lowp_f32mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, lowp &gt; lowp_f32mat4x3</div><div class="ttdoc">Low single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00677">fwd.hpp:677</a></div></div>
<div class="ttc" id="a00304_html_ga1900c6ab74acd392809425953359ef52"><div class="ttname"><a href="a00304.html#ga1900c6ab74acd392809425953359ef52">glm::mediump_u64vec3</a></div><div class="ttdeci">vec&lt; 3, u64, mediump &gt; mediump_u64vec3</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00391">fwd.hpp:391</a></div></div>
<div class="ttc" id="a00304_html_gab83d8aae6e4f115e97a785e8574a115f"><div class="ttname"><a href="a00304.html#gab83d8aae6e4f115e97a785e8574a115f">glm::mediump_float64</a></div><div class="ttdeci">double mediump_float64</div><div class="ttdoc">Medium 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00169">fwd.hpp:169</a></div></div>
<div class="ttc" id="a00304_html_ga232fad1b0d6dcc7c16aabde98b2e2a80"><div class="ttname"><a href="a00304.html#ga232fad1b0d6dcc7c16aabde98b2e2a80">glm::float64</a></div><div class="ttdeci">double float64</div><div class="ttdoc">Double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00171">fwd.hpp:171</a></div></div>
<div class="ttc" id="a00304_html_gaa7db3ad10947cf70cae6474d05ebd227"><div class="ttname"><a href="a00304.html#gaa7db3ad10947cf70cae6474d05ebd227">glm::highp_i16vec2</a></div><div class="ttdeci">vec&lt; 2, i16, highp &gt; highp_i16vec2</div><div class="ttdoc">High qualifier 16 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00253">fwd.hpp:253</a></div></div>
<div class="ttc" id="a00304_html_ga8c8aa45aafcc23238edb1d5aeb801774"><div class="ttname"><a href="a00304.html#ga8c8aa45aafcc23238edb1d5aeb801774">glm::fmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, defaultp &gt; fmat4x2</div><div class="ttdoc">Single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00662">fwd.hpp:662</a></div></div>
<div class="ttc" id="a00304_html_ga39e90adcffe33264bd608fa9c6bd184b"><div class="ttname"><a href="a00304.html#ga39e90adcffe33264bd608fa9c6bd184b">glm::lowp_f64mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f64, lowp &gt; lowp_f64mat2x3</div><div class="ttdoc">Low double-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00751">fwd.hpp:751</a></div></div>
<div class="ttc" id="a00304_html_ga44e158af77a670ee1b58c03cda9e1619"><div class="ttname"><a href="a00304.html#ga44e158af77a670ee1b58c03cda9e1619">glm::fmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, defaultp &gt; fmat3x4</div><div class="ttdoc">Single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00667">fwd.hpp:667</a></div></div>
<div class="ttc" id="a00304_html_gad3ad390560a671b1f676fbf03cd3aa15"><div class="ttname"><a href="a00304.html#gad3ad390560a671b1f676fbf03cd3aa15">glm::lowp_u32vec3</a></div><div class="ttdeci">vec&lt; 3, u32, lowp &gt; lowp_u32vec3</div><div class="ttdoc">Low qualifier 32 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00366">fwd.hpp:366</a></div></div>
<div class="ttc" id="a00304_html_gaf512b74c4400b68f9fdf9388b3d6aac8"><div class="ttname"><a href="a00304.html#gaf512b74c4400b68f9fdf9388b3d6aac8">glm::f32mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, defaultp &gt; f32mat2x4</div><div class="ttdoc">Single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00706">fwd.hpp:706</a></div></div>
<div class="ttc" id="a00304_html_ga755484ffbe39ae3db2875953ed04e7b7"><div class="ttname"><a href="a00304.html#ga755484ffbe39ae3db2875953ed04e7b7">glm::lowp_fvec4</a></div><div class="ttdeci">vec&lt; 4, float, lowp &gt; lowp_fvec4</div><div class="ttdoc">Low single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00429">fwd.hpp:429</a></div></div>
<div class="ttc" id="a00304_html_gad80c84bcd5f585840faa6179f6fd446c"><div class="ttname"><a href="a00304.html#gad80c84bcd5f585840faa6179f6fd446c">glm::mediump_f32vec4</a></div><div class="ttdeci">vec&lt; 4, f32, mediump &gt; mediump_f32vec4</div><div class="ttdoc">Medium single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00454">fwd.hpp:454</a></div></div>
<div class="ttc" id="a00304_html_ga550831bfc26d1e0101c1cb3d79938c06"><div class="ttname"><a href="a00304.html#ga550831bfc26d1e0101c1cb3d79938c06">glm::i16vec4</a></div><div class="ttdeci">vec&lt; 4, i16, defaultp &gt; i16vec4</div><div class="ttdoc">16 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00260">fwd.hpp:260</a></div></div>
<div class="ttc" id="a00304_html_ga667b2ece2b258be898812dc2177995d1"><div class="ttname"><a href="a00304.html#ga667b2ece2b258be898812dc2177995d1">glm::lowp_uint8_t</a></div><div class="ttdeci">uint8 lowp_uint8_t</div><div class="ttdoc">Low qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00098">fwd.hpp:98</a></div></div>
<div class="ttc" id="a00304_html_gae978599c9711ac263ba732d4ac225b0e"><div class="ttname"><a href="a00304.html#gae978599c9711ac263ba732d4ac225b0e">glm::highp_uint32_t</a></div><div class="ttdeci">uint32 highp_uint32_t</div><div class="ttdoc">High qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00128">fwd.hpp:128</a></div></div>
<div class="ttc" id="a00304_html_gaa07c86650253672a19dbfb898f3265b8"><div class="ttname"><a href="a00304.html#gaa07c86650253672a19dbfb898f3265b8">glm::fmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, defaultp &gt; fmat3x3</div><div class="ttdoc">Single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00664">fwd.hpp:664</a></div></div>
<div class="ttc" id="a00304_html_gad38c544d332b8c4bd0b70b1bd9feccc2"><div class="ttname"><a href="a00304.html#gad38c544d332b8c4bd0b70b1bd9feccc2">glm::mediump_f64mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f64, mediump &gt; mediump_f64mat3x4</div><div class="ttdoc">Medium double-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00765">fwd.hpp:765</a></div></div>
<div class="ttc" id="a00304_html_ga71cdb53801ed4c3aadb3603c04723210"><div class="ttname"><a href="a00304.html#ga71cdb53801ed4c3aadb3603c04723210">glm::lowp_fmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, lowp &gt; lowp_fmat2x3</div><div class="ttdoc">Low single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00631">fwd.hpp:631</a></div></div>
<div class="ttc" id="a00304_html_gabed3be8dfdc4a0df4bf3271dbd7344c4"><div class="ttname"><a href="a00304.html#gabed3be8dfdc4a0df4bf3271dbd7344c4">glm::lowp_u32vec1</a></div><div class="ttdeci">vec&lt; 1, u32, lowp &gt; lowp_u32vec1</div><div class="ttdoc">Low qualifier 32 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00364">fwd.hpp:364</a></div></div>
<div class="ttc" id="a00304_html_gab256cdab5eb582e426d749ae77b5b566"><div class="ttname"><a href="a00304.html#gab256cdab5eb582e426d749ae77b5b566">glm::f32mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, defaultp &gt; f32mat2x3</div><div class="ttdoc">Single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00703">fwd.hpp:703</a></div></div>
<div class="ttc" id="a00304_html_ga46a57f71e430637559097a732b550a7e"><div class="ttname"><a href="a00304.html#ga46a57f71e430637559097a732b550a7e">glm::mediump_i32vec1</a></div><div class="ttdeci">vec&lt; 1, i32, mediump &gt; mediump_i32vec1</div><div class="ttdoc">Medium qualifier 32 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00267">fwd.hpp:267</a></div></div>
<div class="ttc" id="a00304_html_ga8a85a3d54a8a9e14fe7a1f96196c4f61"><div class="ttname"><a href="a00304.html#ga8a85a3d54a8a9e14fe7a1f96196c4f61">glm::highp_u16vec4</a></div><div class="ttdeci">vec&lt; 4, u16, highp &gt; highp_u16vec4</div><div class="ttdoc">High qualifier 16 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00357">fwd.hpp:357</a></div></div>
<div class="ttc" id="a00304_html_gae31ac3608cf643ceffd6554874bec4a0"><div class="ttname"><a href="a00304.html#gae31ac3608cf643ceffd6554874bec4a0">glm::lowp_i32vec1</a></div><div class="ttdeci">vec&lt; 1, i32, lowp &gt; lowp_i32vec1</div><div class="ttdoc">Low qualifier 32 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00262">fwd.hpp:262</a></div></div>
<div class="ttc" id="a00304_html_gab0f7d875db5f3cc9f3168c5a0ed56437"><div class="ttname"><a href="a00304.html#gab0f7d875db5f3cc9f3168c5a0ed56437">glm::lowp_i64vec1</a></div><div class="ttdeci">vec&lt; 1, i64, lowp &gt; lowp_i64vec1</div><div class="ttdoc">Low qualifier 64 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00282">fwd.hpp:282</a></div></div>
<div class="ttc" id="a00304_html_ga0e35a565b9036bfc3989f5e23a0792e3"><div class="ttname"><a href="a00304.html#ga0e35a565b9036bfc3989f5e23a0792e3">glm::highp_u32vec1</a></div><div class="ttdeci">vec&lt; 1, u32, highp &gt; highp_u32vec1</div><div class="ttdoc">High qualifier 32 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00374">fwd.hpp:374</a></div></div>
<div class="ttc" id="a00304_html_gadff3608baa4b5bd3ed28f95c1c2c345d"><div class="ttname"><a href="a00304.html#gadff3608baa4b5bd3ed28f95c1c2c345d">glm::mediump_int16</a></div><div class="ttdeci">int16 mediump_int16</div><div class="ttdoc">Medium qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00051">fwd.hpp:51</a></div></div>
<div class="ttc" id="a00304_html_ga9df98857be695d5a30cb30f5bfa38a80"><div class="ttname"><a href="a00304.html#ga9df98857be695d5a30cb30f5bfa38a80">glm::mediump_u16</a></div><div class="ttdeci">uint16 mediump_u16</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00104">fwd.hpp:104</a></div></div>
<div class="ttc" id="a00304_html_ga2b114a2f2af0fe1dfeb569c767822940"><div class="ttname"><a href="a00304.html#ga2b114a2f2af0fe1dfeb569c767822940">glm::f64quat</a></div><div class="ttdeci">qua&lt; f64, defaultp &gt; f64quat</div><div class="ttdoc">Double-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00815">fwd.hpp:815</a></div></div>
<div class="ttc" id="a00304_html_ga461c4c7d0757404dd0dba931760b25cf"><div class="ttname"><a href="a00304.html#ga461c4c7d0757404dd0dba931760b25cf">glm::mediump_f64vec3</a></div><div class="ttdeci">vec&lt; 3, f64, mediump &gt; mediump_f64vec3</div><div class="ttdoc">Medium double-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00493">fwd.hpp:493</a></div></div>
<div class="ttc" id="a00304_html_gaf09f3ca4b671a4a4f84505eb4cc865fd"><div class="ttname"><a href="a00304.html#gaf09f3ca4b671a4a4f84505eb4cc865fd">glm::u64vec1</a></div><div class="ttdeci">vec&lt; 1, u64, defaultp &gt; u64vec1</div><div class="ttdoc">Default qualifier 64 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00399">fwd.hpp:399</a></div></div>
<div class="ttc" id="a00304_html_ga322a7d7d2c2c68994dc872a33de63c61"><div class="ttname"><a href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">glm::int64_t</a></div><div class="ttdeci">int64 int64_t</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00085">fwd.hpp:85</a></div></div>
<div class="ttc" id="a00304_html_ga29b349e037f0b24320b4548a143daee2"><div class="ttname"><a href="a00304.html#ga29b349e037f0b24320b4548a143daee2">glm::u8vec1</a></div><div class="ttdeci">vec&lt; 1, u8, defaultp &gt; u8vec1</div><div class="ttdoc">Default qualifier 8 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00339">fwd.hpp:339</a></div></div>
<div class="ttc" id="a00304_html_ga1d8c10949691b0fd990253476f47beb3"><div class="ttname"><a href="a00304.html#ga1d8c10949691b0fd990253476f47beb3">glm::highp_i8vec1</a></div><div class="ttdeci">vec&lt; 1, i8, highp &gt; highp_i8vec1</div><div class="ttdoc">High qualifier 8 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00232">fwd.hpp:232</a></div></div>
<div class="ttc" id="a00304_html_ga20779a61de2fd526a17f12fe53ec46b1"><div class="ttname"><a href="a00304.html#ga20779a61de2fd526a17f12fe53ec46b1">glm::u8vec4</a></div><div class="ttdeci">vec&lt; 4, u8, defaultp &gt; u8vec4</div><div class="ttdoc">Default qualifier 8 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00342">fwd.hpp:342</a></div></div>
<div class="ttc" id="a00304_html_ga4bf09d8838a86866b39ee6e109341645"><div class="ttname"><a href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">glm::int8_t</a></div><div class="ttdeci">int8 int8_t</div><div class="ttdoc">8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00043">fwd.hpp:43</a></div></div>
<div class="ttc" id="a00304_html_ga96faea43ac5f875d2d3ffbf8d213e3eb"><div class="ttname"><a href="a00304.html#ga96faea43ac5f875d2d3ffbf8d213e3eb">glm::i32</a></div><div class="ttdeci">int32 i32</div><div class="ttdoc">32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00062">fwd.hpp:62</a></div></div>
<div class="ttc" id="a00304_html_ga8a11ccd2e38f674bbf3c2d1afc232aee"><div class="ttname"><a href="a00304.html#ga8a11ccd2e38f674bbf3c2d1afc232aee">glm::mediump_u32vec1</a></div><div class="ttdeci">vec&lt; 1, u32, mediump &gt; mediump_u32vec1</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00369">fwd.hpp:369</a></div></div>
<div class="ttc" id="a00304_html_ga9ec7c4c79e303c053e30729a95fb2c37"><div class="ttname"><a href="a00304.html#ga9ec7c4c79e303c053e30729a95fb2c37">glm::f64mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f64, defaultp &gt; f64mat2x2</div><div class="ttdoc">Double-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00780">fwd.hpp:780</a></div></div>
<div class="ttc" id="a00304_html_ga1d091b6abfba1772450e1745a06525bc"><div class="ttname"><a href="a00304.html#ga1d091b6abfba1772450e1745a06525bc">glm::lowp_f32mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, lowp &gt; lowp_f32mat2x2</div><div class="ttdoc">Low single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00670">fwd.hpp:670</a></div></div>
<div class="ttc" id="a00304_html_ga4e2e1debe022074ab224c9faf856d374"><div class="ttname"><a href="a00304.html#ga4e2e1debe022074ab224c9faf856d374">glm::lowp_f32vec4</a></div><div class="ttdeci">vec&lt; 4, f32, lowp &gt; lowp_f32vec4</div><div class="ttdoc">Low single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00449">fwd.hpp:449</a></div></div>
<div class="ttc" id="a00304_html_ga6ef77413883f48d6b53b4169b25edbd0"><div class="ttname"><a href="a00304.html#ga6ef77413883f48d6b53b4169b25edbd0">glm::highp_fvec3</a></div><div class="ttdeci">vec&lt; 3, float, highp &gt; highp_fvec3</div><div class="ttdoc">High Single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00438">fwd.hpp:438</a></div></div>
<div class="ttc" id="a00304_html_gab0cecb80fd106bc369b9e46a165815ce"><div class="ttname"><a href="a00304.html#gab0cecb80fd106bc369b9e46a165815ce">glm::lowp_f64mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f64, lowp &gt; lowp_f64mat4x2</div><div class="ttdoc">Low double-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00756">fwd.hpp:756</a></div></div>
<div class="ttc" id="a00304_html_gaa6f526388c74a66b3d52315a14d434ae"><div class="ttname"><a href="a00304.html#gaa6f526388c74a66b3d52315a14d434ae">glm::mediump_fmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, mediump &gt; mediump_fmat3x3</div><div class="ttdoc">Medium single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00644">fwd.hpp:644</a></div></div>
<div class="ttc" id="a00304_html_gabd2fda3cd208acf5a370ec9b5b3c58d4"><div class="ttname"><a href="a00304.html#gabd2fda3cd208acf5a370ec9b5b3c58d4">glm::highp_i64vec1</a></div><div class="ttdeci">vec&lt; 1, i64, highp &gt; highp_i64vec1</div><div class="ttdoc">High qualifier 64 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00292">fwd.hpp:292</a></div></div>
<div class="ttc" id="a00304_html_ga4177a44206121dabc8c4ff1c0f544574"><div class="ttname"><a href="a00304.html#ga4177a44206121dabc8c4ff1c0f544574">glm::i8vec4</a></div><div class="ttdeci">vec&lt; 4, i8, defaultp &gt; i8vec4</div><div class="ttdoc">8 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00240">fwd.hpp:240</a></div></div>
<div class="ttc" id="a00304_html_ga84ed04b4e0de18c977e932d617e7c223"><div class="ttname"><a href="a00304.html#ga84ed04b4e0de18c977e932d617e7c223">glm::highp_int32</a></div><div class="ttdeci">int32 highp_int32</div><div class="ttdoc">High qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00066">fwd.hpp:66</a></div></div>
<div class="ttc" id="a00304_html_gad4cc829ab1ad3e05ac0a24828a3c95cf"><div class="ttname"><a href="a00304.html#gad4cc829ab1ad3e05ac0a24828a3c95cf">glm::mediump_f32mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, mediump &gt; mediump_f32mat2x3</div><div class="ttdoc">Medium single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00681">fwd.hpp:681</a></div></div>
<div class="ttc" id="a00304_html_gab0eb2d65514ee3e49905aa2caad8c0ad"><div class="ttname"><a href="a00304.html#gab0eb2d65514ee3e49905aa2caad8c0ad">glm::lowp_f64mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f64, lowp &gt; lowp_f64mat3x2</div><div class="ttdoc">Low double-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00753">fwd.hpp:753</a></div></div>
<div class="ttc" id="a00304_html_ga7a6f1929464dcc680b16381a4ee5f2cf"><div class="ttname"><a href="a00304.html#ga7a6f1929464dcc680b16381a4ee5f2cf">glm::highp_u32</a></div><div class="ttdeci">uint32 highp_u32</div><div class="ttdoc">High qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00119">fwd.hpp:119</a></div></div>
<div class="ttc" id="a00304_html_ga727675ac6b5d2fc699520e0059735e25"><div class="ttname"><a href="a00304.html#ga727675ac6b5d2fc699520e0059735e25">glm::highp_i32</a></div><div class="ttdeci">int32 highp_i32</div><div class="ttdoc">High qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00061">fwd.hpp:61</a></div></div>
<div class="ttc" id="a00304_html_ga3945dd6515d4498cb603e65ff867ab03"><div class="ttname"><a href="a00304.html#ga3945dd6515d4498cb603e65ff867ab03">glm::u64vec4</a></div><div class="ttdeci">vec&lt; 4, u64, defaultp &gt; u64vec4</div><div class="ttdoc">Default qualifier 64 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00402">fwd.hpp:402</a></div></div>
<div class="ttc" id="a00304_html_ga31c6ca0e074a44007f49a9a3720b18c8"><div class="ttname"><a href="a00304.html#ga31c6ca0e074a44007f49a9a3720b18c8">glm::f32vec4</a></div><div class="ttdeci">vec&lt; 4, f32, defaultp &gt; f32vec4</div><div class="ttdoc">Single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00464">fwd.hpp:464</a></div></div>
<div class="ttc" id="a00304_html_gae3ab5719fc4c1e966631dbbcba8d412a"><div class="ttname"><a href="a00304.html#gae3ab5719fc4c1e966631dbbcba8d412a">glm::f64mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f64, defaultp &gt; f64mat2x3</div><div class="ttdoc">Double-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00783">fwd.hpp:783</a></div></div>
<div class="ttc" id="a00304_html_ga66edb8de17b9235029472f043ae107e9"><div class="ttname"><a href="a00304.html#ga66edb8de17b9235029472f043ae107e9">glm::mediump_f64mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f64, mediump &gt; mediump_f64mat4x4</div><div class="ttdoc">Medium double-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00768">fwd.hpp:768</a></div></div>
<div class="ttc" id="a00304_html_gaa7f00459b9a2e5b2757e70afc0c189e1"><div class="ttname"><a href="a00304.html#gaa7f00459b9a2e5b2757e70afc0c189e1">glm::lowp_u16vec4</a></div><div class="ttdeci">vec&lt; 4, u16, lowp &gt; lowp_u16vec4</div><div class="ttdoc">Low qualifier 16 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00347">fwd.hpp:347</a></div></div>
<div class="ttc" id="a00304_html_ga256b12b650c3f2fb86878fd1c5db8bc3"><div class="ttname"><a href="a00304.html#ga256b12b650c3f2fb86878fd1c5db8bc3">glm::highp_uint32</a></div><div class="ttdeci">uint32 highp_uint32</div><div class="ttdoc">High qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00124">fwd.hpp:124</a></div></div>
<div class="ttc" id="a00304_html_ga766aed2871e6173a81011a877f398f04"><div class="ttname"><a href="a00304.html#ga766aed2871e6173a81011a877f398f04">glm::lowp_f32mat4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, lowp &gt; lowp_f32mat4</div><div class="ttdoc">Low single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00542">fwd.hpp:542</a></div></div>
<div class="ttc" id="a00304_html_ga2473d8bf3f4abf967c4d0e18175be6f7"><div class="ttname"><a href="a00304.html#ga2473d8bf3f4abf967c4d0e18175be6f7">glm::f64mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f64, defaultp &gt; f64mat3x2</div><div class="ttdoc">Double-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00781">fwd.hpp:781</a></div></div>
<div class="ttc" id="a00304_html_ga7812bf00676fb1a86dcd62cca354d2c7"><div class="ttname"><a href="a00304.html#ga7812bf00676fb1a86dcd62cca354d2c7">glm::mediump_float32</a></div><div class="ttdeci">float mediump_float32</div><div class="ttdoc">Medium 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00153">fwd.hpp:153</a></div></div>
<div class="ttc" id="a00304_html_gae627372cfd5f20dd87db490387b71195"><div class="ttname"><a href="a00304.html#gae627372cfd5f20dd87db490387b71195">glm::u32vec1</a></div><div class="ttdeci">vec&lt; 1, u32, defaultp &gt; u32vec1</div><div class="ttdoc">Default qualifier 32 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00379">fwd.hpp:379</a></div></div>
<div class="ttc" id="a00304_html_gacfea053bd6bb3eddb996a4f94de22a3e"><div class="ttname"><a href="a00304.html#gacfea053bd6bb3eddb996a4f94de22a3e">glm::mediump_f64vec4</a></div><div class="ttdeci">vec&lt; 4, f64, mediump &gt; mediump_f64vec4</div><div class="ttdoc">Medium double-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00494">fwd.hpp:494</a></div></div>
<div class="ttc" id="a00304_html_ga916c1aed91cf91f7b41399ebe7c6e185"><div class="ttname"><a href="a00304.html#ga916c1aed91cf91f7b41399ebe7c6e185">glm::f64mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f64, defaultp &gt; f64mat3x3</div><div class="ttdoc">Double-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00784">fwd.hpp:784</a></div></div>
<div class="ttc" id="a00304_html_gac5a7f21136e0a78d0a1b9f60ef2f8aea"><div class="ttname"><a href="a00304.html#gac5a7f21136e0a78d0a1b9f60ef2f8aea">glm::highp_float32</a></div><div class="ttdeci">float highp_float32</div><div class="ttdoc">High 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00154">fwd.hpp:154</a></div></div>
<div class="ttc" id="a00304_html_ga97432f9979e73e66567361fd01e4cffb"><div class="ttname"><a href="a00304.html#ga97432f9979e73e66567361fd01e4cffb">glm::highp_uint8</a></div><div class="ttdeci">uint8 highp_uint8</div><div class="ttdoc">High qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00096">fwd.hpp:96</a></div></div>
<div class="ttc" id="a00304_html_gacb88796f2d08ef253d0345aff20c3aee"><div class="ttname"><a href="a00304.html#gacb88796f2d08ef253d0345aff20c3aee">glm::highp_i8</a></div><div class="ttdeci">int8 highp_i8</div><div class="ttdoc">High qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00033">fwd.hpp:33</a></div></div>
<div class="ttc" id="a00304_html_ga50265a202fbfe0a25fc70066c31d9336"><div class="ttname"><a href="a00304.html#ga50265a202fbfe0a25fc70066c31d9336">glm::lowp_f64mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f64, lowp &gt; lowp_f64mat2x4</div><div class="ttdoc">Low double-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00752">fwd.hpp:752</a></div></div>
<div class="ttc" id="a00304_html_gade8d1edfb23996ab6c622e65e3893271"><div class="ttname"><a href="a00304.html#gade8d1edfb23996ab6c622e65e3893271">glm::lowp_f64mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f64, lowp &gt; lowp_f64mat3x4</div><div class="ttdoc">Low double-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00755">fwd.hpp:755</a></div></div>
<div class="ttc" id="a00304_html_gacf1ded173e1e2d049c511d095b259e21"><div class="ttname"><a href="a00304.html#gacf1ded173e1e2d049c511d095b259e21">glm::mediump_i8</a></div><div class="ttdeci">int8 mediump_i8</div><div class="ttdoc">Medium qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00032">fwd.hpp:32</a></div></div>
<div class="ttc" id="a00304_html_ga73c6abb280a45feeff60f9accaee91f3"><div class="ttname"><a href="a00304.html#ga73c6abb280a45feeff60f9accaee91f3">glm::highp_int64_t</a></div><div class="ttdeci">int64 highp_int64_t</div><div class="ttdoc">High qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00084">fwd.hpp:84</a></div></div>
<div class="ttc" id="a00304_html_gaee4da0e9fbd8cfa2f89cb80889719dc3"><div class="ttname"><a href="a00304.html#gaee4da0e9fbd8cfa2f89cb80889719dc3">glm::f32mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, defaultp &gt; f32mat4x4</div><div class="ttdoc">Single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00708">fwd.hpp:708</a></div></div>
<div class="ttc" id="a00304_html_gaa4947bc8b47c72fceea9bda730ecf603"><div class="ttname"><a href="a00304.html#gaa4947bc8b47c72fceea9bda730ecf603">glm::float32_t</a></div><div class="ttdeci">float float32_t</div><div class="ttdoc">Default 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00160">fwd.hpp:160</a></div></div>
<div class="ttc" id="a00304_html_ga04100c76f7d55a0dd0983ccf05142bff"><div class="ttname"><a href="a00304.html#ga04100c76f7d55a0dd0983ccf05142bff">glm::f32mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, defaultp &gt; f32mat2x2</div><div class="ttdoc">Single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00700">fwd.hpp:700</a></div></div>
<div class="ttc" id="a00304_html_gab485c48f06a4fdd6b8d58d343bb49f3c"><div class="ttname"><a href="a00304.html#gab485c48f06a4fdd6b8d58d343bb49f3c">glm::lowp_i64vec2</a></div><div class="ttdeci">vec&lt; 2, i64, lowp &gt; lowp_i64vec2</div><div class="ttdoc">Low qualifier 64 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00283">fwd.hpp:283</a></div></div>
<div class="ttc" id="a00304_html_gacc6bf0209dda0c7c14851a646071c974"><div class="ttname"><a href="a00304.html#gacc6bf0209dda0c7c14851a646071c974">glm::lowp_f32mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, lowp &gt; lowp_f32mat2x4</div><div class="ttdoc">Low single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00672">fwd.hpp:672</a></div></div>
<div class="ttc" id="a00304_html_ga2171d9dc1fefb1c82e2817f45b622eac"><div class="ttname"><a href="a00304.html#ga2171d9dc1fefb1c82e2817f45b622eac">glm::uint32_t</a></div><div class="ttdeci">uint32 uint32_t</div><div class="ttdoc">Default qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00129">fwd.hpp:129</a></div></div>
<div class="ttc" id="a00304_html_ga24f9ef3263b1638564713892cc37981f"><div class="ttname"><a href="a00304.html#ga24f9ef3263b1638564713892cc37981f">glm::highp_f32mat3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, highp &gt; highp_f32mat3</div><div class="ttdoc">High single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00549">fwd.hpp:549</a></div></div>
<div class="ttc" id="a00304_html_ga84d88a6e3a54ccd2b67e195af4a4c23e"><div class="ttname"><a href="a00304.html#ga84d88a6e3a54ccd2b67e195af4a4c23e">glm::mediump_f64mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f64, mediump &gt; mediump_f64mat3x3</div><div class="ttdoc">Medium double-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00764">fwd.hpp:764</a></div></div>
<div class="ttc" id="a00304_html_gaecc7082561fc9028b844b6cf3d305d36"><div class="ttname"><a href="a00304.html#gaecc7082561fc9028b844b6cf3d305d36">glm::u8</a></div><div class="ttdeci">uint8 u8</div><div class="ttdoc">Default qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00092">fwd.hpp:92</a></div></div>
<div class="ttc" id="a00304_html_ga31e070ea3bdee623e6e18a61ba5718b1"><div class="ttname"><a href="a00304.html#ga31e070ea3bdee623e6e18a61ba5718b1">glm::highp_i32vec3</a></div><div class="ttdeci">vec&lt; 3, i32, highp &gt; highp_i32vec3</div><div class="ttdoc">High qualifier 32 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00274">fwd.hpp:274</a></div></div>
<div class="ttc" id="a00304_html_gaacdc525d6f7bddb3ae95d5c311bd06a1"><div class="ttname"><a href="a00304.html#gaacdc525d6f7bddb3ae95d5c311bd06a1">glm::float32</a></div><div class="ttdeci">float float32</div><div class="ttdoc">Single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00155">fwd.hpp:155</a></div></div>
<div class="ttc" id="a00304_html_ga713c796c54875cf4092d42ff9d9096b0"><div class="ttname"><a href="a00304.html#ga713c796c54875cf4092d42ff9d9096b0">glm::fvec4</a></div><div class="ttdeci">vec&lt; 4, f32, defaultp &gt; fvec4</div><div class="ttdoc">Single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00444">fwd.hpp:444</a></div></div>
<div class="ttc" id="a00304_html_ga6a9d71cc62745302f70422b7dc98755c"><div class="ttname"><a href="a00304.html#ga6a9d71cc62745302f70422b7dc98755c">glm::highp_i32vec1</a></div><div class="ttdeci">vec&lt; 1, i32, highp &gt; highp_i32vec1</div><div class="ttdoc">High qualifier 32 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00272">fwd.hpp:272</a></div></div>
<div class="ttc" id="a00304_html_ga4187f89f196505b40e63f516139511e5"><div class="ttname"><a href="a00304.html#ga4187f89f196505b40e63f516139511e5">glm::lowp_f32mat3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, lowp &gt; lowp_f32mat3</div><div class="ttdoc">Low single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00541">fwd.hpp:541</a></div></div>
<div class="ttc" id="a00304_html_ga08c05ba8ffb19f5d14ab584e1e9e9ee5"><div class="ttname"><a href="a00304.html#ga08c05ba8ffb19f5d14ab584e1e9e9ee5">glm::u16vec1</a></div><div class="ttdeci">vec&lt; 1, u16, defaultp &gt; u16vec1</div><div class="ttdoc">Default qualifier 16 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00359">fwd.hpp:359</a></div></div>
<div class="ttc" id="a00304_html_ga7e80d927ff0a3861ced68dfff8a4020b"><div class="ttname"><a href="a00304.html#ga7e80d927ff0a3861ced68dfff8a4020b">glm::i8vec1</a></div><div class="ttdeci">vec&lt; 1, i8, defaultp &gt; i8vec1</div><div class="ttdoc">8 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00237">fwd.hpp:237</a></div></div>
<div class="ttc" id="a00304_html_ga13a221b910aa9eb1b04ca1c86e81015a"><div class="ttname"><a href="a00304.html#ga13a221b910aa9eb1b04ca1c86e81015a">glm::mediump_i32vec3</a></div><div class="ttdeci">vec&lt; 3, i32, mediump &gt; mediump_i32vec3</div><div class="ttdoc">Medium qualifier 32 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00269">fwd.hpp:269</a></div></div>
<div class="ttc" id="a00304_html_ga8b44026374982dcd1e52d22bac99247e"><div class="ttname"><a href="a00304.html#ga8b44026374982dcd1e52d22bac99247e">glm::i32vec2</a></div><div class="ttdeci">vec&lt; 2, i32, defaultp &gt; i32vec2</div><div class="ttdoc">32 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00278">fwd.hpp:278</a></div></div>
<div class="ttc" id="a00304_html_ga7cac84b520a6b57f2fbd880d3d63c51b"><div class="ttname"><a href="a00304.html#ga7cac84b520a6b57f2fbd880d3d63c51b">glm::lowp_i16vec2</a></div><div class="ttdeci">vec&lt; 2, i16, lowp &gt; lowp_i16vec2</div><div class="ttdoc">Low qualifier 16 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00243">fwd.hpp:243</a></div></div>
<div class="ttc" id="a00304_html_ga73a08ef5a74798f3a1a99250b5f86a7d"><div class="ttname"><a href="a00304.html#ga73a08ef5a74798f3a1a99250b5f86a7d">glm::mediump_u64vec2</a></div><div class="ttdeci">vec&lt; 2, u64, mediump &gt; mediump_u64vec2</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00390">fwd.hpp:390</a></div></div>
<div class="ttc" id="a00304_html_ga9910927f3a4d1addb3da6a82542a8287"><div class="ttname"><a href="a00304.html#ga9910927f3a4d1addb3da6a82542a8287">glm::lowp_u8vec4</a></div><div class="ttdeci">vec&lt; 4, u8, lowp &gt; lowp_u8vec4</div><div class="ttdoc">Low qualifier 8 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00327">fwd.hpp:327</a></div></div>
<div class="ttc" id="a00304_html_gaab691ae40c37976d268d8cac0096e0e1"><div class="ttname"><a href="a00304.html#gaab691ae40c37976d268d8cac0096e0e1">glm::highp_f32mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, highp &gt; highp_f32mat3x3</div><div class="ttdoc">High single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00694">fwd.hpp:694</a></div></div>
<div class="ttc" id="a00304_html_ga8408cb76b6550ff01fa0a3024e7b68d2"><div class="ttname"><a href="a00304.html#ga8408cb76b6550ff01fa0a3024e7b68d2">glm::highp_u8vec1</a></div><div class="ttdeci">vec&lt; 1, u8, highp &gt; highp_u8vec1</div><div class="ttdoc">High qualifier 8 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00334">fwd.hpp:334</a></div></div>
<div class="ttc" id="a00304_html_gac4e00a26a2adb5f2c0a7096810df29e5"><div class="ttname"><a href="a00304.html#gac4e00a26a2adb5f2c0a7096810df29e5">glm::highp_uint8_t</a></div><div class="ttdeci">uint8 highp_uint8_t</div><div class="ttdoc">High qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00100">fwd.hpp:100</a></div></div>
<div class="ttc" id="a00304_html_ga5d43ee8b5dbaa06c327b03b83682598a"><div class="ttname"><a href="a00304.html#ga5d43ee8b5dbaa06c327b03b83682598a">glm::mediump_u32vec4</a></div><div class="ttdeci">vec&lt; 4, u32, mediump &gt; mediump_u32vec4</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00372">fwd.hpp:372</a></div></div>
<div class="ttc" id="a00304_html_gae5eb02d92b7d4605a4b7f37ae5cb2968"><div class="ttname"><a href="a00304.html#gae5eb02d92b7d4605a4b7f37ae5cb2968">glm::highp_f32mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, highp &gt; highp_f32mat2x2</div><div class="ttdoc">High single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00690">fwd.hpp:690</a></div></div>
<div class="ttc" id="a00304_html_ga61185c44c8cc0b25d9a0f67d8a267444"><div class="ttname"><a href="a00304.html#ga61185c44c8cc0b25d9a0f67d8a267444">glm::highp_f64vec4</a></div><div class="ttdeci">vec&lt; 4, f64, highp &gt; highp_f64vec4</div><div class="ttdoc">High double-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00499">fwd.hpp:499</a></div></div>
<div class="ttc" id="a00304_html_ga150dc47e31c6b8cf8461803c8d56f7bd"><div class="ttname"><a href="a00304.html#ga150dc47e31c6b8cf8461803c8d56f7bd">glm::lowp_u8vec3</a></div><div class="ttdeci">vec&lt; 3, u8, lowp &gt; lowp_u8vec3</div><div class="ttdoc">Low qualifier 8 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00326">fwd.hpp:326</a></div></div>
<div class="ttc" id="a00304_html_ga6906e1ef0b34064b4b675489c5c38725"><div class="ttname"><a href="a00304.html#ga6906e1ef0b34064b4b675489c5c38725">glm::highp_f32</a></div><div class="ttdeci">float highp_f32</div><div class="ttdoc">High 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00149">fwd.hpp:149</a></div></div>
<div class="ttc" id="a00304_html_ga30652709815ad9404272a31957daa59e"><div class="ttname"><a href="a00304.html#ga30652709815ad9404272a31957daa59e">glm::mediump_uint64</a></div><div class="ttdeci">uint64 mediump_uint64</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00137">fwd.hpp:137</a></div></div>
<div class="ttc" id="a00304_html_ga2c71c8bd9e2fe7d2e93ca250d8b6157f"><div class="ttname"><a href="a00304.html#ga2c71c8bd9e2fe7d2e93ca250d8b6157f">glm::highp_int32_t</a></div><div class="ttdeci">int32 highp_int32_t</div><div class="ttdoc">32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00070">fwd.hpp:70</a></div></div>
<div class="ttc" id="a00304_html_gaa7a1ddca75c5f629173bf4772db7a635"><div class="ttname"><a href="a00304.html#gaa7a1ddca75c5f629173bf4772db7a635">glm::f64vec3</a></div><div class="ttdeci">vec&lt; 3, f64, defaultp &gt; f64vec3</div><div class="ttdoc">Double-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00503">fwd.hpp:503</a></div></div>
<div class="ttc" id="a00304_html_ga961ccb34cd1a5654c772c8709e001dc5"><div class="ttname"><a href="a00304.html#ga961ccb34cd1a5654c772c8709e001dc5">glm::lowp_f32mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, lowp &gt; lowp_f32mat2x3</div><div class="ttdoc">Low single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00671">fwd.hpp:671</a></div></div>
<div class="ttc" id="a00304_html_gac9ba20234b0c3751d45ce575fc71e551"><div class="ttname"><a href="a00304.html#gac9ba20234b0c3751d45ce575fc71e551">glm::mediump_u16vec3</a></div><div class="ttdeci">vec&lt; 3, u16, mediump &gt; mediump_u16vec3</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00351">fwd.hpp:351</a></div></div>
<div class="ttc" id="a00304_html_gac87278e0c702ba8afff76316d4eeb769"><div class="ttname"><a href="a00304.html#gac87278e0c702ba8afff76316d4eeb769">glm::f64mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f64, defaultp &gt; f64mat2x4</div><div class="ttdoc">Double-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00786">fwd.hpp:786</a></div></div>
<div class="ttc" id="a00304_html_ga856f3905ee7cc2e4890a8a1d56c150be"><div class="ttname"><a href="a00304.html#ga856f3905ee7cc2e4890a8a1d56c150be">glm::f32mat3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, defaultp &gt; f32mat3</div><div class="ttdoc">Single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00553">fwd.hpp:553</a></div></div>
<div class="ttc" id="a00304_html_ga4fd527644cccbca4cb205320eab026f3"><div class="ttname"><a href="a00304.html#ga4fd527644cccbca4cb205320eab026f3">glm::mediump_f64mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f64, mediump &gt; mediump_f64mat2x2</div><div class="ttdoc">Medium double-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00760">fwd.hpp:760</a></div></div>
<div class="ttc" id="a00304_html_ga2af9490085ae3bdf36a544e9dd073610"><div class="ttname"><a href="a00304.html#ga2af9490085ae3bdf36a544e9dd073610">glm::mediump_u64</a></div><div class="ttdeci">uint64 mediump_u64</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00132">fwd.hpp:132</a></div></div>
<div class="ttc" id="a00304_html_ga7a18659438828f91ccca28f1a1e067b4"><div class="ttname"><a href="a00304.html#ga7a18659438828f91ccca28f1a1e067b4">glm::highp_i16vec4</a></div><div class="ttdeci">vec&lt; 4, i16, highp &gt; highp_i16vec4</div><div class="ttdoc">High qualifier 16 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00255">fwd.hpp:255</a></div></div>
<div class="ttc" id="a00304_html_ga5803cb9ae26399762d8bba9e0b2fc09f"><div class="ttname"><a href="a00304.html#ga5803cb9ae26399762d8bba9e0b2fc09f">glm::lowp_fmat4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, lowp &gt; lowp_fmat4</div><div class="ttdoc">Low single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00526">fwd.hpp:526</a></div></div>
<div class="ttc" id="a00304_html_ga94f74851fce338549c705b5f0d601c4f"><div class="ttname"><a href="a00304.html#ga94f74851fce338549c705b5f0d601c4f">glm::mediump_u32vec2</a></div><div class="ttdeci">vec&lt; 2, u32, mediump &gt; mediump_u32vec2</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00370">fwd.hpp:370</a></div></div>
<div class="ttc" id="a00304_html_ga41bdabea6e589029659331ba47eb78c1"><div class="ttname"><a href="a00304.html#ga41bdabea6e589029659331ba47eb78c1">glm::highp_u64vec3</a></div><div class="ttdeci">vec&lt; 3, u64, highp &gt; highp_u64vec3</div><div class="ttdoc">High qualifier 64 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00396">fwd.hpp:396</a></div></div>
<div class="ttc" id="a00304_html_ga504ce1631cb2ac02fcf1d44d8c2aa126"><div class="ttname"><a href="a00304.html#ga504ce1631cb2ac02fcf1d44d8c2aa126">glm::lowp_u16</a></div><div class="ttdeci">uint16 lowp_u16</div><div class="ttdoc">Low qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00103">fwd.hpp:103</a></div></div>
<div class="ttc" id="a00304_html_gab69ef9cbc2a9214bf5596c528c801b72"><div class="ttname"><a href="a00304.html#gab69ef9cbc2a9214bf5596c528c801b72">glm::lowp_i16vec3</a></div><div class="ttdeci">vec&lt; 3, i16, lowp &gt; lowp_i16vec3</div><div class="ttdoc">Low qualifier 16 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00244">fwd.hpp:244</a></div></div>
<div class="ttc" id="a00304_html_gac0253db6c3d3bae1f591676307a9dd8c"><div class="ttname"><a href="a00304.html#gac0253db6c3d3bae1f591676307a9dd8c">glm::lowp_u16vec3</a></div><div class="ttdeci">vec&lt; 3, u16, lowp &gt; lowp_u16vec3</div><div class="ttdoc">Low qualifier 16 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00346">fwd.hpp:346</a></div></div>
<div class="ttc" id="a00304_html_gaf739a2cd7b81783a43148b53e40d983b"><div class="ttname"><a href="a00304.html#gaf739a2cd7b81783a43148b53e40d983b">glm::lowp_f32vec3</a></div><div class="ttdeci">vec&lt; 3, f32, lowp &gt; lowp_f32vec3</div><div class="ttdoc">Low single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00448">fwd.hpp:448</a></div></div>
<div class="ttc" id="a00304_html_gabf28443ce0cc0959077ec39b21f32c39"><div class="ttname"><a href="a00304.html#gabf28443ce0cc0959077ec39b21f32c39">glm::highp_fmat4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, highp &gt; highp_fmat4</div><div class="ttdoc">High single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00534">fwd.hpp:534</a></div></div>
<div class="ttc" id="a00304_html_ga83079315e230e8f39728f4bf0d2f9a9b"><div class="ttname"><a href="a00304.html#ga83079315e230e8f39728f4bf0d2f9a9b">glm::lowp_fmat3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, lowp &gt; lowp_fmat3</div><div class="ttdoc">Low single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00525">fwd.hpp:525</a></div></div>
<div class="ttc" id="a00304_html_ga0336abc2604dd2c20c30e036454b64f8"><div class="ttname"><a href="a00304.html#ga0336abc2604dd2c20c30e036454b64f8">glm::highp_i16</a></div><div class="ttdeci">int16 highp_i16</div><div class="ttdoc">High qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00047">fwd.hpp:47</a></div></div>
<div class="ttc" id="a00304_html_gaa40c03d52dbfbfaf03e75773b9606ff3"><div class="ttname"><a href="a00304.html#gaa40c03d52dbfbfaf03e75773b9606ff3">glm::mediump_f32quat</a></div><div class="ttdeci">qua&lt; f32, mediump &gt; mediump_f32quat</div><div class="ttdoc">Medium single-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00803">fwd.hpp:803</a></div></div>
<div class="ttc" id="a00304_html_gad0549c902a96a7164e4ac858d5f39dbf"><div class="ttname"><a href="a00304.html#gad0549c902a96a7164e4ac858d5f39dbf">glm::highp_int8</a></div><div class="ttdeci">int8 highp_int8</div><div class="ttdoc">High qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00038">fwd.hpp:38</a></div></div>
<div class="ttc" id="a00304_html_gae52e2b7077a9ff928a06ab5ce600b81e"><div class="ttname"><a href="a00304.html#gae52e2b7077a9ff928a06ab5ce600b81e">glm::f64mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f64, defaultp &gt; f64mat4x4</div><div class="ttdoc">Double-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00788">fwd.hpp:788</a></div></div>
<div class="ttc" id="a00304_html_ga4295048a78bdf46b8a7de77ec665b497"><div class="ttname"><a href="a00304.html#ga4295048a78bdf46b8a7de77ec665b497">glm::fmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, defaultp &gt; fmat4x3</div><div class="ttdoc">Single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00665">fwd.hpp:665</a></div></div>
<div class="ttc" id="a00304_html_gaab217601c74974a84acbca428123ecf7"><div class="ttname"><a href="a00304.html#gaab217601c74974a84acbca428123ecf7">glm::lowp_fmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, lowp &gt; lowp_fmat2x4</div><div class="ttdoc">Low single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00632">fwd.hpp:632</a></div></div>
<div class="ttc" id="a00304_html_ga609bf0ace941d6ab1bb2f9522a04e546"><div class="ttname"><a href="a00304.html#ga609bf0ace941d6ab1bb2f9522a04e546">glm::highp_f64mat3</a></div><div class="ttdeci">mat&lt; 3, 3, f64, highp &gt; highp_f64mat3</div><div class="ttdoc">High double-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00581">fwd.hpp:581</a></div></div>
<div class="ttc" id="a00304_html_ga6dd1c1618378c6f94d522a61c28773c9"><div class="ttname"><a href="a00304.html#ga6dd1c1618378c6f94d522a61c28773c9">glm::mediump_i8vec3</a></div><div class="ttdeci">vec&lt; 3, i8, mediump &gt; mediump_i8vec3</div><div class="ttdoc">Medium qualifier 8 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00229">fwd.hpp:229</a></div></div>
<div class="ttc" id="a00304_html_gab1b1c9e8667902b78b2c330e4d383a61"><div class="ttname"><a href="a00304.html#gab1b1c9e8667902b78b2c330e4d383a61">glm::highp_f32vec1</a></div><div class="ttdeci">vec&lt; 1, f32, highp &gt; highp_f32vec1</div><div class="ttdoc">High single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00456">fwd.hpp:456</a></div></div>
<div class="ttc" id="a00304_html_gae2f43ace6b5b33ab49516d9e40af1845"><div class="ttname"><a href="a00304.html#gae2f43ace6b5b33ab49516d9e40af1845">glm::lowp_i8vec3</a></div><div class="ttdeci">vec&lt; 3, i8, lowp &gt; lowp_i8vec3</div><div class="ttdoc">Low qualifier 8 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00224">fwd.hpp:224</a></div></div>
<div class="ttc" id="a00304_html_gae731613b25db3a5ef5a05d21e57a57d3"><div class="ttname"><a href="a00304.html#gae731613b25db3a5ef5a05d21e57a57d3">glm::lowp_f64mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f64, lowp &gt; lowp_f64mat4x3</div><div class="ttdoc">Low double-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00757">fwd.hpp:757</a></div></div>
<div class="ttc" id="a00304_html_ga4f15b41aa24b11cc42ad5798c04a2325"><div class="ttname"><a href="a00304.html#ga4f15b41aa24b11cc42ad5798c04a2325">glm::highp_u64vec4</a></div><div class="ttdeci">vec&lt; 4, u64, highp &gt; highp_u64vec4</div><div class="ttdoc">High qualifier 64 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00397">fwd.hpp:397</a></div></div>
<div class="ttc" id="a00304_html_ga89930533646b30d021759298aa6bf04a"><div class="ttname"><a href="a00304.html#ga89930533646b30d021759298aa6bf04a">glm::fvec3</a></div><div class="ttdeci">vec&lt; 3, f32, defaultp &gt; fvec3</div><div class="ttdoc">Single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00443">fwd.hpp:443</a></div></div>
<div class="ttc" id="a00304_html_ga2996630ba7b10535af8e065cf326f761"><div class="ttname"><a href="a00304.html#ga2996630ba7b10535af8e065cf326f761">glm::i16vec2</a></div><div class="ttdeci">vec&lt; 2, i16, defaultp &gt; i16vec2</div><div class="ttdoc">16 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00258">fwd.hpp:258</a></div></div>
<div class="ttc" id="a00304_html_ga239b96198771b7add8eea7e6b59840c0"><div class="ttname"><a href="a00304.html#ga239b96198771b7add8eea7e6b59840c0">glm::f32mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, defaultp &gt; f32mat4x3</div><div class="ttdoc">Single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00705">fwd.hpp:705</a></div></div>
<div class="ttc" id="a00304_html_gab12383ed6ac7595ed6fde4d266c58425"><div class="ttname"><a href="a00304.html#gab12383ed6ac7595ed6fde4d266c58425">glm::f32mat2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, defaultp &gt; f32mat2</div><div class="ttdoc">Single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00552">fwd.hpp:552</a></div></div>
<div class="ttc" id="a00304_html_ga429c201b3e92c90b4ef4356f2be52ee1"><div class="ttname"><a href="a00304.html#ga429c201b3e92c90b4ef4356f2be52ee1">glm::mediump_u16vec2</a></div><div class="ttdeci">vec&lt; 2, u16, mediump &gt; mediump_u16vec2</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00350">fwd.hpp:350</a></div></div>
<div class="ttc" id="a00304_html_ga35223623e9ccebd8a281873b71b7d213"><div class="ttname"><a href="a00304.html#ga35223623e9ccebd8a281873b71b7d213">glm::mediump_fmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, mediump &gt; mediump_fmat2x4</div><div class="ttdoc">Medium single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00642">fwd.hpp:642</a></div></div>
<div class="ttc" id="a00304_html_ga4143d129898f91545948c46859adce44"><div class="ttname"><a href="a00304.html#ga4143d129898f91545948c46859adce44">glm::lowp_f32mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, lowp &gt; lowp_f32mat4x4</div><div class="ttdoc">Low single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00678">fwd.hpp:678</a></div></div>
<div class="ttc" id="a00304_html_gae6f63fa38635431e51a8f2602f15c566"><div class="ttname"><a href="a00304.html#gae6f63fa38635431e51a8f2602f15c566">glm::lowp_u8vec2</a></div><div class="ttdeci">vec&lt; 2, u8, lowp &gt; lowp_u8vec2</div><div class="ttdoc">Low qualifier 8 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00325">fwd.hpp:325</a></div></div>
<div class="ttc" id="a00304_html_gad35fb67eb1d03c5a514f0bd7aed1c776"><div class="ttname"><a href="a00304.html#gad35fb67eb1d03c5a514f0bd7aed1c776">glm::mediump_f64mat3</a></div><div class="ttdeci">mat&lt; 3, 3, f64, mediump &gt; mediump_f64mat3</div><div class="ttdoc">Medium double-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00577">fwd.hpp:577</a></div></div>
<div class="ttc" id="a00304_html_ga392b673fd10847bfb78fb808c6cf8ff7"><div class="ttname"><a href="a00304.html#ga392b673fd10847bfb78fb808c6cf8ff7">glm::lowp_i16</a></div><div class="ttdeci">int16 lowp_i16</div><div class="ttdoc">Low qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00045">fwd.hpp:45</a></div></div>
<div class="ttc" id="a00304_html_ga961fac2a885907ffcf4d40daac6615c5"><div class="ttname"><a href="a00304.html#ga961fac2a885907ffcf4d40daac6615c5">glm::highp_fmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, highp &gt; highp_fmat3x4</div><div class="ttdoc">High single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00655">fwd.hpp:655</a></div></div>
<div class="ttc" id="a00304_html_ga728366fef72cd96f0a5fa6429f05469e"><div class="ttname"><a href="a00304.html#ga728366fef72cd96f0a5fa6429f05469e">glm::float64_t</a></div><div class="ttdeci">double float64_t</div><div class="ttdoc">Default 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00176">fwd.hpp:176</a></div></div>
<div class="ttc" id="a00304_html_ga09a2374b725c4246d263ee36fb66434c"><div class="ttname"><a href="a00304.html#ga09a2374b725c4246d263ee36fb66434c">glm::highp_f64mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f64, highp &gt; highp_f64mat4x4</div><div class="ttdoc">High double-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00778">fwd.hpp:778</a></div></div>
<div class="ttc" id="a00304_html_gade108f16633cf95fa500b5b8c36c8b00"><div class="ttname"><a href="a00304.html#gade108f16633cf95fa500b5b8c36c8b00">glm::mediump_f32mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, mediump &gt; mediump_f32mat4x3</div><div class="ttdoc">Medium single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00687">fwd.hpp:687</a></div></div>
<div class="ttc" id="a00304_html_ga698e36b01167fc0f037889334dce8def"><div class="ttname"><a href="a00304.html#ga698e36b01167fc0f037889334dce8def">glm::lowp_int16</a></div><div class="ttdeci">int16 lowp_int16</div><div class="ttdoc">Low qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00050">fwd.hpp:50</a></div></div>
<div class="ttc" id="a00304_html_ga80823dfad5dba98512c76af498343847"><div class="ttname"><a href="a00304.html#ga80823dfad5dba98512c76af498343847">glm::mediump_fmat3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, mediump &gt; mediump_fmat3</div><div class="ttdoc">Medium single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00529">fwd.hpp:529</a></div></div>
<div class="ttc" id="a00304_html_gaf1b712b97b2322685fbbed28febe5f84"><div class="ttname"><a href="a00304.html#gaf1b712b97b2322685fbbed28febe5f84">glm::highp_f32mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, highp &gt; highp_f32mat4x4</div><div class="ttdoc">High single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00698">fwd.hpp:698</a></div></div>
<div class="ttc" id="a00304_html_gaebf341fc4a5be233f7dde962c2e33847"><div class="ttname"><a href="a00304.html#gaebf341fc4a5be233f7dde962c2e33847">glm::lowp_int64_t</a></div><div class="ttdeci">int64 lowp_int64_t</div><div class="ttdoc">Low qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00082">fwd.hpp:82</a></div></div>
<div class="ttc" id="a00304_html_ga91f91f411080c37730856ff5887f5bcf"><div class="ttname"><a href="a00304.html#ga91f91f411080c37730856ff5887f5bcf">glm::uint16_t</a></div><div class="ttdeci">uint16 uint16_t</div><div class="ttdoc">Default qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00115">fwd.hpp:115</a></div></div>
<div class="ttc" id="a00304_html_ga670ea1b0a1172bc73b1d7c1e0c26cce2"><div class="ttname"><a href="a00304.html#ga670ea1b0a1172bc73b1d7c1e0c26cce2">glm::highp_f64vec2</a></div><div class="ttdeci">vec&lt; 2, f64, highp &gt; highp_f64vec2</div><div class="ttdoc">High double-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00497">fwd.hpp:497</a></div></div>
<div class="ttc" id="a00304_html_ga581485db4ba6ddb501505ee711fd8e42"><div class="ttname"><a href="a00304.html#ga581485db4ba6ddb501505ee711fd8e42">glm::lowp_u64vec2</a></div><div class="ttdeci">vec&lt; 2, u64, lowp &gt; lowp_u64vec2</div><div class="ttdoc">Low qualifier 64 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00385">fwd.hpp:385</a></div></div>
<div class="ttc" id="a00304_html_ga253d453c20e037730023fea0215cb6f6"><div class="ttname"><a href="a00304.html#ga253d453c20e037730023fea0215cb6f6">glm::fmat3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, defaultp &gt; fmat3</div><div class="ttdoc">Single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00537">fwd.hpp:537</a></div></div>
<div class="ttc" id="a00304_html_gaaab39454f56cf9fc6d940358ce5e6a0f"><div class="ttname"><a href="a00304.html#gaaab39454f56cf9fc6d940358ce5e6a0f">glm::mediump_f32mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, mediump &gt; mediump_f32mat3x2</div><div class="ttdoc">Medium single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00683">fwd.hpp:683</a></div></div>
<div class="ttc" id="a00304_html_gae6f3fcb702a666de07650c149cfa845a"><div class="ttname"><a href="a00304.html#gae6f3fcb702a666de07650c149cfa845a">glm::lowp_f32mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, lowp &gt; lowp_f32mat4x2</div><div class="ttdoc">Low single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00676">fwd.hpp:676</a></div></div>
<div class="ttc" id="a00304_html_ga864aabca5f3296e176e0c3ed9cc16b02"><div class="ttname"><a href="a00304.html#ga864aabca5f3296e176e0c3ed9cc16b02">glm::lowp_int32</a></div><div class="ttdeci">int32 lowp_int32</div><div class="ttdoc">Low qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00064">fwd.hpp:64</a></div></div>
<div class="ttc" id="a00304_html_gabf1f16c5ab8cb0484bd1e846ae4368f1"><div class="ttname"><a href="a00304.html#gabf1f16c5ab8cb0484bd1e846ae4368f1">glm::mediump_i64vec4</a></div><div class="ttdeci">vec&lt; 4, i64, mediump &gt; mediump_i64vec4</div><div class="ttdoc">Medium qualifier 64 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00290">fwd.hpp:290</a></div></div>
<div class="ttc" id="a00304_html_ga28d97808322d3c92186e4a0c067d7e8e"><div class="ttname"><a href="a00304.html#ga28d97808322d3c92186e4a0c067d7e8e">glm::uint8_t</a></div><div class="ttdeci">uint8 uint8_t</div><div class="ttdoc">Default qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00101">fwd.hpp:101</a></div></div>
<div class="ttc" id="a00304_html_ga85e8893f4ae3630065690a9000c0c483"><div class="ttname"><a href="a00304.html#ga85e8893f4ae3630065690a9000c0c483">glm::mediump_i8vec1</a></div><div class="ttdeci">vec&lt; 1, i8, mediump &gt; mediump_i8vec1</div><div class="ttdoc">Medium qualifier 8 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00227">fwd.hpp:227</a></div></div>
<div class="ttc" id="a00304_html_ga26fc7ced1ad7ca5024f1c973c8dc9180"><div class="ttname"><a href="a00304.html#ga26fc7ced1ad7ca5024f1c973c8dc9180">glm::mediump_int32_t</a></div><div class="ttdeci">int32 mediump_int32_t</div><div class="ttdoc">Medium qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00069">fwd.hpp:69</a></div></div>
<div class="ttc" id="a00304_html_ga5376ef18dca9d248897c3363ef5a06b2"><div class="ttname"><a href="a00304.html#ga5376ef18dca9d248897c3363ef5a06b2">glm::highp_float32_t</a></div><div class="ttdeci">float highp_float32_t</div><div class="ttdoc">High 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00159">fwd.hpp:159</a></div></div>
<div class="ttc" id="a00304_html_ga65261fa8a21045c8646ddff114a56174"><div class="ttname"><a href="a00304.html#ga65261fa8a21045c8646ddff114a56174">glm::f32mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, defaultp &gt; f32mat3x3</div><div class="ttdoc">Single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00704">fwd.hpp:704</a></div></div>
<div class="ttc" id="a00304_html_gacd1259f3a9e8d2a9df5be2d74322ef9c"><div class="ttname"><a href="a00304.html#gacd1259f3a9e8d2a9df5be2d74322ef9c">glm::highp_u8</a></div><div class="ttdeci">uint8 highp_u8</div><div class="ttdoc">High qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00091">fwd.hpp:91</a></div></div>
<div class="ttc" id="a00304_html_ga1fa92a233b9110861cdbc8c2ccf0b5a3"><div class="ttname"><a href="a00304.html#ga1fa92a233b9110861cdbc8c2ccf0b5a3">glm::mediump_uint8</a></div><div class="ttdeci">uint8 mediump_uint8</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00095">fwd.hpp:95</a></div></div>
<div class="ttc" id="a00304_html_ga076961cf2d120c7168b957cb2ed107b3"><div class="ttname"><a href="a00304.html#ga076961cf2d120c7168b957cb2ed107b3">glm::highp_fmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, highp &gt; highp_fmat4x2</div><div class="ttdoc">High single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00656">fwd.hpp:656</a></div></div>
<div class="ttc" id="a00304_html_ga0b8ebd4262331e139ff257d7cf2a4b77"><div class="ttname"><a href="a00304.html#ga0b8ebd4262331e139ff257d7cf2a4b77">glm::highp_f32vec2</a></div><div class="ttdeci">vec&lt; 2, f32, highp &gt; highp_f32vec2</div><div class="ttdoc">High single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00457">fwd.hpp:457</a></div></div>
<div class="ttc" id="a00304_html_gac3bc41bcac61d1ba8f02a6f68ce23f64"><div class="ttname"><a href="a00304.html#gac3bc41bcac61d1ba8f02a6f68ce23f64">glm::mediump_int64_t</a></div><div class="ttdeci">int64 mediump_int64_t</div><div class="ttdoc">Medium qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00083">fwd.hpp:83</a></div></div>
<div class="ttc" id="a00304_html_gaa4a8682bec7ec8af666ef87fae38d5d1"><div class="ttname"><a href="a00304.html#gaa4a8682bec7ec8af666ef87fae38d5d1">glm::lowp_u64vec3</a></div><div class="ttdeci">vec&lt; 3, u64, lowp &gt; lowp_u64vec3</div><div class="ttdoc">Low qualifier 64 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00386">fwd.hpp:386</a></div></div>
<div class="ttc" id="a00304_html_ga773ea237a051827cfc20de960bc73ff0"><div class="ttname"><a href="a00304.html#ga773ea237a051827cfc20de960bc73ff0">glm::highp_f64mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f64, highp &gt; highp_f64mat2x2</div><div class="ttdoc">High double-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00770">fwd.hpp:770</a></div></div>
<div class="ttc" id="a00304_html_gaf14d7a50502464e7cbfa074f24684cb1"><div class="ttname"><a href="a00304.html#gaf14d7a50502464e7cbfa074f24684cb1">glm::highp_u32vec3</a></div><div class="ttdeci">vec&lt; 3, u32, highp &gt; highp_u32vec3</div><div class="ttdoc">High qualifier 32 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00376">fwd.hpp:376</a></div></div>
<div class="ttc" id="a00304_html_ga1085c50dd8fbeb5e7e609b1c127492a5"><div class="ttname"><a href="a00304.html#ga1085c50dd8fbeb5e7e609b1c127492a5">glm::highp_int8_t</a></div><div class="ttdeci">int8 highp_int8_t</div><div class="ttdoc">High qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00042">fwd.hpp:42</a></div></div>
<div class="ttc" id="a00304_html_gaa3ba60ef8f69c6aeb1629594eaa95347"><div class="ttname"><a href="a00304.html#gaa3ba60ef8f69c6aeb1629594eaa95347">glm::lowp_f32quat</a></div><div class="ttdeci">qua&lt; f32, lowp &gt; lowp_f32quat</div><div class="ttdoc">Low single-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00802">fwd.hpp:802</a></div></div>
<div class="ttc" id="a00304_html_gac9b2eb4296ffe50a32eacca9ed932c08"><div class="ttname"><a href="a00304.html#gac9b2eb4296ffe50a32eacca9ed932c08">glm::lowp_i32vec4</a></div><div class="ttdeci">vec&lt; 4, i32, lowp &gt; lowp_i32vec4</div><div class="ttdoc">Low qualifier 32 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00265">fwd.hpp:265</a></div></div>
<div class="ttc" id="a00304_html_ga70fdfcc1fd38084bde83c3f06a8b9f19"><div class="ttname"><a href="a00304.html#ga70fdfcc1fd38084bde83c3f06a8b9f19">glm::highp_i16vec1</a></div><div class="ttdeci">vec&lt; 1, i16, highp &gt; highp_i16vec1</div><div class="ttdoc">High qualifier 16 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00252">fwd.hpp:252</a></div></div>
<div class="ttc" id="a00304_html_gac2f5263708ac847b361a9841e74ddf9f"><div class="ttname"><a href="a00304.html#gac2f5263708ac847b361a9841e74ddf9f">glm::lowp_fmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, lowp &gt; lowp_fmat4x4</div><div class="ttdoc">Low single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00638">fwd.hpp:638</a></div></div>
<div class="ttc" id="a00304_html_ga1320a08e14fdff3821241eefab6947e9"><div class="ttname"><a href="a00304.html#ga1320a08e14fdff3821241eefab6947e9">glm::f32mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, defaultp &gt; f32mat3x2</div><div class="ttdoc">Single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00701">fwd.hpp:701</a></div></div>
<div class="ttc" id="a00304_html_ga841211b641cff1fcf861bdb14e5e4abc"><div class="ttname"><a href="a00304.html#ga841211b641cff1fcf861bdb14e5e4abc">glm::lowp_f32mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, lowp &gt; lowp_f32mat3x3</div><div class="ttdoc">Low single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00674">fwd.hpp:674</a></div></div>
<div class="ttc" id="a00304_html_gac03e5099d27eeaa74b6016ea435a1df2"><div class="ttname"><a href="a00304.html#gac03e5099d27eeaa74b6016ea435a1df2">glm::lowp_i8vec2</a></div><div class="ttdeci">vec&lt; 2, i8, lowp &gt; lowp_i8vec2</div><div class="ttdoc">Low qualifier 8 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00223">fwd.hpp:223</a></div></div>
<div class="ttc" id="a00304_html_ga866a05905c49912309ed1fa5f5980e61"><div class="ttname"><a href="a00304.html#ga866a05905c49912309ed1fa5f5980e61">glm::i32vec4</a></div><div class="ttdeci">vec&lt; 4, i32, defaultp &gt; i32vec4</div><div class="ttdoc">32 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00280">fwd.hpp:280</a></div></div>
<div class="ttc" id="a00304_html_ga298f7d4d273678d0282812368da27fda"><div class="ttname"><a href="a00304.html#ga298f7d4d273678d0282812368da27fda">glm::highp_f32mat2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, highp &gt; highp_f32mat2</div><div class="ttdoc">High single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00548">fwd.hpp:548</a></div></div>
<div class="ttc" id="a00304_html_gaeea53879fc327293cf3352a409b7867b"><div class="ttname"><a href="a00304.html#gaeea53879fc327293cf3352a409b7867b">glm::lowp_f32</a></div><div class="ttdeci">float lowp_f32</div><div class="ttdoc">Low 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00147">fwd.hpp:147</a></div></div>
<div class="ttc" id="a00304_html_ga5793393686ce5bd2d5968ff9144762b8"><div class="ttname"><a href="a00304.html#ga5793393686ce5bd2d5968ff9144762b8">glm::mediump_u16vec4</a></div><div class="ttdeci">vec&lt; 4, u16, mediump &gt; mediump_u16vec4</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00352">fwd.hpp:352</a></div></div>
<div class="ttc" id="a00304_html_gae267358ff2a41d156d97f5762630235a"><div class="ttname"><a href="a00304.html#gae267358ff2a41d156d97f5762630235a">glm::u32vec3</a></div><div class="ttdeci">vec&lt; 3, u32, defaultp &gt; u32vec3</div><div class="ttdoc">Default qualifier 32 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00381">fwd.hpp:381</a></div></div>
<div class="ttc" id="a00304_html_ga518b8d948a6b4ddb72f84d5c3b7b6611"><div class="ttname"><a href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">glm::u8vec2</a></div><div class="ttdeci">vec&lt; 2, u8, defaultp &gt; u8vec2</div><div class="ttdoc">Default qualifier 8 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00340">fwd.hpp:340</a></div></div>
<div class="ttc" id="a00304_html_ga62a17cddeb4dffb4e18fe3aea23f051a"><div class="ttname"><a href="a00304.html#ga62a17cddeb4dffb4e18fe3aea23f051a">glm::mediump_i16</a></div><div class="ttdeci">int16 mediump_i16</div><div class="ttdoc">Medium qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00046">fwd.hpp:46</a></div></div>
<div class="ttc" id="a00304_html_ga78f77b8b2d17b431ac5a68c0b5d7050d"><div class="ttname"><a href="a00304.html#ga78f77b8b2d17b431ac5a68c0b5d7050d">glm::highp_u64vec2</a></div><div class="ttdeci">vec&lt; 2, u64, highp &gt; highp_u64vec2</div><div class="ttdoc">High qualifier 64 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00395">fwd.hpp:395</a></div></div>
<div class="ttc" id="a00304_html_ga5a08d36cf7917cd19d081a603d0eae3e"><div class="ttname"><a href="a00304.html#ga5a08d36cf7917cd19d081a603d0eae3e">glm::i8vec3</a></div><div class="ttdeci">vec&lt; 3, i8, defaultp &gt; i8vec3</div><div class="ttdoc">8 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00239">fwd.hpp:239</a></div></div>
<div class="ttc" id="a00304_html_gaa3ca74a44102035b3ffb5c9c52dfdd3f"><div class="ttname"><a href="a00304.html#gaa3ca74a44102035b3ffb5c9c52dfdd3f">glm::mediump_f32mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, mediump &gt; mediump_f32mat2x2</div><div class="ttdoc">High single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00680">fwd.hpp:680</a></div></div>
<div class="ttc" id="a00304_html_ga3963b1050fc65a383ee28e3f827b6e3e"><div class="ttname"><a href="a00304.html#ga3963b1050fc65a383ee28e3f827b6e3e">glm::mediump_uint16_t</a></div><div class="ttdeci">uint16 mediump_uint16_t</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00113">fwd.hpp:113</a></div></div>
<div class="ttc" id="a00304_html_ga54697a78f9a4643af6a57fc2e626ec0d"><div class="ttname"><a href="a00304.html#ga54697a78f9a4643af6a57fc2e626ec0d">glm::mediump_f64mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f64, mediump &gt; mediump_f64mat4x3</div><div class="ttdoc">Medium double-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00767">fwd.hpp:767</a></div></div>
<div class="ttc" id="a00304_html_ga7c5706f6bbe5282e5598acf7e7b377e2"><div class="ttname"><a href="a00304.html#ga7c5706f6bbe5282e5598acf7e7b377e2">glm::u8vec3</a></div><div class="ttdeci">vec&lt; 3, u8, defaultp &gt; u8vec3</div><div class="ttdoc">Default qualifier 8 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00341">fwd.hpp:341</a></div></div>
<div class="ttc" id="a00304_html_ga51d5266017d88f62737c1973923a7cf4"><div class="ttname"><a href="a00304.html#ga51d5266017d88f62737c1973923a7cf4">glm::highp_f64</a></div><div class="ttdeci">double highp_f64</div><div class="ttdoc">High 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00165">fwd.hpp:165</a></div></div>
<div class="ttc" id="a00304_html_ga999dc6703ad16e3d3c26b74ea8083f07"><div class="ttname"><a href="a00304.html#ga999dc6703ad16e3d3c26b74ea8083f07">glm::mediump_fvec3</a></div><div class="ttdeci">vec&lt; 3, float, mediump &gt; mediump_fvec3</div><div class="ttdoc">Medium Single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00433">fwd.hpp:433</a></div></div>
<div class="ttc" id="a00304_html_ga7b968f2b86a0442a89c7359171e1d866"><div class="ttname"><a href="a00304.html#ga7b968f2b86a0442a89c7359171e1d866">glm::mediump_int64</a></div><div class="ttdeci">int64 mediump_int64</div><div class="ttdoc">Medium qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00079">fwd.hpp:79</a></div></div>
<div class="ttc" id="a00304_html_gaec7ee455cb379ec2993e81482123e1cc"><div class="ttname"><a href="a00304.html#gaec7ee455cb379ec2993e81482123e1cc">glm::mediump_u64vec4</a></div><div class="ttdeci">vec&lt; 4, u64, mediump &gt; mediump_u64vec4</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00392">fwd.hpp:392</a></div></div>
<div class="ttc" id="a00304_html_ga3999d3e7ff22025c16ddb601e14dfdee"><div class="ttname"><a href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">glm::uint64_t</a></div><div class="ttdeci">uint64 uint64_t</div><div class="ttdoc">Default qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00143">fwd.hpp:143</a></div></div>
<div class="ttc" id="a00304_html_ga2f256334f83fba4c2d219e414b51df6c"><div class="ttname"><a href="a00304.html#ga2f256334f83fba4c2d219e414b51df6c">glm::highp_u32vec2</a></div><div class="ttdeci">vec&lt; 2, u32, highp &gt; highp_u32vec2</div><div class="ttdoc">High qualifier 32 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00375">fwd.hpp:375</a></div></div>
<div class="ttc" id="a00304_html_gaa1040342c4efdedc8f90e6267db8d41c"><div class="ttname"><a href="a00304.html#gaa1040342c4efdedc8f90e6267db8d41c">glm::highp_fvec1</a></div><div class="ttdeci">vec&lt; 1, float, highp &gt; highp_fvec1</div><div class="ttdoc">High single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00436">fwd.hpp:436</a></div></div>
<div class="ttc" id="a00304_html_gabb4229a4c1488bf063eed0c45355bb9c"><div class="ttname"><a href="a00304.html#gabb4229a4c1488bf063eed0c45355bb9c">glm::lowp_i64vec4</a></div><div class="ttdeci">vec&lt; 4, i64, lowp &gt; lowp_i64vec4</div><div class="ttdoc">Low qualifier 64 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00285">fwd.hpp:285</a></div></div>
<div class="ttc" id="a00304_html_ga7f526b5cccef126a2ebcf9bdd890394e"><div class="ttname"><a href="a00304.html#ga7f526b5cccef126a2ebcf9bdd890394e">glm::i32vec3</a></div><div class="ttdeci">vec&lt; 3, i32, defaultp &gt; i32vec3</div><div class="ttdoc">32 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00279">fwd.hpp:279</a></div></div>
<div class="ttc" id="a00304_html_ga88938ee1e7981fa3402e88da6ad74531"><div class="ttname"><a href="a00304.html#ga88938ee1e7981fa3402e88da6ad74531">glm::highp_f32mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, highp &gt; highp_f32mat2x4</div><div class="ttdoc">High single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00692">fwd.hpp:692</a></div></div>
<div class="ttc" id="a00304_html_ga036d6c7ca9fbbdc5f3871bfcb937c85c"><div class="ttname"><a href="a00304.html#ga036d6c7ca9fbbdc5f3871bfcb937c85c">glm::lowp_i8vec1</a></div><div class="ttdeci">vec&lt; 1, i8, lowp &gt; lowp_i8vec1</div><div class="ttdoc">Low qualifier 8 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00222">fwd.hpp:222</a></div></div>
<div class="ttc" id="a00304_html_gaf7adb92ce8de0afaff01436b039fd924"><div class="ttname"><a href="a00304.html#gaf7adb92ce8de0afaff01436b039fd924">glm::highp_f64mat2</a></div><div class="ttdeci">mat&lt; 2, 2, f64, highp &gt; highp_f64mat2</div><div class="ttdoc">High double-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00580">fwd.hpp:580</a></div></div>
<div class="ttc" id="a00304_html_ga91c4815f93177eb423362fd296a87e9f"><div class="ttname"><a href="a00304.html#ga91c4815f93177eb423362fd296a87e9f">glm::lowp_uint16_t</a></div><div class="ttdeci">uint16 lowp_uint16_t</div><div class="ttdoc">Low qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00112">fwd.hpp:112</a></div></div>
<div class="ttc" id="a00304_html_ga5bdbfb4ce7d05ce1e1b663f50be17e8a"><div class="ttname"><a href="a00304.html#ga5bdbfb4ce7d05ce1e1b663f50be17e8a">glm::highp_f64mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f64, highp &gt; highp_f64mat3x2</div><div class="ttdoc">High double-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00773">fwd.hpp:773</a></div></div>
<div class="ttc" id="a00304_html_ga012c24c8fc69707b90260474c70275a2"><div class="ttname"><a href="a00304.html#ga012c24c8fc69707b90260474c70275a2">glm::mediump_u32vec3</a></div><div class="ttdeci">vec&lt; 3, u32, mediump &gt; mediump_u32vec3</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00371">fwd.hpp:371</a></div></div>
<div class="ttc" id="a00304_html_gad68bfd9f881856fc863a6ebca0b67f78"><div class="ttname"><a href="a00304.html#gad68bfd9f881856fc863a6ebca0b67f78">glm::lowp_uint16</a></div><div class="ttdeci">uint16 lowp_uint16</div><div class="ttdoc">Low qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00108">fwd.hpp:108</a></div></div>
<div class="ttc" id="a00304_html_ga45721c13b956eb691cbd6c6c1429167a"><div class="ttname"><a href="a00304.html#ga45721c13b956eb691cbd6c6c1429167a">glm::highp_u8vec3</a></div><div class="ttdeci">vec&lt; 3, u8, highp &gt; highp_u8vec3</div><div class="ttdoc">High qualifier 8 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00336">fwd.hpp:336</a></div></div>
<div class="ttc" id="a00304_html_ga66e92e57260bdb910609b9a56bf83e97"><div class="ttname"><a href="a00304.html#ga66e92e57260bdb910609b9a56bf83e97">glm::f64vec4</a></div><div class="ttdeci">vec&lt; 4, f64, defaultp &gt; f64vec4</div><div class="ttdoc">Double-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00504">fwd.hpp:504</a></div></div>
<div class="ttc" id="a00304_html_ga50542e4cb9b2f9bec213b66e06145d07"><div class="ttname"><a href="a00304.html#ga50542e4cb9b2f9bec213b66e06145d07">glm::highp_i8vec2</a></div><div class="ttdeci">vec&lt; 2, i8, highp &gt; highp_i8vec2</div><div class="ttdoc">High qualifier 8 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00233">fwd.hpp:233</a></div></div>
<div class="ttc" id="a00304_html_ga5fe17c87ede1b1b4d92454cff4da076d"><div class="ttname"><a href="a00304.html#ga5fe17c87ede1b1b4d92454cff4da076d">glm::lowp_i32vec3</a></div><div class="ttdeci">vec&lt; 3, i32, lowp &gt; lowp_i32vec3</div><div class="ttdoc">Low qualifier 32 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00264">fwd.hpp:264</a></div></div>
<div class="ttc" id="a00304_html_ga7ff73a45cea9613ebf1a9fad0b9f82ac"><div class="ttname"><a href="a00304.html#ga7ff73a45cea9613ebf1a9fad0b9f82ac">glm::lowp_i32</a></div><div class="ttdeci">int32 lowp_i32</div><div class="ttdoc">Low qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00059">fwd.hpp:59</a></div></div>
<div class="ttc" id="a00304_html_ga22e27beead07bff4d5ce9d6065a57279"><div class="ttname"><a href="a00304.html#ga22e27beead07bff4d5ce9d6065a57279">glm::mediump_fmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, mediump &gt; mediump_fmat4x4</div><div class="ttdoc">Medium single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00648">fwd.hpp:648</a></div></div>
<div class="ttc" id="a00304_html_ga3ebcb1f6d8d8387253de8bccb058d77f"><div class="ttname"><a href="a00304.html#ga3ebcb1f6d8d8387253de8bccb058d77f">glm::mediump_i64</a></div><div class="ttdeci">int64 mediump_i64</div><div class="ttdoc">Medium qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00074">fwd.hpp:74</a></div></div>
<div class="ttc" id="a00304_html_ga1d47d94d17c2406abdd1f087a816e387"><div class="ttname"><a href="a00304.html#ga1d47d94d17c2406abdd1f087a816e387">glm::lowp_i16vec4</a></div><div class="ttdeci">vec&lt; 4, i16, lowp &gt; lowp_i16vec4</div><div class="ttdoc">Low qualifier 16 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00245">fwd.hpp:245</a></div></div>
<div class="ttc" id="a00304_html_gae94823d65648e44d972863c6caa13103"><div class="ttname"><a href="a00304.html#gae94823d65648e44d972863c6caa13103">glm::highp_f64mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f64, highp &gt; highp_f64mat4x3</div><div class="ttdoc">High double-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00777">fwd.hpp:777</a></div></div>
<div class="ttc" id="a00304_html_ga27585b7c3ab300059f11fcba465f6fd2"><div class="ttname"><a href="a00304.html#ga27585b7c3ab300059f11fcba465f6fd2">glm::highp_u8vec2</a></div><div class="ttdeci">vec&lt; 2, u8, highp &gt; highp_u8vec2</div><div class="ttdoc">High qualifier 8 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00335">fwd.hpp:335</a></div></div>
<div class="ttc" id="a00304_html_ga8396bfdc081d9113190d0c39c9f67084"><div class="ttname"><a href="a00304.html#ga8396bfdc081d9113190d0c39c9f67084">glm::highp_i8vec3</a></div><div class="ttdeci">vec&lt; 3, i8, highp &gt; highp_i8vec3</div><div class="ttdoc">High qualifier 8 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00234">fwd.hpp:234</a></div></div>
<div class="ttc" id="a00304_html_gacd1196090ece7a69fb5c3e43a7d4d851"><div class="ttname"><a href="a00304.html#gacd1196090ece7a69fb5c3e43a7d4d851">glm::highp_f64vec3</a></div><div class="ttdeci">vec&lt; 3, f64, highp &gt; highp_f64vec3</div><div class="ttdoc">High double-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00498">fwd.hpp:498</a></div></div>
<div class="ttc" id="a00304_html_ga24273aa02abaecaab7f160bac437a339"><div class="ttname"><a href="a00304.html#ga24273aa02abaecaab7f160bac437a339">glm::fvec2</a></div><div class="ttdeci">vec&lt; 2, f32, defaultp &gt; fvec2</div><div class="ttdoc">Single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00442">fwd.hpp:442</a></div></div>
<div class="ttc" id="a00304_html_gaa666bb9e6d204d3bea0b3a39a3a335f4"><div class="ttname"><a href="a00304.html#gaa666bb9e6d204d3bea0b3a39a3a335f4">glm::lowp_f64vec4</a></div><div class="ttdeci">vec&lt; 4, f64, lowp &gt; lowp_f64vec4</div><div class="ttdoc">Low double-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00489">fwd.hpp:489</a></div></div>
<div class="ttc" id="a00304_html_ga062ffef2973bd8241df993c3b30b327c"><div class="ttname"><a href="a00304.html#ga062ffef2973bd8241df993c3b30b327c">glm::mediump_f32vec3</a></div><div class="ttdeci">vec&lt; 3, f32, mediump &gt; mediump_f32vec3</div><div class="ttdoc">Medium single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00453">fwd.hpp:453</a></div></div>
<div class="ttc" id="a00304_html_gabc7a97c07cbfac8e35eb5e63beb4b679"><div class="ttname"><a href="a00304.html#gabc7a97c07cbfac8e35eb5e63beb4b679">glm::lowp_f64</a></div><div class="ttdeci">double lowp_f64</div><div class="ttdoc">Low 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00163">fwd.hpp:163</a></div></div>
<div class="ttc" id="a00304_html_ga5868c2dcce41cc3ea5edcaeae239f62c"><div class="ttname"><a href="a00304.html#ga5868c2dcce41cc3ea5edcaeae239f62c">glm::lowp_fmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, lowp &gt; lowp_fmat4x2</div><div class="ttdoc">Low single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00636">fwd.hpp:636</a></div></div>
<div class="ttc" id="a00304_html_ga5a67a7440b9c0d1538533540f99036a5"><div class="ttname"><a href="a00304.html#ga5a67a7440b9c0d1538533540f99036a5">glm::highp_f64mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f64, highp &gt; highp_f64mat2x4</div><div class="ttdoc">High double-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00772">fwd.hpp:772</a></div></div>
<div class="ttc" id="a00304_html_gad308e0ed27d64daa4213fb257fcbd5a5"><div class="ttname"><a href="a00304.html#gad308e0ed27d64daa4213fb257fcbd5a5">glm::highp_f64mat4</a></div><div class="ttdeci">mat&lt; 4, 4, f64, highp &gt; highp_f64mat4</div><div class="ttdoc">High double-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00582">fwd.hpp:582</a></div></div>
<div class="ttc" id="a00304_html_ga6addd4dfee87fc09ab9525e3d07db4c8"><div class="ttname"><a href="a00304.html#ga6addd4dfee87fc09ab9525e3d07db4c8">glm::mediump_i32vec4</a></div><div class="ttdeci">vec&lt; 4, i32, mediump &gt; mediump_i32vec4</div><div class="ttdoc">Medium qualifier 32 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00270">fwd.hpp:270</a></div></div>
<div class="ttc" id="a00304_html_ga52409bc6d4a2ce3421526c069220d685"><div class="ttname"><a href="a00304.html#ga52409bc6d4a2ce3421526c069220d685">glm::lowp_f32mat2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, lowp &gt; lowp_f32mat2</div><div class="ttdoc">Low single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00540">fwd.hpp:540</a></div></div>
<div class="ttc" id="a00304_html_gae8f5e3e964ca2ae240adc2c0d74adede"><div class="ttname"><a href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">glm::int16_t</a></div><div class="ttdeci">int16 int16_t</div><div class="ttdoc">16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00057">fwd.hpp:57</a></div></div>
<div class="ttc" id="a00304_html_gac25db6d2b1e2a0f351b77ba3409ac4cd"><div class="ttname"><a href="a00304.html#gac25db6d2b1e2a0f351b77ba3409ac4cd">glm::highp_i64</a></div><div class="ttdeci">int64 highp_i64</div><div class="ttdoc">High qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00075">fwd.hpp:75</a></div></div>
<div class="ttc" id="a00304_html_gad310b1dddeec9ec837a104e7db8de580"><div class="ttname"><a href="a00304.html#gad310b1dddeec9ec837a104e7db8de580">glm::highp_f64mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f64, highp &gt; highp_f64mat3x4</div><div class="ttdoc">High double-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00775">fwd.hpp:775</a></div></div>
<div class="ttc" id="a00304_html_gaed2dc69e0d507d4191092dbd44b3eb75"><div class="ttname"><a href="a00304.html#gaed2dc69e0d507d4191092dbd44b3eb75">glm::highp_fmat3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, highp &gt; highp_fmat3</div><div class="ttdoc">High single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00533">fwd.hpp:533</a></div></div>
<div class="ttc" id="a00304_html_gacd80ad7640e9e32f2edcb8330b1ffe4f"><div class="ttname"><a href="a00304.html#gacd80ad7640e9e32f2edcb8330b1ffe4f">glm::mediump_f32mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, mediump &gt; mediump_f32mat3x3</div><div class="ttdoc">Medium single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00684">fwd.hpp:684</a></div></div>
<div class="ttc" id="a00304_html_ga5e52f485059ce6e3010c590b882602c9"><div class="ttname"><a href="a00304.html#ga5e52f485059ce6e3010c590b882602c9">glm::mediump_f64quat</a></div><div class="ttdeci">qua&lt; f64, mediump &gt; mediump_f64quat</div><div class="ttdoc">Medium double-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00813">fwd.hpp:813</a></div></div>
<div class="ttc" id="a00304_html_ga042ef09ff2f0cb24a36f541bcb3a3710"><div class="ttname"><a href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">glm::int32_t</a></div><div class="ttdeci">int32 int32_t</div><div class="ttdoc">32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00071">fwd.hpp:71</a></div></div>
<div class="ttc" id="a00304_html_gadc4e1594f9555d919131ee02b17822a2"><div class="ttname"><a href="a00304.html#gadc4e1594f9555d919131ee02b17822a2">glm::f64vec2</a></div><div class="ttdeci">vec&lt; 2, f64, defaultp &gt; f64vec2</div><div class="ttdoc">Double-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00502">fwd.hpp:502</a></div></div>
<div class="ttc" id="a00304_html_ga8dd3a3281ae5c970ffe0c41d538aa153"><div class="ttname"><a href="a00304.html#ga8dd3a3281ae5c970ffe0c41d538aa153">glm::lowp_uint64_t</a></div><div class="ttdeci">uint64 lowp_uint64_t</div><div class="ttdoc">Low qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00140">fwd.hpp:140</a></div></div>
<div class="ttc" id="a00263_html_gab630f76c26b50298187f7889104d4b9c"><div class="ttname"><a href="a00263.html#gab630f76c26b50298187f7889104d4b9c">glm::uint64</a></div><div class="ttdeci">detail::uint64 uint64</div><div class="ttdoc">64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00151_source.html#l00067">scalar_uint_sized.hpp:67</a></div></div>
<div class="ttc" id="a00304_html_ga5fde0fa4a3852a9dd5d637a92ee74718"><div class="ttname"><a href="a00304.html#ga5fde0fa4a3852a9dd5d637a92ee74718">glm::highp_int16</a></div><div class="ttdeci">int16 highp_int16</div><div class="ttdoc">High qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00052">fwd.hpp:52</a></div></div>
<div class="ttc" id="a00304_html_gacc44265ed440bf5e6e566782570de842"><div class="ttname"><a href="a00304.html#gacc44265ed440bf5e6e566782570de842">glm::mediump_i16vec1</a></div><div class="ttdeci">vec&lt; 1, i16, mediump &gt; mediump_i16vec1</div><div class="ttdoc">Medium qualifier 16 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00247">fwd.hpp:247</a></div></div>
<div class="ttc" id="a00304_html_ga681381f19f11c9e5ee45cda2c56937ff"><div class="ttname"><a href="a00304.html#ga681381f19f11c9e5ee45cda2c56937ff">glm::fmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, defaultp &gt; fmat2x4</div><div class="ttdoc">Single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00666">fwd.hpp:666</a></div></div>
<div class="ttc" id="a00304_html_ga28635abcddb2f3e92c33c3f0fcc682ad"><div class="ttname"><a href="a00304.html#ga28635abcddb2f3e92c33c3f0fcc682ad">glm::highp_fmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, highp &gt; highp_fmat2x2</div><div class="ttdoc">High single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00650">fwd.hpp:650</a></div></div>
<div class="ttc" id="a00304_html_ga8b839abbb44f5102609eed89f6ed61f7"><div class="ttname"><a href="a00304.html#ga8b839abbb44f5102609eed89f6ed61f7">glm::highp_fvec4</a></div><div class="ttdeci">vec&lt; 4, float, highp &gt; highp_fvec4</div><div class="ttdoc">High Single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00439">fwd.hpp:439</a></div></div>
<div class="ttc" id="a00304_html_ga7c2cadb9b85cc7e0d125db21ca19dea4"><div class="ttname"><a href="a00304.html#ga7c2cadb9b85cc7e0d125db21ca19dea4">glm::highp_f64mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f64, highp &gt; highp_f64mat3x3</div><div class="ttdoc">High double-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00774">fwd.hpp:774</a></div></div>
<div class="ttc" id="a00304_html_gaf5e94bf2a20af7601787c154751dc2e1"><div class="ttname"><a href="a00304.html#gaf5e94bf2a20af7601787c154751dc2e1">glm::mediump_i32</a></div><div class="ttdeci">int32 mediump_i32</div><div class="ttdoc">Medium qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00060">fwd.hpp:60</a></div></div>
<div class="ttc" id="a00304_html_ga2a7d997da9ac29cb931e35bd399f58df"><div class="ttname"><a href="a00304.html#ga2a7d997da9ac29cb931e35bd399f58df">glm::lowp_u16vec2</a></div><div class="ttdeci">vec&lt; 2, u16, lowp &gt; lowp_u16vec2</div><div class="ttdoc">Low qualifier 16 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00345">fwd.hpp:345</a></div></div>
<div class="ttc" id="a00304_html_ga22166f0da65038b447f3c5e534fff1c2"><div class="ttname"><a href="a00304.html#ga22166f0da65038b447f3c5e534fff1c2">glm::highp_u32vec4</a></div><div class="ttdeci">vec&lt; 4, u32, highp &gt; highp_u32vec4</div><div class="ttdoc">High qualifier 32 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00377">fwd.hpp:377</a></div></div>
<div class="ttc" id="a00304_html_gaea881cae4ddc6c0fbf7cc5b08177ca5b"><div class="ttname"><a href="a00304.html#gaea881cae4ddc6c0fbf7cc5b08177ca5b">glm::lowp_float32_t</a></div><div class="ttdeci">float lowp_float32_t</div><div class="ttdoc">Low 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00157">fwd.hpp:157</a></div></div>
<div class="ttc" id="a00304_html_gaa46172d7dc1c7ffe3e78107ff88adf08"><div class="ttname"><a href="a00304.html#gaa46172d7dc1c7ffe3e78107ff88adf08">glm::highp_uint64_t</a></div><div class="ttdeci">uint64 highp_uint64_t</div><div class="ttdoc">High qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00142">fwd.hpp:142</a></div></div>
<div class="ttc" id="a00304_html_gaf3b694b2b8ded7e0b9f07b061917e1a0"><div class="ttname"><a href="a00304.html#gaf3b694b2b8ded7e0b9f07b061917e1a0">glm::lowp_f32vec2</a></div><div class="ttdeci">vec&lt; 2, f32, lowp &gt; lowp_f32vec2</div><div class="ttdoc">Low single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00447">fwd.hpp:447</a></div></div>
<div class="ttc" id="a00304_html_ga31cef34e4cd04840c54741ff2f7005f0"><div class="ttname"><a href="a00304.html#ga31cef34e4cd04840c54741ff2f7005f0">glm::u32vec4</a></div><div class="ttdeci">vec&lt; 4, u32, defaultp &gt; u32vec4</div><div class="ttdoc">Default qualifier 32 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00382">fwd.hpp:382</a></div></div>
<div class="ttc" id="a00304_html_gac1281da5ded55047e8892b0e1f1ae965"><div class="ttname"><a href="a00304.html#gac1281da5ded55047e8892b0e1f1ae965">glm::mediump_f64mat2</a></div><div class="ttdeci">mat&lt; 2, 2, f64, mediump &gt; mediump_f64mat2</div><div class="ttdoc">Medium double-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00576">fwd.hpp:576</a></div></div>
<div class="ttc" id="a00304_html_ga66bffdd8e5c0d3ef9958bbab9ca1ba59"><div class="ttname"><a href="a00304.html#ga66bffdd8e5c0d3ef9958bbab9ca1ba59">glm::highp_f32mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, highp &gt; highp_f32mat4x3</div><div class="ttdoc">High single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00697">fwd.hpp:697</a></div></div>
<div class="ttc" id="a00304_html_ga38e674196ba411d642be40c47bf33939"><div class="ttname"><a href="a00304.html#ga38e674196ba411d642be40c47bf33939">glm::f32quat</a></div><div class="ttdeci">qua&lt; f32, defaultp &gt; f32quat</div><div class="ttdoc">Single-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00805">fwd.hpp:805</a></div></div>
<div class="ttc" id="a00260_html_gaff5189f97f9e842d9636a0f240001b2e"><div class="ttname"><a href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">glm::int64</a></div><div class="ttdeci">detail::int64 int64</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00146_source.html#l00067">scalar_int_sized.hpp:67</a></div></div>
<div class="ttc" id="a00304_html_gae4fe774744852c4d7d069be2e05257ab"><div class="ttname"><a href="a00304.html#gae4fe774744852c4d7d069be2e05257ab">glm::highp_u64vec1</a></div><div class="ttdeci">vec&lt; 1, u64, highp &gt; highp_u64vec1</div><div class="ttdoc">High qualifier 64 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00394">fwd.hpp:394</a></div></div>
<div class="ttc" id="a00304_html_ga8342c7469384c6d769cacc9e309278d9"><div class="ttname"><a href="a00304.html#ga8342c7469384c6d769cacc9e309278d9">glm::highp_f64mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f64, highp &gt; highp_f64mat2x3</div><div class="ttdoc">High double-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00771">fwd.hpp:771</a></div></div>
<div class="ttc" id="a00304_html_ga6d388e9b9aa1b389f0672d9c7dfc61c5"><div class="ttname"><a href="a00304.html#ga6d388e9b9aa1b389f0672d9c7dfc61c5">glm::lowp_i8vec4</a></div><div class="ttdeci">vec&lt; 4, i8, lowp &gt; lowp_i8vec4</div><div class="ttdoc">Low qualifier 8 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00225">fwd.hpp:225</a></div></div>
<div class="ttc" id="a00304_html_ga5e649bbdb135fbcb4bfe950f4c73a444"><div class="ttname"><a href="a00304.html#ga5e649bbdb135fbcb4bfe950f4c73a444">glm::lowp_fmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, lowp &gt; lowp_fmat4x3</div><div class="ttdoc">Low single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00637">fwd.hpp:637</a></div></div>
<div class="ttc" id="a00304_html_gabe6a542dd6c1d5ffd847f1b9b4c9c9b7"><div class="ttname"><a href="a00304.html#gabe6a542dd6c1d5ffd847f1b9b4c9c9b7">glm::f32</a></div><div class="ttdeci">float f32</div><div class="ttdoc">Default 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00150">fwd.hpp:150</a></div></div>
<div class="ttc" id="a00304_html_gaa9b4579f8e6f3d9b649a965bcb785530"><div class="ttname"><a href="a00304.html#gaa9b4579f8e6f3d9b649a965bcb785530">glm::highp_i32vec2</a></div><div class="ttdeci">vec&lt; 2, i32, highp &gt; highp_i32vec2</div><div class="ttdoc">High qualifier 32 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00273">fwd.hpp:273</a></div></div>
<div class="ttc" id="a00304_html_ga4a43050843b141bdc7e85437faef6f55"><div class="ttname"><a href="a00304.html#ga4a43050843b141bdc7e85437faef6f55">glm::mediump_u8vec1</a></div><div class="ttdeci">vec&lt; 1, u8, mediump &gt; mediump_u8vec1</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00329">fwd.hpp:329</a></div></div>
<div class="ttc" id="a00304_html_gae406ec670f64170a7437b5e302eeb2cb"><div class="ttname"><a href="a00304.html#gae406ec670f64170a7437b5e302eeb2cb">glm::highp_fmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, highp &gt; highp_fmat4x3</div><div class="ttdoc">High single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00657">fwd.hpp:657</a></div></div>
<div class="ttc" id="a00304_html_gaf52982bb23e3a3772649b2c5bb84b107"><div class="ttname"><a href="a00304.html#gaf52982bb23e3a3772649b2c5bb84b107">glm::mediump_i16vec4</a></div><div class="ttdeci">vec&lt; 4, i16, mediump &gt; mediump_i16vec4</div><div class="ttdoc">Medium qualifier 16 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00250">fwd.hpp:250</a></div></div>
<div class="ttc" id="a00304_html_gab7daf79d6bc06a68bea1c6f5e11b5512"><div class="ttname"><a href="a00304.html#gab7daf79d6bc06a68bea1c6f5e11b5512">glm::f64mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f64, defaultp &gt; f64mat4x2</div><div class="ttdoc">Double-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00782">fwd.hpp:782</a></div></div>
<div class="ttc" id="a00304_html_ga55a2d2a8eb09b5633668257eb3cad453"><div class="ttname"><a href="a00304.html#ga55a2d2a8eb09b5633668257eb3cad453">glm::fmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, defaultp &gt; fmat2x3</div><div class="ttdoc">Single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00663">fwd.hpp:663</a></div></div>
<div class="ttc" id="a00304_html_gaa805ef691c711dc41e2776cfb67f5cf5"><div class="ttname"><a href="a00304.html#gaa805ef691c711dc41e2776cfb67f5cf5">glm::mediump_f64mat4</a></div><div class="ttdeci">mat&lt; 4, 4, f64, mediump &gt; mediump_f64mat4</div><div class="ttdoc">Medium double-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00578">fwd.hpp:578</a></div></div>
<div class="ttc" id="a00304_html_gaaf4ee3b76d43d98da02ec399b99bda4b"><div class="ttname"><a href="a00304.html#gaaf4ee3b76d43d98da02ec399b99bda4b">glm::mediump_u8vec4</a></div><div class="ttdeci">vec&lt; 4, u8, mediump &gt; mediump_u8vec4</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00332">fwd.hpp:332</a></div></div>
<div class="ttc" id="a00304_html_ga21b1b22dec013a72656e3644baf8a1e1"><div class="ttname"><a href="a00304.html#ga21b1b22dec013a72656e3644baf8a1e1">glm::lowp_f32mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, lowp &gt; lowp_f32mat3x4</div><div class="ttdoc">Low single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00675">fwd.hpp:675</a></div></div>
<div class="ttc" id="a00304_html_gac61843e4fa96c1f4e9d8316454f32a8e"><div class="ttname"><a href="a00304.html#gac61843e4fa96c1f4e9d8316454f32a8e">glm::mediump_float64_t</a></div><div class="ttdeci">double mediump_float64_t</div><div class="ttdoc">Medium 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00174">fwd.hpp:174</a></div></div>
<div class="ttc" id="a00304_html_ga7c0d196f5fa79f7e892a2f323a0be1ae"><div class="ttname"><a href="a00304.html#ga7c0d196f5fa79f7e892a2f323a0be1ae">glm::highp_fvec2</a></div><div class="ttdeci">vec&lt; 2, float, highp &gt; highp_fvec2</div><div class="ttdoc">High Single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00437">fwd.hpp:437</a></div></div>
<div class="ttc" id="a00304_html_gaa2d7acc0adb536fab71fe261232a40ff"><div class="ttname"><a href="a00304.html#gaa2d7acc0adb536fab71fe261232a40ff">glm::u16</a></div><div class="ttdeci">uint16 u16</div><div class="ttdoc">Default qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00106">fwd.hpp:106</a></div></div>
<div class="ttc" id="a00304_html_ga354736e0c645099cd44c42fb2f87c2b8"><div class="ttname"><a href="a00304.html#ga354736e0c645099cd44c42fb2f87c2b8">glm::lowp_i64</a></div><div class="ttdeci">int64 lowp_i64</div><div class="ttdoc">Low qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00073">fwd.hpp:73</a></div></div>
<div class="ttc" id="a00304_html_ga99d1b85ff99956b33da7e9992aad129a"><div class="ttname"><a href="a00304.html#ga99d1b85ff99956b33da7e9992aad129a">glm::f32mat4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, defaultp &gt; f32mat4</div><div class="ttdoc">Single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00554">fwd.hpp:554</a></div></div>
<div class="ttc" id="a00304_html_gacea38a85893e17e6834b6cb09a9ad0cf"><div class="ttname"><a href="a00304.html#gacea38a85893e17e6834b6cb09a9ad0cf">glm::mediump_fmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, mediump &gt; mediump_fmat4x2</div><div class="ttdoc">Medium single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00646">fwd.hpp:646</a></div></div>
<div class="ttc" id="a00304_html_gafc730f6b4242763b0eda0ffa25150292"><div class="ttname"><a href="a00304.html#gafc730f6b4242763b0eda0ffa25150292">glm::lowp_f64mat2</a></div><div class="ttdeci">mat&lt; 2, 2, f64, lowp &gt; lowp_f64mat2</div><div class="ttdoc">Low double-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00572">fwd.hpp:572</a></div></div>
<div class="ttc" id="a00304_html_ga6d7b3789ecb932c26430009478cac7ae"><div class="ttname"><a href="a00304.html#ga6d7b3789ecb932c26430009478cac7ae">glm::mediump_int8_t</a></div><div class="ttdeci">int8 mediump_int8_t</div><div class="ttdoc">Medium qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00041">fwd.hpp:41</a></div></div>
<div class="ttc" id="a00304_html_gaba56275dd04a7a61560b0e8fa5d365b4"><div class="ttname"><a href="a00304.html#gaba56275dd04a7a61560b0e8fa5d365b4">glm::lowp_fmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, lowp &gt; lowp_fmat3x3</div><div class="ttdoc">Low single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00634">fwd.hpp:634</a></div></div>
<div class="ttc" id="a00304_html_ga7286a37076a09da140df18bfa75d4e38"><div class="ttname"><a href="a00304.html#ga7286a37076a09da140df18bfa75d4e38">glm::lowp_float64_t</a></div><div class="ttdeci">double lowp_float64_t</div><div class="ttdoc">Low 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00173">fwd.hpp:173</a></div></div>
<div class="ttc" id="a00304_html_gacaea06d0a79ef3172e887a7a6ba434ff"><div class="ttname"><a href="a00304.html#gacaea06d0a79ef3172e887a7a6ba434ff">glm::highp_int16_t</a></div><div class="ttdeci">int16 highp_int16_t</div><div class="ttdoc">High qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00056">fwd.hpp:56</a></div></div>
<div class="ttc" id="a00304_html_gaa5b44d3ef6efcf33f44876673a7a936e"><div class="ttname"><a href="a00304.html#gaa5b44d3ef6efcf33f44876673a7a936e">glm::highp_fmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, highp &gt; highp_fmat3x3</div><div class="ttdoc">High single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00654">fwd.hpp:654</a></div></div>
<div class="ttc" id="a00304_html_ga2b65767f8b5aed1bd1cf86c541662b50"><div class="ttname"><a href="a00304.html#ga2b65767f8b5aed1bd1cf86c541662b50">glm::i64vec1</a></div><div class="ttdeci">vec&lt; 1, i64, defaultp &gt; i64vec1</div><div class="ttdoc">64 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00297">fwd.hpp:297</a></div></div>
<div class="ttc" id="a00304_html_ga4f072ada9552e1e480bbb3b1acde5250"><div class="ttname"><a href="a00304.html#ga4f072ada9552e1e480bbb3b1acde5250">glm::lowp_u32</a></div><div class="ttdeci">uint32 lowp_u32</div><div class="ttdoc">Low qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00117">fwd.hpp:117</a></div></div>
<div class="ttc" id="a00304_html_ga4b2e0e10d8d154fec9cab50e216588ec"><div class="ttname"><a href="a00304.html#ga4b2e0e10d8d154fec9cab50e216588ec">glm::lowp_u8vec1</a></div><div class="ttdeci">vec&lt; 1, u8, lowp &gt; lowp_u8vec1</div><div class="ttdoc">Low qualifier 8 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00324">fwd.hpp:324</a></div></div>
<div class="ttc" id="a00304_html_ga8089722ffdf868cdfe721dea1fb6a90e"><div class="ttname"><a href="a00304.html#ga8089722ffdf868cdfe721dea1fb6a90e">glm::mediump_i64vec3</a></div><div class="ttdeci">vec&lt; 3, i64, mediump &gt; mediump_i64vec3</div><div class="ttdoc">Medium qualifier 64 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00289">fwd.hpp:289</a></div></div>
<div class="ttc" id="a00304_html_ga4252cf7f5b0e3cd47c3d3badf0ef43b3"><div class="ttname"><a href="a00304.html#ga4252cf7f5b0e3cd47c3d3badf0ef43b3">glm::highp_f32quat</a></div><div class="ttdeci">qua&lt; f32, highp &gt; highp_f32quat</div><div class="ttdoc">High single-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00804">fwd.hpp:804</a></div></div>
<div class="ttc" id="a00304_html_ga8e62c883d13f47015f3b70ed88751369"><div class="ttname"><a href="a00304.html#ga8e62c883d13f47015f3b70ed88751369">glm::highp_u16</a></div><div class="ttdeci">uint16 highp_u16</div><div class="ttdoc">High qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00105">fwd.hpp:105</a></div></div>
<div class="ttc" id="a00304_html_ga98b9ed43cf8c5cf1d354b23c7df9119f"><div class="ttname"><a href="a00304.html#ga98b9ed43cf8c5cf1d354b23c7df9119f">glm::fvec1</a></div><div class="ttdeci">vec&lt; 1, f32, defaultp &gt; fvec1</div><div class="ttdoc">Single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00441">fwd.hpp:441</a></div></div>
<div class="ttc" id="a00304_html_ga907f85d4a0eac3d8aaf571e5c2647194"><div class="ttname"><a href="a00304.html#ga907f85d4a0eac3d8aaf571e5c2647194">glm::mediump_u8vec2</a></div><div class="ttdeci">vec&lt; 2, u8, mediump &gt; mediump_u8vec2</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00330">fwd.hpp:330</a></div></div>
<div class="ttc" id="a00304_html_ga0350631d35ff800e6133ac6243b13cbc"><div class="ttname"><a href="a00304.html#ga0350631d35ff800e6133ac6243b13cbc">glm::lowp_int32_t</a></div><div class="ttdeci">int32 lowp_int32_t</div><div class="ttdoc">Low qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00068">fwd.hpp:68</a></div></div>
<div class="ttc" id="a00304_html_gaa6aab4ee7189b86716f5d7015d43021d"><div class="ttname"><a href="a00304.html#gaa6aab4ee7189b86716f5d7015d43021d">glm::lowp_u16vec1</a></div><div class="ttdeci">vec&lt; 1, u16, lowp &gt; lowp_u16vec1</div><div class="ttdoc">Low qualifier 16 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00344">fwd.hpp:344</a></div></div>
<div class="ttc" id="a00304_html_gaee80c7cd3caa0f2635058656755f6f69"><div class="ttname"><a href="a00304.html#gaee80c7cd3caa0f2635058656755f6f69">glm::highp_fmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, highp &gt; highp_fmat4x4</div><div class="ttdoc">High single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00658">fwd.hpp:658</a></div></div>
<div class="ttc" id="a00304_html_gaa5086dbd6efb272d13fc88829330861d"><div class="ttname"><a href="a00304.html#gaa5086dbd6efb272d13fc88829330861d">glm::highp_f32mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, highp &gt; highp_f32mat3x4</div><div class="ttdoc">High single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00695">fwd.hpp:695</a></div></div>
<div class="ttc" id="a00304_html_ga5d6c70e080409a76a257dc55bd8ea2c8"><div class="ttname"><a href="a00304.html#ga5d6c70e080409a76a257dc55bd8ea2c8">glm::f32vec2</a></div><div class="ttdeci">vec&lt; 2, f32, defaultp &gt; f32vec2</div><div class="ttdoc">Single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00462">fwd.hpp:462</a></div></div>
<div class="ttc" id="a00304_html_gacfd806749008f0ade6ac4bb9dd91082f"><div class="ttname"><a href="a00304.html#gacfd806749008f0ade6ac4bb9dd91082f">glm::highp_u16vec3</a></div><div class="ttdeci">vec&lt; 3, u16, highp &gt; highp_u16vec3</div><div class="ttdoc">High qualifier 16 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00356">fwd.hpp:356</a></div></div>
<div class="ttc" id="a00304_html_gae4dee61f8fe1caccec309fbed02faf12"><div class="ttname"><a href="a00304.html#gae4dee61f8fe1caccec309fbed02faf12">glm::mediump_float32_t</a></div><div class="ttdeci">float mediump_float32_t</div><div class="ttdoc">Medium 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00158">fwd.hpp:158</a></div></div>
<div class="ttc" id="a00304_html_ga3350c93c3275298f940a42875388e4b4"><div class="ttname"><a href="a00304.html#ga3350c93c3275298f940a42875388e4b4">glm::fmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, defaultp &gt; fmat2x2</div><div class="ttdoc">Single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00660">fwd.hpp:660</a></div></div>
<div class="ttc" id="a00304_html_ga3b27fcd9eaa2757f0aaf6b0ce0d85c80"><div class="ttname"><a href="a00304.html#ga3b27fcd9eaa2757f0aaf6b0ce0d85c80">glm::mediump_f32</a></div><div class="ttdeci">float mediump_f32</div><div class="ttdoc">Medium 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00148">fwd.hpp:148</a></div></div>
<div class="ttc" id="a00304_html_ga936e95b881ecd2d109459ca41913fa99"><div class="ttname"><a href="a00304.html#ga936e95b881ecd2d109459ca41913fa99">glm::mediump_f32mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, mediump &gt; mediump_f32mat4x4</div><div class="ttdoc">Medium single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00688">fwd.hpp:688</a></div></div>
<div class="ttc" id="a00304_html_gad6eb11412a3161ca8dc1d63b2a307c4b"><div class="ttname"><a href="a00304.html#gad6eb11412a3161ca8dc1d63b2a307c4b">glm::mediump_f32vec2</a></div><div class="ttdeci">vec&lt; 2, f32, mediump &gt; mediump_f32vec2</div><div class="ttdoc">Medium single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00452">fwd.hpp:452</a></div></div>
<div class="ttc" id="a00304_html_ga760bcf26fdb23a2c3ecad3c928a19ae6"><div class="ttname"><a href="a00304.html#ga760bcf26fdb23a2c3ecad3c928a19ae6">glm::lowp_int8</a></div><div class="ttdeci">int8 lowp_int8</div><div class="ttdoc">Low qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00036">fwd.hpp:36</a></div></div>
<div class="ttc" id="a00304_html_gaf2d02c5f4d59135b9bc524fe317fd26b"><div class="ttname"><a href="a00304.html#gaf2d02c5f4d59135b9bc524fe317fd26b">glm::lowp_f64vec1</a></div><div class="ttdeci">vec&lt; 1, f64, lowp &gt; lowp_f64vec1</div><div class="ttdoc">Low double-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00486">fwd.hpp:486</a></div></div>
<div class="ttc" id="a00304_html_ga36537e701456f12c20e73f469cac4967"><div class="ttname"><a href="a00304.html#ga36537e701456f12c20e73f469cac4967">glm::highp_f32mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, highp &gt; highp_f32mat3x2</div><div class="ttdoc">High single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00693">fwd.hpp:693</a></div></div>
<div class="ttc" id="a00304_html_gacd926d36a72433f6cac51dd60fa13107"><div class="ttname"><a href="a00304.html#gacd926d36a72433f6cac51dd60fa13107">glm::mediump_f64mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f64, mediump &gt; mediump_f64mat3x2</div><div class="ttdoc">Medium double-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00763">fwd.hpp:763</a></div></div>
<div class="ttc" id="a00304_html_gaddc6f7748b699254942c5216b68f8f7f"><div class="ttname"><a href="a00304.html#gaddc6f7748b699254942c5216b68f8f7f">glm::mediump_u8vec3</a></div><div class="ttdeci">vec&lt; 3, u8, mediump &gt; mediump_u8vec3</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00331">fwd.hpp:331</a></div></div>
<div class="ttc" id="a00304_html_ga8c9cd734e03cd49674f3e287aa4a6f95"><div class="ttname"><a href="a00304.html#ga8c9cd734e03cd49674f3e287aa4a6f95">glm::lowp_f64mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f64, lowp &gt; lowp_f64mat4x4</div><div class="ttdoc">Low double-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00758">fwd.hpp:758</a></div></div>
<div class="ttc" id="a00304_html_ga501a2f313f1c220eef4ab02bdabdc3c6"><div class="ttname"><a href="a00304.html#ga501a2f313f1c220eef4ab02bdabdc3c6">glm::lowp_i16vec1</a></div><div class="ttdeci">vec&lt; 1, i16, lowp &gt; lowp_i16vec1</div><div class="ttdoc">Low qualifier 16 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00242">fwd.hpp:242</a></div></div>
<div class="ttc" id="a00304_html_ga119c41d73fe9977358174eb3ac1035a3"><div class="ttname"><a href="a00304.html#ga119c41d73fe9977358174eb3ac1035a3">glm::lowp_int8_t</a></div><div class="ttdeci">int8 lowp_int8_t</div><div class="ttdoc">Low qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00040">fwd.hpp:40</a></div></div>
<div class="ttc" id="a00304_html_gaf7e286e81347011e257ee779524e73b9"><div class="ttname"><a href="a00304.html#gaf7e286e81347011e257ee779524e73b9">glm::lowp_u32vec2</a></div><div class="ttdeci">vec&lt; 2, u32, lowp &gt; lowp_u32vec2</div><div class="ttdoc">Low qualifier 32 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00365">fwd.hpp:365</a></div></div>
<div class="ttc" id="a00304_html_gae71445ac6cd0b9fba3e5c905cd030fb1"><div class="ttname"><a href="a00304.html#gae71445ac6cd0b9fba3e5c905cd030fb1">glm::mediump_f32mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, mediump &gt; mediump_f32mat2x4</div><div class="ttdoc">Medium single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00682">fwd.hpp:682</a></div></div>
<div class="ttc" id="a00304_html_ga3e2e66ffbe341a80bc005ba2b9552110"><div class="ttname"><a href="a00304.html#ga3e2e66ffbe341a80bc005ba2b9552110">glm::f64mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f64, defaultp &gt; f64mat4x3</div><div class="ttdoc">Double-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00785">fwd.hpp:785</a></div></div>
<div class="ttc" id="a00304_html_gad9d1903cb20899966e8ebe0670889a5f"><div class="ttname"><a href="a00304.html#gad9d1903cb20899966e8ebe0670889a5f">glm::highp_i64vec2</a></div><div class="ttdeci">vec&lt; 2, i64, highp &gt; highp_i64vec2</div><div class="ttdoc">High qualifier 64 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00293">fwd.hpp:293</a></div></div>
<div class="ttc" id="a00304_html_ga4491baaebbc46a20f1cb5da985576bf4"><div class="ttname"><a href="a00304.html#ga4491baaebbc46a20f1cb5da985576bf4">glm::mediump_f32mat4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, mediump &gt; mediump_f32mat4</div><div class="ttdoc">Medium single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00546">fwd.hpp:546</a></div></div>
<div class="ttc" id="a00304_html_gadb997e409103d4da18abd837e636a496"><div class="ttname"><a href="a00304.html#gadb997e409103d4da18abd837e636a496">glm::i64</a></div><div class="ttdeci">int64 i64</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00076">fwd.hpp:76</a></div></div>
<div class="ttc" id="a00304_html_ga1d794d240091678f602e8de225b8d8c9"><div class="ttname"><a href="a00304.html#ga1d794d240091678f602e8de225b8d8c9">glm::f64</a></div><div class="ttdeci">double f64</div><div class="ttdoc">Default 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00166">fwd.hpp:166</a></div></div>
<div class="ttc" id="a00304_html_gabb33cab7d7c74cc14aa95455d0690865"><div class="ttname"><a href="a00304.html#gabb33cab7d7c74cc14aa95455d0690865">glm::mediump_f32vec1</a></div><div class="ttdeci">vec&lt; 1, f32, mediump &gt; mediump_f32vec1</div><div class="ttdoc">Medium single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00451">fwd.hpp:451</a></div></div>
<div class="ttc" id="a00304_html_ga8df705d775b776f5ae6b39e2ab892899"><div class="ttname"><a href="a00304.html#ga8df705d775b776f5ae6b39e2ab892899">glm::mediump_f32mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, mediump &gt; mediump_f32mat3x4</div><div class="ttdoc">Medium single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00685">fwd.hpp:685</a></div></div>
<div class="ttc" id="a00304_html_gae98c88d9a7befa9b5877f49176225535"><div class="ttname"><a href="a00304.html#gae98c88d9a7befa9b5877f49176225535">glm::highp_fmat2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, highp &gt; highp_fmat2</div><div class="ttdoc">High single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00532">fwd.hpp:532</a></div></div>
<div class="ttc" id="a00304_html_ga522775dbcc6d96246a1c5cf02344fd8c"><div class="ttname"><a href="a00304.html#ga522775dbcc6d96246a1c5cf02344fd8c">glm::highp_f32vec3</a></div><div class="ttdeci">vec&lt; 3, f32, highp &gt; highp_f32vec3</div><div class="ttdoc">High single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00458">fwd.hpp:458</a></div></div>
<div class="ttc" id="a00304_html_gac7bb04fb857ef7b520e49f6c381432be"><div class="ttname"><a href="a00304.html#gac7bb04fb857ef7b520e49f6c381432be">glm::mediump_i8vec4</a></div><div class="ttdeci">vec&lt; 4, i8, mediump &gt; mediump_i8vec4</div><div class="ttdoc">Medium qualifier 8 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00230">fwd.hpp:230</a></div></div>
<div class="ttc" id="a00304_html_ga41b0d390bd8cc827323b1b3816ff4bf8"><div class="ttname"><a href="a00304.html#ga41b0d390bd8cc827323b1b3816ff4bf8">glm::lowp_float32</a></div><div class="ttdeci">float lowp_float32</div><div class="ttdoc">Low 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00152">fwd.hpp:152</a></div></div>
<div class="ttc" id="a00304_html_ga2a266e46ee218d0c680f12b35c500cc0"><div class="ttname"><a href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">glm::u32vec2</a></div><div class="ttdeci">vec&lt; 2, u32, defaultp &gt; u32vec2</div><div class="ttdoc">Default qualifier 32 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00380">fwd.hpp:380</a></div></div>
<div class="ttc" id="a00304_html_ga1bed890513c0f50b7e7ba4f7f359dbfb"><div class="ttname"><a href="a00304.html#ga1bed890513c0f50b7e7ba4f7f359dbfb">glm::mediump_fvec4</a></div><div class="ttdeci">vec&lt; 4, float, mediump &gt; mediump_fvec4</div><div class="ttdoc">Medium Single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00434">fwd.hpp:434</a></div></div>
<div class="ttc" id="a00304_html_ga5244cef85d6e870e240c76428a262ae8"><div class="ttname"><a href="a00304.html#ga5244cef85d6e870e240c76428a262ae8">glm::mediump_int32</a></div><div class="ttdeci">int32 mediump_int32</div><div class="ttdoc">Medium qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00065">fwd.hpp:65</a></div></div>
<div class="ttc" id="a00304_html_ga48310188e1d0c616bf8d78c92447523b"><div class="ttname"><a href="a00304.html#ga48310188e1d0c616bf8d78c92447523b">glm::i64vec2</a></div><div class="ttdeci">vec&lt; 2, i64, defaultp &gt; i64vec2</div><div class="ttdoc">64 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00298">fwd.hpp:298</a></div></div>
<div class="ttc" id="a00304_html_ga3ab5fe184343d394fb6c2723c3ee3699"><div class="ttname"><a href="a00304.html#ga3ab5fe184343d394fb6c2723c3ee3699">glm::i16</a></div><div class="ttdeci">int16 i16</div><div class="ttdoc">16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00048">fwd.hpp:48</a></div></div>
<div class="ttc" id="a00304_html_gad01cc6479bde1fd1870f13d3ed9530b3"><div class="ttname"><a href="a00304.html#gad01cc6479bde1fd1870f13d3ed9530b3">glm::fmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, defaultp &gt; fmat4x4</div><div class="ttdoc">Single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00668">fwd.hpp:668</a></div></div>
<div class="ttc" id="a00304_html_gaa3ee2bc4af03cc06578b66b3e3f878ae"><div class="ttname"><a href="a00304.html#gaa3ee2bc4af03cc06578b66b3e3f878ae">glm::lowp_f64quat</a></div><div class="ttdeci">qua&lt; f64, lowp &gt; lowp_f64quat</div><div class="ttdoc">Low double-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00812">fwd.hpp:812</a></div></div>
<div class="ttc" id="a00304_html_ga6af54d70d9beb0a7ef992a879e86b04f"><div class="ttname"><a href="a00304.html#ga6af54d70d9beb0a7ef992a879e86b04f">glm::fmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, defaultp &gt; fmat3x2</div><div class="ttdoc">Single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00661">fwd.hpp:661</a></div></div>
<div class="ttc" id="a00304_html_ga529496d75775fb656a07993ea9af2450"><div class="ttname"><a href="a00304.html#ga529496d75775fb656a07993ea9af2450">glm::u16vec4</a></div><div class="ttdeci">vec&lt; 4, u16, defaultp &gt; u16vec4</div><div class="ttdoc">Default qualifier 16 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00362">fwd.hpp:362</a></div></div>
<div class="ttc" id="a00304_html_ga2a78447eb9d66a114b193f4a25899c16"><div class="ttname"><a href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">glm::u16vec2</a></div><div class="ttdeci">vec&lt; 2, u16, defaultp &gt; u16vec2</div><div class="ttdoc">Default qualifier 16 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00360">fwd.hpp:360</a></div></div>
<div class="ttc" id="a00304_html_gad1213a22bbb9e4107f07eaa4956f8281"><div class="ttname"><a href="a00304.html#gad1213a22bbb9e4107f07eaa4956f8281">glm::mediump_u8</a></div><div class="ttdeci">uint8 mediump_u8</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00090">fwd.hpp:90</a></div></div>
<div class="ttc" id="a00304_html_gab0feb11edd0d3ab3e8ed996d349a5066"><div class="ttname"><a href="a00304.html#gab0feb11edd0d3ab3e8ed996d349a5066">glm::lowp_fmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, lowp &gt; lowp_fmat2x2</div><div class="ttdoc">Low single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00630">fwd.hpp:630</a></div></div>
<div class="ttc" id="a00304_html_ga4824e3ddf6e608117dfe4809430737b4"><div class="ttname"><a href="a00304.html#ga4824e3ddf6e608117dfe4809430737b4">glm::highp_i8vec4</a></div><div class="ttdeci">vec&lt; 4, i8, highp &gt; highp_i8vec4</div><div class="ttdoc">High qualifier 8 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00235">fwd.hpp:235</a></div></div>
<div class="ttc" id="a00304_html_ga6fccc89c34045c86339f6fa781ce96de"><div class="ttname"><a href="a00304.html#ga6fccc89c34045c86339f6fa781ce96de">glm::lowp_u64vec4</a></div><div class="ttdeci">vec&lt; 4, u64, lowp &gt; lowp_u64vec4</div><div class="ttdoc">Low qualifier 64 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00387">fwd.hpp:387</a></div></div>
<div class="ttc" id="a00304_html_ga2c94aeae3457325944ca1059b0b68330"><div class="ttname"><a href="a00304.html#ga2c94aeae3457325944ca1059b0b68330">glm::mediump_i64vec2</a></div><div class="ttdeci">vec&lt; 2, i64, mediump &gt; mediump_i64vec2</div><div class="ttdoc">Medium qualifier 64 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00288">fwd.hpp:288</a></div></div>
<div class="ttc" id="a00304_html_ga58c4631421e323e252fc716b6103e38c"><div class="ttname"><a href="a00304.html#ga58c4631421e323e252fc716b6103e38c">glm::highp_f64mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f64, highp &gt; highp_f64mat4x2</div><div class="ttdoc">High double-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00776">fwd.hpp:776</a></div></div>
<div class="ttc" id="a00304_html_ga80e72fe94c88498537e8158ba7591c54"><div class="ttname"><a href="a00304.html#ga80e72fe94c88498537e8158ba7591c54">glm::mediump_int16_t</a></div><div class="ttdeci">int16 mediump_int16_t</div><div class="ttdoc">Medium qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00055">fwd.hpp:55</a></div></div>
<div class="ttc" id="a00304_html_ga552a6bde5e75984efb0f863278da2e54"><div class="ttname"><a href="a00304.html#ga552a6bde5e75984efb0f863278da2e54">glm::lowp_i8</a></div><div class="ttdeci">int8 lowp_i8</div><div class="ttdoc">Low qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00031">fwd.hpp:31</a></div></div>
<div class="ttc" id="a00304_html_ga667948cfe6fb3d6606c750729ec49f77"><div class="ttname"><a href="a00304.html#ga667948cfe6fb3d6606c750729ec49f77">glm::i64vec3</a></div><div class="ttdeci">vec&lt; 3, i64, defaultp &gt; i64vec3</div><div class="ttdoc">64 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00299">fwd.hpp:299</a></div></div>
<div class="ttc" id="a00304_html_ga867a3c2d99ab369a454167d2c0a24dbd"><div class="ttname"><a href="a00304.html#ga867a3c2d99ab369a454167d2c0a24dbd">glm::lowp_i32vec2</a></div><div class="ttdeci">vec&lt; 2, i32, lowp &gt; lowp_i32vec2</div><div class="ttdoc">Low qualifier 32 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00263">fwd.hpp:263</a></div></div>
<div class="ttc" id="a00304_html_gafcfdd74a115163af2ce1093551747352"><div class="ttname"><a href="a00304.html#gafcfdd74a115163af2ce1093551747352">glm::highp_f64quat</a></div><div class="ttdeci">qua&lt; f64, highp &gt; highp_f64quat</div><div class="ttdoc">High double-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00814">fwd.hpp:814</a></div></div>
<div class="ttc" id="a00304_html_ga8ebc04ecf6440c4ee24718a16600ce6b"><div class="ttname"><a href="a00304.html#ga8ebc04ecf6440c4ee24718a16600ce6b">glm::mediump_f64vec2</a></div><div class="ttdeci">vec&lt; 2, f64, mediump &gt; mediump_f64vec2</div><div class="ttdoc">Medium double-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00492">fwd.hpp:492</a></div></div>
<div class="ttc" id="a00304_html_gacf54c3330ef60aa3d16cb676c7bcb8c7"><div class="ttname"><a href="a00304.html#gacf54c3330ef60aa3d16cb676c7bcb8c7">glm::highp_uint16_t</a></div><div class="ttdeci">uint16 highp_uint16_t</div><div class="ttdoc">High qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00114">fwd.hpp:114</a></div></div>
<div class="ttc" id="a00304_html_ga346b2336fff168a7e0df1583aae3e5a5"><div class="ttname"><a href="a00304.html#ga346b2336fff168a7e0df1583aae3e5a5">glm::lowp_fvec1</a></div><div class="ttdeci">vec&lt; 1, float, lowp &gt; lowp_fvec1</div><div class="ttdoc">Low single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00426">fwd.hpp:426</a></div></div>
<div class="ttc" id="a00304_html_ga302ec977b0c0c3ea245b6c9275495355"><div class="ttname"><a href="a00304.html#ga302ec977b0c0c3ea245b6c9275495355">glm::i8</a></div><div class="ttdeci">int8 i8</div><div class="ttdoc">8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00034">fwd.hpp:34</a></div></div>
<div class="ttc" id="a00304_html_ga9b170dd4a8f38448a2dc93987c7875e9"><div class="ttname"><a href="a00304.html#ga9b170dd4a8f38448a2dc93987c7875e9">glm::mediump_uint64_t</a></div><div class="ttdeci">uint64 mediump_uint64_t</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00141">fwd.hpp:141</a></div></div>
<div class="ttc" id="a00304_html_ga659f372ccb8307d5db5beca942cde5e8"><div class="ttname"><a href="a00304.html#ga659f372ccb8307d5db5beca942cde5e8">glm::mediump_u64vec1</a></div><div class="ttdeci">vec&lt; 1, u64, mediump &gt; mediump_u64vec1</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00389">fwd.hpp:389</a></div></div>
<div class="ttc" id="a00304_html_gaf9020c6176a75bc84828ab01ea7dac25"><div class="ttname"><a href="a00304.html#gaf9020c6176a75bc84828ab01ea7dac25">glm::mediump_f32mat2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, mediump &gt; mediump_f32mat2</div><div class="ttdoc">Medium single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00544">fwd.hpp:544</a></div></div>
<div class="ttc" id="a00304_html_gadfe65c78231039e90507770db50c98c7"><div class="ttname"><a href="a00304.html#gadfe65c78231039e90507770db50c98c7">glm::mediump_uint8_t</a></div><div class="ttdeci">uint8 mediump_uint8_t</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00099">fwd.hpp:99</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
<div class="ttc" id="a00304_html_ga6d40381d78472553f878f66e443feeef"><div class="ttname"><a href="a00304.html#ga6d40381d78472553f878f66e443feeef">glm::mediump_f64</a></div><div class="ttdeci">double mediump_f64</div><div class="ttdoc">Medium 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00164">fwd.hpp:164</a></div></div>
<div class="ttc" id="a00304_html_ga367964fc2133d3f1b5b3755ff9cf6c9b"><div class="ttname"><a href="a00304.html#ga367964fc2133d3f1b5b3755ff9cf6c9b">glm::mediump_fvec1</a></div><div class="ttdeci">vec&lt; 1, float, mediump &gt; mediump_fvec1</div><div class="ttdoc">Medium single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00431">fwd.hpp:431</a></div></div>
<div class="ttc" id="a00304_html_ga2885a6c89916911e418c06bb76b9bdbb"><div class="ttname"><a href="a00304.html#ga2885a6c89916911e418c06bb76b9bdbb">glm::mediump_uint16</a></div><div class="ttdeci">uint16 mediump_uint16</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00109">fwd.hpp:109</a></div></div>
<div class="ttc" id="a00304_html_gae0b75ad0fed8c00ddc0b5ce335d31060"><div class="ttname"><a href="a00304.html#gae0b75ad0fed8c00ddc0b5ce335d31060">glm::highp_u8vec4</a></div><div class="ttdeci">vec&lt; 4, u8, highp &gt; highp_u8vec4</div><div class="ttdoc">High qualifier 8 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00337">fwd.hpp:337</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
