"""
RCA Data Preparation: RCA数据准备器
阶段3.3: 准备并传递RCA所需的精确输入

本模块负责为RCA（区域概念注意力）机制准备所需的输入数据，
包括2D概念权重图的生成与预处理、分概念文本嵌入的准备，
以及完整cross_attention_kwargs的构建。

主要功能：
- RCA数据准备器（RCADataPreparator）
- 2D概念权重图生成与预处理
- 分概念文本嵌入管理
- cross_attention_kwargs构建
- CFG兼容的数据格式处理

技术特性：
- 基于阶段1的render_concept_weights功能
- 支持多概念权重图下采样和归一化
- 灵活的文本嵌入管理
- 完整的CFG数据复制处理
- 详细的数据验证和错误处理

参考实现：
- MultiDreamer3D主训练循环中的数据准备逻辑
- 2D概念权重图的预处理策略

作者: Claude Sonnet 4 (Anthropic 最先进模型)
版本: 阶段3 - RCA机制移植与适配
"""

import torch
import torch.nn.functional as F
from typing import Dict, Optional, List, Union, Tuple, Any
import logging
import warnings

# 抑制不重要的警告
warnings.filterwarnings("ignore", category=UserWarning, module="torch")

class RCADataPreparator:
    """
    RCA数据准备器
    
    负责为RCA机制准备所需的输入数据，包括：
    1. 2D概念权重图的生成与预处理
    2. 分概念文本嵌入的准备
    3. cross_attention_kwargs的构建
    """
    
    def __init__(
        self,
        concept_names: List[str] = None,
        latent_resolution: Tuple[int, int] = (64, 64),
        enable_normalization: bool = True,
        debug_mode: bool = False
    ):
        """
        初始化RCA数据准备器
        
        Args:
            concept_names: 概念名称列表，例如 ["bg", "concept0", "concept1"]
            latent_resolution: 潜空间分辨率 (H_latent, W_latent)
            enable_normalization: 是否启用权重归一化
            debug_mode: 是否启用调试模式
        """
        self.concept_names = concept_names or ["bg", "concept0", "concept1"]
        self.latent_resolution = latent_resolution
        self.enable_normalization = enable_normalization
        self.debug_mode = debug_mode
        
        # 概念映射：概念ID -> 概念名称
        self.concept_id_to_name = {
            0: "bg",      # 背景
            1: "concept0", # 修复区域
            2: "concept1"  # 边界区域
        }
        
        # 适配器名称（用于LoRA）
        self.adapter_names = [name for name in self.concept_names if name != "bg"]
        
        logging.info(f"RCADataPreparator initialized with concepts: {self.concept_names}")
        if self.debug_mode:
            logging.debug(f"Latent resolution: {latent_resolution}")
            logging.debug(f"Adapter names: {self.adapter_names}")
    
    def prepare_concept_masks(
        self,
        rendered_concept_weights: torch.Tensor,
        batch_size: int = 1,
        cfg_enabled: bool = True
    ) -> Dict[str, torch.Tensor]:
        """
        准备概念掩码字典
        
        Args:
            rendered_concept_weights: 渲染的概念权重 [num_concepts, H_render, W_render]
            batch_size: 批量大小
            cfg_enabled: 是否启用CFG（需要复制数据）
            
        Returns:
            concept_masks: 概念掩码字典 {"bg": [B, 1, H_lat, W_lat], ...}
        """
        if self.debug_mode:
            logging.debug(f"Preparing concept masks from weights: {rendered_concept_weights.shape}")
        
        # 验证输入
        if rendered_concept_weights.dim() != 3:
            raise ValueError(f"Expected 3D concept weights [num_concepts, H, W], got {rendered_concept_weights.shape}")
        
        num_concepts, h_render, w_render = rendered_concept_weights.shape
        
        # 下采样到潜空间分辨率
        h_latent, w_latent = self.latent_resolution
        
        if (h_render, w_render) != (h_latent, w_latent):
            # 需要下采样
            weights_resized = F.interpolate(
                rendered_concept_weights.unsqueeze(0),  # [1, num_concepts, H, W]
                size=(h_latent, w_latent),
                mode='bilinear',
                align_corners=False
            ).squeeze(0)  # [num_concepts, H_lat, W_lat]
            
            if self.debug_mode:
                logging.debug(f"Resized concept weights from {(h_render, w_render)} to {(h_latent, w_latent)}")
        else:
            weights_resized = rendered_concept_weights
        
        # 归一化（确保每个像素位置的权重和为1）
        if self.enable_normalization:
            weights_sum = weights_resized.sum(dim=0, keepdim=True)  # [1, H_lat, W_lat]
            weights_sum = torch.clamp(weights_sum, min=1e-8)  # 避免除零
            weights_resized = weights_resized / weights_sum
            
            if self.debug_mode:
                logging.debug(f"Normalized concept weights, sum range: [{weights_sum.min():.6f}, {weights_sum.max():.6f}]")
        
        # 构建概念掩码字典
        concept_masks = {}
        
        for i, concept_name in enumerate(self.concept_names):
            if i < num_concepts:
                # 使用实际的概念权重
                mask = weights_resized[i:i+1]  # [1, H_lat, W_lat]
            else:
                # 创建零掩码（如果概念数量不足）
                mask = torch.zeros(1, h_latent, w_latent, device=weights_resized.device)
                if self.debug_mode:
                    logging.debug(f"Created zero mask for {concept_name}")
            
            # 扩展批量维度
            mask = mask.unsqueeze(0).repeat(batch_size, 1, 1, 1)  # [B, 1, H_lat, W_lat]
            
            # CFG复制（如果启用）
            if cfg_enabled:
                mask = torch.cat([mask, mask], dim=0)  # [2*B, 1, H_lat, W_lat]
            
            concept_masks[concept_name] = mask
        
        if self.debug_mode:
            for name, mask in concept_masks.items():
                logging.debug(f"Concept mask '{name}': {mask.shape}, range: [{mask.min():.6f}, {mask.max():.6f}]")
        
        return concept_masks
    
    def prepare_text_embeddings(
        self,
        concept_embeddings: Dict[str, torch.Tensor],
        batch_size: int = 1,
        cfg_enabled: bool = True
    ) -> Dict[str, torch.Tensor]:
        """
        准备分概念文本嵌入字典
        
        Args:
            concept_embeddings: 概念文本嵌入 {"bg": [N, C], "concept0": [N, C], ...}
            batch_size: 批量大小
            cfg_enabled: 是否启用CFG
            
        Returns:
            text_embeddings: 文本嵌入字典 {"base": [B, N, C], "concept0": [B, N, C], ...}
        """
        if self.debug_mode:
            logging.debug(f"Preparing text embeddings for {len(concept_embeddings)} concepts")
        
        text_embeddings = {}
        
        for concept_name, embedding in concept_embeddings.items():
            if embedding.dim() == 2:
                # [N, C] -> [B, N, C]
                embedding = embedding.unsqueeze(0).repeat(batch_size, 1, 1)
            elif embedding.dim() == 3 and embedding.shape[0] != batch_size:
                # 调整批量维度
                if embedding.shape[0] == 1:
                    embedding = embedding.repeat(batch_size, 1, 1)
                else:
                    logging.warning(f"Embedding batch size mismatch for {concept_name}: {embedding.shape[0]} vs {batch_size}")
            
            # CFG复制（如果启用）
            if cfg_enabled:
                embedding = torch.cat([embedding, embedding], dim=0)  # [2*B, N, C]
            
            text_embeddings[concept_name] = embedding
        
        # 确保有base嵌入（通常使用背景或第一个概念的嵌入）
        if "base" not in text_embeddings:
            if "bg" in text_embeddings:
                text_embeddings["base"] = text_embeddings["bg"]
            elif len(text_embeddings) > 0:
                first_key = list(text_embeddings.keys())[0]
                text_embeddings["base"] = text_embeddings[first_key]
                if self.debug_mode:
                    logging.debug(f"Using {first_key} as base embedding")
        
        if self.debug_mode:
            for name, embedding in text_embeddings.items():
                logging.debug(f"Text embedding '{name}': {embedding.shape}")
        
        return text_embeddings
    
    def build_cross_attention_kwargs(
        self,
        concept_masks: Dict[str, torch.Tensor],
        text_embeddings: Dict[str, torch.Tensor],
        lora_scale: float = 1.0,
        enable_concept_forward: bool = True
    ) -> Dict[str, Any]:
        """
        构建完整的cross_attention_kwargs
        
        Args:
            concept_masks: 概念掩码字典
            text_embeddings: 文本嵌入字典
            lora_scale: LoRA缩放因子
            enable_concept_forward: 是否启用概念感知前向
            
        Returns:
            cross_attention_kwargs: 完整的RCA参数字典
        """
        cross_attention_kwargs = {
            "mask": concept_masks,
            "adapter_names": self.adapter_names,
            "text_embeddings": text_embeddings,
            "lora_scale": lora_scale,
            "concept_forward": enable_concept_forward
        }
        
        if self.debug_mode:
            logging.debug(f"Built cross_attention_kwargs:")
            logging.debug(f"  - Masks: {list(concept_masks.keys())}")
            logging.debug(f"  - Text embeddings: {list(text_embeddings.keys())}")
            logging.debug(f"  - Adapter names: {self.adapter_names}")
            logging.debug(f"  - LoRA scale: {lora_scale}")
            logging.debug(f"  - Concept forward: {enable_concept_forward}")
        
        return cross_attention_kwargs
    
    def prepare_rca_data(
        self,
        rendered_concept_weights: torch.Tensor,
        concept_embeddings: Dict[str, torch.Tensor],
        batch_size: int = 1,
        cfg_enabled: bool = True,
        lora_scale: float = 1.0
    ) -> Dict[str, Any]:
        """
        一站式RCA数据准备
        
        Args:
            rendered_concept_weights: 渲染的概念权重 [num_concepts, H_render, W_render]
            concept_embeddings: 概念文本嵌入字典
            batch_size: 批量大小
            cfg_enabled: 是否启用CFG
            lora_scale: LoRA缩放因子
            
        Returns:
            cross_attention_kwargs: 完整的RCA参数字典
        """
        if self.debug_mode:
            logging.debug("Starting one-stop RCA data preparation")
        
        # 准备概念掩码
        concept_masks = self.prepare_concept_masks(
            rendered_concept_weights, batch_size, cfg_enabled
        )
        
        # 准备文本嵌入
        text_embeddings = self.prepare_text_embeddings(
            concept_embeddings, batch_size, cfg_enabled
        )
        
        # 构建cross_attention_kwargs
        cross_attention_kwargs = self.build_cross_attention_kwargs(
            concept_masks, text_embeddings, lora_scale, True
        )
        
        if self.debug_mode:
            logging.debug("RCA data preparation completed")
        
        return cross_attention_kwargs
    
    def validate_rca_data(self, cross_attention_kwargs: Dict[str, Any]) -> bool:
        """
        验证RCA数据的完整性和正确性
        
        Args:
            cross_attention_kwargs: RCA参数字典
            
        Returns:
            valid: 数据是否有效
        """
        try:
            # 检查必需的键
            required_keys = ["mask", "adapter_names", "text_embeddings", "concept_forward"]
            for key in required_keys:
                if key not in cross_attention_kwargs:
                    logging.error(f"Missing required key: {key}")
                    return False
            
            # 检查掩码
            masks = cross_attention_kwargs["mask"]
            if not isinstance(masks, dict) or len(masks) == 0:
                logging.error("Invalid or empty mask dictionary")
                return False
            
            # 检查文本嵌入
            text_embeddings = cross_attention_kwargs["text_embeddings"]
            if not isinstance(text_embeddings, dict) or len(text_embeddings) == 0:
                logging.error("Invalid or empty text embeddings dictionary")
                return False
            
            # 检查适配器名称
            adapter_names = cross_attention_kwargs["adapter_names"]
            if not isinstance(adapter_names, list):
                logging.error("Adapter names must be a list")
                return False
            
            if self.debug_mode:
                logging.debug("RCA data validation passed")
            
            return True
            
        except Exception as e:
            logging.error(f"RCA data validation failed: {e}")
            return False
