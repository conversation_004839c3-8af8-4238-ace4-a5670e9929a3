#!/usr/bin/env python3
"""
🔥 CISM逻辑验证测试（无PyTorch依赖）
验证CISM核心逻辑和配置的正确性
作者: Claude <PERSON> 4 (Anthropic 最先进模型)
"""

import sys
import numpy as np
from pathlib import Path
import time
import logging
from typing import Dict, List, Tuple

# 添加项目路径
sys.path.append('.')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cism_imports():
    """测试CISM组件导入"""
    logger.info("🔥 测试CISM组件导入...")
    
    try:
        # 测试基础导入
        import yaml
        logger.info("   ✅ YAML导入成功")
        
        # 测试项目结构
        cism_core_path = Path('cism_core')
        if cism_core_path.exists():
            logger.info("   ✅ cism_core目录存在")
            
            # 检查核心文件
            core_files = [
                'diffusion_engine.py',
                'concept_guidance.py', 
                'sds_loss.py',
                'cism_trainer.py'
            ]
            
            for file_name in core_files:
                file_path = cism_core_path / file_name
                if file_path.exists():
                    logger.info(f"   ✅ {file_name} 存在")
                else:
                    logger.warning(f"   ⚠️ {file_name} 不存在")
        else:
            logger.error("   ❌ cism_core目录不存在")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ CISM组件导入失败: {e}")
        return False

def test_config_files():
    """测试配置文件"""
    logger.info("🔥 测试配置文件...")
    
    try:
        import yaml
        
        # 测试主配置文件
        main_config_path = Path('configs/cism_training_config.yaml')
        if main_config_path.exists():
            with open(main_config_path, 'r', encoding='utf-8') as f:
                main_config = yaml.safe_load(f)
            
            logger.info("   ✅ 主配置文件加载成功")
            
            # 检查关键配置项
            required_keys = [
                'model_id',
                'guidance_scale',
                'min_step',
                'max_step',
                'concept_config_path'
            ]
            
            for key in required_keys:
                if key in main_config:
                    logger.info(f"      - {key}: {main_config[key]}")
                else:
                    logger.warning(f"      ⚠️ 缺少配置项: {key}")
        else:
            logger.warning("   ⚠️ 主配置文件不存在")
        
        # 测试概念配置文件
        concept_config_path = Path('configs/concept_prompts.yaml')
        if concept_config_path.exists():
            with open(concept_config_path, 'r', encoding='utf-8') as f:
                concept_config = yaml.safe_load(f)
            
            logger.info("   ✅ 概念配置文件加载成功")
            
            concept_prompts = concept_config.get('concept_prompts', {})
            logger.info(f"      - 概念数量: {len(concept_prompts)}")
            
            for concept_id, prompt in concept_prompts.items():
                logger.info(f"      - 概念 {concept_id}: {prompt[:50]}...")
        else:
            logger.warning("   ⚠️ 概念配置文件不存在")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件测试失败: {e}")
        return False

def test_training_schedule_logic():
    """测试训练调度逻辑（纯数学计算）"""
    logger.info("🔥 测试训练调度逻辑...")
    
    try:
        # 测试参数
        cism_start_iter = 10
        cism_end_iter = 50
        cism_interval = 10
        sds_start_weight = 0.1
        sds_end_weight = 1.0
        
        def should_apply_cism(iteration, start_iter, end_iter, interval):
            """判断是否应用CISM"""
            if iteration < start_iter or iteration > end_iter:
                return False
            return (iteration - start_iter) % interval == 0
        
        def get_sds_weight(iteration, start_iter, end_iter, start_weight, end_weight):
            """计算SDS权重"""
            if iteration < start_iter or iteration > end_iter:
                return 0.0
            progress = (iteration - start_iter) / (end_iter - start_iter)
            return start_weight + progress * (end_weight - start_weight)
        
        # 测试迭代范围
        test_iterations = list(range(0, 60, 5))
        
        logger.info("   📊 训练调度测试结果:")
        logger.info("   迭代 | 应用CISM | SDS权重")
        logger.info("   -----|---------|--------")
        
        schedule_results = []
        
        for iteration in test_iterations:
            should_apply = should_apply_cism(iteration, cism_start_iter, cism_end_iter, cism_interval)
            weight = get_sds_weight(iteration, cism_start_iter, cism_end_iter, sds_start_weight, sds_end_weight)
            
            schedule_results.append({
                'iteration': iteration,
                'should_apply': should_apply,
                'weight': weight
            })
            
            apply_str = "✅" if should_apply else "⏭️"
            logger.info(f"   {iteration:4d} | {apply_str:7s} | {weight:.3f}")
        
        # 验证逻辑正确性
        applied_iterations = [r['iteration'] for r in schedule_results if r['should_apply']]
        expected_iterations = [10, 20, 30, 40, 50]
        
        if applied_iterations == expected_iterations:
            logger.info("   ✅ 调度逻辑正确")
        else:
            logger.warning(f"   ⚠️ 调度逻辑异常: 期望 {expected_iterations}, 实际 {applied_iterations}")
        
        # 验证权重递增
        weights = [r['weight'] for r in schedule_results if r['should_apply']]
        if all(weights[i] <= weights[i+1] for i in range(len(weights)-1)):
            logger.info("   ✅ 权重递增正确")
        else:
            logger.warning(f"   ⚠️ 权重递增异常: {weights}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 训练调度逻辑测试失败: {e}")
        return False

def test_concept_guidance_math():
    """测试概念引导数学逻辑"""
    logger.info("🔥 测试概念引导数学逻辑...")
    
    try:
        # 模拟CFG计算
        def simulate_cfg(noise_pred_uncond, noise_pred_cond, guidance_scale):
            """模拟CFG计算"""
            return noise_pred_uncond + guidance_scale * (noise_pred_cond - noise_pred_uncond)
        
        def simulate_delta_epsilon(noise_pred_cond, noise_pred_uncond):
            """模拟引导差异计算"""
            return noise_pred_cond - noise_pred_uncond
        
        # 测试数据
        test_cases = [
            {'uncond': 0.1, 'cond': 0.8, 'scale': 7.5},
            {'uncond': 0.3, 'cond': 0.6, 'scale': 5.0},
            {'uncond': 0.5, 'cond': 0.2, 'scale': 10.0}
        ]
        
        logger.info("   📊 CFG计算测试:")
        logger.info("   无条件 | 条件 | 引导强度 | CFG结果 | Delta")
        logger.info("   ------|------|---------|--------|------")
        
        for case in test_cases:
            uncond = case['uncond']
            cond = case['cond']
            scale = case['scale']
            
            cfg_result = simulate_cfg(uncond, cond, scale)
            delta = simulate_delta_epsilon(cond, uncond)
            
            logger.info(f"   {uncond:6.1f} | {cond:4.1f} | {scale:7.1f} | {cfg_result:6.2f} | {delta:5.2f}")
        
        # 测试多概念权重混合
        def simulate_multi_concept_blend(deltas, weights):
            """模拟多概念混合"""
            if len(deltas) != len(weights):
                raise ValueError("Delta和权重数量不匹配")
            
            blended = 0.0
            for delta, weight in zip(deltas, weights):
                blended += weight * delta
            
            return blended
        
        # 测试多概念混合
        concept_deltas = [0.5, 0.8, 0.3]
        concept_weights = [0.3, 1.0, 0.5]
        
        blended_delta = simulate_multi_concept_blend(concept_deltas, concept_weights)
        
        logger.info(f"\n   📊 多概念混合测试:")
        logger.info(f"   概念Delta: {concept_deltas}")
        logger.info(f"   概念权重: {concept_weights}")
        logger.info(f"   混合结果: {blended_delta:.3f}")
        
        # 验证数学正确性
        expected_blend = sum(d * w for d, w in zip(concept_deltas, concept_weights))
        if abs(blended_delta - expected_blend) < 1e-6:
            logger.info("   ✅ 多概念混合计算正确")
        else:
            logger.warning(f"   ⚠️ 多概念混合计算异常: 期望 {expected_blend}, 实际 {blended_delta}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 概念引导数学逻辑测试失败: {e}")
        return False

def test_ism_logic():
    """测试ISM逻辑（数学部分）"""
    logger.info("🔥 测试ISM逻辑...")
    
    try:
        # 模拟DDIM逆向步骤的数学
        def simulate_ddim_reverse_step(x_t, noise_pred, alpha_t, alpha_s, eta=0.0):
            """模拟DDIM逆向步骤"""
            # 预测x0
            sqrt_alpha_t = np.sqrt(alpha_t)
            sqrt_one_minus_alpha_t = np.sqrt(1 - alpha_t)
            pred_x0 = (x_t - sqrt_one_minus_alpha_t * noise_pred) / sqrt_alpha_t
            
            # 计算方向
            sqrt_alpha_s = np.sqrt(alpha_s)
            sqrt_one_minus_alpha_s = np.sqrt(1 - alpha_s)
            direction = sqrt_one_minus_alpha_s * noise_pred
            
            # 逆向步骤
            x_s = sqrt_alpha_s * pred_x0 + direction
            
            return x_s, pred_x0
        
        # 测试参数
        x_t = 0.8  # 当前潜变量
        noise_pred = 0.3  # 预测噪声
        alpha_t = 0.9  # 当前时间步的alpha
        alpha_s = 0.95  # 前一时间步的alpha
        
        x_s, pred_x0 = simulate_ddim_reverse_step(x_t, noise_pred, alpha_t, alpha_s)
        
        logger.info(f"   📊 DDIM逆向步骤测试:")
        logger.info(f"   输入 x_t: {x_t}")
        logger.info(f"   噪声预测: {noise_pred}")
        logger.info(f"   alpha_t: {alpha_t}")
        logger.info(f"   alpha_s: {alpha_s}")
        logger.info(f"   输出 x_s: {x_s:.4f}")
        logger.info(f"   预测 x0: {pred_x0:.4f}")
        
        # 验证数学约束
        if 0 <= pred_x0 <= 1:
            logger.info("   ✅ 预测x0在合理范围内")
        else:
            logger.warning(f"   ⚠️ 预测x0超出范围: {pred_x0}")
        
        # 测试时间步计算
        def calculate_timestep_delta(current_t, ism_steps, max_timestep=1000):
            """计算ISM时间步差"""
            delta_t = max(1, ism_steps * 50)
            prev_t = max(0, current_t - delta_t)
            return prev_t, delta_t
        
        test_timesteps = [100, 500, 800]
        
        logger.info(f"\n   📊 时间步计算测试:")
        logger.info("   当前t | ISM步数 | 前一t | Delta")
        logger.info("   ------|--------|------|------")
        
        for t in test_timesteps:
            for steps in [1, 2, 3]:
                prev_t, delta_t = calculate_timestep_delta(t, steps)
                logger.info(f"   {t:5d} | {steps:6d} | {prev_t:4d} | {delta_t:4d}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ISM逻辑测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构完整性"""
    logger.info("🔥 测试文件结构完整性...")
    
    try:
        # 检查必要的目录
        required_dirs = [
            'cism_core',
            'configs',
            'reports',
            'reports/stage2'
        ]
        
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists():
                logger.info(f"   ✅ 目录存在: {dir_name}")
            else:
                logger.warning(f"   ⚠️ 目录不存在: {dir_name}")
        
        # 检查必要的文件
        required_files = [
            'cism_core/__init__.py',
            'cism_core/diffusion_engine.py',
            'cism_core/concept_guidance.py',
            'cism_core/sds_loss.py',
            'cism_core/cism_trainer.py',
            'configs/cism_training_config.yaml',
            'configs/concept_prompts.yaml',
            'train_cism.py'
        ]
        
        for file_name in required_files:
            file_path = Path(file_name)
            if file_path.exists():
                logger.info(f"   ✅ 文件存在: {file_name}")
            else:
                logger.warning(f"   ⚠️ 文件不存在: {file_name}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 文件结构测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🔥 CISM逻辑验证测试（无PyTorch依赖）")
    logger.info("🎯 验证CISM核心逻辑和配置的正确性")
    logger.info("👨‍💻 测试者: Claude Sonnet 4 (Anthropic 最先进模型)")
    logger.info("=" * 80)
    
    tests = [
        ("文件结构完整性", test_file_structure),
        ("CISM组件导入", test_cism_imports),
        ("配置文件", test_config_files),
        ("训练调度逻辑", test_training_schedule_logic),
        ("概念引导数学", test_concept_guidance_math),
        ("ISM逻辑", test_ism_logic)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 {test_name}")
        logger.info("-" * 50)
        
        start_time = time.time()
        
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results[test_name] = False
        
        elapsed_time = time.time() - start_time
        logger.info(f"测试耗时: {elapsed_time:.2f}秒")
    
    # 总结结果
    logger.info("\n" + "=" * 80)
    logger.info("📋 测试结果总结:")
    
    passed_tests = 0
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"   - {test_name}: {status}")
        if result:
            passed_tests += 1
    
    total_tests = len(results)
    success_rate = passed_tests / total_tests
    
    logger.info(f"\n🎯 总体状态: {passed_tests}/{total_tests} 测试通过 ({success_rate:.1%})")
    
    # 详细分析
    if success_rate >= 0.8:
        logger.info("🎉 CISM核心逻辑完全正确")
        logger.info("\n💡 下一步建议:")
        logger.info("1. 安装PyTorch和相关依赖进行完整测试")
        logger.info("2. 下载Stable Diffusion模型")
        logger.info("3. 进行真实的端到端训练测试")
        logger.info("4. 验证语义引导的实际效果")
    elif success_rate >= 0.6:
        logger.info("⚠️ CISM核心逻辑基本正确")
        logger.info("\n🔧 改进建议:")
        logger.info("1. 修复失败的逻辑测试")
        logger.info("2. 完善配置文件")
        logger.info("3. 检查文件结构完整性")
    else:
        logger.info("❌ CISM核心逻辑需要重大修复")
        logger.info("\n🚨 紧急修复:")
        logger.info("1. 检查所有失败的测试")
        logger.info("2. 修复基础逻辑错误")
        logger.info("3. 重新验证数学计算")
    
    return success_rate >= 0.8, results

if __name__ == "__main__":
    success, results = main()
    sys.exit(0 if success else 1)
