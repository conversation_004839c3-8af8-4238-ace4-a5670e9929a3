# CISM语义引导系统实现指南

## 🎯 阶段2目标

集成Stable Diffusion，实现基于concept_id的语义引导系统，为InFusion-Enhanced提供强大的AI语义能力。

## 📋 前置条件确认

### ✅ 已完成的基础
- **阶段1**: concept_id语义标签体系已构建完成
- **30000次训练结果**: 稳定收敛的3D高斯点云 (`output/garden/point_cloud.ply`)
- **concept_id集成**: 每个高斯点已具备concept_id和concept_confidence属性

### 📁 预期输入文件结构
```
output/garden/
├── point_cloud.ply           # 30000次训练的高斯参数（含concept_id）
├── renders/                  # 多视角渲染结果
├── depth_dis/                # 深度图输出
├── c2w/                      # 相机外参矩阵
└── intri/                    # 相机内参矩阵

data/garden/
├── images/                   # RGB图像
├── seg/                     # 原始二值掩码
├── concept_masks/           # 三概念掩码（阶段1生成）
└── sparse/                  # COLMAP稀疏重建
```

## 🚀 实施步骤

### 2.1 Stable Diffusion模型集成

#### 环境准备
```bash
# 安装依赖
pip install diffusers transformers accelerate
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 验证安装
python -c "from diffusers import StableDiffusionPipeline; print('Diffusers installed successfully')"
```

#### 核心组件
- **DiffusionEngine**: 扩散模型引擎 (`cism_core/diffusion_engine.py`)
  - 加载Stable Diffusion组件 (UNet, VAE, Text Encoder)
  - 内存优化配置 (半精度、注意力切片)
  - 文本编码和图像潜在空间转换

### 2.2 概念条件化引导实现

#### 概念映射配置
```yaml
# configs/concept_prompts.yaml
concept_prompts:
  0: "natural garden background, lush vegetation, harmonious landscape"
  1: "USER_DEFINED_CONCEPT"  # 用户自定义
  2: "smooth natural transition, seamless blending edge"
```

#### 核心功能
- **ConceptGuidance**: 概念引导系统 (`cism_core/concept_guidance.py`)
  - concept_id与文本概念映射
  - 文本嵌入缓存机制
  - 多概念引导差异计算
  - 用户自定义概念支持

#### 使用示例
```python
# 设置用户概念
concept_guidance.set_user_concept(1, "modern minimalist garden with geometric patterns")

# 计算概念引导
delta_epsilon = concept_guidance.compute_guidance_delta(
    latents, timesteps, concept_id=1, guidance_scale=7.5
)
```

### 2.3 Score Distillation Sampling损失实现

#### SDS损失公式
```
L_SDS = w(t) * (delta_epsilon * ∂latent/∂θ)
```

#### 核心组件
- **SDSLoss**: SDS损失计算器 (`cism_core/sds_loss.py`)
  - 时间步权重函数 (DreamFusion权重)
  - 单概念和多概念SDS损失
  - 概念感知的空间化SDS损失

#### 多概念SDS损失
```python
# 计算多概念SDS损失
sds_loss, info = sds_loss.compute_sds_loss_multi_concept(
    rendered_images,
    concept_ids=[0, 1, 2],
    concept_weights=[0.3, 1.0, 0.5],
    guidance_scale=7.5
)
```

## 🔧 训练集成

### CISM训练器
- **CISMTrainer**: 完整训练系统 (`cism_core/cism_trainer.py`)
  - 集成CISM到3DGS训练流程
  - 训练调度和权重管理
  - 监控和统计收集

### 训练配置
```yaml
# configs/cism_training_config.yaml
training_schedule:
  cism_start_iter: 1000      # CISM开始迭代
  cism_end_iter: 15000       # CISM结束迭代
  cism_interval: 10          # CISM应用间隔

sds_loss:
  guidance_scale: 7.5        # CFG引导强度
  min_step: 20               # 最小时间步
  max_step: 980              # 最大时间步
```

### 训练命令
```bash
# 启动CISM增强训练
python train_cism.py \
    --config configs/cism_training_config.yaml \
    --source_path data/garden \
    --model_path output/garden/point_cloud.ply \
    --user_concept "modern minimalist garden with geometric patterns"
```

## 📊 评估和监控

### 评估脚本
```bash
# 评估CISM增强结果
python evaluate_cism.py \
    --model_path output/cism_experiments/enhanced_gaussians.ply \
    --source_path data/garden \
    --config configs/cism_training_config.yaml
```

### 评估指标
- **渲染质量**: PSNR, SSIM, L1 Loss
- **概念质量**: 概念分布统计, 置信度分析
- **训练效率**: SDS损失收敛, 计算时间

## 🎛️ 关键参数调优

### 引导强度 (guidance_scale)
- **背景概念**: 5.0 (较低强度)
- **修复概念**: 7.5 (标准强度)  
- **边界概念**: 6.0 (中等强度)

### 概念权重 (concept_weights)
- **背景**: 0.3 (辅助作用)
- **修复**: 1.0 (主要概念)
- **边界**: 0.5 (平滑过渡)

### 时间步范围
- **min_step**: 20 (避免过度噪声)
- **max_step**: 980 (保持引导效果)

## 📈 预期效果

### 语义一致性提升
- 修复区域与用户概念描述高度一致
- 背景区域保持自然和谐
- 边界区域实现平滑过渡

### 渲染质量保持
- PSNR保持或略有提升
- SSIM保持高水平
- 视觉质量显著改善

### 概念控制精度
- 概念边界清晰准确
- 多概念协调一致
- 用户概念表达准确

## 🔍 故障排除

### 常见问题

1. **GPU内存不足**
   ```yaml
   # 启用内存优化
   diffusion_model:
     half_precision: true
     attention_slicing: true
     memory_efficient_attention: true
   ```

2. **SDS损失不收敛**
   ```yaml
   # 调整权重调度
   training_schedule:
     sds_start_weight: 0.05  # 降低起始权重
     sds_warmup_iters: 1000  # 增加预热时间
   ```

3. **概念表达不准确**
   ```python
   # 优化概念描述
   concept_guidance.set_user_concept(1, 
       "detailed, photorealistic modern garden with clean geometric patterns, high quality")
   ```

## 📁 输出文件结构

```
output/cism_experiments/
├── enhanced_gaussians.ply    # CISM增强后的高斯模型
├── logs/                     # 训练日志
├── visualizations/           # 可视化结果
├── checkpoints/              # 训练检查点
└── evaluation_report.json   # 评估报告
```

## 🔄 与阶段3的衔接

CISM系统为阶段3的RCA区域控制系统提供：
- **精确的概念引导**: 为RCA提供语义基础
- **稳定的概念表示**: 确保空间一致性
- **可微分的概念嵌入**: 支持端到端优化
- **多概念协调机制**: 为复杂场景编辑做准备

阶段2完成后，系统具备了强大的语义理解和引导能力，为阶段3的精确空间控制奠定了坚实基础。
