#!/usr/bin/env python3
"""
Claude Sonnet 4 - 联合训练主脚本
阶段4: 基于用户建议实现的concept_id + CISM + RCA端到端联合优化

用户建议核心要点:
1. 三阶段渐进式训练策略
2. 动态损失权重调度
3. 现有stage4组件集成
4. 完整的监控和验证系统

使用方法:
python train_joint.py --config configs/joint_training_config.yaml --source_path data/garden --model_path output/rca_experiments/rca_enhanced_gaussians.ply
"""

import os
import sys
import argparse
import yaml
import logging
from pathlib import Path
import torch
import time
from typing import Dict, Optional

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入3DGS组件
try:
    from gaussian_splatting.scene import Scene, GaussianModel
    from gaussian_splatting.utils.general_utils import safe_state
    from gaussian_splatting.utils.loss_utils import l1_loss, ssim
    from gaussian_splatting.gaussian_renderer import render
    from gaussian_splatting.arguments import ModelParams, PipelineParams, OptimizationParams
    GAUSSIAN_SPLATTING_AVAILABLE = True
except ImportError:
    GAUSSIAN_SPLATTING_AVAILABLE = False
    logging.warning("Gaussian Splatting modules not available")

# 导入联合训练组件
from joint_core import JointTrainer

def setup_logging(log_dir: str, verbose: bool = False):
    """设置日志系统"""
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    log_level = logging.DEBUG if verbose else logging.INFO
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 文件处理器
    file_handler = logging.FileHandler(Path(log_dir) / 'joint_training.log')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(log_level)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

def load_config(config_path: str) -> Dict:
    """加载训练配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_output_dirs(config: Dict):
    """创建输出目录"""
    output_dir = Path(config['output_dir'])
    
    dirs_to_create = [
        output_dir,
        output_dir / 'logs',
        output_dir / 'visualizations',
        output_dir / 'checkpoints',
        output_dir / 'weight_schedules'
    ]
    
    for dir_path in dirs_to_create:
        dir_path.mkdir(parents=True, exist_ok=True)

def load_gaussians(model_path: str, device: str = "cuda") -> GaussianModel:
    """加载预训练的高斯模型（RCA增强后的）"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    # 创建高斯模型
    gaussians = GaussianModel(sh_degree=3)
    
    # 加载PLY文件
    gaussians.load_ply(model_path)
    
    logging.info(f"Loaded RCA-enhanced Gaussian model from {model_path}")
    logging.info(f"Number of Gaussian points: {gaussians.get_xyz.shape[0]}")
    
    # 检查是否包含concept信息
    if hasattr(gaussians, '_concept_id') and hasattr(gaussians, '_concept_confidence'):
        concept_ids = gaussians.get_concept_id
        unique_concepts, counts = torch.unique(concept_ids, return_counts=True)
        logging.info(f"Concept distribution: {dict(zip(unique_concepts.cpu().numpy(), counts.cpu().numpy()))}")
        
        concept_confidences = gaussians.get_concept_confidence
        logging.info(f"Average concept confidence: {concept_confidences.mean().item():.3f}")
    else:
        logging.warning("Model does not contain concept information")
    
    return gaussians

def setup_scene(source_path: str, gaussians: GaussianModel, config: Dict):
    """设置场景"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    # 创建模型参数
    model_params = ModelParams()
    model_params.source_path = source_path
    model_params.model_path = config['data_paths']['output_gaussians']
    model_params.resolution = config['rendering']['image_resolution']
    
    # 创建场景
    scene = Scene(model_params, gaussians, load_iteration=None, shuffle=False)
    
    return scene, model_params

def joint_training_loop(
    gaussians: GaussianModel,
    scene: Scene,
    joint_trainer: JointTrainer,
    config: Dict
):
    """
    🔥 联合训练循环
    
    实现用户建议的三阶段渐进式训练:
    - Stage A (0-5000): 仅3DGS，稳定几何结构
    - Stage B (5000-15000): 3DGS + CISM，引入语义引导
    - Stage C (15000-25000): 完整联合优化，3DGS + CISM + RCA
    """
    logging.info("🔥 Starting Joint Enhanced Training with User-Suggested Strategy...")
    
    # 训练参数
    scheduler_config = config['scheduler']
    start_iter = 0
    end_iter = scheduler_config['total_iterations']
    log_interval = config['monitoring']['log_interval']
    save_interval = config['monitoring']['save_interval']
    eval_interval = config['monitoring']['eval_interval']
    
    # 获取训练视角
    viewpoint_stack = scene.getTrainCameras().copy()
    
    # 生成权重调度可视化
    weight_schedule_path = Path(config['output_dir']) / 'weight_schedules' / 'loss_weight_schedule.png'
    joint_trainer.loss_scheduler.visualize_weight_schedule(str(weight_schedule_path))
    
    # 训练循环
    for iteration in range(start_iter, end_iter + 1):
        iter_start_time = time.time()
        
        # 选择随机视角
        if not viewpoint_stack:
            viewpoint_stack = scene.getTrainCameras().copy()
        viewpoint_cam = viewpoint_stack.pop()
        
        # 3DGS渲染
        render_pkg = render(viewpoint_cam, gaussians, PipelineParams(), torch.tensor([1.0, 1.0, 1.0], device="cuda"))
        rendered_image = render_pkg["render"]
        
        # 计算基础3DGS损失
        gt_image = viewpoint_cam.original_image.cuda()
        l1_loss_val = l1_loss(rendered_image, gt_image)
        ssim_loss_val = 1.0 - ssim(rendered_image, gt_image)
        base_loss = (1.0 - 0.2) * l1_loss_val + 0.2 * ssim_loss_val
        
        # 🔥 执行联合训练步骤
        joint_result = joint_trainer.training_step(
            rendered_image.unsqueeze(0),  # 添加batch维度
            viewpoint_cam,
            iteration
        )
        
        # 计算总损失
        if joint_result is not None:
            joint_loss, joint_info = joint_result
            total_loss = base_loss + joint_loss
        else:
            total_loss = base_loss
            joint_info = {'current_stage': 'stage_a', 'loss_weights': {}}
        
        # 反向传播
        total_loss.backward()
        
        # 更新参数
        with torch.no_grad():
            # 更新学习率
            gaussians.update_learning_rate(iteration)
            
            # 优化器步骤
            gaussians.optimizer.step()
            gaussians.optimizer.zero_grad(set_to_none=True)
            
            # 密化和剪枝
            if iteration < 15000:
                # 添加密化统计
                gaussians.add_densification_stats(render_pkg["viewspace_points"], render_pkg["visibility_filter"])
                
                if iteration > 500 and iteration % 100 == 0:
                    size_threshold = 20 if iteration > 3000 else None
                    gaussians.densify_and_prune(0.0002, 0.005, scene.cameras_extent, size_threshold)
                
                if iteration % 3000 == 0:
                    gaussians.reset_opacity()
        
        # 记录训练进度
        iter_time = time.time() - iter_start_time
        
        if iteration % log_interval == 0:
            current_stage = joint_info.get('current_stage', 'unknown')
            loss_weights = joint_info.get('loss_weights', {})
            
            log_message = f"Iter {iteration:5d} | Stage: {current_stage} | L1={l1_loss_val:.4f}, SSIM={ssim_loss_val:.4f}"
            
            if 'loss_components' in joint_info:
                loss_components = joint_info['loss_components']
                if 'sds_loss' in loss_components:
                    log_message += f", SDS={loss_components['sds_loss']:.4f}"
                if 'rca_loss' in loss_components:
                    log_message += f", RCA={loss_components['rca_loss']:.4f}"
                if 'rca_supervision_loss' in loss_components:
                    log_message += f", RCA_Sup={loss_components['rca_supervision_loss']:.4f}"
            
            log_message += f" | Weights: {loss_weights}"
            log_message += f" | Time={iter_time:.2f}s"
            
            logging.info(log_message)
        
        # 保存检查点
        if iteration % save_interval == 0:
            checkpoint_path = Path(config['output_dir']) / 'checkpoints' / f"iteration_{iteration}.ply"
            gaussians.save_ply(str(checkpoint_path))
            
            # 保存联合训练状态
            joint_state_path = Path(config['output_dir']) / 'checkpoints' / f"joint_state_{iteration}.pth"
            joint_trainer.save_training_state(str(joint_state_path))
            
            logging.info(f"Saved checkpoint at iteration {iteration}")
        
        # 评估和可视化
        if iteration % eval_interval == 0 and iteration > 0:
            try:
                # 获取训练统计
                training_stats = joint_trainer.get_training_statistics()

                # 保存统计信息
                stats_path = Path(config['output_dir']) / 'logs' / f"training_stats_{iteration}.json"
                import json
                with open(stats_path, 'w') as f:
                    # 转换tensor为可序列化格式
                    serializable_stats = {}
                    for key, value in training_stats.items():
                        if isinstance(value, torch.Tensor):
                            serializable_stats[key] = value.cpu().numpy().tolist()
                        elif isinstance(value, list) and value and isinstance(value[0], torch.Tensor):
                            serializable_stats[key] = [v.cpu().numpy().tolist() for v in value]
                        else:
                            serializable_stats[key] = value

                    json.dump(serializable_stats, f, indent=2)

                logging.info(f"Saved training statistics at iteration {iteration}")
            except Exception as e:
                logging.warning(f"Failed to save training statistics: {e}")
    
    # 保存最终模型
    final_model_path = config['data_paths']['output_gaussians']
    gaussians.save_ply(final_model_path)
    logging.info(f"🔥 Final joint-enhanced model saved to {final_model_path}")
    
    # 输出最终统计
    final_stats = joint_trainer.get_training_statistics()
    logging.info("🔥 Joint Training Completed Successfully!")
    logging.info(f"Total iterations: {end_iter}")
    logging.info(f"Stage transitions: {len(final_stats.get('stage_transitions', []))}")
    
    if 'avg_recent_losses' in final_stats:
        avg_losses = final_stats['avg_recent_losses']
        logging.info(f"Final average losses: {avg_losses}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Joint Enhanced 3D Gaussian Splatting Training")
    parser.add_argument("--config", type=str, required=True, help="Joint training configuration file")
    parser.add_argument("--source_path", type=str, required=True, help="Source data path")
    parser.add_argument("--model_path", type=str, required=True, help="RCA-enhanced Gaussian model path")
    parser.add_argument("--user_concept", type=str, help="User-defined concept description")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # 检查依赖
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        print("Error: Gaussian Splatting modules not available")
        print("Please ensure the gaussian_splatting package is properly installed")
        sys.exit(1)
    
    # 加载配置
    config = load_config(args.config)
    
    # 覆盖用户概念
    if args.user_concept:
        config['cism']['user_concept'] = args.user_concept
    
    # 创建输出目录
    create_output_dirs(config)
    
    # 设置日志
    setup_logging(
        Path(config['output_dir']) / 'logs',
        args.verbose or config['debug'].get('verbose_logging', False)
    )
    
    logging.info("🔥 Starting Joint Enhanced Training - Claude Sonnet 4")
    logging.info(f"Config: {args.config}")
    logging.info(f"Source: {args.source_path}")
    logging.info(f"Model: {args.model_path}")
    logging.info("User-suggested three-stage progressive training strategy enabled")
    
    try:
        # 设置设备
        device = config.get('device', 'cuda')
        if not torch.cuda.is_available() and device == 'cuda':
            device = 'cpu'
            logging.warning("CUDA not available, using CPU")
        
        # 加载高斯模型
        gaussians = load_gaussians(args.model_path, device)
        
        # 设置场景
        scene, model_params = setup_scene(args.source_path, gaussians, config)
        
        # 初始化联合训练器
        joint_trainer = JointTrainer(gaussians, config, device)
        
        # 开始联合训练
        joint_training_loop(gaussians, scene, joint_trainer, config)
        
        logging.info("🔥 Joint training completed successfully!")
        
    except Exception as e:
        logging.error(f"Joint training failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # 清理资源
        if 'joint_trainer' in locals():
            joint_trainer.cleanup()

if __name__ == "__main__":
    main()
