import os
import torch
import json
import time
import numpy as np
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass

from cism_rca.stage5_validation.concept_separation_validator import ConceptSeparationValidator
from cism_rca.stage5_validation.spatial_control_validator import SpatialControlValidator
from cism_rca.stage5_validation.system_performance_evaluator import SystemPerformanceEvaluator
from cism_rca.stage5_validation.visualization_tools import ValidationVisualizer

@dataclass
class Stage5ValidationResult:
    """阶段5验证结果"""
    concept_separation_results: Dict     # 概念分离验证结果
    spatial_control_results: Dict        # 空间控制验证结果
    system_performance_results: Dict     # 系统性能评估结果
    visualization_outputs: Dict          # 可视化输出路径
    validation_stats: Dict               # 验证统计信息

class Stage5MainPipeline:
    """
    🎯 阶段5主流程控制器
    执行集成测试与验证，评估整体系统效果
    """
    
    def __init__(self, 
                experiment_dirs: Dict[str, str],
                config: Dict = None):
        """
        Args:
            experiment_dirs: 实验目录路径字典，包含输入/输出路径
            config: 验证配置参数
        """
        self.experiment_dirs = experiment_dirs
        self.output_dir = os.path.join(experiment_dirs['output'], 'validation_results')
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 默认配置
        self.config = {
            'concept_separation': {
                'test_views': 8,            # 测试视角数量
                'metrics': ['ssim', 'lpips', 'psnr'],  # 评估指标
                'ablation_modes': ['no_rca', 'full']   # 对比模式
            },
            'spatial_control': {
                'boundary_precision_threshold': 0.8,  # 边界精度阈值
                'smoothness_sigma': 1.0,             # 平滑度参数
                'local_control_radius': 0.05         # 局部控制半径
            },
            'system_performance': {
                'batch_sizes': [1, 2, 4, 8],         # 批处理大小
                'resolutions': [512, 1024, 2048],    # 测试分辨率
                'max_concepts': 5,                   # 最大概念数
                'memory_profile': True              # 是否分析内存
            },
            'visualization': {
                'heatmap_alpha': 0.7,               # 热力图透明度
                'save_formats': ['png', 'mp4'],     # 保存格式
                'include_3d': True                 # 是否包含3D可视化
            }
        }
        
        # 更新配置
        if config:
            self._update_config(self.config, config)
        
        # 组件初始化
        self.concept_validator = ConceptSeparationValidator(self.output_dir, self.config['concept_separation'])
        self.spatial_validator = SpatialControlValidator(self.output_dir, self.config['spatial_control'])
        self.performance_evaluator = SystemPerformanceEvaluator(self.output_dir, self.config['system_performance'])
        self.visualizer = ValidationVisualizer(self.output_dir, self.config['visualization'])
        
        # 保存配置
        self._save_config()
    
    def run_complete_validation(self) -> Stage5ValidationResult:
        """
        🎯 运行完整验证流程
        
        Returns:
            Stage5ValidationResult: 完整验证结果
        """
        print("🚀 开始阶段5集成测试与验证...")
        print("=" * 60)
        
        start_time = time.time()
        validation_stats = {'start_time': start_time}
        
        try:
            # 🔥 步骤1: 概念分离效果验证
            print("\n【步骤1/3】概念分离效果验证")
            print("-" * 40)
            step1_start = time.time()
            
            concept_separation_results = self._step1_validate_concept_separation()
            
            step1_time = time.time() - step1_start
            validation_stats['step1_concept_separation'] = {
                'duration': step1_time,
                'status': 'completed'
            }
            print(f"✅ 步骤1完成，耗时: {step1_time:.2f}s")
            
            # 🔥 步骤2: 空间控制精度验证
            print("\n【步骤2/3】空间控制精度验证")
            print("-" * 40)
            step2_start = time.time()
            
            spatial_control_results = self._step2_validate_spatial_control()
            
            step2_time = time.time() - step2_start
            validation_stats['step2_spatial_control'] = {
                'duration': step2_time,
                'status': 'completed'
            }
            print(f"✅ 步骤2完成，耗时: {step2_time:.2f}s")
            
            # 🔥 步骤3: 系统性能评估
            print("\n【步骤3/3】系统性能评估")
            print("-" * 40)
            step3_start = time.time()
            
            system_performance_results = self._step3_evaluate_system_performance()
            
            step3_time = time.time() - step3_start
            validation_stats['step3_system_performance'] = {
                'duration': step3_time,
                'status': 'completed'
            }
            print(f"✅ 步骤3完成，耗时: {step3_time:.2f}s")
            
            # 可视化结果
            print("\n【可视化】生成验证结果可视化")
            print("-" * 40)
            vis_start = time.time()
            
            visualization_outputs = self._generate_visualizations(
                concept_separation_results,
                spatial_control_results,
                system_performance_results
            )
            
            vis_time = time.time() - vis_start
            validation_stats['visualization'] = {
                'duration': vis_time,
                'status': 'completed'
            }
            print(f"✅ 可视化完成，耗时: {vis_time:.2f}s")
            
            # 总结果
            total_time = time.time() - start_time
            validation_stats['total_duration'] = total_time
            validation_stats['overall_status'] = 'completed'
            
            # 创建结果对象
            result = Stage5ValidationResult(
                concept_separation_results=concept_separation_results,
                spatial_control_results=spatial_control_results,
                system_performance_results=system_performance_results,
                visualization_outputs=visualization_outputs,
                validation_stats=validation_stats
            )
            
            # 保存结果
            self._save_validation_results(result)
            
            # 生成报告
            self._generate_validation_report(result)
            
            print(f"\n🎉 阶段5验证完成，总耗时: {total_time:.2f}s")
            return result
            
        except Exception as e:
            # 错误处理
            validation_stats['error'] = str(e)
            validation_stats['overall_status'] = 'failed'
            print(f"❌ 验证过程发生错误: {e}")
            raise
    
    def _step1_validate_concept_separation(self) -> Dict:
        """步骤1: 概念分离效果验证"""
        print("   🔍 验证不同概念引导的差异性效果...")
        
        # 1.1 加载模型和数据
        model_paths = {
            'cism_model': os.path.join(self.experiment_dirs['checkpoints'], 'cism_model.pt'),
            'rca_model': os.path.join(self.experiment_dirs['checkpoints'], 'rca_model.pt'),
            'gaussian_model': os.path.join(self.experiment_dirs['output'], 'final_gaussians.ply')
        }
        
        # 1.2 执行单概念测试
        single_concept_results = self.concept_validator.run_single_concept_tests(model_paths)
        print(f"   ✓ 单概念测试完成，生成 {len(single_concept_results['test_cases'])} 个测试案例")
        
        # 1.3 执行对比测试
        comparative_results = self.concept_validator.run_comparative_tests(model_paths)
        print(f"   ✓ 对比测试完成，生成 {len(comparative_results['comparisons'])} 个对比")
        
        # 1.4 执行消融实验
        ablation_results = self.concept_validator.run_ablation_studies(model_paths)
        print(f"   ✓ 消融实验完成，对比 {len(ablation_results['ablation_modes'])} 种模式")
        
        # 1.5 评估质量指标
        quality_metrics = self.concept_validator.compute_quality_metrics()
        print(f"   ✓ 质量评估完成，计算 {len(quality_metrics['metrics'])} 项指标")
        
        # 组合结果
        results = {
            'single_concept_tests': single_concept_results,
            'comparative_tests': comparative_results,
            'ablation_studies': ablation_results,
            'quality_metrics': quality_metrics,
            'summary': self.concept_validator.get_summary()
        }
        
        return results
    
    def _step2_validate_spatial_control(self) -> Dict:
        """步骤2: 空间控制精度验证"""
        print("   🔍 验证RCA的空间控制精确性...")
        
        # 2.1 生成权重图可视化
        weight_vis_results = self.spatial_validator.generate_weight_visualizations()
        print(f"   ✓ 权重图可视化完成，生成 {len(weight_vis_results['visualizations'])} 个可视化")
        
        # 2.2 执行边界精度测试
        boundary_results = self.spatial_validator.test_boundary_precision()
        print(f"   ✓ 边界精度测试完成，边界清晰度得分: {boundary_results['sharpness_score']:.3f}")
        
        # 2.3 执行控制精度测试
        control_results = self.spatial_validator.test_control_precision()
        print(f"   ✓ 控制精度测试完成，精度得分: {control_results['precision_score']:.3f}")
        
        # 组合结果
        results = {
            'weight_visualizations': weight_vis_results,
            'boundary_precision': boundary_results,
            'control_precision': control_results,
            'summary': self.spatial_validator.get_summary()
        }
        
        return results
    
    def _step3_evaluate_system_performance(self) -> Dict:
        """步骤3: 系统性能评估"""
        print("   🔍 评估整体系统的计算效率...")
        
        # 3.1 测试计算性能
        compute_results = self.performance_evaluator.benchmark_compute_performance()
        print(f"   ✓ 计算性能测试完成，平均推理速度: {compute_results['inference_time_ms']:.1f}ms/帧")
        
        # 3.2 测试可扩展性
        scalability_results = self.performance_evaluator.test_scalability()
        print(f"   ✓ 可扩展性测试完成，分辨率测试: {len(scalability_results['resolution_tests'])} 项")
        
        # 3.3 测试系统稳定性
        stability_results = self.performance_evaluator.test_system_stability()
        print(f"   ✓ 系统稳定性测试完成，稳定性得分: {stability_results['stability_score']:.3f}")
        
        # 组合结果
        results = {
            'compute_performance': compute_results,
            'scalability': scalability_results,
            'system_stability': stability_results,
            'summary': self.performance_evaluator.get_summary()
        }
        
        return results
    
    def _generate_visualizations(self, 
                                concept_results: Dict,
                                spatial_results: Dict,
                                performance_results: Dict) -> Dict:
        """生成可视化结果"""
        # 1. 概念分离可视化
        concept_vis = self.visualizer.visualize_concept_separation(concept_results)
        
        # 2. 空间控制可视化
        spatial_vis = self.visualizer.visualize_spatial_control(spatial_results)
        
        # 3. 性能指标可视化
        performance_vis = self.visualizer.visualize_performance_metrics(performance_results)
        
        # 4. 综合可视化报告
        summary_vis = self.visualizer.create_summary_visualization(
            concept_results, spatial_results, performance_results
        )
        
        return {
            'concept_separation_visualizations': concept_vis,
            'spatial_control_visualizations': spatial_vis,
            'performance_visualizations': performance_vis,
            'summary_visualization': summary_vis
        }
    
    def _save_validation_results(self, result: Stage5ValidationResult):
        """保存验证结果"""
        # 保存完整结果
        torch.save(
            result,
            os.path.join(self.output_dir, "stage5_complete_results.pt")
        )
        
        # 保存核心结果JSON
        core_results = {
            'concept_separation': result.concept_separation_results['summary'],
            'spatial_control': result.spatial_control_results['summary'],
            'system_performance': result.system_performance_results['summary'],
            'visualization_paths': result.visualization_outputs['summary_visualization'],
            'validation_stats': {
                'total_duration': result.validation_stats['total_duration'],
                'overall_status': result.validation_stats['overall_status']
            }
        }
        
        with open(os.path.join(self.output_dir, "stage5_core_results.json"), 'w', encoding='utf-8') as f:
            json.dump(core_results, f, indent=2, ensure_ascii=False)
    
    def _generate_validation_report(self, result: Stage5ValidationResult):
        """生成验证报告"""
        report = {
            'validation_summary': {
                'execution_time': result.validation_stats['total_duration'],
                'overall_status': result.validation_stats['overall_status'],
                'output_directory': self.output_dir
            },
            'concept_separation_results': {
                'semantic_consistency': result.concept_separation_results['quality_metrics']['semantic_consistency'],
                'visual_quality': {
                    'psnr': result.concept_separation_results['quality_metrics']['metrics'].get('psnr', 0),
                    'ssim': result.concept_separation_results['quality_metrics']['metrics'].get('ssim', 0),
                    'lpips': result.concept_separation_results['quality_metrics']['metrics'].get('lpips', 0)
                },
                'ablation_improvement': result.concept_separation_results['summary']['ablation_improvement']
            },
            'spatial_control_results': {
                'boundary_precision': result.spatial_control_results['boundary_precision']['sharpness_score'],
                'transition_smoothness': result.spatial_control_results['boundary_precision']['smoothness_score'],
                'control_precision': result.spatial_control_results['control_precision']['precision_score'],
                'geometric_consistency': result.spatial_control_results['summary']['geometric_consistency']
            },
            'system_performance_results': {
                'inference_speed': {
                    'avg_ms_per_frame': result.system_performance_results['compute_performance']['inference_time_ms'],
                    'fps': result.system_performance_results['compute_performance']['fps']
                },
                'memory_usage': {
                    'peak_gb': result.system_performance_results['compute_performance']['peak_memory_gb'],
                    'avg_gb': result.system_performance_results['compute_performance']['avg_memory_gb']
                },
                'scalability': {
                    'max_resolution': result.system_performance_results['scalability']['max_supported_resolution'],
                    'max_concepts': result.system_performance_results['scalability']['max_concepts']
                },
                'stability_score': result.system_performance_results['system_stability']['stability_score']
            },
            'overall_assessment': {
                'concept_separation_score': result.concept_separation_results['summary']['overall_score'],
                'spatial_control_score': result.spatial_control_results['summary']['overall_score'],
                'system_performance_score': result.system_performance_results['summary']['overall_score'],
                'final_score': (
                    result.concept_separation_results['summary']['overall_score'] +
                    result.spatial_control_results['summary']['overall_score'] +
                    result.system_performance_results['summary']['overall_score']
                ) / 3
            }
        }
        
        # 保存报告
        report_path = os.path.join(self.output_dir, "stage5_validation_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        self._print_report_summary(report)
        
        print(f"📊 详细验证报告已保存到: {report_path}")
    
    def _print_report_summary(self, report: Dict):
        """打印报告摘要"""
        print("\n📊 阶段5验证报告摘要:")
        print(f"   总耗时: {report['validation_summary']['execution_time']:.2f}s")
        print(f"   执行状态: {report['validation_summary']['overall_status']}")
        
        print("\n   💫 概念分离效果评分:")
        print(f"   - 语义一致性: {report['concept_separation_results']['semantic_consistency']:.3f}")
        print(f"   - 视觉质量 PSNR: {report['concept_separation_results']['visual_quality']['psnr']:.2f}dB")
        print(f"   - 视觉质量 SSIM: {report['concept_separation_results']['visual_quality']['ssim']:.3f}")
        
        print("\n   🎯 空间控制精度评分:")
        print(f"   - 边界精度: {report['spatial_control_results']['boundary_precision']:.3f}")
        print(f"   - 过渡平滑度: {report['spatial_control_results']['transition_smoothness']:.3f}")
        print(f"   - 控制精度: {report['spatial_control_results']['control_precision']:.3f}")
        
        print("\n   ⚡ 系统性能评分:")
        print(f"   - 推理速度: {report['system_performance_results']['inference_speed']['avg_ms_per_frame']:.1f}ms/帧 ({report['system_performance_results']['inference_speed']['fps']:.1f}FPS)")
        print(f"   - 峰值内存: {report['system_performance_results']['memory_usage']['peak_gb']:.2f}GB")
        print(f"   - 可扩展分辨率: {report['system_performance_results']['scalability']['max_resolution']}px")
        print(f"   - 系统稳定性: {report['system_performance_results']['stability_score']:.3f}")
        
        print("\n   🌟 总体评估:")
        print(f"   - 概念分离总分: {report['overall_assessment']['concept_separation_score']:.3f}/1.0")
        print(f"   - 空间控制总分: {report['overall_assessment']['spatial_control_score']:.3f}/1.0")
        print(f"   - 系统性能总分: {report['overall_assessment']['system_performance_score']:.3f}/1.0")
        print(f"   - 最终评分: {report['overall_assessment']['final_score']:.3f}/1.0")
    
    def _update_config(self, base_config: Dict, update_config: Dict):
        """递归更新配置"""
        for key, value in update_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._update_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def _save_config(self):
        """保存配置"""
        config_path = os.path.join(self.output_dir, "stage5_config.json")
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)


def run_stage5_validation(experiment_dirs: Dict[str, str], 
                        config: Dict = None) -> Stage5ValidationResult:
    """
    运行阶段5验证的便捷函数
    
    Args:
        experiment_dirs: 实验目录路径
        config: 验证配置
        
    Returns:
        Stage5ValidationResult: 验证结果
    """
    pipeline = Stage5MainPipeline(experiment_dirs, config)
    return pipeline.run_complete_validation()


if __name__ == "__main__":
    # 测试运行
    test_dirs = {
        'output': 'output/test_experiment',
        'checkpoints': 'checkpoints/test_experiment'
    }
    
    run_stage5_validation(test_dirs)