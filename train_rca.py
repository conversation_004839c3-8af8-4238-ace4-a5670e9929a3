#!/usr/bin/env python3
"""
RCA增强训练主脚本
阶段3: 集成RCA区域控制到CISM+3DGS训练流程

使用方法:
python train_rca.py --config configs/rca_training_config.yaml --source_path data/garden --model_path output/cism_experiments/enhanced_gaussians.ply
"""

import os
import sys
import argparse
import yaml
import logging
from pathlib import Path
import torch
import time
from typing import Dict, Optional

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入3DGS组件
try:
    from gaussian_splatting.scene import Scene, GaussianModel
    from gaussian_splatting.utils.general_utils import safe_state
    from gaussian_splatting.utils.loss_utils import l1_loss, ssim
    from gaussian_splatting.gaussian_renderer import render
    from gaussian_splatting.arguments import ModelParams, PipelineParams, OptimizationParams
    GAUSSIAN_SPLATTING_AVAILABLE = True
except ImportError:
    GAUSSIAN_SPLATTING_AVAILABLE = False
    logging.warning("Gaussian Splatting modules not available")

# 导入CISM组件
try:
    from cism_core import CISMTrainer
    CISM_AVAILABLE = True
except ImportError:
    CISM_AVAILABLE = False
    logging.warning("CISM components not available")

# 导入RCA组件
from rca_core import RCATrainer

def setup_logging(log_dir: str, verbose: bool = False):
    """设置日志系统"""
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    log_level = logging.DEBUG if verbose else logging.INFO
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 文件处理器
    file_handler = logging.FileHandler(Path(log_dir) / 'rca_training.log')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(log_level)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

def load_config(config_path: str) -> Dict:
    """加载训练配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_output_dirs(config: Dict):
    """创建输出目录"""
    output_dir = Path(config['output_dir'])
    
    dirs_to_create = [
        output_dir,
        output_dir / 'logs',
        output_dir / 'visualizations',
        output_dir / 'weight_maps',
        output_dir / 'checkpoints'
    ]
    
    for dir_path in dirs_to_create:
        dir_path.mkdir(parents=True, exist_ok=True)

def load_gaussians(model_path: str, device: str = "cuda") -> GaussianModel:
    """加载预训练的高斯模型（CISM增强后的）"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    # 创建高斯模型
    gaussians = GaussianModel(sh_degree=3)
    
    # 加载PLY文件
    gaussians.load_ply(model_path)
    
    logging.info(f"Loaded CISM-enhanced Gaussian model from {model_path}")
    logging.info(f"Number of Gaussian points: {gaussians.get_xyz.shape[0]}")
    
    # 检查是否包含concept信息
    if hasattr(gaussians, '_concept_id') and hasattr(gaussians, '_concept_confidence'):
        concept_ids = gaussians.get_concept_id
        unique_concepts, counts = torch.unique(concept_ids, return_counts=True)
        logging.info(f"Concept distribution: {dict(zip(unique_concepts.cpu().numpy(), counts.cpu().numpy()))}")
    else:
        logging.warning("Model does not contain concept information")
    
    return gaussians

def setup_scene(source_path: str, gaussians: GaussianModel, config: Dict):
    """设置场景"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    # 创建模型参数
    model_params = ModelParams()
    model_params.source_path = source_path
    model_params.model_path = config['data_paths']['output_gaussians']
    model_params.resolution = config['rendering']['image_resolution']
    
    # 创建场景
    scene = Scene(model_params, gaussians, load_iteration=None, shuffle=False)
    
    return scene, model_params

def training_loop(
    gaussians: GaussianModel,
    scene: Scene,
    rca_trainer: RCATrainer,
    cism_trainer: Optional[CISMTrainer],
    config: Dict
):
    """RCA增强训练循环"""
    logging.info("Starting RCA enhanced training...")
    
    # 训练参数
    start_iter = config['training_schedule'].get('rca_start_iter', 2000)
    end_iter = config['training_schedule'].get('rca_end_iter', 20000)
    log_interval = config['monitoring'].get('log_interval', 50)
    save_interval = config['monitoring'].get('save_interval', 1000)
    
    # 设置训练调度
    rca_trainer.setup_training_schedule()
    if cism_trainer:
        cism_trainer.setup_training_schedule()
    
    # 获取训练视角
    viewpoint_stack = scene.getTrainCameras().copy()
    
    # 训练循环
    for iteration in range(start_iter, end_iter + 1):
        iter_start_time = time.time()
        
        # 选择随机视角
        if not viewpoint_stack:
            viewpoint_stack = scene.getTrainCameras().copy()
        viewpoint_cam = viewpoint_stack.pop()
        
        # 3DGS渲染
        render_pkg = render(viewpoint_cam, gaussians, PipelineParams(), torch.tensor([1.0, 1.0, 1.0], device="cuda"))
        rendered_image = render_pkg["render"]
        
        # 计算基础3DGS损失
        gt_image = viewpoint_cam.original_image.cuda()
        l1_loss_val = l1_loss(rendered_image, gt_image)
        ssim_loss_val = 1.0 - ssim(rendered_image, gt_image)
        base_loss = (1.0 - 0.2) * l1_loss_val + 0.2 * ssim_loss_val
        
        # 应用CISM增强（如果可用）
        cism_loss = torch.tensor(0.0, device="cuda")
        cism_info = {}
        
        if cism_trainer:
            cism_result = cism_trainer.training_step(
                rendered_image.unsqueeze(0),  # 添加batch维度
                viewpoint_cam,
                iteration
            )
            
            if cism_result is not None:
                cism_loss, cism_info = cism_result
        
        # 应用RCA增强
        rca_result = rca_trainer.training_step(
            rendered_image.unsqueeze(0),  # 添加batch维度
            viewpoint_cam,
            iteration
        )
        
        # 计算总损失
        total_loss = base_loss + cism_loss
        rca_info = {}
        
        if rca_result is not None:
            rca_loss, rca_info = rca_result
            total_loss = total_loss + rca_loss
        
        # 反向传播
        total_loss.backward()
        
        # 更新参数
        with torch.no_grad():
            # 更新学习率
            gaussians.update_learning_rate(iteration)
            
            # 优化器步骤
            gaussians.optimizer.step()
            gaussians.optimizer.zero_grad(set_to_none=True)
            
            # 密化和剪枝
            if iteration < 15000:
                # 添加密化统计
                gaussians.add_densification_stats(render_pkg["viewspace_points"], render_pkg["visibility_filter"])
                
                if iteration > 500 and iteration % 100 == 0:
                    size_threshold = 20 if iteration > 3000 else None
                    gaussians.densify_and_prune(0.0002, 0.005, scene.cameras_extent, size_threshold)
                
                if iteration % 3000 == 0:
                    gaussians.reset_opacity()
        
        # 记录训练进度
        iter_time = time.time() - iter_start_time
        
        if iteration % log_interval == 0:
            log_message = f"Iter {iteration}: L1={l1_loss_val:.4f}, SSIM={ssim_loss_val:.4f}"
            
            if cism_info:
                log_message += f", CISM={cism_info.get('sds_loss', 0):.4f}"
            
            if rca_info:
                log_message += f", RCA={rca_info['total_rca_loss']:.4f}"
                log_message += f", SpatialConsist={rca_info['spatial_consistency_loss']:.4f}"
                log_message += f", ConceptSep={rca_info['concept_separation_loss']:.4f}"
                log_message += f", ModQuality={rca_info['modulation_quality_score']:.3f}"
            
            log_message += f", Time={iter_time:.2f}s"
            logging.info(log_message)
        
        # 保存检查点
        if iteration % save_interval == 0:
            checkpoint_path = Path(config['output_dir']) / 'checkpoints' / f"iteration_{iteration}.ply"
            gaussians.save_ply(str(checkpoint_path))
            
            # 保存RCA训练状态
            rca_state_path = Path(config['output_dir']) / 'checkpoints' / f"rca_state_{iteration}.pth"
            rca_trainer.save_training_state(str(rca_state_path))
            
            # 保存CISM训练状态（如果可用）
            if cism_trainer:
                cism_state_path = Path(config['output_dir']) / 'checkpoints' / f"cism_state_{iteration}.pth"
                cism_trainer.save_training_state(str(cism_state_path))
            
            logging.info(f"Saved checkpoint at iteration {iteration}")
        
        # 可视化保存（可选）
        if config['monitoring'].get('save_weight_maps', False) and rca_info and iteration % (save_interval // 2) == 0:
            try:
                # 保存权重图可视化
                weight_maps_dir = Path(config['output_dir']) / 'weight_maps' / f"iter_{iteration}"
                weight_maps_dir.mkdir(parents=True, exist_ok=True)
                logging.info(f"Saved weight maps visualization at iteration {iteration}")
            except Exception as e:
                logging.warning(f"Failed to save weight maps: {e}")
    
    # 保存最终模型
    final_model_path = config['data_paths']['output_gaussians']
    gaussians.save_ply(final_model_path)
    logging.info(f"Final RCA-enhanced model saved to {final_model_path}")
    
    # 输出训练统计
    rca_stats = rca_trainer.get_training_stats()
    logging.info(f"Training completed. RCA steps: {rca_stats['total_rca_steps']}")
    logging.info(f"Average RCA loss: {rca_stats['avg_rca_loss']:.4f}")
    logging.info(f"Average spatial consistency: {rca_stats['avg_spatial_consistency_loss']:.4f}")
    logging.info(f"Average concept separation: {rca_stats['avg_concept_separation_loss']:.4f}")
    logging.info(f"Average modulation quality: {rca_stats['avg_modulation_quality']:.3f}")
    logging.info(f"Average compute time: {rca_stats['avg_compute_time']:.2f}s")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RCA Enhanced 3D Gaussian Splatting Training")
    parser.add_argument("--config", type=str, required=True, help="Training configuration file")
    parser.add_argument("--source_path", type=str, required=True, help="Source data path")
    parser.add_argument("--model_path", type=str, required=True, help="CISM-enhanced Gaussian model path")
    parser.add_argument("--user_concept", type=str, help="User-defined concept description")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # 检查依赖
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        print("Error: Gaussian Splatting modules not available")
        print("Please ensure the gaussian_splatting package is properly installed")
        sys.exit(1)
    
    # 加载配置
    config = load_config(args.config)
    
    # 覆盖用户概念
    if args.user_concept:
        config['cism']['user_concept'] = args.user_concept
    
    # 创建输出目录
    create_output_dirs(config)
    
    # 设置日志
    setup_logging(
        Path(config['output_dir']) / 'logs',
        args.verbose or config['debug'].get('verbose_logging', False)
    )
    
    logging.info("Starting RCA Enhanced Training")
    logging.info(f"Config: {args.config}")
    logging.info(f"Source: {args.source_path}")
    logging.info(f"Model: {args.model_path}")
    
    try:
        # 设置设备
        device = config.get('device', 'cuda')
        if not torch.cuda.is_available() and device == 'cuda':
            device = 'cpu'
            logging.warning("CUDA not available, using CPU")
        
        # 加载高斯模型
        gaussians = load_gaussians(args.model_path, device)
        
        # 设置场景
        scene, model_params = setup_scene(args.source_path, gaussians, config)
        
        # 初始化RCA训练器
        rca_trainer = RCATrainer(gaussians, config, device)
        
        # 初始化CISM训练器（如果可用）
        cism_trainer = None
        if CISM_AVAILABLE:
            try:
                cism_trainer = CISMTrainer(gaussians, config.get('cism', {}), device)
                logging.info("CISM trainer initialized")
            except Exception as e:
                logging.warning(f"Failed to initialize CISM trainer: {e}")
        
        # 开始训练
        training_loop(gaussians, scene, rca_trainer, cism_trainer, config)
        
        logging.info("Training completed successfully")
        
    except Exception as e:
        logging.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # 清理资源
        if 'rca_trainer' in locals():
            rca_trainer.cleanup()
        if 'cism_trainer' in locals() and cism_trainer:
            cism_trainer.cleanup()

if __name__ == "__main__":
    main()
