<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_quaternion_trigonometric</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_quaternion_trigonometric<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides trigonometric functions for quaternion types.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga8aa248b31d5ade470c87304df5eb7bd8"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8aa248b31d5ade470c87304df5eb7bd8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga8aa248b31d5ade470c87304df5eb7bd8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the quaternion rotation angle.  <a href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">More...</a><br /></td></tr>
<tr class="separator:ga8aa248b31d5ade470c87304df5eb7bd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c0095cfcb218c75a4b79d7687950036"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5c0095cfcb218c75a4b79d7687950036"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00257.html#ga5c0095cfcb218c75a4b79d7687950036">angleAxis</a> (T const &amp;angle, vec&lt; 3, T, Q &gt; const &amp;axis)</td></tr>
<tr class="memdesc:ga5c0095cfcb218c75a4b79d7687950036"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a quaternion from an angle and a normalized axis.  <a href="a00257.html#ga5c0095cfcb218c75a4b79d7687950036">More...</a><br /></td></tr>
<tr class="separator:ga5c0095cfcb218c75a4b79d7687950036"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga764254f10248b505e936e5309a88c23d"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga764254f10248b505e936e5309a88c23d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00257.html#ga764254f10248b505e936e5309a88c23d">axis</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga764254f10248b505e936e5309a88c23d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the q rotation axis.  <a href="a00257.html#ga764254f10248b505e936e5309a88c23d">More...</a><br /></td></tr>
<tr class="separator:ga764254f10248b505e936e5309a88c23d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Provides trigonometric functions for quaternion types. </p>
<p>Include &lt;<a class="el" href="a00136.html" title="GLM_EXT_quaternion_trigonometric ">glm/ext/quaternion_trigonometric.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00252.html" title="Exposes single-precision floating point quaternion type. ">GLM_EXT_quaternion_float</a> </dd>
<dd>
<a class="el" href="a00249.html" title="Exposes double-precision floating point quaternion type. ">GLM_EXT_quaternion_double</a> </dd>
<dd>
<a class="el" href="a00251.html" title="Provides exponential functions for quaternion types. ">GLM_EXT_quaternion_exponential</a> </dd>
<dd>
<a class="el" href="a00254.html" title="Provides geometric functions for quaternion types. ">GLM_EXT_quaternion_geometric</a> </dd>
<dd>
<a class="el" href="a00255.html" title="Exposes comparison functions for quaternion types that take a user defined epsilon values...">GLM_EXT_quaternion_relational</a> </dd>
<dd>
<a class="el" href="a00256.html" title="Provides transformation functions for quaternion types. ">GLM_EXT_quaternion_transform</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga8aa248b31d5ade470c87304df5eb7bd8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::angle </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the quaternion rotation angle. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga5c0095cfcb218c75a4b79d7687950036"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::angleAxis </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>axis</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a quaternion from an angle and a normalized axis. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">angle</td><td>Angle expressed in radians. </td></tr>
    <tr><td class="paramname">axis</td><td>Axis of the quaternion, must be normalized.</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga764254f10248b505e936e5309a88c23d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::axis </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the q rotation axis. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
