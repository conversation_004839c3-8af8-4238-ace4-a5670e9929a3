#!/usr/bin/env python3
"""
阶段4基础测试脚本：三大机制协同训练基础验证
测试阶段4主训练脚本的基本功能和组件集成

本脚本验证：
1. Stage4Config配置类
2. EnhancedTrainer基本初始化
3. 核心组件初始化流程
4. 基础接口完整性

作者: Claude Sonnet 4 (Anthropic 最先进模型)
版本: 阶段4 - 三大机制协同训练与优化
"""

import sys
import os
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_stage4_config():
    """测试阶段4配置类"""
    print("\n🧪 测试阶段4配置类")
    print("=" * 50)
    
    try:
        # 导入配置类
        sys.path.append('.')
        from enhanced_train import Stage4Config
        
        # 创建配置
        config = Stage4Config()
        print("   ✅ Stage4Config创建成功")
        
        # 检查关键配置项
        required_attrs = [
            'model_path', 'concept_masks_path', 'output_path',
            'iterations', 'cism_start_iter', 'enable_rca',
            'user_concept_prompt', 'background_prompt', 'boundary_prompt'
        ]
        
        for attr in required_attrs:
            if hasattr(config, attr):
                value = getattr(config, attr)
                print(f"   ✅ {attr}: {value}")
            else:
                print(f"   ❌ 缺失配置项: {attr}")
                return False
        
        print("   ✅ Stage4Config配置完整")
        return True
        
    except Exception as e:
        print(f"   ❌ Stage4Config测试失败: {e}")
        return False

def test_enhanced_trainer_creation():
    """测试增强训练器创建"""
    print("\n🧪 测试增强训练器创建")
    print("=" * 50)
    
    try:
        from enhanced_train import Stage4Config, EnhancedTrainer
        
        # 创建配置
        config = Stage4Config()
        config.debug_mode = True
        config.iterations = 100  # 减少迭代次数用于测试
        
        # 创建训练器
        trainer = EnhancedTrainer(config)
        print("   ✅ EnhancedTrainer创建成功")
        
        # 检查基本属性
        required_attrs = [
            'config', 'device', 'output_path', 'iteration', 'training_stats'
        ]
        
        for attr in required_attrs:
            if hasattr(trainer, attr):
                print(f"   ✅ {attr}: 已初始化")
            else:
                print(f"   ❌ 缺失属性: {attr}")
                return False
        
        # 检查训练统计结构
        expected_stats = ['total_losses', 'cism_losses', 'rca_losses', 'training_times']
        for stat in expected_stats:
            if stat in trainer.training_stats:
                print(f"   ✅ 训练统计 {stat}: 已初始化")
            else:
                print(f"   ❌ 缺失训练统计: {stat}")
                return False
        
        print("   ✅ EnhancedTrainer基本结构完整")
        return True
        
    except Exception as e:
        print(f"   ❌ EnhancedTrainer创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trainer_methods():
    """测试训练器方法完整性"""
    print("\n🧪 测试训练器方法完整性")
    print("=" * 50)
    
    try:
        from enhanced_train import Stage4Config, EnhancedTrainer
        
        config = Stage4Config()
        trainer = EnhancedTrainer(config)
        
        # 检查关键方法
        required_methods = [
            'initialize_all_components',
            'main_training_loop',
            'single_iteration_optimization_step',
            '_render_inputs',
            '_prepare_rca_data',
            '_compute_cism_loss',
            '_compute_total_loss_and_backward',
            '_update_learning_rate',
            '_log_training_progress',
            '_save_checkpoint',
            '_visualize_progress'
        ]
        
        for method in required_methods:
            if hasattr(trainer, method):
                print(f"   ✅ {method}: 已定义")
            else:
                print(f"   ❌ 缺失方法: {method}")
                return False
        
        print("   ✅ 训练器方法完整性验证通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 训练器方法测试失败: {e}")
        return False

def test_sds_loss_rca_support():
    """测试SDSLoss的RCA支持"""
    print("\n🧪 测试SDSLoss的RCA支持")
    print("=" * 50)
    
    try:
        from cism_core import SDSLoss
        import inspect
        
        # 检查compute_sds_loss_with_rca方法
        if hasattr(SDSLoss, 'compute_sds_loss_with_rca'):
            print("   ✅ compute_sds_loss_with_rca方法存在")
            
            # 检查方法签名
            sig = inspect.signature(SDSLoss.compute_sds_loss_with_rca)
            params = list(sig.parameters.keys())
            
            expected_params = [
                'self', 'rendered_images', 'concept_id', 'cross_attention_kwargs'
            ]
            
            for param in expected_params:
                if param in params:
                    print(f"   ✅ 参数 {param}: 存在")
                else:
                    print(f"   ❌ 缺失参数: {param}")
                    return False
            
        else:
            print("   ❌ compute_sds_loss_with_rca方法不存在")
            return False
        
        # 检查辅助方法
        helper_methods = ['_compute_ism_target_with_rca', '_compute_rca_guidance_delta']
        for method in helper_methods:
            if hasattr(SDSLoss, method):
                print(f"   ✅ 辅助方法 {method}: 存在")
            else:
                print(f"   ❌ 缺失辅助方法: {method}")
                return False
        
        print("   ✅ SDSLoss RCA支持验证通过")
        return True
        
    except Exception as e:
        print(f"   ❌ SDSLoss RCA支持测试失败: {e}")
        return False

def test_main_function():
    """测试主函数"""
    print("\n🧪 测试主函数")
    print("=" * 50)
    
    try:
        from enhanced_train import main
        
        # 检查main函数存在
        if callable(main):
            print("   ✅ main函数存在且可调用")
        else:
            print("   ❌ main函数不存在或不可调用")
            return False
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(main)
        
        print(f"   📊 main函数签名: {sig}")
        print("   ✅ main函数结构验证通过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ main函数测试失败: {e}")
        return False

def test_integration_readiness():
    """测试集成就绪性"""
    print("\n🧪 测试集成就绪性")
    print("=" * 50)
    
    try:
        # 检查所有必需的模块导入
        required_modules = [
            ('cism_core', ['DiffusionEngine', 'ConceptGuidance', 'SDSLoss', 'CISMTrainer']),
            ('rca_core', ['RCAIntegrator', 'RCADataPreparator', 'integrate_rca_to_diffusion_engine']),
            ('scene', ['Scene', 'GaussianModel']),
            ('gaussian_splatting.gaussian_renderer', ['render', 'render_concept_weights'])
        ]
        
        for module_name, classes in required_modules:
            try:
                module = __import__(module_name, fromlist=classes)
                for cls_name in classes:
                    if hasattr(module, cls_name):
                        print(f"   ✅ {module_name}.{cls_name}: 可用")
                    else:
                        print(f"   ⚠️  {module_name}.{cls_name}: 不可用")
            except ImportError as e:
                print(f"   ⚠️  {module_name}: 导入失败 ({e})")
        
        print("   ✅ 集成就绪性检查完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 集成就绪性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 阶段4基础测试：三大机制协同训练基础验证")
    print("=" * 60)
    
    # 测试项目列表
    tests = [
        ("阶段4配置类", test_stage4_config),
        ("增强训练器创建", test_enhanced_trainer_creation),
        ("训练器方法完整性", test_trainer_methods),
        ("SDSLoss RCA支持", test_sds_loss_rca_support),
        ("主函数", test_main_function),
        ("集成就绪性", test_integration_readiness),
    ]
    
    # 执行测试
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ 测试 '{test_name}' 执行异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 阶段4基础测试结果汇总:")
    print("-" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   - {test_name}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🏆 阶段4基础测试：全部通过！")
        print("\n🎉 三大机制协同训练系统基础结构完整！")
        print("   ✅ 配置系统完整")
        print("   ✅ 训练器结构完整")
        print("   ✅ RCA集成支持完整")
        print("   ✅ 接口完整性验证通过")
        print("\n🚀 系统准备好进行实际训练测试！")
        return True
    else:
        print("⚠️  阶段4基础测试：部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
