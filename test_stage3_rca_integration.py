#!/usr/bin/env python3
"""
阶段3测试脚本：RCA机制集成验证
测试RCA（区域概念注意力）机制的集成和基本功能

本脚本验证：
1. RCA模块导入和初始化
2. RCA集成器功能
3. 概念感知注意力处理器
4. DiffusionEngine的RCA支持
5. 基础的RCA功能测试

作者: Claude Sonnet 4 (Anthropic 最先进模型)
版本: 阶段3 - RCA机制移植与适配
"""

import sys
import os
import torch
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_rca_module_import():
    """测试RCA模块导入"""
    print("\n🧪 测试RCA模块导入")
    print("=" * 50)
    
    try:
        # 测试RCA核心模块导入
        from rca_core import (
            ConceptAwareAttnProcessor,
            RCAIntegrator,
            create_rca_integrator,
            integrate_rca_to_diffusion_engine,
            get_rca_info,
            print_rca_info
        )
        print("   ✅ RCA核心模块导入成功")
        
        # 打印RCA信息
        print_rca_info()
        
        return True
        
    except ImportError as e:
        print(f"   ❌ RCA模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ RCA模块导入异常: {e}")
        return False

def test_concept_aware_attention_processor():
    """测试概念感知注意力处理器"""
    print("\n🧪 测试概念感知注意力处理器")
    print("=" * 50)
    
    try:
        from rca_core import ConceptAwareAttnProcessor
        
        # 创建处理器
        processor = ConceptAwareAttnProcessor(
            enable_lora=False,
            debug_mode=True
        )
        print("   ✅ ConceptAwareAttnProcessor创建成功")
        
        # 测试基本属性
        assert hasattr(processor, '__call__'), "处理器缺少__call__方法"
        assert hasattr(processor, '_base_forward'), "处理器缺少_base_forward方法"
        assert hasattr(processor, '_concept_forward'), "处理器缺少_concept_forward方法"
        print("   ✅ 处理器接口完整")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 概念感知注意力处理器测试失败: {e}")
        return False

def test_rca_integrator():
    """测试RCA集成器"""
    print("\n🧪 测试RCA集成器")
    print("=" * 50)
    
    try:
        from rca_core import RCAIntegrator, create_rca_integrator
        
        # 创建集成器
        integrator = create_rca_integrator(
            enable_lora=False,
            debug_mode=True
        )
        print("   ✅ RCAIntegrator创建成功")
        
        # 测试基本属性
        assert hasattr(integrator, 'integrate_rca'), "集成器缺少integrate_rca方法"
        assert hasattr(integrator, 'disable_rca'), "集成器缺少disable_rca方法"
        assert hasattr(integrator, 'is_rca_enabled'), "集成器缺少is_rca_enabled方法"
        assert hasattr(integrator, 'get_integration_status'), "集成器缺少get_integration_status方法"
        print("   ✅ 集成器接口完整")
        
        # 测试状态查询
        status = integrator.get_integration_status()
        assert isinstance(status, dict), "状态信息应该是字典"
        assert 'is_integrated' in status, "状态信息缺少is_integrated"
        print("   ✅ 集成器状态查询正常")
        
        return True
        
    except Exception as e:
        print(f"   ❌ RCA集成器测试失败: {e}")
        return False

def test_diffusion_engine_rca_support():
    """测试DiffusionEngine的RCA支持"""
    print("\n🧪 测试DiffusionEngine的RCA支持")
    print("=" * 50)
    
    try:
        # 检查DiffusionEngine是否支持cross_attention_kwargs
        from cism_core import DiffusionEngine
        import inspect
        
        # 检查predict_noise方法签名
        sig = inspect.signature(DiffusionEngine.predict_noise)
        params = list(sig.parameters.keys())
        
        if 'cross_attention_kwargs' in params:
            print("   ✅ DiffusionEngine支持cross_attention_kwargs参数")
        else:
            print("   ❌ DiffusionEngine缺少cross_attention_kwargs参数")
            return False
        
        print("   ✅ DiffusionEngine RCA支持验证通过")
        return True
        
    except Exception as e:
        print(f"   ❌ DiffusionEngine RCA支持测试失败: {e}")
        return False

def test_mock_rca_integration():
    """测试模拟RCA集成"""
    print("\n🧪 测试模拟RCA集成")
    print("=" * 50)
    
    try:
        # 创建模拟UNet（简化版本）
        class MockUNet:
            def __init__(self):
                self.attn_processors = {}
            
            def set_attn_processor(self, processors):
                self.attn_processors = processors
                
        mock_unet = MockUNet()
        print("   ✅ 模拟UNet创建成功")
        
        # 测试RCA集成
        from rca_core import RCAIntegrator
        
        integrator = RCAIntegrator(debug_mode=True)
        
        # 由于是模拟UNet，集成可能会失败，但我们可以测试基本流程
        try:
            success = integrator.integrate_rca(mock_unet)
            if success:
                print("   ✅ 模拟RCA集成成功")
            else:
                print("   ⚠️  模拟RCA集成失败（预期行为）")
        except Exception as e:
            print(f"   ⚠️  模拟RCA集成异常（预期行为）: {e}")
        
        # 测试状态查询
        status = integrator.get_integration_status()
        print(f"   📊 集成状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模拟RCA集成测试失败: {e}")
        return False

def test_cross_attention_kwargs_format():
    """测试cross_attention_kwargs格式"""
    print("\n🧪 测试cross_attention_kwargs格式")
    print("=" * 50)
    
    try:
        # 创建模拟的cross_attention_kwargs
        batch_size = 2
        height, width = 64, 64  # 潜空间分辨率
        
        # 模拟概念掩码
        concept_masks = {
            "bg": torch.randn(batch_size, 1, height, width),
            "concept0": torch.randn(batch_size, 1, height, width),
            "concept1": torch.randn(batch_size, 1, height, width)
        }
        
        # 模拟文本嵌入
        text_embeddings = {
            "base": torch.randn(batch_size, 77, 768),
            "concept0": torch.randn(batch_size, 77, 768),
            "concept1": torch.randn(batch_size, 77, 768)
        }
        
        # 构建cross_attention_kwargs
        cross_attention_kwargs = {
            "mask": concept_masks,
            "adapter_names": ["concept0", "concept1"],
            "text_embeddings": text_embeddings,
            "lora_scale": 1.0,
            "concept_forward": True
        }
        
        print("   ✅ cross_attention_kwargs格式创建成功")
        print(f"   📊 掩码数量: {len(concept_masks)}")
        print(f"   📊 文本嵌入数量: {len(text_embeddings)}")
        print(f"   📊 适配器数量: {len(cross_attention_kwargs['adapter_names'])}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ cross_attention_kwargs格式测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 阶段3测试：RCA机制集成验证")
    print("=" * 60)
    
    # 测试项目列表
    tests = [
        ("RCA模块导入", test_rca_module_import),
        ("概念感知注意力处理器", test_concept_aware_attention_processor),
        ("RCA集成器", test_rca_integrator),
        ("DiffusionEngine RCA支持", test_diffusion_engine_rca_support),
        ("模拟RCA集成", test_mock_rca_integration),
        ("cross_attention_kwargs格式", test_cross_attention_kwargs_format),
    ]
    
    # 执行测试
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ 测试 '{test_name}' 执行异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📋 阶段3测试结果汇总:")
    print("-" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   - {test_name}: {status}")
        if result:
            passed += 1
    
    print("-" * 40)
    print(f"🎯 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🏆 阶段3 RCA机制集成验证：全部通过！")
        return True
    else:
        print("⚠️  阶段3 RCA机制集成验证：部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
