#!/usr/bin/env python3
"""
快速调试投影问题
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
import argparse

# 添加gaussian_splatting路径
sys.path.append('./gaussian_splatting')
from scene import Scene, GaussianModel
from arguments import ModelParams

def debug_projection():
    """调试投影问题"""
    print("🔍 开始调试投影问题...")
    
    # 1. 加载模型
    print("📦 加载3DGS模型...")
    parser = argparse.ArgumentParser()
    model_params = ModelParams(parser)
    
    args = argparse.Namespace()
    args.source_path = "data/garden"
    args.model_path = "output/garden_incomplete"
    args.images = "images"
    args.resolution = 4
    args.white_background = False
    args.data_device = "cuda"
    args.eval = True
    args.sh_degree = 0
    args.unique_image = "nothing"
    
    gaussians = GaussianModel(args.sh_degree)
    scene = Scene(model_params.extract(args), gaussians, load_iteration=-1)
    
    print(f"✅ 模型加载完成，高斯点数量: {gaussians.get_xyz.shape[0]}")
    
    # 2. 获取相机
    cameras = scene.getTrainCameras()
    print(f"📷 找到 {len(cameras)} 个训练相机")
    
    # 3. 测试第一个相机的投影
    camera = cameras[0]
    print(f"\n🎯 测试相机: {camera.image_name}")
    print(f"   📍 相机分辨率: {camera.image_height} x {camera.image_width}")
    
    # 4. 检查相机矩阵
    print(f"   📍 world_view_transform shape: {camera.world_view_transform.shape}")
    print(f"   📍 projection_matrix shape: {camera.projection_matrix.shape}")
    print(f"   📍 znear: {camera.znear}, zfar: {camera.zfar}")
    
    # 5. 检查3D点分布
    xyz = gaussians.get_xyz
    print(f"\n📊 3D点分布:")
    print(f"   X: [{xyz[:, 0].min():.2f}, {xyz[:, 0].max():.2f}]")
    print(f"   Y: [{xyz[:, 1].min():.2f}, {xyz[:, 1].max():.2f}]")
    print(f"   Z: [{xyz[:, 2].min():.2f}, {xyz[:, 2].max():.2f}]")
    
    # 6. 手动投影测试
    print(f"\n🔧 手动投影测试...")
    with torch.no_grad():
        pixel_coords, visible_mask = gaussians._project_points_to_camera_view(xyz, camera)
        print(f"   📊 投影结果: {visible_mask.sum()}/{len(visible_mask)} 点可见")
        
        if visible_mask.sum() > 0:
            visible_pixels = pixel_coords[visible_mask]
            print(f"   📍 可见点像素坐标范围:")
            print(f"      X: [{visible_pixels[:, 0].min():.1f}, {visible_pixels[:, 0].max():.1f}]")
            print(f"      Y: [{visible_pixels[:, 1].min():.1f}, {visible_pixels[:, 1].max():.1f}]")
        else:
            print("   ❌ 没有可见点！")
            
            # 详细调试步骤
            print("\n🔍 详细调试步骤:")
            
            # 步骤1: 世界坐标到相机坐标
            ones = torch.ones(xyz.shape[0], 1, device=xyz.device, dtype=xyz.dtype)
            xyz_homo = torch.cat([xyz, ones], dim=1)
            xyz_camera_homo = xyz_homo @ camera.world_view_transform.T
            xyz_camera = xyz_camera_homo[:, :3]
            
            print(f"   📍 相机坐标范围:")
            print(f"      X: [{xyz_camera[:, 0].min():.2f}, {xyz_camera[:, 0].max():.2f}]")
            print(f"      Y: [{xyz_camera[:, 1].min():.2f}, {xyz_camera[:, 1].max():.2f}]")
            print(f"      Z: [{xyz_camera[:, 2].min():.2f}, {xyz_camera[:, 2].max():.2f}]")
            
            # 深度剔除
            depth_valid = (xyz_camera[:, 2] < -camera.znear) & (xyz_camera[:, 2] > -camera.zfar)
            print(f"   ✂️ 深度剔除: {depth_valid.sum()}/{len(depth_valid)} 点在深度范围内")
            
            # 步骤2: 相机坐标到裁剪空间
            clip_homo = xyz_camera_homo @ camera.projection_matrix.T
            
            print(f"   📍 裁剪空间坐标范围:")
            print(f"      X: [{clip_homo[:, 0].min():.2f}, {clip_homo[:, 0].max():.2f}]")
            print(f"      Y: [{clip_homo[:, 1].min():.2f}, {clip_homo[:, 1].max():.2f}]")
            print(f"      Z: [{clip_homo[:, 2].min():.2f}, {clip_homo[:, 2].max():.2f}]")
            print(f"      W: [{clip_homo[:, 3].min():.2f}, {clip_homo[:, 3].max():.2f}]")
            
            # 透视除法
            w = clip_homo[:, 3:4]
            w_safe = torch.where(torch.abs(w) < 1e-6, torch.sign(w) * 1e-6, w)
            ndc_coords = clip_homo[:, :3] / w_safe
            
            print(f"   📍 NDC坐标范围:")
            print(f"      X: [{ndc_coords[:, 0].min():.2f}, {ndc_coords[:, 0].max():.2f}]")
            print(f"      Y: [{ndc_coords[:, 1].min():.2f}, {ndc_coords[:, 1].max():.2f}]")
            print(f"      Z: [{ndc_coords[:, 2].min():.2f}, {ndc_coords[:, 2].max():.2f}]")
            
            # NDC剔除
            ndc_valid = (
                (ndc_coords[:, 0] >= -1.0) & (ndc_coords[:, 0] <= 1.0) &
                (ndc_coords[:, 1] >= -1.0) & (ndc_coords[:, 1] <= 1.0) &
                (ndc_coords[:, 2] >= -1.0) & (ndc_coords[:, 2] <= 1.0)
            )
            print(f"   ✂️ NDC剔除: {ndc_valid.sum()}/{len(ndc_valid)} 点在NDC范围内")
            
            # 综合可见性
            combined_valid = depth_valid & ndc_valid
            print(f"   👁️ 综合可见性: {combined_valid.sum()}/{len(combined_valid)} 点可见")

if __name__ == "__main__":
    debug_projection()
