var searchData=
[
  ['integer_20functions',['Integer functions',['../a00370.html',1,'']]],
  ['i16',['i16',['../a00304.html#ga3ab5fe184343d394fb6c2723c3ee3699',1,'glm']]],
  ['i16vec1',['i16vec1',['../a00304.html#gafe730798732aa7b0647096a004db1b1c',1,'glm']]],
  ['i16vec2',['i16vec2',['../a00304.html#ga2996630ba7b10535af8e065cf326f761',1,'glm']]],
  ['i16vec3',['i16vec3',['../a00304.html#gae9c90a867a6026b1f6eab00456f3fb8b',1,'glm']]],
  ['i16vec4',['i16vec4',['../a00304.html#ga550831bfc26d1e0101c1cb3d79938c06',1,'glm']]],
  ['i32',['i32',['../a00304.html#ga96faea43ac5f875d2d3ffbf8d213e3eb',1,'glm']]],
  ['i32vec1',['i32vec1',['../a00304.html#ga54b8a4e0f5a7203a821bf8e9c1265bcf',1,'glm']]],
  ['i32vec2',['i32vec2',['../a00304.html#ga8b44026374982dcd1e52d22bac99247e',1,'glm']]],
  ['i32vec3',['i32vec3',['../a00304.html#ga7f526b5cccef126a2ebcf9bdd890394e',1,'glm']]],
  ['i32vec4',['i32vec4',['../a00304.html#ga866a05905c49912309ed1fa5f5980e61',1,'glm']]],
  ['i64',['i64',['../a00304.html#gadb997e409103d4da18abd837e636a496',1,'glm']]],
  ['i64vec1',['i64vec1',['../a00304.html#ga2b65767f8b5aed1bd1cf86c541662b50',1,'glm']]],
  ['i64vec2',['i64vec2',['../a00304.html#ga48310188e1d0c616bf8d78c92447523b',1,'glm']]],
  ['i64vec3',['i64vec3',['../a00304.html#ga667948cfe6fb3d6606c750729ec49f77',1,'glm']]],
  ['i64vec4',['i64vec4',['../a00304.html#gaa4e31c3d9de067029efeb161a44b0232',1,'glm']]],
  ['i8',['i8',['../a00304.html#ga302ec977b0c0c3ea245b6c9275495355',1,'glm']]],
  ['i8vec1',['i8vec1',['../a00304.html#ga7e80d927ff0a3861ced68dfff8a4020b',1,'glm']]],
  ['i8vec2',['i8vec2',['../a00304.html#gad06935764d78f43f9d542c784c2212ec',1,'glm']]],
  ['i8vec3',['i8vec3',['../a00304.html#ga5a08d36cf7917cd19d081a603d0eae3e',1,'glm']]],
  ['i8vec4',['i8vec4',['../a00304.html#ga4177a44206121dabc8c4ff1c0f544574',1,'glm']]],
  ['identity',['identity',['../a00247.html#ga81696f2b8d1db02ea1aff8da8f269314',1,'glm']]],
  ['imat2',['imat2',['../a00294.html#gaabe04f9948d4a213bb1c20137de03e01',1,'glm']]],
  ['imat2x2',['imat2x2',['../a00294.html#gaa4732a240522ad9bc28144fda2fc14ec',1,'glm']]],
  ['imat2x3',['imat2x3',['../a00294.html#ga3f42dd3d5d94a0fd5706f7ec8dd0c605',1,'glm']]],
  ['imat2x4',['imat2x4',['../a00294.html#ga9d8faafdca42583d67e792dd038fc668',1,'glm']]],
  ['imat3',['imat3',['../a00294.html#ga038f68437155ffa3c2583a15264a8195',1,'glm']]],
  ['imat3x2',['imat3x2',['../a00294.html#ga7b33bbe4f12c060892bd3cc8d4cd737f',1,'glm']]],
  ['imat3x3',['imat3x3',['../a00294.html#ga6aacc960f62e8f7d2fe9d32d5050e7a4',1,'glm']]],
  ['imat3x4',['imat3x4',['../a00294.html#ga6e9ce23496d8b08dfc302d4039694b58',1,'glm']]],
  ['imat4',['imat4',['../a00294.html#ga96b0d26a33b81bb6a60ca0f39682f7eb',1,'glm']]],
  ['imat4x2',['imat4x2',['../a00294.html#ga8ce7ef51d8b2c1901fa5414deccbc3fa',1,'glm']]],
  ['imat4x3',['imat4x3',['../a00294.html#ga705ee0bf49d6c3de4404ce2481bf0df5',1,'glm']]],
  ['imat4x4',['imat4x4',['../a00294.html#ga43ed5e4f475b6f4cad7cba78f29c405b',1,'glm']]],
  ['imulextended',['imulExtended',['../a00370.html#gac0c510a70e852f57594a9141848642e3',1,'glm']]],
  ['infiniteperspective',['infinitePerspective',['../a00243.html#ga44fa38a18349450325cae2661bb115ca',1,'glm']]],
  ['infiniteperspectivelh',['infinitePerspectiveLH',['../a00243.html#ga3201b30f5b3ea0f933246d87bfb992a9',1,'glm']]],
  ['infiniteperspectiverh',['infinitePerspectiveRH',['../a00243.html#ga99672ffe5714ef478dab2437255fe7e1',1,'glm']]],
  ['int1',['int1',['../a00315.html#ga0670a2111b5e4a6410bd027fa0232fc3',1,'glm']]],
  ['int16',['int16',['../a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9',1,'glm']]],
  ['int16_5ft',['int16_t',['../a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede',1,'glm']]],
  ['int1x1',['int1x1',['../a00315.html#ga056ffe02d3a45af626f8e62221881c7a',1,'glm']]],
  ['int2',['int2',['../a00315.html#gafe3a8fd56354caafe24bfe1b1e3ad22a',1,'glm']]],
  ['int2x2',['int2x2',['../a00315.html#ga4e5ce477c15836b21e3c42daac68554d',1,'glm']]],
  ['int2x3',['int2x3',['../a00315.html#ga197ded5ad8354f6b6fb91189d7a269b3',1,'glm']]],
  ['int2x4',['int2x4',['../a00315.html#ga2749d59a7fddbac44f34ba78e57ef807',1,'glm']]],
  ['int3',['int3',['../a00315.html#ga909c38a425f215a50c847145d7da09f0',1,'glm']]],
  ['int32',['int32',['../a00260.html#ga43d43196463bde49cb067f5c20ab8481',1,'glm']]],
  ['int32_5ft',['int32_t',['../a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710',1,'glm']]],
  ['int3x2',['int3x2',['../a00315.html#gaa4cbe16a92cf3664376c7a2fc5126aa8',1,'glm']]],
  ['int3x3',['int3x3',['../a00315.html#ga15c9649286f0bf431bdf9b3509580048',1,'glm']]],
  ['int3x4',['int3x4',['../a00315.html#gaacac46ddc7d15d0f9529d05c92946a0f',1,'glm']]],
  ['int4',['int4',['../a00315.html#gaecdef18c819c205aeee9f94dc93de56a',1,'glm']]],
  ['int4x2',['int4x2',['../a00315.html#ga97a39dd9bc7d572810d80b8467cbffa1',1,'glm']]],
  ['int4x3',['int4x3',['../a00315.html#gae4a2c53f14aeec9a17c2b81142b7e82d',1,'glm']]],
  ['int4x4',['int4x4',['../a00315.html#ga04dee1552424198b8f58b377c2ee00d8',1,'glm']]],
  ['int64',['int64',['../a00260.html#gaff5189f97f9e842d9636a0f240001b2e',1,'glm']]],
  ['int64_5ft',['int64_t',['../a00304.html#ga322a7d7d2c2c68994dc872a33de63c61',1,'glm']]],
  ['int8',['int8',['../a00260.html#ga1b956fe1df85f3c132b21edb4e116458',1,'glm']]],
  ['int8_5ft',['int8_t',['../a00304.html#ga4bf09d8838a86866b39ee6e109341645',1,'glm']]],
  ['intbitstofloat',['intBitsToFloat',['../a00241.html#ga4fb7c21c2dce064b26fd9ccdaf9adcd4',1,'glm::intBitsToFloat(int const &amp;v)'],['../a00241.html#ga7a0a8291a1cf3e1c2aee33030a1bd7b0',1,'glm::intBitsToFloat(vec&lt; L, int, Q &gt; const &amp;v)']]],
  ['integer_2ehpp',['integer.hpp',['../a00043.html',1,'']]],
  ['intermediate',['intermediate',['../a00352.html#gacc5cd5f3e78de61d141c2355417424de',1,'glm']]],
  ['interpolate',['interpolate',['../a00337.html#ga4e67863d150724b10c1ac00972dc958c',1,'glm']]],
  ['intersect_2ehpp',['intersect.hpp',['../a00044.html',1,'']]],
  ['intersectlinesphere',['intersectLineSphere',['../a00331.html#ga9c68139f3d8a4f3d7fe45f9dbc0de5b7',1,'glm']]],
  ['intersectlinetriangle',['intersectLineTriangle',['../a00331.html#ga9d29b9b3acb504d43986502f42740df4',1,'glm']]],
  ['intersectrayplane',['intersectRayPlane',['../a00331.html#gad3697a9700ea379739a667ea02573488',1,'glm']]],
  ['intersectraysphere',['intersectRaySphere',['../a00331.html#gac88f8cd84c4bcb5b947d56acbbcfa56e',1,'glm::intersectRaySphere(genType const &amp;rayStarting, genType const &amp;rayNormalizedDirection, genType const &amp;sphereCenter, typename genType::value_type const sphereRadiusSquared, typename genType::value_type &amp;intersectionDistance)'],['../a00331.html#gad28c00515b823b579c608aafa1100c1d',1,'glm::intersectRaySphere(genType const &amp;rayStarting, genType const &amp;rayNormalizedDirection, genType const &amp;sphereCenter, const typename genType::value_type sphereRadius, genType &amp;intersectionPosition, genType &amp;intersectionNormal)']]],
  ['intersectraytriangle',['intersectRayTriangle',['../a00331.html#ga65bf2c594482f04881c36bc761f9e946',1,'glm']]],
  ['inverse',['inverse',['../a00248.html#gab41da854ae678e23e114b598cbca4065',1,'glm::inverse(qua&lt; T, Q &gt; const &amp;q)'],['../a00317.html#ga070f521a953f6461af4ab4cf8ccbf27e',1,'glm::inverse(tdualquat&lt; T, Q &gt; const &amp;q)'],['../a00371.html#gaed509fe8129b01e4f20a6d0de5690091',1,'glm::inverse(mat&lt; C, R, T, Q &gt; const &amp;m)']]],
  ['inversesqrt',['inversesqrt',['../a00242.html#ga523dd6bd0ad9f75ae2d24c8e4b017b7a',1,'glm']]],
  ['inversetranspose',['inverseTranspose',['../a00295.html#gab213cd0e3ead5f316d583f99d6312008',1,'glm']]],
  ['io_2ehpp',['io.hpp',['../a00045.html',1,'']]],
  ['iround',['iround',['../a00292.html#ga57824268ebe13a922f1d69a5d37f637f',1,'glm']]],
  ['iscompnull',['isCompNull',['../a00368.html#gaf6ec1688eab7442fe96fe4941d5d4e76',1,'glm']]],
  ['isdenormal',['isdenormal',['../a00314.html#ga74aa7c7462245d83bd5a9edf9c6c2d91',1,'glm']]],
  ['isfinite',['isfinite',['../a00315.html#gaf4b04dcd3526996d68c1bfe17bfc8657',1,'glm::isfinite(genType const &amp;x)'],['../a00315.html#gac3b12b8ac3014418fe53c299478b6603',1,'glm::isfinite(const vec&lt; 1, T, Q &gt; &amp;x)'],['../a00315.html#ga8e76dc3e406ce6a4155c2b12a2e4b084',1,'glm::isfinite(const vec&lt; 2, T, Q &gt; &amp;x)'],['../a00315.html#ga929ef27f896d902c1771a2e5e150fc97',1,'glm::isfinite(const vec&lt; 3, T, Q &gt; &amp;x)'],['../a00315.html#ga19925badbe10ce61df1d0de00be0b5ad',1,'glm::isfinite(const vec&lt; 4, T, Q &gt; &amp;x)']]],
  ['isidentity',['isIdentity',['../a00340.html#gaee935d145581c82e82b154ccfd78ad91',1,'glm']]],
  ['isinf',['isinf',['../a00241.html#ga2885587c23a106301f20443896365b62',1,'glm::isinf(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00248.html#ga45722741ea266b4e861938b365c5f362',1,'glm::isinf(qua&lt; T, Q &gt; const &amp;x)']]],
  ['ismultiple',['isMultiple',['../a00261.html#gaec593d33956a8fe43f78fccc63ddde9a',1,'glm::isMultiple(genIUType v, genIUType Multiple)'],['../a00274.html#ga354caf634ef333d9cb4844407416256a',1,'glm::isMultiple(vec&lt; L, T, Q &gt; const &amp;v, T Multiple)'],['../a00274.html#gabb4360e38c0943d8981ba965dead519d',1,'glm::isMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)']]],
  ['isnan',['isnan',['../a00241.html#ga29ef934c00306490de837b4746b4e14d',1,'glm::isnan(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00248.html#ga1bb55f8963616502e96dc564384d8a03',1,'glm::isnan(qua&lt; T, Q &gt; const &amp;x)']]],
  ['isnormalized',['isNormalized',['../a00340.html#gae785af56f47ce220a1609f7f84aa077a',1,'glm::isNormalized(mat&lt; 2, 2, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00340.html#gaa068311695f28f5f555f5f746a6a66fb',1,'glm::isNormalized(mat&lt; 3, 3, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00340.html#ga4d9bb4d0465df49fedfad79adc6ce4ad',1,'glm::isNormalized(mat&lt; 4, 4, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00368.html#gac3c974f459fd75453134fad7ae89a39e',1,'glm::isNormalized(vec&lt; L, T, Q &gt; const &amp;v, T const &amp;epsilon)']]],
  ['isnull',['isNull',['../a00340.html#ga9790ec222ce948c0ff0d8ce927340dba',1,'glm::isNull(mat&lt; 2, 2, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00340.html#gae14501c6b14ccda6014cc5350080103d',1,'glm::isNull(mat&lt; 3, 3, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00340.html#ga2b98bb30a9fefa7cdea5f1dcddba677b',1,'glm::isNull(mat&lt; 4, 4, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00368.html#gab4a3637dbcb4bb42dc55caea7a1e0495',1,'glm::isNull(vec&lt; L, T, Q &gt; const &amp;v, T const &amp;epsilon)']]],
  ['isorthogonal',['isOrthogonal',['../a00340.html#ga58f3289f74dcab653387dd78ad93ca40',1,'glm']]],
  ['ispoweroftwo',['isPowerOfTwo',['../a00261.html#gadf491730354aa7da67fbe23d4d688763',1,'glm::isPowerOfTwo(genIUType v)'],['../a00274.html#gabf2b61ded7049bcb13e25164f832a290',1,'glm::isPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)']]],
  ['ivec1',['ivec1',['../a00272.html#gaedd0562c2e77714929d7723a7e2e0dba',1,'glm']]],
  ['ivec2',['ivec2',['../a00281.html#ga6f9269106d91b2d2b91bcf27cd5f5560',1,'glm']]],
  ['ivec3',['ivec3',['../a00281.html#gad0d784d8eee201aca362484d2daee46c',1,'glm']]],
  ['ivec4',['ivec4',['../a00281.html#ga5abb4603dae0ce58c595e66d9123d812',1,'glm']]]
];
