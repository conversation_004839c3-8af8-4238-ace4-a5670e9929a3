# Claude Sonnet 4 - 联合训练配置
# 基于用户建议实现的阶段4联合优化配置

# 基本配置
experiment_name: "joint_enhanced_garden"
output_dir: "output/joint_experiments"
device: "cuda"
num_concepts: 3

# 用户建议的三阶段渐进式训练调度
scheduler:
  total_iterations: 25000
  stage_a_end: 5000      # 仅3DGS阶段 - 稳定几何结构
  stage_b_end: 15000     # 3DGS + CISM阶段 - 引入语义引导
  stage_c_end: 25000     # 完整联合优化阶段 - 3DGS + CISM + RCA
  warmup_iterations: 500 # 新损失预热迭代数

# 用户建议的损失权重细致调整
loss_weights:
  # 基础权重配置
  stage_a:
    base: 1.0
    cism: 0.0
    rca: 0.0
    rca_supervision: 0.0
  
  stage_b:
    base: 1.0
    cism: 0.1           # 引入CISM语义引导
    rca: 0.0
    rca_supervision: 0.0
  
  stage_c:
    base: 1.0
    cism: 0.1
    rca: 0.05           # 引入RCA空间控制
    rca_supervision: 0.1 # 初始RCA监督权重
  
  # 用户建议的RCA监督动态调整
  rca_supervision_schedule:
    start_weight: 1.0    # 早期高权重 - 学习区域划分
    end_weight: 0.1      # 后期低权重 - 让CISM主导

# CISM配置 (继承阶段2配置)
cism:
  model_id: "runwayml/stable-diffusion-v1-5"
  half_precision: true
  concept_config_path: "configs/concept_prompts.yaml"
  user_concept: "modern minimalist garden with geometric patterns, clean lines, contemporary landscape design"
  
  # SDS损失配置
  guidance_scale: 7.5
  sds_start_weight: 0.05
  sds_end_weight: 0.1
  
  # 训练调度
  cism_start_iter: 5000   # 与stage_b开始对齐
  cism_end_iter: 25000
  cism_interval: 5

# RCA配置 (继承阶段3配置)
rca:
  # 空间注意力网络
  spatial_attention:
    input_dim: 13
    hidden_dim: 256
    dropout_rate: 0.1
    use_residual: true
    learning_rate: 0.001
    weight_decay: 0.0001
    max_grad_norm: 1.0
  
  # 概念权重渲染器
  weight_renderer:
    enable_sh: false
    background_color: [0.0, 0.0, 0.0]
  
  # 空间调制器
  spatial_modulation:
    enable_boundary_smoothing: true
    smoothing_kernel_size: 5
    smoothing_sigma: 1.0
  
  # 训练调度
  rca_start_iter: 15000   # 与stage_c开始对齐
  rca_end_iter: 25000
  rca_interval: 3         # 更频繁的RCA应用

# 现有stage4组件集成配置
integration:
  use_stage4_components: true  # 是否使用现有stage4组件
  
  # RCA-CISM集成器配置
  rca_config:
    num_concepts: 3
    hidden_dims: [256, 512, 256]
  
  renderer_config:
    num_concepts: 3
    image_height: 512
    image_width: 512
  
  # 空间一致性配置
  spatial_consistency_weight: 0.1
  temporal_consistency_weight: 0.05

# 端到端优化器配置
optimizer:
  # 3DGS基础学习率
  position_lr_init: 0.00016
  position_lr_final: 0.0000016
  feature_lr: 0.0025
  opacity_lr: 0.05
  scaling_lr: 0.005
  rotation_lr: 0.001
  
  # concept_confidence学习率
  concept_confidence_lr: 0.005
  
  # 梯度管理
  max_grad_norm: 1.0
  gradient_accumulation_steps: 1
  
  # 学习率调度
  lr_scheduler: "exponential"
  lr_decay_rate: 0.95
  lr_decay_steps: 1000

# 训练控制器配置
controller:
  # 早停配置
  early_stopping:
    enabled: true
    patience: 2000
    min_delta: 1e-6
  
  # 检查点配置
  checkpoint:
    save_interval: 1000
    max_checkpoints: 5
  
  # 验证配置
  validation:
    interval: 500
    metrics: ["psnr", "ssim", "concept_consistency"]

# 渲染配置
rendering:
  image_resolution: 512
  batch_size: 1
  random_background: true

# 监控和日志
monitoring:
  log_interval: 50
  save_interval: 1000
  eval_interval: 500
  
  # 可视化
  save_training_images: true
  save_concept_visualizations: true
  save_weight_maps: true
  save_modulation_visualizations: true
  save_loss_curves: true
  
  # 用户建议的梯度流验证
  gradient_monitoring:
    enabled: true
    check_interval: 100
    gradient_norm_threshold: 10.0

# 内存管理
memory:
  gradient_accumulation_steps: 1
  max_memory_usage: 0.9
  clear_cache_interval: 50
  
  # 混合精度训练
  mixed_precision:
    enabled: true
    loss_scale: "dynamic"

# 用户建议的质量保证配置
quality_assurance:
  # 渐进式验证
  progressive_validation:
    enabled: true
    stage_end_validation: true
  
  # 消融实验配置
  ablation_experiments:
    enabled: false  # 可选启用
    experiments:
      - "3dgs_only"
      - "3dgs_concept_id"
      - "3dgs_concept_id_cism"
      - "3dgs_concept_id_rca"
      - "full_joint"
  
  # 对比评估
  comparison_baselines:
    - "original_3dgs"
    - "cism_only"
    - "rca_only"
    - "joint_enhanced"

# 数据路径
data_paths:
  # 输入数据 (基于阶段3结果)
  gaussians_path: "output/rca_experiments/rca_enhanced_gaussians.ply"
  cameras_dir: "output/garden/"
  concept_masks_dir: "stage1_results/concept_masks/"
  
  # 输出路径
  output_gaussians: "output/joint_experiments/joint_enhanced_gaussians.ply"
  training_logs: "output/joint_experiments/logs/"
  visualizations: "output/joint_experiments/visualizations/"
  checkpoints: "output/joint_experiments/checkpoints/"

# 评估配置
evaluation:
  # 评估指标
  metrics: ["psnr", "ssim", "lpips", "concept_consistency", "spatial_coherence", "joint_quality"]
  
  # 评估视角
  eval_cameras: "test"
  
  # 联合质量评估
  joint_evaluation:
    enabled: true
    cism_quality_threshold: 0.75
    rca_quality_threshold: 0.8
    joint_consistency_threshold: 0.85
  
  # 用户建议的验证策略
  validation_strategy:
    stage_a_metrics: ["psnr", "ssim"]  # 仅几何质量
    stage_b_metrics: ["psnr", "ssim", "concept_consistency"]  # 加入语义
    stage_c_metrics: ["psnr", "ssim", "concept_consistency", "spatial_coherence", "joint_quality"]  # 完整评估

# 调试和开发
debug:
  enable_debug_mode: false
  save_intermediate_results: true
  profile_performance: true
  verbose_logging: false
  
  # 用户建议的计算图验证
  gradient_flow_check:
    enabled: true
    check_frequency: 100
    save_gradient_plots: false

# 使用说明
usage_instructions: |
  1. 确保已完成阶段1-3的训练
  2. 检查输入模型路径配置
  3. 根据需要调整三阶段训练参数
  4. 运行联合训练：
     python train_joint.py --config configs/joint_training_config.yaml
  
  5. 监控训练进度：
     tensorboard --logdir output/joint_experiments/logs/
  
  6. 评估最终结果：
     python evaluate_joint.py --model_path output/joint_experiments/joint_enhanced_gaussians.ply

# 用户建议的性能优化提示
performance_tips:
  - 使用较小的rca_interval以获得更好的空间控制
  - 在stage_c阶段监控梯度范数，避免梯度爆炸
  - 启用mixed_precision以节省内存
  - 使用gradient_monitoring验证梯度流正确性
  - 根据GPU内存调整batch_size和image_resolution

# 用户建议的故障排除
troubleshooting:
  gradient_issues:
    - 检查计算图连接是否正确
    - 验证SDS损失到3DGS参数的梯度流
    - 确认RCA网络参数正确更新
  
  convergence_issues:
    - 降低新引入损失的初始权重
    - 增加warmup_iterations
    - 调整三阶段的迭代分配
  
  memory_issues:
    - 启用mixed_precision
    - 减少image_resolution
    - 增加clear_cache_interval频率
  
  quality_issues:
    - 检查concept_id传递是否正确
    - 验证权重渲染质量
    - 调整spatial_consistency_weight
