#!/usr/bin/env python3
"""
🔥 Concept 初始化诊断脚本 - 完全修复版

修复所有问题：
1. 标准相机变换矩阵
2. 手动3D到2D投影
3. 正确的点云分布
4. 完整的坐标转换验证
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
from PIL import Image
import matplotlib.pyplot as plt

# 添加gaussian_splatting路径
sys.path.append('./gaussian_splatting')
from scene import Scene, GaussianModel
from gaussian_renderer import render
from utils.general_utils import safe_state
from arguments import ModelParams, PipelineParams
from utils.graphics_utils import BasicPointCloud
import argparse

class ConceptInitializationFixed:
    """Concept初始化完全修复版诊断器"""
    
    def __init__(self):
        self.test_dir = Path("test_concept_fixed")
        self.test_dir.mkdir(exist_ok=True)
        
    def create_test_gaussian_model(self):
        """创建正确分布的测试高斯模型"""
        print("📝 创建测试高斯模型...")
        
        # 在相机视野内创建点云 (相机在z=5，朝向原点)
        num_points = 100
        
        # 确保点在相机前方且在合理范围内
        points = np.random.randn(num_points, 3) * 0.8  # 缩小分布范围
        points[:, 2] = np.random.uniform(-2.0, 2.0, num_points)  # Z轴在[-2, 2]之间
        
        colors = np.random.rand(num_points, 3)
        normals = np.zeros_like(points)
        
        pcd = BasicPointCloud(points=points, colors=colors, normals=normals)
        
        # 创建高斯模型
        gaussians = GaussianModel(sh_degree=3)
        gaussians.create_from_pcd(pcd, spatial_lr_scale=1.0)
        
        # 调整初始参数以提高可见性
        with torch.no_grad():
            # 增加初始透明度
            gaussians._opacity.data = torch.logit(torch.ones_like(gaussians._opacity) * 0.8)
            
            # 设置合理的初始尺度
            gaussians._scaling.data = torch.log(torch.ones_like(gaussians._scaling) * 0.05)
        
        print(f"   ✅ 创建高斯模型：{num_points} 个点")
        print(f"   📍 点云范围 X:[{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
        print(f"   📍 点云范围 Y:[{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]") 
        print(f"   📍 点云范围 Z:[{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
        
        return gaussians
    
    def build_view_matrix(self, camera_center, look_at_target, up_vector):
        """构建标准LookAt视图矩阵"""
        # 计算相机坐标系的轴
        cam_z = torch.nn.functional.normalize(camera_center - look_at_target, p=2, dim=0)  # 相机Z轴（朝后）
        cam_x = torch.nn.functional.normalize(torch.cross(up_vector, cam_z), p=2, dim=0)       # 相机X轴（朝右）
        cam_y = torch.nn.functional.normalize(torch.cross(cam_z, cam_x), p=2, dim=0)               # 相机Y轴（朝上）
        
        # 构建旋转矩阵 R (世界到相机)
        R = torch.stack([cam_x, cam_y, cam_z], dim=0)  # 3x3
        
        # 构建平移向量 t (世界到相机)
        t = -torch.matmul(R, camera_center)  # 3x1
        
        # 构建4x4视图矩阵
        view_matrix = torch.eye(4, device="cuda")
        view_matrix[:3, :3] = R
        view_matrix[:3, 3] = t
        
        return view_matrix
    
    def build_projection_matrix(self, FoVx, FoVy, image_height, image_width):
        """构建标准透视投影矩阵"""
        near, far = 0.1, 100.0
        H, W = image_height, image_width
        
        # 计算焦距
        focal_y = H / (2.0 * np.tan(FoVy * 0.5))
        focal_x = W / (2.0 * np.tan(FoVx * 0.5))
        
        # 构建OpenGL风格的透视投影矩阵
        projection_matrix = torch.zeros((4, 4), device="cuda")
        projection_matrix[0, 0] = 2.0 * focal_x / W
        projection_matrix[1, 1] = 2.0 * focal_y / H
        projection_matrix[2, 2] = -(far + near) / (far - near)
        projection_matrix[2, 3] = -2.0 * far * near / (far - near)
        projection_matrix[3, 2] = -1.0
        
        return projection_matrix
    
    def create_standard_camera(self):
        """创建标准相机，使用正确的变换矩阵"""
        print("📷 创建标准相机...")
        
        class StandardCamera:
            def __init__(self, parent):
                self.image_height = 512
                self.image_width = 512
                self.FoVx = 1.2  # 约68.75度
                self.FoVy = 1.2
                
                # 相机参数
                self.camera_center = torch.tensor([0.0, 0.0, 5.0], device="cuda")
                self.look_at_target = torch.tensor([0.0, 0.0, 0.0], device="cuda")
                self.up_vector = torch.tensor([0.0, 1.0, 0.0], device="cuda")
                
                # 构建标准的LookAt视图矩阵
                self.world_view_transform = parent.build_view_matrix(
                    self.camera_center, self.look_at_target, self.up_vector
                )
                
                # 构建标准透视投影矩阵
                self.projection_matrix = parent.build_projection_matrix(
                    self.FoVx, self.FoVy, self.image_height, self.image_width
                )
                
                # 完整变换矩阵 (MVP = Projection * View)
                self.full_proj_transform = (self.projection_matrix @ self.world_view_transform).T
        
        camera = StandardCamera(self)
        
        print(f"   ✅ 相机分辨率：{camera.image_width}x{camera.image_height}")
        print(f"   📍 相机位置：{camera.camera_center}")
        print(f"   📍 朝向目标：{camera.look_at_target}")
        print(f"   📊 视场角：{np.degrees(camera.FoVx):.1f}°")
        
        return camera
    
    def manual_3d_to_2d_projection(self, gaussians, camera):
        """手动执行完整的3D到2D投影"""
        print("🔄 执行手动3D到2D投影...")
        
        # 获取所有3D点
        xyz_world = gaussians.get_xyz  # (N, 3)
        N = xyz_world.shape[0]
        
        # 转换为齐次坐标
        ones = torch.ones(N, 1, device=xyz_world.device, dtype=xyz_world.dtype)
        xyz_world_homo = torch.cat([xyz_world, ones], dim=1)  # (N, 4)
        
        print(f"   📊 输入3D点数量：{N}")
        print(f"   📍 世界坐标范围：")
        print(f"      X: [{xyz_world[:, 0].min():.3f}, {xyz_world[:, 0].max():.3f}]")
        print(f"      Y: [{xyz_world[:, 1].min():.3f}, {xyz_world[:, 1].max():.3f}]")
        print(f"      Z: [{xyz_world[:, 2].min():.3f}, {xyz_world[:, 2].max():.3f}]")
        
        # 步骤1：世界坐标到相机坐标
        xyz_camera_homo = xyz_world_homo @ camera.world_view_transform.T  # (N, 4)
        xyz_camera = xyz_camera_homo[:, :3]  # (N, 3)
        
        print(f"   📍 相机坐标范围：")
        print(f"      X: [{xyz_camera[:, 0].min():.3f}, {xyz_camera[:, 0].max():.3f}]")
        print(f"      Y: [{xyz_camera[:, 1].min():.3f}, {xyz_camera[:, 1].max():.3f}]")
        print(f"      Z: [{xyz_camera[:, 2].min():.3f}, {xyz_camera[:, 2].max():.3f}]")
        
        # 视锥剔除：保留在相机前方的点 (Z < 0，因为相机看向-Z)
        in_front_mask = xyz_camera[:, 2] < -0.1  # 大于near平面
        in_back_mask = xyz_camera[:, 2] > -100.0  # 小于far平面
        depth_valid_mask = in_front_mask & in_back_mask
        
        print(f"   ✂️ 深度剔除：{depth_valid_mask.sum()}/{N} 点在合理深度范围内")
        
        # 步骤2：相机坐标到裁剪空间坐标
        xyz_camera_homo = torch.cat([xyz_camera, ones], dim=1)  # (N, 4)
        clip_space_homo = xyz_camera_homo @ camera.projection_matrix.T  # (N, 4)
        
        print(f"   📍 裁剪空间坐标范围（齐次）：")
        print(f"      X: [{clip_space_homo[:, 0].min():.3f}, {clip_space_homo[:, 0].max():.3f}]")
        print(f"      Y: [{clip_space_homo[:, 1].min():.3f}, {clip_space_homo[:, 1].max():.3f}]")
        print(f"      Z: [{clip_space_homo[:, 2].min():.3f}, {clip_space_homo[:, 2].max():.3f}]")
        print(f"      W: [{clip_space_homo[:, 3].min():.3f}, {clip_space_homo[:, 3].max():.3f}]")
        
        # 步骤3：透视除法得到NDC坐标
        w = clip_space_homo[:, 3:4]
        w = torch.where(torch.abs(w) < 1e-6, torch.sign(w) * 1e-6, w)  # 避免除零
        ndc_coords = clip_space_homo[:, :3] / w  # (N, 3)
        
        print(f"   📍 NDC坐标范围：")
        print(f"      X: [{ndc_coords[:, 0].min():.3f}, {ndc_coords[:, 0].max():.3f}]")
        print(f"      Y: [{ndc_coords[:, 1].min():.3f}, {ndc_coords[:, 1].max():.3f}]")
        print(f"      Z: [{ndc_coords[:, 2].min():.3f}, {ndc_coords[:, 2].max():.3f}]")
        
        # 视锥剔除：保留在NDC立方体内的点
        ndc_valid_mask = (
            (ndc_coords[:, 0] >= -1.0) & (ndc_coords[:, 0] <= 1.0) &
            (ndc_coords[:, 1] >= -1.0) & (ndc_coords[:, 1] <= 1.0) &
            (ndc_coords[:, 2] >= -1.0) & (ndc_coords[:, 2] <= 1.0)
        )
        
        # 综合可见性掩码
        visible_mask = depth_valid_mask & ndc_valid_mask
        print(f"   👁️ 最终可见点：{visible_mask.sum()}/{N}")
        
        # 步骤4：NDC到像素坐标
        H, W = camera.image_height, camera.image_width
        pixel_x = (ndc_coords[:, 0] + 1.0) * 0.5 * W
        pixel_y = (1.0 - ndc_coords[:, 1]) * 0.5 * H  # Y轴翻转
        
        pixel_coords = torch.stack([pixel_x, pixel_y], dim=1)  # (N, 2)
        
        print(f"   📍 像素坐标范围：")
        print(f"      X: [{pixel_coords[:, 0].min():.1f}, {pixel_coords[:, 0].max():.1f}]")
        print(f"      Y: [{pixel_coords[:, 1].min():.1f}, {pixel_coords[:, 1].max():.1f}]")
        
        # 屏幕边界剔除
        screen_valid_mask = (
            (pixel_coords[:, 0] >= 0) & (pixel_coords[:, 0] < W) &
            (pixel_coords[:, 1] >= 0) & (pixel_coords[:, 1] < H)
        )
        
        final_valid_mask = visible_mask & screen_valid_mask
        print(f"   📺 屏幕内可见点：{final_valid_mask.sum()}/{N}")
        
        # 返回处理结果
        return {
            'pixel_coords': pixel_coords,
            'visible_mask': final_valid_mask,
            'ndc_coords': ndc_coords,
            'camera_coords': xyz_camera
        }
    
    def test_concept_sampling_fixed(self, projection_result, concept_masks, camera):
        """测试修复后的概念采样"""
        print("🎯 测试概念掩码采样...")
        
        pixel_coords = projection_result['pixel_coords']
        visible_mask = projection_result['visible_mask']
        
        if visible_mask.sum() == 0:
            print("   ❌ 没有可见点可供采样")
            return None
        
        # 获取可见点的像素坐标
        visible_pixel_coords = pixel_coords[visible_mask]
        visible_indices = torch.where(visible_mask)[0]
        
        print(f"   📊 准备采样 {len(visible_pixel_coords)} 个可见点")
        
        # 转换为整数像素坐标
        H, W = camera.image_height, camera.image_width
        pixel_coords_int = visible_pixel_coords.round().long()
        
        # 确保在边界内
        pixel_coords_int[:, 0] = torch.clamp(pixel_coords_int[:, 0], 0, W-1)
        pixel_coords_int[:, 1] = torch.clamp(pixel_coords_int[:, 1], 0, H-1)
        
        # 采样每个概念的掩码值
        sampled_values = {}
        for concept_id, mask in concept_masks.items():
            values = mask[pixel_coords_int[:, 1], pixel_coords_int[:, 0]]
            sampled_values[concept_id] = values
            
            # 统计
            positive_count = (values > 0.5).sum()
            print(f"   📍 概念{concept_id}: {positive_count}/{len(values)} 点为正值")
        
        # 构建概念隶属度向量
        concept_memberships = torch.zeros((len(visible_pixel_coords), 3), device="cuda")
        for concept_id, values in sampled_values.items():
            concept_memberships[:, concept_id] = values
        
        # 归一化为概率分布
        membership_sums = concept_memberships.sum(dim=1, keepdim=True)
        membership_sums = torch.where(membership_sums < 1e-6, torch.ones_like(membership_sums), membership_sums)
        concept_memberships = concept_memberships / membership_sums
        
        print(f"   ✅ 生成概念隶属度向量：{concept_memberships.shape}")
        print(f"   📊 隶属度分布统计：")
        for i in range(3):
            mean_val = concept_memberships[:, i].mean()
            print(f"      概念{i}: 平均={mean_val:.3f}")
        
        return {
            'visible_indices': visible_indices,
            'concept_memberships': concept_memberships,
            'sampled_values': sampled_values
        }
    
    def create_test_concept_masks(self, camera):
        """创建测试概念掩码"""
        print("🎭 创建测试概念掩码...")
        
        H, W = camera.image_height, camera.image_width
        concept_masks = {}
        
        # 创建更明显的区域分布
        center_y, center_x = H // 2, W // 2
        y, x = torch.meshgrid(torch.arange(H, device="cuda"), torch.arange(W, device="cuda"), indexing='ij')
        dist_from_center = torch.sqrt((x - center_x)**2 + (y - center_y)**2)
        max_dist = min(H, W) // 2
        
        # 概念0：背景（外围区域）
        mask0 = torch.zeros((H, W), dtype=torch.float32, device="cuda")
        mask0[dist_from_center > max_dist * 0.8] = 1.0
        
        # 概念1：修复区域（中心圆形）
        mask1 = torch.zeros((H, W), dtype=torch.float32, device="cuda")
        mask1[dist_from_center < max_dist * 0.4] = 1.0
        
        # 概念2：边界区域（环形）
        mask2 = torch.zeros((H, W), dtype=torch.float32, device="cuda")
        mask2[(dist_from_center >= max_dist * 0.4) & (dist_from_center <= max_dist * 0.8)] = 1.0
        
        concept_masks[0] = mask0
        concept_masks[1] = mask1
        concept_masks[2] = mask2
        
        # 保存可视化
        self.save_mask_visualizations(concept_masks, H, W)
        
        print(f"   ✅ 创建3个概念掩码，分辨率：{H}x{W}")
        return concept_masks
    
    def save_mask_visualizations(self, concept_masks, H, W):
        """保存掩码可视化"""
        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
        
        # 显示各个概念掩码
        for i, (concept_id, mask) in enumerate(concept_masks.items()):
            axes[i].imshow(mask.cpu().numpy(), cmap='viridis')
            axes[i].set_title(f'Concept {concept_id}')
            axes[i].axis('off')
        
        # 显示合成掩码
        combined_mask = torch.zeros((H, W, 3), device="cuda")
        for concept_id, mask in concept_masks.items():
            combined_mask[:, :, concept_id] = mask
        
        axes[3].imshow(combined_mask.cpu().numpy())
        axes[3].set_title('Combined (RGB)')
        axes[3].axis('off')
        
        plt.tight_layout()
        plt.savefig(self.test_dir / "concept_masks_visualization.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"   💾 掩码可视化已保存：{self.test_dir}/concept_masks_visualization.png")
    
    def test_render_comparison(self, gaussians, camera):
        """对比render函数和手动投影的结果"""
        print("🔍 对比render函数和手动投影...")
        
        # 创建渲染管道参数
        pipe_params = PipelineParams(argparse.ArgumentParser())
        pipe_args = argparse.Namespace()
        pipe_args.convert_SHs_python = False
        pipe_args.compute_cov3D_python = False
        pipe_args.debug = False
        pipe = pipe_params.extract(pipe_args)
        
        bg_color = torch.tensor([0, 0, 0], dtype=torch.float32, device="cuda")
        
        with torch.no_grad():
            render_result = render(camera, gaussians, pipe, bg_color)
            
        viewspace_points = render_result.get("viewspace_points", None)
        visibility_filter = render_result.get("visibility_filter", None)
        
        print(f"   📊 Render函数结果：")
        if viewspace_points is not None:
            print(f"      viewspace_points形状：{viewspace_points.shape}")
            if visibility_filter is not None:
                visible_count = visibility_filter.sum()
                print(f"      可见点数量：{visible_count}/{len(visibility_filter)}")
            else:
                print(f"      无visibility_filter")
        else:
            print(f"      无viewspace_points")
            
        return render_result
    
    def run_complete_test(self):
        """运行完整的修复测试"""
        print("🚀 启动完整修复测试...")
        print("=" * 60)
        
        # 1. 创建测试组件
        gaussians = self.create_test_gaussian_model()
        camera = self.create_standard_camera()
        concept_masks = self.create_test_concept_masks(camera)
        
        print("\n" + "=" * 60)
        
        # 2. 对比render函数结果
        render_result = self.test_render_comparison(gaussians, camera)
        
        print("\n" + "=" * 60)
        
        # 3. 执行手动投影
        projection_result = self.manual_3d_to_2d_projection(gaussians, camera)
        
        print("\n" + "=" * 60)
        
        # 4. 测试概念采样
        sampling_result = self.test_concept_sampling_fixed(projection_result, concept_masks, camera)
        
        print("\n" + "=" * 60)
        
        # 5. 生成总结报告
        self.generate_summary_report(render_result, projection_result, sampling_result)
        
        print(f"\n🎉 完整测试完成！结果保存在：{self.test_dir}")
    
    def generate_summary_report(self, render_result, projection_result, sampling_result):
        """生成总结报告"""
        print("📋 生成总结报告...")
        
        # 统计信息
        total_points = projection_result['pixel_coords'].shape[0]
        visible_points = projection_result['visible_mask'].sum().item()
        
        # render函数结果
        render_visible = 0
        if render_result.get("visibility_filter") is not None:
            render_visible = render_result["visibility_filter"].sum().item()
        
        print(f"\n📊 测试结果总结：")
        print(f"   🔢 总点数：{total_points}")
        print(f"   👁️ 手动投影可见点：{visible_points}")
        print(f"   🎭 Render函数可见点：{render_visible}")
        
        if sampling_result is not None:
            sampled_points = len(sampling_result['visible_indices'])
            print(f"   🎯 成功采样点：{sampled_points}")
            
            # 概念分布
            concept_memberships = sampling_result['concept_memberships']
            dominant_concepts = concept_memberships.argmax(dim=1)
            for i in range(3):
                count = (dominant_concepts == i).sum().item()
                print(f"   📍 主导概念{i}的点数：{count}")
        else:
            print(f"   ❌ 概念采样失败")
        
        # 保存详细报告到文件
        report_file = self.test_dir / "test_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Concept初始化测试报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"测试时间：{np.datetime64('now')}\n")
            f.write(f"总点数：{total_points}\n")
            f.write(f"手动投影可见点：{visible_points}\n")
            f.write(f"Render函数可见点：{render_visible}\n")
            
            if sampling_result is not None:
                f.write(f"成功采样点：{len(sampling_result['visible_indices'])}\n")
                f.write("\n概念分布：\n")
                concept_memberships = sampling_result['concept_memberships']
                dominant_concepts = concept_memberships.argmax(dim=1)
                for i in range(3):
                    count = (dominant_concepts == i).sum().item()
                    f.write(f"  概念{i}：{count} 点\n")
        
        print(f"   💾 详细报告已保存：{report_file}")

def main():
    """主函数"""
    print("🔥 Concept初始化诊断工具 - 完全修复版")
    
    diagnostic = ConceptInitializationFixed()
    diagnostic.run_complete_test()

if __name__ == "__main__":
    main() 