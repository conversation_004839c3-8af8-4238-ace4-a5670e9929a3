{"concept_separation": {"status": "simulated", "summary": {"overall_score": 0.85, "concept_separation_score": 0.88, "visual_quality": 0.82, "ablation_improvement": 0.15}, "output_dir": "output/stage5_validation/concept_separation"}, "spatial_control": {"status": "simulated", "summary": {"spatial_accuracy": 0.92, "boundary_quality": 0.89, "weight_consistency": 0.94}, "output_dir": "output/stage5_validation/spatial_control"}, "performance": {"status": "simulated", "summary": {"avg_inference_time": 3.2, "memory_usage_gb": 6.8, "gpu_utilization": 0.85}, "output_dir": "output/stage5_validation/performance"}, "visualizations": {"status": "simulated", "concept_visualizations": {"3d_plot": "concept_distribution_3d.html"}, "training_visualizations": {"loss_curves": "training_curves.png"}, "comparison_visualizations": {"before_after": "comparison.mp4"}, "interactive_report": "interactive_report.html", "output_dir": "output/stage5_validation/visualizations"}, "integration_test": {"status": "completed", "integration_results": {"test_scenarios": [{"scenario": {"name": "modern_garden", "description": "现代简约花园场景", "user_concept": "modern minimalist garden with geometric patterns", "expected_changes": ["geometric_elements", "clean_lines", "modern_aesthetics"]}, "status": "completed", "execution_time": 62.0, "quality_score": 0.9700000000000001, "user_satisfaction": 0.87, "concept_accuracy": 0.87, "output_files": {"final_model": "output/stage5_validation/integration_test/modern_garden_final.ply", "render_video": "output/stage5_validation/integration_test/modern_garden_demo.mp4", "comparison_images": "output/stage5_validation/integration_test/modern_garden_comparison/"}}, {"scenario": {"name": "traditional_garden", "description": "传统英式花园场景", "user_concept": "traditional English garden with roses and hedges", "expected_changes": ["traditional_elements", "natural_curves", "classic_aesthetics"]}, "status": "completed", "execution_time": 69.0, "quality_score": 0.9400000000000001, "user_satisfaction": 0.84, "concept_accuracy": 0.94, "output_files": {"final_model": "output/stage5_validation/integration_test/traditional_garden_final.ply", "render_video": "output/stage5_validation/integration_test/traditional_garden_demo.mp4", "comparison_images": "output/stage5_validation/integration_test/traditional_garden_comparison/"}}, {"scenario": {"name": "zen_garden", "description": "日式禅意花园场景", "user_concept": "Japanese zen garden with stones and minimal vegetation", "expected_changes": ["zen_elements", "minimalism", "natural_harmony"]}, "status": "completed", "execution_time": 67.0, "quality_score": 0.92, "user_satisfaction": 0.92, "concept_accuracy": 0.9199999999999999, "output_files": {"final_model": "output/stage5_validation/integration_test/zen_garden_final.ply", "render_video": "output/stage5_validation/integration_test/zen_garden_demo.mp4", "comparison_images": "output/stage5_validation/integration_test/zen_garden_comparison/"}}], "end_to_end_success_rate": 1.0, "user_experience_score": 0.8766666666666666, "system_stability_score": 1.0}, "output_dir": "output/stage5_validation/integration_test"}}