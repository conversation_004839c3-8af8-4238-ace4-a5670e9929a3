#!/usr/bin/env python3
"""
CISM激活修复验证测试
验证CISM调度参数是否正确读取
"""

import sys
import yaml
import logging

# 添加项目路径
sys.path.append('.')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cism_activation():
    """测试CISM激活逻辑"""
    logger.info("🔥 测试CISM激活修复...")
    
    try:
        # 1. 加载配置
        logger.info("   - 加载配置文件...")
        with open("configs/cism_training_config.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        logger.info(f"   - 配置加载成功")
        
        # 2. 检查配置结构
        training_schedule = config.get('training_schedule', {})
        logger.info(f"   - training_schedule: {training_schedule}")
        
        cism_start_iter = training_schedule.get('cism_start_iter', 'NOT_FOUND')
        cism_end_iter = training_schedule.get('cism_end_iter', 'NOT_FOUND')
        cism_interval = training_schedule.get('cism_interval', 'NOT_FOUND')
        
        logger.info(f"   - cism_start_iter: {cism_start_iter}")
        logger.info(f"   - cism_end_iter: {cism_end_iter}")
        logger.info(f"   - cism_interval: {cism_interval}")
        
        # 3. 测试CISMTrainer初始化
        logger.info("   - 测试CISMTrainer初始化...")
        from cism_core.cism_trainer import CISMTrainer
        
        # 创建简化配置用于测试
        test_config = {
            'device': 'cuda',
            'model_id': 'runwayml/stable-diffusion-v1-5',
            'half_precision': True,
            'attention_slicing': True,
            'memory_efficient_attention': True,
            'concept_config_path': 'configs/concept_prompts.yaml',
            'user_concept': 'a glowing lava pool with bright orange and red molten rock',
            'training_schedule': training_schedule
        }
        
        # 注意：这里只测试参数读取，不初始化完整的CISM组件
        logger.info("   - 创建CISMTrainer实例（仅测试参数读取）...")
        
        # 模拟参数读取逻辑
        training_schedule_test = test_config.get('training_schedule', {})
        cism_start_iter_test = training_schedule_test.get('cism_start_iter', test_config.get('cism_start_iter', 2000))
        cism_end_iter_test = training_schedule_test.get('cism_end_iter', test_config.get('cism_end_iter', 25000))
        cism_interval_test = training_schedule_test.get('cism_interval', test_config.get('cism_interval', 100))
        
        logger.info(f"   ✅ 参数读取结果:")
        logger.info(f"     - cism_start_iter: {cism_start_iter_test}")
        logger.info(f"     - cism_end_iter: {cism_end_iter_test}")
        logger.info(f"     - cism_interval: {cism_interval_test}")
        
        # 4. 测试should_apply_cism逻辑
        logger.info("   - 测试should_apply_cism逻辑...")
        
        def should_apply_cism(iteration: int, start_iter: int, end_iter: int, interval: int) -> bool:
            """模拟should_apply_cism逻辑"""
            if iteration < start_iter or iteration > end_iter:
                return False
            return (iteration - start_iter) % interval == 0
        
        test_iterations = [1, 2, 3, 4, 5, 10, 15, 20]
        for iteration in test_iterations:
            should_apply = should_apply_cism(
                iteration, 
                cism_start_iter_test, 
                cism_end_iter_test, 
                cism_interval_test
            )
            status = "🔥 应用CISM" if should_apply else "⚪ 跳过CISM"
            logger.info(f"     迭代 {iteration:2d}: {status}")
        
        # 5. 验证结果
        if cism_start_iter_test == 1 and cism_interval_test == 1:
            logger.info("   ✅ CISM激活修复成功！")
            logger.info("   💡 CISM将从第1次迭代开始，每次迭代都应用")
            return True
        else:
            logger.error("   ❌ CISM激活修复失败")
            logger.error(f"   期望: start=1, interval=1")
            logger.error(f"   实际: start={cism_start_iter_test}, interval={cism_interval_test}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🚀 CISM激活修复验证测试")
    logger.info("🎯 验证CISM调度参数是否正确读取")
    logger.info("=" * 60)
    
    success = test_cism_activation()
    
    if success:
        logger.info("🎉 CISM激活修复验证成功！")
        logger.info("💡 现在可以运行完整的CISM效果测试")
    else:
        logger.error("❌ CISM激活修复验证失败")
        logger.error("🔧 需要进一步调试配置读取逻辑")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
