#!/usr/bin/env python3
"""
🔥 InFusion环境验证测试
验证CISM系统运行环境的完整性
作者: Claude Sonnet 4 (Anthropic 最先进模型)
"""

import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_python_environment():
    """测试Python环境"""
    logger.info("🔥 测试Python环境...")
    
    try:
        python_version = sys.version_info
        logger.info(f"   ✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version.major >= 3 and python_version.minor >= 8:
            logger.info("   ✅ Python版本满足要求 (>=3.8)")
        else:
            logger.warning(f"   ⚠️ Python版本可能过低: {python_version}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Python环境测试失败: {e}")
        return False

def test_pytorch():
    """测试PyTorch"""
    logger.info("🔥 测试PyTorch...")
    
    try:
        import torch
        logger.info(f"   ✅ PyTorch版本: {torch.__version__}")
        
        # 测试CUDA
        if torch.cuda.is_available():
            logger.info(f"   ✅ CUDA可用: {torch.version.cuda}")
            logger.info(f"   ✅ GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                logger.info(f"      - GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            logger.warning("   ⚠️ CUDA不可用，将使用CPU")
        
        # 测试基本操作
        x = torch.randn(2, 3)
        y = torch.randn(2, 3)
        z = x + y
        logger.info("   ✅ PyTorch基本操作正常")
        
        return True
    except ImportError:
        logger.error("❌ PyTorch未安装")
        return False
    except Exception as e:
        logger.error(f"❌ PyTorch测试失败: {e}")
        return False

def test_diffusers():
    """测试Diffusers"""
    logger.info("🔥 测试Diffusers...")
    
    try:
        import diffusers
        logger.info(f"   ✅ Diffusers版本: {diffusers.__version__}")
        
        # 测试基本导入
        from diffusers import StableDiffusionPipeline, UNet2DConditionModel, DDIMScheduler
        from diffusers.models import AutoencoderKL
        logger.info("   ✅ Diffusers核心组件导入成功")
        
        return True
    except ImportError:
        logger.error("❌ Diffusers未安装")
        return False
    except Exception as e:
        logger.error(f"❌ Diffusers测试失败: {e}")
        return False

def test_transformers():
    """测试Transformers"""
    logger.info("🔥 测试Transformers...")
    
    try:
        import transformers
        logger.info(f"   ✅ Transformers版本: {transformers.__version__}")
        
        # 测试基本导入
        from transformers import CLIPTextModel, CLIPTokenizer
        logger.info("   ✅ Transformers核心组件导入成功")
        
        return True
    except ImportError:
        logger.error("❌ Transformers未安装")
        return False
    except Exception as e:
        logger.error(f"❌ Transformers测试失败: {e}")
        return False

def test_other_dependencies():
    """测试其他依赖"""
    logger.info("🔥 测试其他依赖...")
    
    dependencies = [
        ('numpy', 'NumPy'),
        ('yaml', 'PyYAML'),
        ('PIL', 'Pillow'),
        ('matplotlib', 'Matplotlib')
    ]
    
    success_count = 0
    
    for module_name, display_name in dependencies:
        try:
            module = __import__(module_name)
            if hasattr(module, '__version__'):
                logger.info(f"   ✅ {display_name}版本: {module.__version__}")
            else:
                logger.info(f"   ✅ {display_name}: 已安装")
            success_count += 1
        except ImportError:
            logger.warning(f"   ⚠️ {display_name}未安装")
        except Exception as e:
            logger.warning(f"   ⚠️ {display_name}测试失败: {e}")
    
    logger.info(f"   📊 依赖测试: {success_count}/{len(dependencies)} 成功")
    return success_count >= len(dependencies) * 0.75  # 75%成功率

def test_project_structure():
    """测试项目结构"""
    logger.info("🔥 测试项目结构...")
    
    required_dirs = [
        'cism_core',
        'configs',
        'checkpoints',
        'reports',
        'reports/stage2'
    ]
    
    required_files = [
        'cism_core/__init__.py',
        'cism_core/diffusion_engine.py',
        'cism_core/concept_guidance.py',
        'cism_core/sds_loss.py',
        'cism_core/cism_trainer.py',
        'configs/cism_training_config.yaml',
        'configs/concept_prompts.yaml',
        'train_cism.py'
    ]
    
    success_count = 0
    
    # 检查目录
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists() and dir_path.is_dir():
            logger.info(f"   ✅ 目录存在: {dir_name}")
            success_count += 1
        else:
            logger.warning(f"   ⚠️ 目录不存在: {dir_name}")
    
    # 检查文件
    for file_name in required_files:
        file_path = Path(file_name)
        if file_path.exists() and file_path.is_file():
            logger.info(f"   ✅ 文件存在: {file_name}")
            success_count += 1
        else:
            logger.warning(f"   ⚠️ 文件不存在: {file_name}")
    
    total_items = len(required_dirs) + len(required_files)
    logger.info(f"   📊 项目结构: {success_count}/{total_items} 完整")
    
    return success_count >= total_items * 0.9  # 90%完整率

def test_stable_diffusion_model():
    """测试Stable Diffusion模型"""
    logger.info("🔥 测试Stable Diffusion模型...")
    
    try:
        checkpoints_dir = Path('checkpoints')
        if not checkpoints_dir.exists():
            logger.warning("   ⚠️ checkpoints目录不存在")
            return False
        
        # 检查是否有模型文件
        model_files = list(checkpoints_dir.rglob('*.bin')) + list(checkpoints_dir.rglob('*.safetensors'))
        config_files = list(checkpoints_dir.rglob('config.json'))
        
        if model_files:
            logger.info(f"   ✅ 找到 {len(model_files)} 个模型文件")
        else:
            logger.warning("   ⚠️ 未找到模型文件")
        
        if config_files:
            logger.info(f"   ✅ 找到 {len(config_files)} 个配置文件")
        else:
            logger.warning("   ⚠️ 未找到配置文件")
        
        # 检查是否有stable-diffusion相关目录
        sd_dirs = [d for d in checkpoints_dir.iterdir() if d.is_dir() and 'stable-diffusion' in d.name.lower()]
        if sd_dirs:
            logger.info(f"   ✅ 找到Stable Diffusion目录: {[d.name for d in sd_dirs]}")
            return True
        else:
            logger.warning("   ⚠️ 未找到Stable Diffusion目录")
            return len(model_files) > 0
        
    except Exception as e:
        logger.error(f"❌ Stable Diffusion模型测试失败: {e}")
        return False

def test_cism_import():
    """测试CISM组件导入"""
    logger.info("🔥 测试CISM组件导入...")
    
    try:
        # 添加项目路径
        sys.path.append('.')
        
        # 测试导入
        from cism_core.diffusion_engine import DiffusionEngine
        logger.info("   ✅ DiffusionEngine导入成功")
        
        from cism_core.concept_guidance import ConceptGuidance
        logger.info("   ✅ ConceptGuidance导入成功")
        
        from cism_core.sds_loss import SDSLoss
        logger.info("   ✅ SDSLoss导入成功")
        
        from cism_core.cism_trainer import CISMTrainer
        logger.info("   ✅ CISMTrainer导入成功")
        
        return True
    except ImportError as e:
        logger.error(f"❌ CISM组件导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ CISM组件测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🔥 InFusion环境验证测试")
    logger.info("🎯 验证CISM系统运行环境的完整性")
    logger.info("👨‍💻 测试者: Claude Sonnet 4 (Anthropic 最先进模型)")
    logger.info("=" * 80)
    
    tests = [
        ("Python环境", test_python_environment),
        ("PyTorch", test_pytorch),
        ("Diffusers", test_diffusers),
        ("Transformers", test_transformers),
        ("其他依赖", test_other_dependencies),
        ("项目结构", test_project_structure),
        ("Stable Diffusion模型", test_stable_diffusion_model),
        ("CISM组件导入", test_cism_import)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 {test_name}")
        logger.info("-" * 50)
        
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results[test_name] = False
    
    # 总结结果
    logger.info("\n" + "=" * 80)
    logger.info("📋 环境验证结果总结:")
    
    passed_tests = 0
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"   - {test_name}: {status}")
        if result:
            passed_tests += 1
    
    total_tests = len(results)
    success_rate = passed_tests / total_tests
    
    logger.info(f"\n🎯 总体状态: {passed_tests}/{total_tests} 测试通过 ({success_rate:.1%})")
    
    # 环境评估
    if success_rate >= 0.9:
        logger.info("🎉 环境完全就绪，可以开始CISM训练！")
        logger.info("\n💡 建议:")
        logger.info("1. 运行 python train_cism.py 开始训练")
        logger.info("2. 检查训练日志和输出")
        logger.info("3. 根据需要调整配置参数")
    elif success_rate >= 0.75:
        logger.info("⚠️ 环境基本就绪，但有一些问题需要解决")
        logger.info("\n🔧 建议:")
        logger.info("1. 解决失败的测试项目")
        logger.info("2. 安装缺失的依赖")
        logger.info("3. 检查模型文件完整性")
    else:
        logger.info("❌ 环境存在重大问题，需要修复")
        logger.info("\n🚨 紧急修复:")
        logger.info("1. 安装PyTorch和相关依赖")
        logger.info("2. 下载Stable Diffusion模型")
        logger.info("3. 检查项目文件完整性")
    
    return success_rate >= 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
