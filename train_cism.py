#!/usr/bin/env python3
"""
CISM增强训练主脚本
阶段2: 集成CISM语义引导到3D高斯训练流程

使用方法:
python train_cism.py --config configs/cism_training_config.yaml --source_path data/garden --model_path output/garden/point_cloud.ply
"""

import os
import sys
import argparse
import yaml
import logging
from pathlib import Path
import torch
import time
from typing import Dict, Optional

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入3DGS组件
try:
    from gaussian_splatting.scene import Scene, GaussianModel
    from gaussian_splatting.utils.general_utils import safe_state
    from gaussian_splatting.utils.loss_utils import l1_loss, ssim
    from gaussian_splatting.gaussian_renderer import render
    from gaussian_splatting.arguments import ModelParams, PipelineParams, OptimizationParams
    GAUSSIAN_SPLATTING_AVAILABLE = True
except ImportError:
    GAUSSIAN_SPLATTING_AVAILABLE = False
    logging.warning("Gaussian Splatting modules not available")

# 导入CISM组件
from cism_core import DiffusionEngine, ConceptGuidance, SDSLoss
from cism_core.cism_trainer import CISMTrainer

def setup_logging(log_dir: str, verbose: bool = False):
    """设置日志系统"""
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    log_level = logging.DEBUG if verbose else logging.INFO
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 文件处理器
    file_handler = logging.FileHandler(Path(log_dir) / 'cism_training.log')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(log_level)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

def load_config(config_path: str) -> Dict:
    """加载训练配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_output_dirs(config: Dict):
    """创建输出目录"""
    output_dir = Path(config['output_dir'])
    
    dirs_to_create = [
        output_dir,
        output_dir / 'logs',
        output_dir / 'visualizations',
        output_dir / 'checkpoints'
    ]
    
    for dir_path in dirs_to_create:
        dir_path.mkdir(parents=True, exist_ok=True)

def load_gaussians(model_path: str, device: str = "cuda") -> GaussianModel:
    """加载预训练的高斯模型"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    # 创建高斯模型
    gaussians = GaussianModel(sh_degree=3)
    
    # 加载PLY文件
    gaussians.load_ply(model_path)
    
    logging.info(f"Loaded Gaussian model from {model_path}")
    logging.info(f"Number of Gaussian points: {gaussians.get_xyz.shape[0]}")
    
    return gaussians

def setup_scene(source_path: str, gaussians: GaussianModel, config: Dict):
    """设置场景"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    # 创建模型参数
    model_params = ModelParams()
    model_params.source_path = source_path
    model_params.model_path = config['data_paths']['output_gaussians']
    model_params.resolution = config['rendering']['image_resolution']
    
    # 创建场景
    scene = Scene(model_params, gaussians, load_iteration=None, shuffle=False)
    
    return scene, model_params

def training_loop(
    gaussians: GaussianModel,
    scene: Scene,
    cism_trainer: CISMTrainer,
    config: Dict
):
    """CISM增强训练循环"""
    logging.info("Starting CISM enhanced training...")
    
    # 训练参数
    start_iter = config['training_schedule'].get('cism_start_iter', 1000)
    end_iter = config['training_schedule'].get('cism_end_iter', 15000)
    log_interval = config['monitoring'].get('log_interval', 100)
    save_interval = config['monitoring'].get('save_interval', 1000)
    
    # 设置训练调度
    cism_trainer.setup_training_schedule()
    
    # 获取训练视角
    viewpoint_stack = scene.getTrainCameras().copy()
    
    # 训练循环
    for iteration in range(start_iter, end_iter + 1):
        iter_start_time = time.time()
        
        # 选择随机视角
        if not viewpoint_stack:
            viewpoint_stack = scene.getTrainCameras().copy()
        viewpoint_cam = viewpoint_stack.pop()
        
        # 3DGS渲染
        render_pkg = render(viewpoint_cam, gaussians, PipelineParams(), torch.tensor([1.0, 1.0, 1.0], device="cuda"))
        rendered_image = render_pkg["render"]
        
        # 计算基础3DGS损失
        gt_image = viewpoint_cam.original_image.cuda()
        l1_loss_val = l1_loss(rendered_image, gt_image)
        ssim_loss_val = 1.0 - ssim(rendered_image, gt_image)
        base_loss = (1.0 - 0.2) * l1_loss_val + 0.2 * ssim_loss_val
        
        # 应用CISM增强
        cism_result = cism_trainer.training_step(
            rendered_image.unsqueeze(0),  # 添加batch维度
            viewpoint_cam,
            iteration
        )
        
        # 计算总损失
        total_loss = base_loss
        cism_info = {}
        
        if cism_result is not None:
            cism_loss, cism_info = cism_result
            total_loss = total_loss + cism_loss
        
        # 反向传播
        total_loss.backward()
        
        # 更新参数
        with torch.no_grad():
            # 更新学习率
            gaussians.update_learning_rate(iteration)
            
            # 优化器步骤
            gaussians.optimizer.step()
            gaussians.optimizer.zero_grad(set_to_none=True)
            
            # 密化和剪枝
            if iteration < 15000:
                # 添加密化统计
                gaussians.add_densification_stats(render_pkg["viewspace_points"], render_pkg["visibility_filter"])
                
                if iteration > 500 and iteration % 100 == 0:
                    size_threshold = 20 if iteration > 3000 else None
                    gaussians.densify_and_prune(0.0002, 0.005, scene.cameras_extent, size_threshold)
                
                if iteration % 3000 == 0:
                    gaussians.reset_opacity()
        
        # 记录训练进度
        iter_time = time.time() - iter_start_time
        
        if iteration % log_interval == 0:
            log_message = f"Iter {iteration}: L1={l1_loss_val:.4f}, SSIM={ssim_loss_val:.4f}"
            
            if cism_info:
                log_message += f", CISM={cism_info['sds_loss']:.4f}, Weight={cism_info['sds_weight']:.3f}"
            
            log_message += f", Time={iter_time:.2f}s"
            logging.info(log_message)
        
        # 保存检查点
        if iteration % save_interval == 0:
            checkpoint_path = Path(config['output_dir']) / 'checkpoints' / f"iteration_{iteration}.ply"
            gaussians.save_ply(str(checkpoint_path))
            
            # 保存CISM训练状态
            cism_state_path = Path(config['output_dir']) / 'checkpoints' / f"cism_state_{iteration}.pth"
            cism_trainer.save_training_state(str(cism_state_path))
            
            logging.info(f"Saved checkpoint at iteration {iteration}")
    
    # 保存最终模型
    final_model_path = config['data_paths']['output_gaussians']
    gaussians.save_ply(final_model_path)
    logging.info(f"Final model saved to {final_model_path}")
    
    # 输出训练统计
    training_stats = cism_trainer.get_training_stats()
    logging.info(f"Training completed. CISM steps: {training_stats['total_cism_steps']}")
    logging.info(f"Average SDS loss: {training_stats['avg_sds_loss']:.4f}")
    logging.info(f"Average compute time: {training_stats['avg_compute_time']:.2f}s")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CISM Enhanced 3D Gaussian Splatting Training")
    parser.add_argument("--config", type=str, required=True, help="Training configuration file")
    parser.add_argument("--source_path", type=str, required=True, help="Source data path")
    parser.add_argument("--model_path", type=str, required=True, help="Pre-trained Gaussian model path")
    parser.add_argument("--user_concept", type=str, help="User-defined concept description")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # 检查依赖
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        print("Error: Gaussian Splatting modules not available")
        print("Please ensure the gaussian_splatting package is properly installed")
        sys.exit(1)
    
    # 加载配置
    config = load_config(args.config)
    
    # 覆盖用户概念
    if args.user_concept:
        config['concept_config']['user_concept'] = args.user_concept
    
    # 创建输出目录
    create_output_dirs(config)
    
    # 设置日志
    setup_logging(
        Path(config['output_dir']) / 'logs',
        args.verbose or config['debug'].get('verbose_logging', False)
    )
    
    logging.info("Starting CISM Enhanced Training")
    logging.info(f"Config: {args.config}")
    logging.info(f"Source: {args.source_path}")
    logging.info(f"Model: {args.model_path}")
    
    try:
        # 设置设备
        device = config.get('device', 'cuda')
        if not torch.cuda.is_available() and device == 'cuda':
            device = 'cpu'
            logging.warning("CUDA not available, using CPU")
        
        # 加载高斯模型
        gaussians = load_gaussians(args.model_path, device)
        
        # 设置场景
        scene, model_params = setup_scene(args.source_path, gaussians, config)
        
        # 初始化CISM训练器
        cism_trainer = CISMTrainer(config)
        
        # 开始训练
        training_loop(gaussians, scene, cism_trainer, config)
        
        logging.info("Training completed successfully")
        
    except Exception as e:
        logging.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # 清理资源
        if 'cism_trainer' in locals():
            cism_trainer.cleanup()

if __name__ == "__main__":
    main()
