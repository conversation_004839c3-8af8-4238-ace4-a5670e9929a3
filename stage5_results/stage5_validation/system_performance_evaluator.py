import os
import torch
import numpy as np
from typing import Dict, List, Any
import matplotlib.pyplot as plt
import time

class SystemPerformanceEvaluator:
    """
    🎯 系统性能评估器
    评估整体系统的计算效率，优化内存使用和推理速度
    """
    
    def __init__(self, output_dir: str, config: Dict = None):
        """
        Args:
            output_dir: 输出目录
            config: 性能评估配置
        """
        self.output_dir = os.path.join(output_dir, 'system_performance')
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 默认配置
        self.config = {
            'batch_sizes': [1, 2, 4, 8],
            'resolutions': [512, 1024, 2048],
            'max_concepts': 5,
            'memory_profile': True
        }
        
        # 更新配置
        if config:
            self.config.update(config)
        
        # 初始化结果存储
        self.compute_results = {}
        self.scalability_results = {}
        self.stability_results = {}
    
    def benchmark_compute_performance(self) -> Dict[str, Any]:
        """
        测试计算性能
        
        Returns:
            计算性能测试结果
        """
        print("   🔍 测试计算性能...")
        
        # 创建输出目录
        perf_dir = os.path.join(self.output_dir, 'compute_performance')
        os.makedirs(perf_dir, exist_ok=True)
        
        # 模拟计算性能测试
        batch_sizes = self.config['batch_sizes']
        
        # 初始化结果
        results = {
            'batch_sizes': batch_sizes,
            'inference_times': [],
            'memory_usage': [],
            'throughput': []
        }
        
        # 模拟各批量大小的性能
        for batch_size in batch_sizes:
            print(f"   - 测试批量大小 {batch_size}...")
            
            # 模拟推理时间 (ms)
            inference_time = 50 + batch_size * 10 + np.random.uniform(-5, 5)
            
            # 模拟内存使用 (GB)
            memory_usage = 2.0 + batch_size * 0.5 + np.random.uniform(-0.2, 0.2)
            
            # 计算吞吐量 (FPS)
            throughput = 1000 / inference_time * batch_size
            
            results['inference_times'].append(inference_time)
            results['memory_usage'].append(memory_usage)
            results['throughput'].append(throughput)
        
        # 计算平均指标
        results['avg_inference_time_ms'] = np.mean(results['inference_times'])
        results['peak_memory_gb'] = np.max(results['memory_usage'])
        results['avg_memory_gb'] = np.mean(results['memory_usage'])
        results['max_throughput_fps'] = np.max(results['throughput'])
        results['inference_time_ms'] = results['inference_times'][0]  # 单批次时间
        results['fps'] = results['throughput'][0]  # 单批次FPS
        
        # 生成性能图表
        self._generate_performance_charts(results, perf_dir)
        
        self.compute_results = results
        return results
    
    def test_scalability(self) -> Dict[str, Any]:
        """
        测试可扩展性
        
        Returns:
            可扩展性测试结果
        """
        print("   🔍 测试可扩展性...")
        
        # 创建输出目录
        scale_dir = os.path.join(self.output_dir, 'scalability')
        os.makedirs(scale_dir, exist_ok=True)
        
        # 1. 分辨率测试
        resolution_results = self._test_resolution_scalability()
        
        # 2. 概念数量测试
        concept_results = self._test_concept_scalability()
        
        # 3. 场景规模测试
        scene_results = self._test_scene_complexity_scalability()
        
        # 汇总结果
        results = {
            'resolution_tests': resolution_results,
            'concept_tests': concept_results,
            'scene_tests': scene_results,
            'max_supported_resolution': self._determine_max_resolution(resolution_results),
            'max_concepts': self._determine_max_concepts(concept_results),
            'max_scene_complexity': self._determine_max_complexity(scene_results)
        }
        
        # 生成可扩展性图表
        self._generate_scalability_charts(results, scale_dir)
        
        self.scalability_results = results
        return results
    
    def test_system_stability(self) -> Dict[str, Any]:
        """
        测试系统稳定性
        
        Returns:
            系统稳定性测试结果
        """
        print("   🔍 测试系统稳定性...")
        
        # 创建输出目录
        stability_dir = os.path.join(self.output_dir, 'stability')
        os.makedirs(stability_dir, exist_ok=True)
        
        # 1. 收敛稳定性测试
        convergence_results = self._test_convergence_stability()
        
        # 2. 鲁棒性测试
        robustness_results = self._test_robustness()
        
        # 3. 长期稳定性测试
        long_term_results = self._test_long_term_stability()
        
        # 汇总结果
        results = {
            'convergence_stability': convergence_results,
            'robustness': robustness_results,
            'long_term_stability': long_term_results,
            'stability_score': (
                convergence_results['stability_score'] +
                robustness_results['stability_score'] +
                long_term_results['stability_score']
            ) / 3
        }
        
        # 生成稳定性图表
        self._generate_stability_charts(results, stability_dir)
        
        self.stability_results = results
        return results
    
    def get_summary(self) -> Dict[str, float]:
        """
        获取系统性能评估的总结
        
        Returns:
            评估总结
        """
        # 计算性能评分
        performance_score = 0.0
        if self.compute_results:
            # 基于推理速度的评分 (理想速度: 30 FPS以上为1.0分)
            if 'fps' in self.compute_results:
                fps_score = min(1.0, self.compute_results['fps'] / 30.0)
            else:
                fps_score = 0.5
            
            # 基于内存使用的评分 (理想内存: 4GB以下为1.0分)
            if 'peak_memory_gb' in self.compute_results:
                memory_score = min(1.0, 4.0 / max(1.0, self.compute_results['peak_memory_gb']))
            else:
                memory_score = 0.5
            
            performance_score = (fps_score + memory_score) / 2
        
        # 可扩展性评分
        scalability_score = 0.0
        if self.scalability_results:
            # 基于最大分辨率的评分 (2048为1.0分)
            if 'max_supported_resolution' in self.scalability_results:
                resolution_score = min(1.0, self.scalability_results['max_supported_resolution'] / 2048)
            else:
                resolution_score = 0.5
            
            # 基于最大概念数的评分 (5个以上为1.0分)
            if 'max_concepts' in self.scalability_results:
                concept_score = min(1.0, self.scalability_results['max_concepts'] / 5)
            else:
                concept_score = 0.5
            
            scalability_score = (resolution_score + concept_score) / 2
        
        # 稳定性评分
        stability_score = 0.0
        if self.stability_results and 'stability_score' in self.stability_results:
            stability_score = self.stability_results['stability_score']
        
        # 总体评分
        overall_score = (performance_score + scalability_score + stability_score) / 3
        
        return {
            'performance_score': performance_score,
            'scalability_score': scalability_score,
            'stability_score': stability_score,
            'overall_score': overall_score
        }
    
    def _generate_performance_charts(self, results: Dict, output_dir: str):
        """生成性能图表"""
        # 1. 推理时间 vs 批量大小
        plt.figure(figsize=(10, 6))
        plt.plot(results['batch_sizes'], results['inference_times'], 'o-', label='Inference Time (ms)')
        plt.xlabel('Batch Size')
        plt.ylabel('Inference Time (ms)')
        plt.title('Inference Time vs Batch Size')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'inference_time.png'))
        plt.close()
        
        # 2. 内存使用 vs 批量大小
        plt.figure(figsize=(10, 6))
        plt.plot(results['batch_sizes'], results['memory_usage'], 'o-', label='Memory Usage (GB)')
        plt.xlabel('Batch Size')
        plt.ylabel('Memory Usage (GB)')
        plt.title('Memory Usage vs Batch Size')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'memory_usage.png'))
        plt.close()
        
        # 3. 吞吐量 vs 批量大小
        plt.figure(figsize=(10, 6))
        plt.plot(results['batch_sizes'], results['throughput'], 'o-', label='Throughput (FPS)')
        plt.xlabel('Batch Size')
        plt.ylabel('Throughput (FPS)')
        plt.title('Throughput vs Batch Size')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'throughput.png'))
        plt.savefig(os.path.join(output_dir, 'throughput.png'))
        plt.close()
        
        # 4. 性能汇总
        plt.figure(figsize=(12, 8))
        
        ax1 = plt.subplot(111)
        line1 = ax1.plot(results['batch_sizes'], results['inference_times'], 'b-o', label='Inference Time (ms)')
        ax1.set_xlabel('Batch Size')
        ax1.set_ylabel('Inference Time (ms)', color='b')
        ax1.tick_params(axis='y', labelcolor='b')
        
        ax2 = ax1.twinx()
        line2 = ax2.plot(results['batch_sizes'], results['throughput'], 'r-o', label='Throughput (FPS)')
        ax2.set_ylabel('Throughput (FPS)', color='r')
        ax2.tick_params(axis='y', labelcolor='r')
        
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='best')
        
        plt.title('Performance Summary')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'performance_summary.png'))
        plt.close()
    
    def _test_resolution_scalability(self) -> Dict[str, Any]:
        """测试分辨率可扩展性"""
        print("   - 测试分辨率可扩展性...")
        
        resolutions = self.config['resolutions']
        results = {
            'resolutions': resolutions,
            'inference_times': [],
            'memory_usage': [],
            'quality_scores': []
        }
        
        for resolution in resolutions:
            print(f"     > 测试分辨率 {resolution}x{resolution}...")
            
            # 模拟推理时间 (ms) - 与分辨率平方成正比
            inference_time = 50 * (resolution / 512) ** 2 + np.random.uniform(-10, 10)
            
            # 模拟内存使用 (GB) - 与分辨率平方成正比
            memory_usage = 2.0 * (resolution / 512) ** 2 + np.random.uniform(-0.3, 0.3)
            
            # 模拟质量得分 (越高越好)
            quality_score = 0.7 + 0.2 * (resolution / 2048) + np.random.uniform(-0.05, 0.05)
            quality_score = min(1.0, quality_score)
            
            results['inference_times'].append(inference_time)
            results['memory_usage'].append(memory_usage)
            results['quality_scores'].append(quality_score)
        
        return results
    
    def _test_concept_scalability(self) -> Dict[str, Any]:
        """测试概念数量可扩展性"""
        print("   - 测试概念数量可扩展性...")
        
        max_concepts = self.config['max_concepts']
        concept_counts = list(range(1, max_concepts + 1))
        
        results = {
            'concept_counts': concept_counts,
            'inference_times': [],
            'memory_usage': [],
            'control_precision': []
        }
        
        for concept_count in concept_counts:
            print(f"     > 测试 {concept_count} 个概念...")
            
            # 模拟推理时间 (ms) - 与概念数量近似线性关系
            inference_time = 50 + 10 * concept_count + np.random.uniform(-5, 5)
            
            # 模拟内存使用 (GB) - 与概念数量近似线性关系
            memory_usage = 2.0 + 0.2 * concept_count + np.random.uniform(-0.1, 0.1)
            
            # 模拟控制精度 (越高越好) - 随概念数量增加而下降
            control_precision = 0.9 - 0.05 * (concept_count - 1) + np.random.uniform(-0.03, 0.03)
            control_precision = max(0.0, min(1.0, control_precision))
            
            results['inference_times'].append(inference_time)
            results['memory_usage'].append(memory_usage)
            results['control_precision'].append(control_precision)
        
        return results
    
    def _test_scene_complexity_scalability(self) -> Dict[str, Any]:
        """测试场景复杂度可扩展性"""
        print("   - 测试场景复杂度可扩展性...")
        
        # 场景复杂度级别 (1-5)
        complexity_levels = list(range(1, 6))
        
        results = {
            'complexity_levels': complexity_levels,
            'gaussian_counts': [],  # 对应的高斯点数
            'inference_times': [],
            'memory_usage': [],
            'rendering_quality': []
        }
        
        for level in complexity_levels:
            print(f"     > 测试复杂度级别 {level}...")
            
            # 估算高斯点数 (随复杂度指数增长)
            gaussian_count = 100000 * (2 ** (level - 1))
            
            # 模拟推理时间 (ms) - 与高斯点数量近似线性关系
            inference_time = 50 + 0.00005 * gaussian_count + np.random.uniform(-10, 10)
            
            # 模拟内存使用 (GB) - 与高斯点数量近似线性关系
            memory_usage = 2.0 + 0.000001 * gaussian_count + np.random.uniform(-0.2, 0.2)
            
            # 模拟渲染质量 (越高越好)
            rendering_quality = 0.95 - 0.05 * (level - 1) + np.random.uniform(-0.02, 0.02)
            rendering_quality = max(0.0, min(1.0, rendering_quality))
            
            results['gaussian_counts'].append(gaussian_count)
            results['inference_times'].append(inference_time)
            results['memory_usage'].append(memory_usage)
            results['rendering_quality'].append(rendering_quality)
        
        return results
    
    def _determine_max_resolution(self, resolution_results: Dict) -> int:
        """确定最大支持分辨率"""
        # 根据内存限制和推理时间确定最大分辨率
        # 假设最大可接受内存为8GB，最大可接受推理时间为500ms
        
        max_memory = 8.0  # GB
        max_inference = 500.0  # ms
        
        for i, resolution in enumerate(resolution_results['resolutions']):
            if (resolution_results['memory_usage'][i] > max_memory or 
                resolution_results['inference_times'][i] > max_inference):
                # 返回前一个分辨率
                if i > 0:
                    return resolution_results['resolutions'][i-1]
                else:
                    return 512  # 最小分辨率
        
        # 如果所有分辨率都可接受，返回最大测试分辨率
        return resolution_results['resolutions'][-1]
    
    def _determine_max_concepts(self, concept_results: Dict) -> int:
        """确定最大支持概念数"""
        # 根据控制精度确定最大概念数
        # 假设最低可接受控制精度为0.7
        
        min_precision = 0.7
        
        for i, count in enumerate(concept_results['concept_counts']):
            if concept_results['control_precision'][i] < min_precision:
                # 返回前一个概念数
                if i > 0:
                    return concept_results['concept_counts'][i-1]
                else:
                    return 1  # 最小概念数
        
        # 如果所有概念数都可接受，返回最大测试概念数
        return concept_results['concept_counts'][-1]
    
    def _determine_max_complexity(self, scene_results: Dict) -> int:
        """确定最大支持场景复杂度"""
        # 根据渲染质量确定最大场景复杂度
        # 假设最低可接受渲染质量为0.75
        
        min_quality = 0.75
        
        for i, level in enumerate(scene_results['complexity_levels']):
            if scene_results['rendering_quality'][i] < min_quality:
                # 返回前一个复杂度级别
                if i > 0:
                    return scene_results['complexity_levels'][i-1]
                else:
                    return 1  # 最小复杂度
        
        # 如果所有复杂度都可接受，返回最大测试复杂度
        return scene_results['complexity_levels'][-1]
    
    def _generate_scalability_charts(self, results: Dict, output_dir: str):
        """生成可扩展性图表"""
        # 1. 分辨率可扩展性
        res_results = results['resolution_tests']
        plt.figure(figsize=(12, 8))
        
        ax1 = plt.subplot(111)
        line1 = ax1.plot(res_results['resolutions'], res_results['inference_times'], 
                        'b-o', label='Inference Time (ms)')
        ax1.set_xlabel('Resolution')
        ax1.set_ylabel('Inference Time (ms)', color='b')
        ax1.tick_params(axis='y', labelcolor='b')
        
        ax2 = ax1.twinx()
        line2 = ax2.plot(res_results['resolutions'], res_results['quality_scores'], 
                        'r-o', label='Quality Score')
        ax2.set_ylabel('Quality Score', color='r')
        ax2.tick_params(axis='y', labelcolor='r')
        ax2.set_ylim([0, 1.05])
        
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='best')
        
        plt.title('Resolution Scalability')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'resolution_scalability.png'))
        plt.close()
        
        # 2. 概念数量可扩展性
        concept_results = results['concept_tests']
        plt.figure(figsize=(12, 8))
        
        ax1 = plt.subplot(111)
        line1 = ax1.plot(concept_results['concept_counts'], concept_results['inference_times'], 
                        'b-o', label='Inference Time (ms)')
        ax1.set_xlabel('Number of Concepts')
        ax1.set_ylabel('Inference Time (ms)', color='b')
        ax1.tick_params(axis='y', labelcolor='b')
        
        ax2 = ax1.twinx()
        line2 = ax2.plot(concept_results['concept_counts'], concept_results['control_precision'], 
                        'r-o', label='Control Precision')
        ax2.set_ylabel('Control Precision', color='r')
        ax2.tick_params(axis='y', labelcolor='r')
        ax2.set_ylim([0, 1.05])
        
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='best')
        
        plt.title('Concept Count Scalability')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'concept_scalability.png'))
        plt.close()
        
        # 3. 场景复杂度可扩展性
        scene_results = results['scene_tests']
        plt.figure(figsize=(12, 8))
        
        ax1 = plt.subplot(111)
        line1 = ax1.plot(scene_results['complexity_levels'], scene_results['inference_times'], 
                        'b-o', label='Inference Time (ms)')
        ax1.set_xlabel('Scene Complexity Level')
        ax1.set_ylabel('Inference Time (ms)', color='b')
        ax1.tick_params(axis='y', labelcolor='b')
        
        ax2 = ax1.twinx()
        line2 = ax2.plot(scene_results['complexity_levels'], scene_results['rendering_quality'], 
                        'r-o', label='Rendering Quality')
        ax2.set_ylabel('Rendering Quality', color='r')
        ax2.tick_params(axis='y', labelcolor='r')
        ax2.set_ylim([0, 1.05])
        
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='best')
        
        plt.title('Scene Complexity Scalability')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'scene_scalability.png'))
        plt.close()
    
    def _test_convergence_stability(self) -> Dict[str, Any]:
        """测试收敛稳定性"""
        print("   - 测试收敛稳定性...")
        
        # 模拟多次训练运行的收敛情况
        num_runs = 5
        num_epochs = 100
        epochs = list(range(num_epochs))
        
        results = {
            'epochs': epochs,
            'loss_curves': [],
            'stability_metrics': {}
        }
        
        # 生成多次运行的损失曲线
        base_curve = 1.0 - 0.8 * np.exp(-0.05 * np.array(epochs))
        
        for run in range(num_runs):
            # 添加随机噪声模拟不同运行
            noise = np.random.normal(0, 0.05, len(epochs))
            # 确保噪声不会导致损失为负
            curve = np.maximum(0.05, base_curve + noise)
            results['loss_curves'].append(curve.tolist())
        
        # 计算稳定性指标
        final_losses = [curve[-1] for curve in results['loss_curves']]
        loss_variance = np.var(final_losses)
        convergence_epochs = []
        
        for curve in results['loss_curves']:
            # 检测曲线何时趋于平稳 (简单实现: 连续10个epoch损失变化小于0.01)
            for i in range(len(curve) - 10):
                if max(curve[i:i+10]) - min(curve[i:i+10]) < 0.01:
                    convergence_epochs.append(i)
                    break
            else:
                convergence_epochs.append(num_epochs)
        
        # 计算收敛epoch的均值和方差
        mean_convergence = np.mean(convergence_epochs)
        var_convergence = np.var(convergence_epochs)
        
        # 稳定性得分 (0-1)，越高越稳定
        stability_score = 1.0 - min(1.0, loss_variance * 10) * 0.5 - min(1.0, var_convergence / 100) * 0.5
        
        results['stability_metrics'] = {
            'final_loss_mean': float(np.mean(final_losses)),
            'final_loss_variance': float(loss_variance),
            'convergence_epoch_mean': float(mean_convergence),
            'convergence_epoch_variance': float(var_convergence),
            'stability_score': float(stability_score)
        }
        
        return results
    
    def _test_robustness(self) -> Dict[str, Any]:
        """测试鲁棒性"""
        print("   - 测试鲁棒性...")
        
        # 鲁棒性测试场景
        test_scenarios = [
            {'name': 'normal', 'description': '正常输入'},
            {'name': 'low_res', 'description': '低分辨率输入'},
            {'name': 'high_noise', 'description': '高噪声输入'},
            {'name': 'extreme_prompt', 'description': '极端文本提示'},
            {'name': 'missing_concept', 'description': '缺失概念'}
        ]
        
        results = {
            'test_scenarios': test_scenarios,
            'metrics': {}
        }
        
        overall_robustness = 0.0
        
        for scenario in test_scenarios:
            print(f"     > 测试场景: {scenario['name']}...")
            
            # 模拟不同场景的表现
            if scenario['name'] == 'normal':
                # 正常情况作为基准
                quality = 0.9 + np.random.uniform(-0.05, 0.05)
                recovery_rate = 1.0
            elif scenario['name'] == 'low_res':
                # 低分辨率对性能影响不大
                quality = 0.8 + np.random.uniform(-0.1, 0.1)
                recovery_rate = 0.95 + np.random.uniform(-0.05, 0.05)
            elif scenario['name'] == 'high_noise':
                # 高噪声有一定影响
                quality = 0.7 + np.random.uniform(-0.1, 0.1)
                recovery_rate = 0.85 + np.random.uniform(-0.1, 0.1)
            elif scenario['name'] == 'extreme_prompt':
                # 极端提示可能有较大影响
                quality = 0.6 + np.random.uniform(-0.2, 0.2)
                recovery_rate = 0.7 + np.random.uniform(-0.2, 0.2)
            elif scenario['name'] == 'missing_concept':
                # 缺失概念可能导致较差表现
                quality = 0.5 + np.random.uniform(-0.2, 0.2)
                recovery_rate = 0.6 + np.random.uniform(-0.2, 0.2)
            else:
                quality = 0.5
                recovery_rate = 0.5
            
            # 保证在有效范围内
            quality = max(0.0, min(1.0, quality))
            recovery_rate = max(0.0, min(1.0, recovery_rate))
            
            # 计算鲁棒性得分
            robustness_score = (quality + recovery_rate) / 2
            
            results['metrics'][scenario['name']] = {
                'quality': quality,
                'recovery_rate': recovery_rate,
                'robustness_score': robustness_score
            }
            
            # 累加总体鲁棒性
            if scenario['name'] != 'normal':  # 不计入正常情况
                overall_robustness += robustness_score / (len(test_scenarios) - 1)
        
        results['stability_score'] = overall_robustness
        
        return results
    
    def _test_long_term_stability(self) -> Dict[str, Any]:
        """测试长期稳定性"""
        print("   - 测试长期稳定性...")
        
        # 模拟长时间运行的性能变化
        hours = list(range(1, 25))  # 24小时测试
        
        results = {
            'hours': hours,
            'memory_usage': [],
            'inference_time': [],
            'stability_metrics': {}
        }
        
        # 基准性能
        base_memory = 3.0  # GB
        base_inference = 60.0  # ms
        
        # 生成模拟数据
        for hour in hours:
            # 模拟内存增长 (轻微泄漏)
            memory = base_memory + 0.05 * hour + np.random.uniform(-0.1, 0.1)
            
            # 模拟推理时间变化 (轻微增长)
            inference = base_inference + 0.5 * hour + np.random.uniform(-2, 2)
            
            results['memory_usage'].append(memory)
            results['inference_time'].append(inference)
        
        # 计算稳定性指标
        memory_growth_rate = (results['memory_usage'][-1] - results['memory_usage'][0]) / 24.0
        inference_growth_rate = (results['inference_time'][-1] - results['inference_time'][0]) / 24.0
        
        # 稳定性得分 (0-1)，越高越稳定
        memory_stability = max(0, 1.0 - memory_growth_rate)
        inference_stability = max(0, 1.0 - inference_growth_rate / 10.0)
        stability_score = (memory_stability + inference_stability) / 2
        
        results['stability_metrics'] = {
            'memory_growth_rate': float(memory_growth_rate),
            'inference_growth_rate': float(inference_growth_rate),
            'memory_stability': float(memory_stability),
            'inference_stability': float(inference_stability),
        }
        
        results['stability_score'] = float(stability_score)
        
        return results
    
    def _generate_stability_charts(self, results: Dict, output_dir: str):
        """生成稳定性图表"""
        # 1. 收敛稳定性
        if 'convergence_stability' in results:
            convergence_results = results['convergence_stability']
            plt.figure(figsize=(10, 6))
            
            for i, curve in enumerate(convergence_results['loss_curves']):
                plt.plot(convergence_results['epochs'], curve, 
                        label=f'Run {i+1}', alpha=0.7)
            
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.title('Convergence Stability Across Multiple Runs')
            plt.grid(True)
            plt.legend()
            plt.savefig(os.path.join(output_dir, 'convergence_stability.png'))
            plt.close()
        
        # 2. 鲁棒性测试
        if 'robustness' in results:
            robustness_results = results['robustness']
            scenarios = [s['name'] for s in robustness_results['test_scenarios']]
            quality_scores = [robustness_results['metrics'][s]['quality'] for s in scenarios]
            recovery_rates = [robustness_results['metrics'][s]['recovery_rate'] for s in scenarios]
            
            plt.figure(figsize=(12, 6))
            x = np.arange(len(scenarios))
            width = 0.35
            
            plt.bar(x - width/2, quality_scores, width, label='Quality')
            plt.bar(x + width/2, recovery_rates, width, label='Recovery Rate')
            
            plt.xlabel('Test Scenario')
            plt.ylabel('Score')
            plt.title('Robustness Test Results')
            plt.xticks(x, scenarios, rotation=45)
            plt.ylim([0, 1.1])
            plt.legend()
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'robustness_test.png'))
            plt.close()
        
        # 3. 长期稳定性
        if 'long_term_stability' in results:
            long_term_results = results['long_term_stability']
            plt.figure(figsize=(12, 8))
            
            ax1 = plt.subplot(111)
            line1 = ax1.plot(long_term_results['hours'], long_term_results['memory_usage'], 
                            'b-o', label='Memory Usage (GB)')
            ax1.set_xlabel('Hours')
            ax1.set_ylabel('Memory Usage (GB)', color='b')
            ax1.tick_params(axis='y', labelcolor='b')
            
            ax2 = ax1.twinx()
            line2 = ax2.plot(long_term_results['hours'], long_term_results['inference_time'], 
                            'r-o', label='Inference Time (ms)')
            ax2.set_ylabel('Inference Time (ms)', color='r')
            ax2.tick_params(axis='y', labelcolor='r')
            
            lines = line1 + line2
            labels = [l.get_label() for l in lines]
            ax1.legend(lines, labels, loc='best')
            
            plt.title('Long-term Stability (24-hour Test)')
            plt.grid(True)
            plt.savefig(os.path.join(output_dir, 'long_term_stability.png'))
            plt.close()