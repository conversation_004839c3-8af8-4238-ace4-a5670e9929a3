<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: vec_swizzle.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">vec_swizzle.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00187.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_vec_swizzle is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_vec_swizzle extension included&quot;)</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a> {</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;        <span class="comment">// xx</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xx(<span class="keyword">const</span> glm::vec&lt;1, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.x);</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;        }</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.x);</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        }</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.x);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;        }</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.x);</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        }</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        <span class="comment">// xy</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.y);</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        }</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.y);</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        }</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.y);</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        }</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        <span class="comment">// xz</span></div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.z);</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        }</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.z);</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        }</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        <span class="comment">// xw</span></div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; xw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.x, v.w);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        }</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <span class="comment">// yx</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; yx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.y, v.x);</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        }</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; yx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.y, v.x);</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        }</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; yx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.y, v.x);</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        }</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        <span class="comment">// yy</span></div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; yy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.y, v.y);</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        }</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; yy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.y, v.y);</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        }</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; yy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.y, v.y);</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        }</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        <span class="comment">// yz</span></div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; yz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.y, v.z);</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        }</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; yz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.y, v.z);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;        }</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        <span class="comment">// yw</span></div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; yw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.y, v.w);</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;        }</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        <span class="comment">// zx</span></div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; zx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.z, v.x);</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;        }</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; zx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.z, v.x);</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        }</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        <span class="comment">// zy</span></div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; zy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.z, v.y);</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        }</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; zy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.z, v.y);</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        }</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;        <span class="comment">// zz</span></div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; zz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.z, v.z);</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        }</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; zz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.z, v.z);</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        }</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;        <span class="comment">// zw</span></div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; zw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.z, v.w);</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;        }</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        <span class="comment">// wx</span></div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; wx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.w, v.x);</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;        }</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        <span class="comment">// wy</span></div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; wy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.w, v.y);</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;        }</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        <span class="comment">// wz</span></div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; wz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.w, v.z);</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;        }</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;        <span class="comment">// ww</span></div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;        GLM_INLINE glm::vec&lt;2, T, Q&gt; ww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;2, T, Q&gt;(v.w, v.w);</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;        }</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;        <span class="comment">// xxx</span></div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxx(<span class="keyword">const</span> glm::vec&lt;1, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.x);</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;        }</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.x);</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;        }</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.x);</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;        }</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.x);</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;        }</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;        <span class="comment">// xxy</span></div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.y);</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;        }</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.y);</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;        }</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.y);</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;        }</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;        <span class="comment">// xxz</span></div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.z);</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;        }</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.z);</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;        }</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;        <span class="comment">// xxw</span></div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.x, v.w);</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;        }</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;        <span class="comment">// xyx</span></div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xyx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.y, v.x);</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;        }</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.y, v.x);</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;        }</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.y, v.x);</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;        }</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;        <span class="comment">// xyy</span></div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xyy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.y, v.y);</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;        }</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.y, v.y);</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;        }</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.y, v.y);</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;        }</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;        <span class="comment">// xyz</span></div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.y, v.z);</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;        }</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.y, v.z);</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;        }</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;        <span class="comment">// xyw</span></div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.y, v.w);</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;        }</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;        <span class="comment">// xzx</span></div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.z, v.x);</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;        }</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.z, v.x);</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;        }</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;        <span class="comment">// xzy</span></div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.z, v.y);</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;        }</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.z, v.y);</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;        }</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;        <span class="comment">// xzz</span></div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.z, v.z);</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;        }</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.z, v.z);</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;        }</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;        <span class="comment">// xzw</span></div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.z, v.w);</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;        }</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;        <span class="comment">// xwx</span></div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.w, v.x);</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;        }</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;        <span class="comment">// xwy</span></div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.w, v.y);</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;        }</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;</div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;        <span class="comment">// xwz</span></div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.w, v.z);</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;        }</div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;        <span class="comment">// xww</span></div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; xww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.x, v.w, v.w);</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;        }</div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;        <span class="comment">// yxx</span></div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yxx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.x, v.x);</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;        }</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.x, v.x);</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;        }</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.x, v.x);</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;        }</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;</div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;        <span class="comment">// yxy</span></div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yxy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.x, v.y);</div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;        }</div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;</div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.x, v.y);</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;        }</div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.x, v.y);</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;        }</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;        <span class="comment">// yxz</span></div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.x, v.z);</div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;        }</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;</div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.x, v.z);</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;        }</div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;</div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;        <span class="comment">// yxw</span></div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.x, v.w);</div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;        }</div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;        <span class="comment">// yyx</span></div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yyx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.y, v.x);</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;        }</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;</div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.y, v.x);</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;        }</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;</div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.y, v.x);</div>
<div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;        }</div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;        <span class="comment">// yyy</span></div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yyy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.y, v.y);</div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;        }</div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;</div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.y, v.y);</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;        }</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;</div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.y, v.y);</div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;        }</div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;</div>
<div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160;        <span class="comment">// yyz</span></div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.y, v.z);</div>
<div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;        }</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;</div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.y, v.z);</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;        }</div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;</div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;        <span class="comment">// yyw</span></div>
<div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.y, v.w);</div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;        }</div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;</div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;        <span class="comment">// yzx</span></div>
<div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.z, v.x);</div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;        }</div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;</div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.z, v.x);</div>
<div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;        }</div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;</div>
<div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;        <span class="comment">// yzy</span></div>
<div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.z, v.y);</div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;        }</div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;</div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.z, v.y);</div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160;        }</div>
<div class="line"><a name="l00477"></a><span class="lineno">  477</span>&#160;</div>
<div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;        <span class="comment">// yzz</span></div>
<div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00481"></a><span class="lineno">  481</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.z, v.z);</div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;        }</div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;</div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.z, v.z);</div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;        }</div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;</div>
<div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;        <span class="comment">// yzw</span></div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.z, v.w);</div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;        }</div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;</div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;        <span class="comment">// ywx</span></div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; ywx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.w, v.x);</div>
<div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;        }</div>
<div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;</div>
<div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;        <span class="comment">// ywy</span></div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; ywy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.w, v.y);</div>
<div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;        }</div>
<div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;</div>
<div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;        <span class="comment">// ywz</span></div>
<div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; ywz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.w, v.z);</div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;        }</div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;</div>
<div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;        <span class="comment">// yww</span></div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; yww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00516"></a><span class="lineno">  516</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.y, v.w, v.w);</div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;        }</div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;</div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;        <span class="comment">// zxx</span></div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.x, v.x);</div>
<div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;        }</div>
<div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;</div>
<div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.x, v.x);</div>
<div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;        }</div>
<div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;</div>
<div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;        <span class="comment">// zxy</span></div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.x, v.y);</div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;        }</div>
<div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;</div>
<div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.x, v.y);</div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;        }</div>
<div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;</div>
<div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;        <span class="comment">// zxz</span></div>
<div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.x, v.z);</div>
<div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160;        }</div>
<div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;</div>
<div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.x, v.z);</div>
<div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;        }</div>
<div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;</div>
<div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;        <span class="comment">// zxw</span></div>
<div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00554"></a><span class="lineno">  554</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.x, v.w);</div>
<div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160;        }</div>
<div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;</div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;        <span class="comment">// zyx</span></div>
<div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.y, v.x);</div>
<div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;        }</div>
<div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;</div>
<div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.y, v.x);</div>
<div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;        }</div>
<div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;</div>
<div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;        <span class="comment">// zyy</span></div>
<div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.y, v.y);</div>
<div class="line"><a name="l00573"></a><span class="lineno">  573</span>&#160;        }</div>
<div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;</div>
<div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.y, v.y);</div>
<div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;        }</div>
<div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;</div>
<div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;        <span class="comment">// zyz</span></div>
<div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.y, v.z);</div>
<div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;        }</div>
<div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;</div>
<div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.y, v.z);</div>
<div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;        }</div>
<div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;</div>
<div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;        <span class="comment">// zyw</span></div>
<div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.y, v.w);</div>
<div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;        }</div>
<div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;</div>
<div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;        <span class="comment">// zzx</span></div>
<div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.z, v.x);</div>
<div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;        }</div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;</div>
<div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.z, v.x);</div>
<div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;        }</div>
<div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;</div>
<div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160;        <span class="comment">// zzy</span></div>
<div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.z, v.y);</div>
<div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160;        }</div>
<div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;</div>
<div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.z, v.y);</div>
<div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;        }</div>
<div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;</div>
<div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;        <span class="comment">// zzz</span></div>
<div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.z, v.z);</div>
<div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;        }</div>
<div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160;</div>
<div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.z, v.z);</div>
<div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;        }</div>
<div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;</div>
<div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;        <span class="comment">// zzw</span></div>
<div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.z, v.w);</div>
<div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;        }</div>
<div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;</div>
<div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;        <span class="comment">// zwx</span></div>
<div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.w, v.x);</div>
<div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;        }</div>
<div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;</div>
<div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160;        <span class="comment">// zwy</span></div>
<div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.w, v.y);</div>
<div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;        }</div>
<div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;</div>
<div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;        <span class="comment">// zwz</span></div>
<div class="line"><a name="l00649"></a><span class="lineno">  649</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.w, v.z);</div>
<div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160;        }</div>
<div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;</div>
<div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;        <span class="comment">// zww</span></div>
<div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; zww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00657"></a><span class="lineno">  657</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.z, v.w, v.w);</div>
<div class="line"><a name="l00658"></a><span class="lineno">  658</span>&#160;        }</div>
<div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;</div>
<div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;        <span class="comment">// wxx</span></div>
<div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00662"></a><span class="lineno">  662</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.x, v.x);</div>
<div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;        }</div>
<div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;</div>
<div class="line"><a name="l00666"></a><span class="lineno">  666</span>&#160;        <span class="comment">// wxy</span></div>
<div class="line"><a name="l00667"></a><span class="lineno">  667</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.x, v.y);</div>
<div class="line"><a name="l00670"></a><span class="lineno">  670</span>&#160;        }</div>
<div class="line"><a name="l00671"></a><span class="lineno">  671</span>&#160;</div>
<div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;        <span class="comment">// wxz</span></div>
<div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00674"></a><span class="lineno">  674</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.x, v.z);</div>
<div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;        }</div>
<div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;</div>
<div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;        <span class="comment">// wxw</span></div>
<div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.x, v.w);</div>
<div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;        }</div>
<div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160;</div>
<div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;        <span class="comment">// wyx</span></div>
<div class="line"><a name="l00685"></a><span class="lineno">  685</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.y, v.x);</div>
<div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;        }</div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;</div>
<div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;        <span class="comment">// wyy</span></div>
<div class="line"><a name="l00691"></a><span class="lineno">  691</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00692"></a><span class="lineno">  692</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.y, v.y);</div>
<div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;        }</div>
<div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160;</div>
<div class="line"><a name="l00696"></a><span class="lineno">  696</span>&#160;        <span class="comment">// wyz</span></div>
<div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.y, v.z);</div>
<div class="line"><a name="l00700"></a><span class="lineno">  700</span>&#160;        }</div>
<div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;</div>
<div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;        <span class="comment">// wyw</span></div>
<div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00704"></a><span class="lineno">  704</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.y, v.w);</div>
<div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;        }</div>
<div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;</div>
<div class="line"><a name="l00708"></a><span class="lineno">  708</span>&#160;        <span class="comment">// wzx</span></div>
<div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.z, v.x);</div>
<div class="line"><a name="l00712"></a><span class="lineno">  712</span>&#160;        }</div>
<div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;</div>
<div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;        <span class="comment">// wzy</span></div>
<div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.z, v.y);</div>
<div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;        }</div>
<div class="line"><a name="l00719"></a><span class="lineno">  719</span>&#160;</div>
<div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;        <span class="comment">// wzz</span></div>
<div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.z, v.z);</div>
<div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;        }</div>
<div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;</div>
<div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;        <span class="comment">// wzw</span></div>
<div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.z, v.w);</div>
<div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;        }</div>
<div class="line"><a name="l00731"></a><span class="lineno">  731</span>&#160;</div>
<div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;        <span class="comment">// wwx</span></div>
<div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00735"></a><span class="lineno">  735</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.w, v.x);</div>
<div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;        }</div>
<div class="line"><a name="l00737"></a><span class="lineno">  737</span>&#160;</div>
<div class="line"><a name="l00738"></a><span class="lineno">  738</span>&#160;        <span class="comment">// wwy</span></div>
<div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00741"></a><span class="lineno">  741</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.w, v.y);</div>
<div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;        }</div>
<div class="line"><a name="l00743"></a><span class="lineno">  743</span>&#160;</div>
<div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;        <span class="comment">// wwz</span></div>
<div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00746"></a><span class="lineno">  746</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; wwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00747"></a><span class="lineno">  747</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.w, v.z);</div>
<div class="line"><a name="l00748"></a><span class="lineno">  748</span>&#160;        }</div>
<div class="line"><a name="l00749"></a><span class="lineno">  749</span>&#160;</div>
<div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160;        <span class="comment">// www</span></div>
<div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00752"></a><span class="lineno">  752</span>&#160;        GLM_INLINE glm::vec&lt;3, T, Q&gt; www(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;3, T, Q&gt;(v.w, v.w, v.w);</div>
<div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;        }</div>
<div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;</div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;        <span class="comment">// xxxx</span></div>
<div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxx(<span class="keyword">const</span> glm::vec&lt;1, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.x);</div>
<div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;        }</div>
<div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;</div>
<div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00763"></a><span class="lineno">  763</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00764"></a><span class="lineno">  764</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.x);</div>
<div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160;        }</div>
<div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;</div>
<div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00768"></a><span class="lineno">  768</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00769"></a><span class="lineno">  769</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.x);</div>
<div class="line"><a name="l00770"></a><span class="lineno">  770</span>&#160;        }</div>
<div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160;</div>
<div class="line"><a name="l00772"></a><span class="lineno">  772</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.x);</div>
<div class="line"><a name="l00775"></a><span class="lineno">  775</span>&#160;        }</div>
<div class="line"><a name="l00776"></a><span class="lineno">  776</span>&#160;</div>
<div class="line"><a name="l00777"></a><span class="lineno">  777</span>&#160;        <span class="comment">// xxxy</span></div>
<div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00779"></a><span class="lineno">  779</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00780"></a><span class="lineno">  780</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.y);</div>
<div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160;        }</div>
<div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;</div>
<div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00785"></a><span class="lineno">  785</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.y);</div>
<div class="line"><a name="l00786"></a><span class="lineno">  786</span>&#160;        }</div>
<div class="line"><a name="l00787"></a><span class="lineno">  787</span>&#160;</div>
<div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.y);</div>
<div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;        }</div>
<div class="line"><a name="l00792"></a><span class="lineno">  792</span>&#160;</div>
<div class="line"><a name="l00793"></a><span class="lineno">  793</span>&#160;        <span class="comment">// xxxz</span></div>
<div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00795"></a><span class="lineno">  795</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00796"></a><span class="lineno">  796</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.z);</div>
<div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;        }</div>
<div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;</div>
<div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00800"></a><span class="lineno">  800</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00801"></a><span class="lineno">  801</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.z);</div>
<div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;        }</div>
<div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;</div>
<div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160;        <span class="comment">// xxxw</span></div>
<div class="line"><a name="l00805"></a><span class="lineno">  805</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00807"></a><span class="lineno">  807</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.x, v.w);</div>
<div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160;        }</div>
<div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160;</div>
<div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160;        <span class="comment">// xxyx</span></div>
<div class="line"><a name="l00811"></a><span class="lineno">  811</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxyx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00813"></a><span class="lineno">  813</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.y, v.x);</div>
<div class="line"><a name="l00814"></a><span class="lineno">  814</span>&#160;        }</div>
<div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160;</div>
<div class="line"><a name="l00816"></a><span class="lineno">  816</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00817"></a><span class="lineno">  817</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.y, v.x);</div>
<div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160;        }</div>
<div class="line"><a name="l00820"></a><span class="lineno">  820</span>&#160;</div>
<div class="line"><a name="l00821"></a><span class="lineno">  821</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00822"></a><span class="lineno">  822</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00823"></a><span class="lineno">  823</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.y, v.x);</div>
<div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160;        }</div>
<div class="line"><a name="l00825"></a><span class="lineno">  825</span>&#160;</div>
<div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160;        <span class="comment">// xxyy</span></div>
<div class="line"><a name="l00827"></a><span class="lineno">  827</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00828"></a><span class="lineno">  828</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxyy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.y, v.y);</div>
<div class="line"><a name="l00830"></a><span class="lineno">  830</span>&#160;        }</div>
<div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;</div>
<div class="line"><a name="l00832"></a><span class="lineno">  832</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00833"></a><span class="lineno">  833</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00834"></a><span class="lineno">  834</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.y, v.y);</div>
<div class="line"><a name="l00835"></a><span class="lineno">  835</span>&#160;        }</div>
<div class="line"><a name="l00836"></a><span class="lineno">  836</span>&#160;</div>
<div class="line"><a name="l00837"></a><span class="lineno">  837</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00838"></a><span class="lineno">  838</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.y, v.y);</div>
<div class="line"><a name="l00840"></a><span class="lineno">  840</span>&#160;        }</div>
<div class="line"><a name="l00841"></a><span class="lineno">  841</span>&#160;</div>
<div class="line"><a name="l00842"></a><span class="lineno">  842</span>&#160;        <span class="comment">// xxyz</span></div>
<div class="line"><a name="l00843"></a><span class="lineno">  843</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00844"></a><span class="lineno">  844</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00845"></a><span class="lineno">  845</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.y, v.z);</div>
<div class="line"><a name="l00846"></a><span class="lineno">  846</span>&#160;        }</div>
<div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;</div>
<div class="line"><a name="l00848"></a><span class="lineno">  848</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00849"></a><span class="lineno">  849</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00850"></a><span class="lineno">  850</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.y, v.z);</div>
<div class="line"><a name="l00851"></a><span class="lineno">  851</span>&#160;        }</div>
<div class="line"><a name="l00852"></a><span class="lineno">  852</span>&#160;</div>
<div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160;        <span class="comment">// xxyw</span></div>
<div class="line"><a name="l00854"></a><span class="lineno">  854</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00855"></a><span class="lineno">  855</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00856"></a><span class="lineno">  856</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.y, v.w);</div>
<div class="line"><a name="l00857"></a><span class="lineno">  857</span>&#160;        }</div>
<div class="line"><a name="l00858"></a><span class="lineno">  858</span>&#160;</div>
<div class="line"><a name="l00859"></a><span class="lineno">  859</span>&#160;        <span class="comment">// xxzx</span></div>
<div class="line"><a name="l00860"></a><span class="lineno">  860</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00861"></a><span class="lineno">  861</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00862"></a><span class="lineno">  862</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.z, v.x);</div>
<div class="line"><a name="l00863"></a><span class="lineno">  863</span>&#160;        }</div>
<div class="line"><a name="l00864"></a><span class="lineno">  864</span>&#160;</div>
<div class="line"><a name="l00865"></a><span class="lineno">  865</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00866"></a><span class="lineno">  866</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.z, v.x);</div>
<div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160;        }</div>
<div class="line"><a name="l00869"></a><span class="lineno">  869</span>&#160;</div>
<div class="line"><a name="l00870"></a><span class="lineno">  870</span>&#160;        <span class="comment">// xxzy</span></div>
<div class="line"><a name="l00871"></a><span class="lineno">  871</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00873"></a><span class="lineno">  873</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.z, v.y);</div>
<div class="line"><a name="l00874"></a><span class="lineno">  874</span>&#160;        }</div>
<div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160;</div>
<div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00877"></a><span class="lineno">  877</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00878"></a><span class="lineno">  878</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.z, v.y);</div>
<div class="line"><a name="l00879"></a><span class="lineno">  879</span>&#160;        }</div>
<div class="line"><a name="l00880"></a><span class="lineno">  880</span>&#160;</div>
<div class="line"><a name="l00881"></a><span class="lineno">  881</span>&#160;        <span class="comment">// xxzz</span></div>
<div class="line"><a name="l00882"></a><span class="lineno">  882</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.z, v.z);</div>
<div class="line"><a name="l00885"></a><span class="lineno">  885</span>&#160;        }</div>
<div class="line"><a name="l00886"></a><span class="lineno">  886</span>&#160;</div>
<div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00888"></a><span class="lineno">  888</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.z, v.z);</div>
<div class="line"><a name="l00890"></a><span class="lineno">  890</span>&#160;        }</div>
<div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;</div>
<div class="line"><a name="l00892"></a><span class="lineno">  892</span>&#160;        <span class="comment">// xxzw</span></div>
<div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00894"></a><span class="lineno">  894</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.z, v.w);</div>
<div class="line"><a name="l00896"></a><span class="lineno">  896</span>&#160;        }</div>
<div class="line"><a name="l00897"></a><span class="lineno">  897</span>&#160;</div>
<div class="line"><a name="l00898"></a><span class="lineno">  898</span>&#160;        <span class="comment">// xxwx</span></div>
<div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00900"></a><span class="lineno">  900</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00901"></a><span class="lineno">  901</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.w, v.x);</div>
<div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160;        }</div>
<div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160;</div>
<div class="line"><a name="l00904"></a><span class="lineno">  904</span>&#160;        <span class="comment">// xxwy</span></div>
<div class="line"><a name="l00905"></a><span class="lineno">  905</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.w, v.y);</div>
<div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160;        }</div>
<div class="line"><a name="l00909"></a><span class="lineno">  909</span>&#160;</div>
<div class="line"><a name="l00910"></a><span class="lineno">  910</span>&#160;        <span class="comment">// xxwz</span></div>
<div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00913"></a><span class="lineno">  913</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.w, v.z);</div>
<div class="line"><a name="l00914"></a><span class="lineno">  914</span>&#160;        }</div>
<div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;</div>
<div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;        <span class="comment">// xxww</span></div>
<div class="line"><a name="l00917"></a><span class="lineno">  917</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00918"></a><span class="lineno">  918</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xxww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00919"></a><span class="lineno">  919</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.x, v.w, v.w);</div>
<div class="line"><a name="l00920"></a><span class="lineno">  920</span>&#160;        }</div>
<div class="line"><a name="l00921"></a><span class="lineno">  921</span>&#160;</div>
<div class="line"><a name="l00922"></a><span class="lineno">  922</span>&#160;        <span class="comment">// xyxx</span></div>
<div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00924"></a><span class="lineno">  924</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyxx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00925"></a><span class="lineno">  925</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.x, v.x);</div>
<div class="line"><a name="l00926"></a><span class="lineno">  926</span>&#160;        }</div>
<div class="line"><a name="l00927"></a><span class="lineno">  927</span>&#160;</div>
<div class="line"><a name="l00928"></a><span class="lineno">  928</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00929"></a><span class="lineno">  929</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00930"></a><span class="lineno">  930</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.x, v.x);</div>
<div class="line"><a name="l00931"></a><span class="lineno">  931</span>&#160;        }</div>
<div class="line"><a name="l00932"></a><span class="lineno">  932</span>&#160;</div>
<div class="line"><a name="l00933"></a><span class="lineno">  933</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00934"></a><span class="lineno">  934</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00935"></a><span class="lineno">  935</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.x, v.x);</div>
<div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;        }</div>
<div class="line"><a name="l00937"></a><span class="lineno">  937</span>&#160;</div>
<div class="line"><a name="l00938"></a><span class="lineno">  938</span>&#160;        <span class="comment">// xyxy</span></div>
<div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00940"></a><span class="lineno">  940</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyxy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00941"></a><span class="lineno">  941</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.x, v.y);</div>
<div class="line"><a name="l00942"></a><span class="lineno">  942</span>&#160;        }</div>
<div class="line"><a name="l00943"></a><span class="lineno">  943</span>&#160;</div>
<div class="line"><a name="l00944"></a><span class="lineno">  944</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00945"></a><span class="lineno">  945</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00946"></a><span class="lineno">  946</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.x, v.y);</div>
<div class="line"><a name="l00947"></a><span class="lineno">  947</span>&#160;        }</div>
<div class="line"><a name="l00948"></a><span class="lineno">  948</span>&#160;</div>
<div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00950"></a><span class="lineno">  950</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00951"></a><span class="lineno">  951</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.x, v.y);</div>
<div class="line"><a name="l00952"></a><span class="lineno">  952</span>&#160;        }</div>
<div class="line"><a name="l00953"></a><span class="lineno">  953</span>&#160;</div>
<div class="line"><a name="l00954"></a><span class="lineno">  954</span>&#160;        <span class="comment">// xyxz</span></div>
<div class="line"><a name="l00955"></a><span class="lineno">  955</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00956"></a><span class="lineno">  956</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.x, v.z);</div>
<div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160;        }</div>
<div class="line"><a name="l00959"></a><span class="lineno">  959</span>&#160;</div>
<div class="line"><a name="l00960"></a><span class="lineno">  960</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.x, v.z);</div>
<div class="line"><a name="l00963"></a><span class="lineno">  963</span>&#160;        }</div>
<div class="line"><a name="l00964"></a><span class="lineno">  964</span>&#160;</div>
<div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;        <span class="comment">// xyxw</span></div>
<div class="line"><a name="l00966"></a><span class="lineno">  966</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00967"></a><span class="lineno">  967</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00968"></a><span class="lineno">  968</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.x, v.w);</div>
<div class="line"><a name="l00969"></a><span class="lineno">  969</span>&#160;        }</div>
<div class="line"><a name="l00970"></a><span class="lineno">  970</span>&#160;</div>
<div class="line"><a name="l00971"></a><span class="lineno">  971</span>&#160;        <span class="comment">// xyyx</span></div>
<div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00973"></a><span class="lineno">  973</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyyx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.y, v.x);</div>
<div class="line"><a name="l00975"></a><span class="lineno">  975</span>&#160;        }</div>
<div class="line"><a name="l00976"></a><span class="lineno">  976</span>&#160;</div>
<div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00978"></a><span class="lineno">  978</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00979"></a><span class="lineno">  979</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.y, v.x);</div>
<div class="line"><a name="l00980"></a><span class="lineno">  980</span>&#160;        }</div>
<div class="line"><a name="l00981"></a><span class="lineno">  981</span>&#160;</div>
<div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00983"></a><span class="lineno">  983</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00984"></a><span class="lineno">  984</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.y, v.x);</div>
<div class="line"><a name="l00985"></a><span class="lineno">  985</span>&#160;        }</div>
<div class="line"><a name="l00986"></a><span class="lineno">  986</span>&#160;</div>
<div class="line"><a name="l00987"></a><span class="lineno">  987</span>&#160;        <span class="comment">// xyyy</span></div>
<div class="line"><a name="l00988"></a><span class="lineno">  988</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00989"></a><span class="lineno">  989</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyyy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00990"></a><span class="lineno">  990</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.y, v.y);</div>
<div class="line"><a name="l00991"></a><span class="lineno">  991</span>&#160;        }</div>
<div class="line"><a name="l00992"></a><span class="lineno">  992</span>&#160;</div>
<div class="line"><a name="l00993"></a><span class="lineno">  993</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00994"></a><span class="lineno">  994</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l00995"></a><span class="lineno">  995</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.y, v.y);</div>
<div class="line"><a name="l00996"></a><span class="lineno">  996</span>&#160;        }</div>
<div class="line"><a name="l00997"></a><span class="lineno">  997</span>&#160;</div>
<div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01000"></a><span class="lineno"> 1000</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.y, v.y);</div>
<div class="line"><a name="l01001"></a><span class="lineno"> 1001</span>&#160;        }</div>
<div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;</div>
<div class="line"><a name="l01003"></a><span class="lineno"> 1003</span>&#160;        <span class="comment">// xyyz</span></div>
<div class="line"><a name="l01004"></a><span class="lineno"> 1004</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01005"></a><span class="lineno"> 1005</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01006"></a><span class="lineno"> 1006</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.y, v.z);</div>
<div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;        }</div>
<div class="line"><a name="l01008"></a><span class="lineno"> 1008</span>&#160;</div>
<div class="line"><a name="l01009"></a><span class="lineno"> 1009</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01011"></a><span class="lineno"> 1011</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.y, v.z);</div>
<div class="line"><a name="l01012"></a><span class="lineno"> 1012</span>&#160;        }</div>
<div class="line"><a name="l01013"></a><span class="lineno"> 1013</span>&#160;</div>
<div class="line"><a name="l01014"></a><span class="lineno"> 1014</span>&#160;        <span class="comment">// xyyw</span></div>
<div class="line"><a name="l01015"></a><span class="lineno"> 1015</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01016"></a><span class="lineno"> 1016</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01017"></a><span class="lineno"> 1017</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.y, v.w);</div>
<div class="line"><a name="l01018"></a><span class="lineno"> 1018</span>&#160;        }</div>
<div class="line"><a name="l01019"></a><span class="lineno"> 1019</span>&#160;</div>
<div class="line"><a name="l01020"></a><span class="lineno"> 1020</span>&#160;        <span class="comment">// xyzx</span></div>
<div class="line"><a name="l01021"></a><span class="lineno"> 1021</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01023"></a><span class="lineno"> 1023</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.z, v.x);</div>
<div class="line"><a name="l01024"></a><span class="lineno"> 1024</span>&#160;        }</div>
<div class="line"><a name="l01025"></a><span class="lineno"> 1025</span>&#160;</div>
<div class="line"><a name="l01026"></a><span class="lineno"> 1026</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01027"></a><span class="lineno"> 1027</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01028"></a><span class="lineno"> 1028</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.z, v.x);</div>
<div class="line"><a name="l01029"></a><span class="lineno"> 1029</span>&#160;        }</div>
<div class="line"><a name="l01030"></a><span class="lineno"> 1030</span>&#160;</div>
<div class="line"><a name="l01031"></a><span class="lineno"> 1031</span>&#160;        <span class="comment">// xyzy</span></div>
<div class="line"><a name="l01032"></a><span class="lineno"> 1032</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01033"></a><span class="lineno"> 1033</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.z, v.y);</div>
<div class="line"><a name="l01035"></a><span class="lineno"> 1035</span>&#160;        }</div>
<div class="line"><a name="l01036"></a><span class="lineno"> 1036</span>&#160;</div>
<div class="line"><a name="l01037"></a><span class="lineno"> 1037</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01038"></a><span class="lineno"> 1038</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01039"></a><span class="lineno"> 1039</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.z, v.y);</div>
<div class="line"><a name="l01040"></a><span class="lineno"> 1040</span>&#160;        }</div>
<div class="line"><a name="l01041"></a><span class="lineno"> 1041</span>&#160;</div>
<div class="line"><a name="l01042"></a><span class="lineno"> 1042</span>&#160;        <span class="comment">// xyzz</span></div>
<div class="line"><a name="l01043"></a><span class="lineno"> 1043</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01044"></a><span class="lineno"> 1044</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01045"></a><span class="lineno"> 1045</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.z, v.z);</div>
<div class="line"><a name="l01046"></a><span class="lineno"> 1046</span>&#160;        }</div>
<div class="line"><a name="l01047"></a><span class="lineno"> 1047</span>&#160;</div>
<div class="line"><a name="l01048"></a><span class="lineno"> 1048</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01049"></a><span class="lineno"> 1049</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01050"></a><span class="lineno"> 1050</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.z, v.z);</div>
<div class="line"><a name="l01051"></a><span class="lineno"> 1051</span>&#160;        }</div>
<div class="line"><a name="l01052"></a><span class="lineno"> 1052</span>&#160;</div>
<div class="line"><a name="l01053"></a><span class="lineno"> 1053</span>&#160;        <span class="comment">// xyzw</span></div>
<div class="line"><a name="l01054"></a><span class="lineno"> 1054</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01055"></a><span class="lineno"> 1055</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01056"></a><span class="lineno"> 1056</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.z, v.w);</div>
<div class="line"><a name="l01057"></a><span class="lineno"> 1057</span>&#160;        }</div>
<div class="line"><a name="l01058"></a><span class="lineno"> 1058</span>&#160;</div>
<div class="line"><a name="l01059"></a><span class="lineno"> 1059</span>&#160;        <span class="comment">// xywx</span></div>
<div class="line"><a name="l01060"></a><span class="lineno"> 1060</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01061"></a><span class="lineno"> 1061</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xywx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01062"></a><span class="lineno"> 1062</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.w, v.x);</div>
<div class="line"><a name="l01063"></a><span class="lineno"> 1063</span>&#160;        }</div>
<div class="line"><a name="l01064"></a><span class="lineno"> 1064</span>&#160;</div>
<div class="line"><a name="l01065"></a><span class="lineno"> 1065</span>&#160;        <span class="comment">// xywy</span></div>
<div class="line"><a name="l01066"></a><span class="lineno"> 1066</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01067"></a><span class="lineno"> 1067</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xywy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01068"></a><span class="lineno"> 1068</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.w, v.y);</div>
<div class="line"><a name="l01069"></a><span class="lineno"> 1069</span>&#160;        }</div>
<div class="line"><a name="l01070"></a><span class="lineno"> 1070</span>&#160;</div>
<div class="line"><a name="l01071"></a><span class="lineno"> 1071</span>&#160;        <span class="comment">// xywz</span></div>
<div class="line"><a name="l01072"></a><span class="lineno"> 1072</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01073"></a><span class="lineno"> 1073</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xywz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01074"></a><span class="lineno"> 1074</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.w, v.z);</div>
<div class="line"><a name="l01075"></a><span class="lineno"> 1075</span>&#160;        }</div>
<div class="line"><a name="l01076"></a><span class="lineno"> 1076</span>&#160;</div>
<div class="line"><a name="l01077"></a><span class="lineno"> 1077</span>&#160;        <span class="comment">// xyww</span></div>
<div class="line"><a name="l01078"></a><span class="lineno"> 1078</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01079"></a><span class="lineno"> 1079</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xyww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01080"></a><span class="lineno"> 1080</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.y, v.w, v.w);</div>
<div class="line"><a name="l01081"></a><span class="lineno"> 1081</span>&#160;        }</div>
<div class="line"><a name="l01082"></a><span class="lineno"> 1082</span>&#160;</div>
<div class="line"><a name="l01083"></a><span class="lineno"> 1083</span>&#160;        <span class="comment">// xzxx</span></div>
<div class="line"><a name="l01084"></a><span class="lineno"> 1084</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01085"></a><span class="lineno"> 1085</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01086"></a><span class="lineno"> 1086</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.x, v.x);</div>
<div class="line"><a name="l01087"></a><span class="lineno"> 1087</span>&#160;        }</div>
<div class="line"><a name="l01088"></a><span class="lineno"> 1088</span>&#160;</div>
<div class="line"><a name="l01089"></a><span class="lineno"> 1089</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01090"></a><span class="lineno"> 1090</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01091"></a><span class="lineno"> 1091</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.x, v.x);</div>
<div class="line"><a name="l01092"></a><span class="lineno"> 1092</span>&#160;        }</div>
<div class="line"><a name="l01093"></a><span class="lineno"> 1093</span>&#160;</div>
<div class="line"><a name="l01094"></a><span class="lineno"> 1094</span>&#160;        <span class="comment">// xzxy</span></div>
<div class="line"><a name="l01095"></a><span class="lineno"> 1095</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01096"></a><span class="lineno"> 1096</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01097"></a><span class="lineno"> 1097</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.x, v.y);</div>
<div class="line"><a name="l01098"></a><span class="lineno"> 1098</span>&#160;        }</div>
<div class="line"><a name="l01099"></a><span class="lineno"> 1099</span>&#160;</div>
<div class="line"><a name="l01100"></a><span class="lineno"> 1100</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01101"></a><span class="lineno"> 1101</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01102"></a><span class="lineno"> 1102</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.x, v.y);</div>
<div class="line"><a name="l01103"></a><span class="lineno"> 1103</span>&#160;        }</div>
<div class="line"><a name="l01104"></a><span class="lineno"> 1104</span>&#160;</div>
<div class="line"><a name="l01105"></a><span class="lineno"> 1105</span>&#160;        <span class="comment">// xzxz</span></div>
<div class="line"><a name="l01106"></a><span class="lineno"> 1106</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01107"></a><span class="lineno"> 1107</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01108"></a><span class="lineno"> 1108</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.x, v.z);</div>
<div class="line"><a name="l01109"></a><span class="lineno"> 1109</span>&#160;        }</div>
<div class="line"><a name="l01110"></a><span class="lineno"> 1110</span>&#160;</div>
<div class="line"><a name="l01111"></a><span class="lineno"> 1111</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01112"></a><span class="lineno"> 1112</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01113"></a><span class="lineno"> 1113</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.x, v.z);</div>
<div class="line"><a name="l01114"></a><span class="lineno"> 1114</span>&#160;        }</div>
<div class="line"><a name="l01115"></a><span class="lineno"> 1115</span>&#160;</div>
<div class="line"><a name="l01116"></a><span class="lineno"> 1116</span>&#160;        <span class="comment">// xzxw</span></div>
<div class="line"><a name="l01117"></a><span class="lineno"> 1117</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01118"></a><span class="lineno"> 1118</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01119"></a><span class="lineno"> 1119</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.x, v.w);</div>
<div class="line"><a name="l01120"></a><span class="lineno"> 1120</span>&#160;        }</div>
<div class="line"><a name="l01121"></a><span class="lineno"> 1121</span>&#160;</div>
<div class="line"><a name="l01122"></a><span class="lineno"> 1122</span>&#160;        <span class="comment">// xzyx</span></div>
<div class="line"><a name="l01123"></a><span class="lineno"> 1123</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01124"></a><span class="lineno"> 1124</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01125"></a><span class="lineno"> 1125</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.y, v.x);</div>
<div class="line"><a name="l01126"></a><span class="lineno"> 1126</span>&#160;        }</div>
<div class="line"><a name="l01127"></a><span class="lineno"> 1127</span>&#160;</div>
<div class="line"><a name="l01128"></a><span class="lineno"> 1128</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01129"></a><span class="lineno"> 1129</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01130"></a><span class="lineno"> 1130</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.y, v.x);</div>
<div class="line"><a name="l01131"></a><span class="lineno"> 1131</span>&#160;        }</div>
<div class="line"><a name="l01132"></a><span class="lineno"> 1132</span>&#160;</div>
<div class="line"><a name="l01133"></a><span class="lineno"> 1133</span>&#160;        <span class="comment">// xzyy</span></div>
<div class="line"><a name="l01134"></a><span class="lineno"> 1134</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01135"></a><span class="lineno"> 1135</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01136"></a><span class="lineno"> 1136</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.y, v.y);</div>
<div class="line"><a name="l01137"></a><span class="lineno"> 1137</span>&#160;        }</div>
<div class="line"><a name="l01138"></a><span class="lineno"> 1138</span>&#160;</div>
<div class="line"><a name="l01139"></a><span class="lineno"> 1139</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01140"></a><span class="lineno"> 1140</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01141"></a><span class="lineno"> 1141</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.y, v.y);</div>
<div class="line"><a name="l01142"></a><span class="lineno"> 1142</span>&#160;        }</div>
<div class="line"><a name="l01143"></a><span class="lineno"> 1143</span>&#160;</div>
<div class="line"><a name="l01144"></a><span class="lineno"> 1144</span>&#160;        <span class="comment">// xzyz</span></div>
<div class="line"><a name="l01145"></a><span class="lineno"> 1145</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01146"></a><span class="lineno"> 1146</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01147"></a><span class="lineno"> 1147</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.y, v.z);</div>
<div class="line"><a name="l01148"></a><span class="lineno"> 1148</span>&#160;        }</div>
<div class="line"><a name="l01149"></a><span class="lineno"> 1149</span>&#160;</div>
<div class="line"><a name="l01150"></a><span class="lineno"> 1150</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01151"></a><span class="lineno"> 1151</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01152"></a><span class="lineno"> 1152</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.y, v.z);</div>
<div class="line"><a name="l01153"></a><span class="lineno"> 1153</span>&#160;        }</div>
<div class="line"><a name="l01154"></a><span class="lineno"> 1154</span>&#160;</div>
<div class="line"><a name="l01155"></a><span class="lineno"> 1155</span>&#160;        <span class="comment">// xzyw</span></div>
<div class="line"><a name="l01156"></a><span class="lineno"> 1156</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01157"></a><span class="lineno"> 1157</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01158"></a><span class="lineno"> 1158</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.y, v.w);</div>
<div class="line"><a name="l01159"></a><span class="lineno"> 1159</span>&#160;        }</div>
<div class="line"><a name="l01160"></a><span class="lineno"> 1160</span>&#160;</div>
<div class="line"><a name="l01161"></a><span class="lineno"> 1161</span>&#160;        <span class="comment">// xzzx</span></div>
<div class="line"><a name="l01162"></a><span class="lineno"> 1162</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01163"></a><span class="lineno"> 1163</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01164"></a><span class="lineno"> 1164</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.z, v.x);</div>
<div class="line"><a name="l01165"></a><span class="lineno"> 1165</span>&#160;        }</div>
<div class="line"><a name="l01166"></a><span class="lineno"> 1166</span>&#160;</div>
<div class="line"><a name="l01167"></a><span class="lineno"> 1167</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01168"></a><span class="lineno"> 1168</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01169"></a><span class="lineno"> 1169</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.z, v.x);</div>
<div class="line"><a name="l01170"></a><span class="lineno"> 1170</span>&#160;        }</div>
<div class="line"><a name="l01171"></a><span class="lineno"> 1171</span>&#160;</div>
<div class="line"><a name="l01172"></a><span class="lineno"> 1172</span>&#160;        <span class="comment">// xzzy</span></div>
<div class="line"><a name="l01173"></a><span class="lineno"> 1173</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01174"></a><span class="lineno"> 1174</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01175"></a><span class="lineno"> 1175</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.z, v.y);</div>
<div class="line"><a name="l01176"></a><span class="lineno"> 1176</span>&#160;        }</div>
<div class="line"><a name="l01177"></a><span class="lineno"> 1177</span>&#160;</div>
<div class="line"><a name="l01178"></a><span class="lineno"> 1178</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01179"></a><span class="lineno"> 1179</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01180"></a><span class="lineno"> 1180</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.z, v.y);</div>
<div class="line"><a name="l01181"></a><span class="lineno"> 1181</span>&#160;        }</div>
<div class="line"><a name="l01182"></a><span class="lineno"> 1182</span>&#160;</div>
<div class="line"><a name="l01183"></a><span class="lineno"> 1183</span>&#160;        <span class="comment">// xzzz</span></div>
<div class="line"><a name="l01184"></a><span class="lineno"> 1184</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01185"></a><span class="lineno"> 1185</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01186"></a><span class="lineno"> 1186</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.z, v.z);</div>
<div class="line"><a name="l01187"></a><span class="lineno"> 1187</span>&#160;        }</div>
<div class="line"><a name="l01188"></a><span class="lineno"> 1188</span>&#160;</div>
<div class="line"><a name="l01189"></a><span class="lineno"> 1189</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01190"></a><span class="lineno"> 1190</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01191"></a><span class="lineno"> 1191</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.z, v.z);</div>
<div class="line"><a name="l01192"></a><span class="lineno"> 1192</span>&#160;        }</div>
<div class="line"><a name="l01193"></a><span class="lineno"> 1193</span>&#160;</div>
<div class="line"><a name="l01194"></a><span class="lineno"> 1194</span>&#160;        <span class="comment">// xzzw</span></div>
<div class="line"><a name="l01195"></a><span class="lineno"> 1195</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01196"></a><span class="lineno"> 1196</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01197"></a><span class="lineno"> 1197</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.z, v.w);</div>
<div class="line"><a name="l01198"></a><span class="lineno"> 1198</span>&#160;        }</div>
<div class="line"><a name="l01199"></a><span class="lineno"> 1199</span>&#160;</div>
<div class="line"><a name="l01200"></a><span class="lineno"> 1200</span>&#160;        <span class="comment">// xzwx</span></div>
<div class="line"><a name="l01201"></a><span class="lineno"> 1201</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01202"></a><span class="lineno"> 1202</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01203"></a><span class="lineno"> 1203</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.w, v.x);</div>
<div class="line"><a name="l01204"></a><span class="lineno"> 1204</span>&#160;        }</div>
<div class="line"><a name="l01205"></a><span class="lineno"> 1205</span>&#160;</div>
<div class="line"><a name="l01206"></a><span class="lineno"> 1206</span>&#160;        <span class="comment">// xzwy</span></div>
<div class="line"><a name="l01207"></a><span class="lineno"> 1207</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01208"></a><span class="lineno"> 1208</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01209"></a><span class="lineno"> 1209</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.w, v.y);</div>
<div class="line"><a name="l01210"></a><span class="lineno"> 1210</span>&#160;        }</div>
<div class="line"><a name="l01211"></a><span class="lineno"> 1211</span>&#160;</div>
<div class="line"><a name="l01212"></a><span class="lineno"> 1212</span>&#160;        <span class="comment">// xzwz</span></div>
<div class="line"><a name="l01213"></a><span class="lineno"> 1213</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01214"></a><span class="lineno"> 1214</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01215"></a><span class="lineno"> 1215</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.w, v.z);</div>
<div class="line"><a name="l01216"></a><span class="lineno"> 1216</span>&#160;        }</div>
<div class="line"><a name="l01217"></a><span class="lineno"> 1217</span>&#160;</div>
<div class="line"><a name="l01218"></a><span class="lineno"> 1218</span>&#160;        <span class="comment">// xzww</span></div>
<div class="line"><a name="l01219"></a><span class="lineno"> 1219</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01220"></a><span class="lineno"> 1220</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xzww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01221"></a><span class="lineno"> 1221</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.z, v.w, v.w);</div>
<div class="line"><a name="l01222"></a><span class="lineno"> 1222</span>&#160;        }</div>
<div class="line"><a name="l01223"></a><span class="lineno"> 1223</span>&#160;</div>
<div class="line"><a name="l01224"></a><span class="lineno"> 1224</span>&#160;        <span class="comment">// xwxx</span></div>
<div class="line"><a name="l01225"></a><span class="lineno"> 1225</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01226"></a><span class="lineno"> 1226</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01227"></a><span class="lineno"> 1227</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.x, v.x);</div>
<div class="line"><a name="l01228"></a><span class="lineno"> 1228</span>&#160;        }</div>
<div class="line"><a name="l01229"></a><span class="lineno"> 1229</span>&#160;</div>
<div class="line"><a name="l01230"></a><span class="lineno"> 1230</span>&#160;        <span class="comment">// xwxy</span></div>
<div class="line"><a name="l01231"></a><span class="lineno"> 1231</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01232"></a><span class="lineno"> 1232</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01233"></a><span class="lineno"> 1233</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.x, v.y);</div>
<div class="line"><a name="l01234"></a><span class="lineno"> 1234</span>&#160;        }</div>
<div class="line"><a name="l01235"></a><span class="lineno"> 1235</span>&#160;</div>
<div class="line"><a name="l01236"></a><span class="lineno"> 1236</span>&#160;        <span class="comment">// xwxz</span></div>
<div class="line"><a name="l01237"></a><span class="lineno"> 1237</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01238"></a><span class="lineno"> 1238</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01239"></a><span class="lineno"> 1239</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.x, v.z);</div>
<div class="line"><a name="l01240"></a><span class="lineno"> 1240</span>&#160;        }</div>
<div class="line"><a name="l01241"></a><span class="lineno"> 1241</span>&#160;</div>
<div class="line"><a name="l01242"></a><span class="lineno"> 1242</span>&#160;        <span class="comment">// xwxw</span></div>
<div class="line"><a name="l01243"></a><span class="lineno"> 1243</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01244"></a><span class="lineno"> 1244</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01245"></a><span class="lineno"> 1245</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.x, v.w);</div>
<div class="line"><a name="l01246"></a><span class="lineno"> 1246</span>&#160;        }</div>
<div class="line"><a name="l01247"></a><span class="lineno"> 1247</span>&#160;</div>
<div class="line"><a name="l01248"></a><span class="lineno"> 1248</span>&#160;        <span class="comment">// xwyx</span></div>
<div class="line"><a name="l01249"></a><span class="lineno"> 1249</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01250"></a><span class="lineno"> 1250</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01251"></a><span class="lineno"> 1251</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.y, v.x);</div>
<div class="line"><a name="l01252"></a><span class="lineno"> 1252</span>&#160;        }</div>
<div class="line"><a name="l01253"></a><span class="lineno"> 1253</span>&#160;</div>
<div class="line"><a name="l01254"></a><span class="lineno"> 1254</span>&#160;        <span class="comment">// xwyy</span></div>
<div class="line"><a name="l01255"></a><span class="lineno"> 1255</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01256"></a><span class="lineno"> 1256</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01257"></a><span class="lineno"> 1257</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.y, v.y);</div>
<div class="line"><a name="l01258"></a><span class="lineno"> 1258</span>&#160;        }</div>
<div class="line"><a name="l01259"></a><span class="lineno"> 1259</span>&#160;</div>
<div class="line"><a name="l01260"></a><span class="lineno"> 1260</span>&#160;        <span class="comment">// xwyz</span></div>
<div class="line"><a name="l01261"></a><span class="lineno"> 1261</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01262"></a><span class="lineno"> 1262</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01263"></a><span class="lineno"> 1263</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.y, v.z);</div>
<div class="line"><a name="l01264"></a><span class="lineno"> 1264</span>&#160;        }</div>
<div class="line"><a name="l01265"></a><span class="lineno"> 1265</span>&#160;</div>
<div class="line"><a name="l01266"></a><span class="lineno"> 1266</span>&#160;        <span class="comment">// xwyw</span></div>
<div class="line"><a name="l01267"></a><span class="lineno"> 1267</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01268"></a><span class="lineno"> 1268</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01269"></a><span class="lineno"> 1269</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.y, v.w);</div>
<div class="line"><a name="l01270"></a><span class="lineno"> 1270</span>&#160;        }</div>
<div class="line"><a name="l01271"></a><span class="lineno"> 1271</span>&#160;</div>
<div class="line"><a name="l01272"></a><span class="lineno"> 1272</span>&#160;        <span class="comment">// xwzx</span></div>
<div class="line"><a name="l01273"></a><span class="lineno"> 1273</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01274"></a><span class="lineno"> 1274</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01275"></a><span class="lineno"> 1275</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.z, v.x);</div>
<div class="line"><a name="l01276"></a><span class="lineno"> 1276</span>&#160;        }</div>
<div class="line"><a name="l01277"></a><span class="lineno"> 1277</span>&#160;</div>
<div class="line"><a name="l01278"></a><span class="lineno"> 1278</span>&#160;        <span class="comment">// xwzy</span></div>
<div class="line"><a name="l01279"></a><span class="lineno"> 1279</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01280"></a><span class="lineno"> 1280</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01281"></a><span class="lineno"> 1281</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.z, v.y);</div>
<div class="line"><a name="l01282"></a><span class="lineno"> 1282</span>&#160;        }</div>
<div class="line"><a name="l01283"></a><span class="lineno"> 1283</span>&#160;</div>
<div class="line"><a name="l01284"></a><span class="lineno"> 1284</span>&#160;        <span class="comment">// xwzz</span></div>
<div class="line"><a name="l01285"></a><span class="lineno"> 1285</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01286"></a><span class="lineno"> 1286</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01287"></a><span class="lineno"> 1287</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.z, v.z);</div>
<div class="line"><a name="l01288"></a><span class="lineno"> 1288</span>&#160;        }</div>
<div class="line"><a name="l01289"></a><span class="lineno"> 1289</span>&#160;</div>
<div class="line"><a name="l01290"></a><span class="lineno"> 1290</span>&#160;        <span class="comment">// xwzw</span></div>
<div class="line"><a name="l01291"></a><span class="lineno"> 1291</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01292"></a><span class="lineno"> 1292</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01293"></a><span class="lineno"> 1293</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.z, v.w);</div>
<div class="line"><a name="l01294"></a><span class="lineno"> 1294</span>&#160;        }</div>
<div class="line"><a name="l01295"></a><span class="lineno"> 1295</span>&#160;</div>
<div class="line"><a name="l01296"></a><span class="lineno"> 1296</span>&#160;        <span class="comment">// xwwx</span></div>
<div class="line"><a name="l01297"></a><span class="lineno"> 1297</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01298"></a><span class="lineno"> 1298</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01299"></a><span class="lineno"> 1299</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.w, v.x);</div>
<div class="line"><a name="l01300"></a><span class="lineno"> 1300</span>&#160;        }</div>
<div class="line"><a name="l01301"></a><span class="lineno"> 1301</span>&#160;</div>
<div class="line"><a name="l01302"></a><span class="lineno"> 1302</span>&#160;        <span class="comment">// xwwy</span></div>
<div class="line"><a name="l01303"></a><span class="lineno"> 1303</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01304"></a><span class="lineno"> 1304</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01305"></a><span class="lineno"> 1305</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.w, v.y);</div>
<div class="line"><a name="l01306"></a><span class="lineno"> 1306</span>&#160;        }</div>
<div class="line"><a name="l01307"></a><span class="lineno"> 1307</span>&#160;</div>
<div class="line"><a name="l01308"></a><span class="lineno"> 1308</span>&#160;        <span class="comment">// xwwz</span></div>
<div class="line"><a name="l01309"></a><span class="lineno"> 1309</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01310"></a><span class="lineno"> 1310</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01311"></a><span class="lineno"> 1311</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.w, v.z);</div>
<div class="line"><a name="l01312"></a><span class="lineno"> 1312</span>&#160;        }</div>
<div class="line"><a name="l01313"></a><span class="lineno"> 1313</span>&#160;</div>
<div class="line"><a name="l01314"></a><span class="lineno"> 1314</span>&#160;        <span class="comment">// xwww</span></div>
<div class="line"><a name="l01315"></a><span class="lineno"> 1315</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01316"></a><span class="lineno"> 1316</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; xwww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01317"></a><span class="lineno"> 1317</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.x, v.w, v.w, v.w);</div>
<div class="line"><a name="l01318"></a><span class="lineno"> 1318</span>&#160;        }</div>
<div class="line"><a name="l01319"></a><span class="lineno"> 1319</span>&#160;</div>
<div class="line"><a name="l01320"></a><span class="lineno"> 1320</span>&#160;        <span class="comment">// yxxx</span></div>
<div class="line"><a name="l01321"></a><span class="lineno"> 1321</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01322"></a><span class="lineno"> 1322</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxxx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01323"></a><span class="lineno"> 1323</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.x, v.x);</div>
<div class="line"><a name="l01324"></a><span class="lineno"> 1324</span>&#160;        }</div>
<div class="line"><a name="l01325"></a><span class="lineno"> 1325</span>&#160;</div>
<div class="line"><a name="l01326"></a><span class="lineno"> 1326</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01327"></a><span class="lineno"> 1327</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01328"></a><span class="lineno"> 1328</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.x, v.x);</div>
<div class="line"><a name="l01329"></a><span class="lineno"> 1329</span>&#160;        }</div>
<div class="line"><a name="l01330"></a><span class="lineno"> 1330</span>&#160;</div>
<div class="line"><a name="l01331"></a><span class="lineno"> 1331</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01332"></a><span class="lineno"> 1332</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01333"></a><span class="lineno"> 1333</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.x, v.x);</div>
<div class="line"><a name="l01334"></a><span class="lineno"> 1334</span>&#160;        }</div>
<div class="line"><a name="l01335"></a><span class="lineno"> 1335</span>&#160;</div>
<div class="line"><a name="l01336"></a><span class="lineno"> 1336</span>&#160;        <span class="comment">// yxxy</span></div>
<div class="line"><a name="l01337"></a><span class="lineno"> 1337</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01338"></a><span class="lineno"> 1338</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxxy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01339"></a><span class="lineno"> 1339</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.x, v.y);</div>
<div class="line"><a name="l01340"></a><span class="lineno"> 1340</span>&#160;        }</div>
<div class="line"><a name="l01341"></a><span class="lineno"> 1341</span>&#160;</div>
<div class="line"><a name="l01342"></a><span class="lineno"> 1342</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01343"></a><span class="lineno"> 1343</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01344"></a><span class="lineno"> 1344</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.x, v.y);</div>
<div class="line"><a name="l01345"></a><span class="lineno"> 1345</span>&#160;        }</div>
<div class="line"><a name="l01346"></a><span class="lineno"> 1346</span>&#160;</div>
<div class="line"><a name="l01347"></a><span class="lineno"> 1347</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01348"></a><span class="lineno"> 1348</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01349"></a><span class="lineno"> 1349</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.x, v.y);</div>
<div class="line"><a name="l01350"></a><span class="lineno"> 1350</span>&#160;        }</div>
<div class="line"><a name="l01351"></a><span class="lineno"> 1351</span>&#160;</div>
<div class="line"><a name="l01352"></a><span class="lineno"> 1352</span>&#160;        <span class="comment">// yxxz</span></div>
<div class="line"><a name="l01353"></a><span class="lineno"> 1353</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01354"></a><span class="lineno"> 1354</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01355"></a><span class="lineno"> 1355</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.x, v.z);</div>
<div class="line"><a name="l01356"></a><span class="lineno"> 1356</span>&#160;        }</div>
<div class="line"><a name="l01357"></a><span class="lineno"> 1357</span>&#160;</div>
<div class="line"><a name="l01358"></a><span class="lineno"> 1358</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01359"></a><span class="lineno"> 1359</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01360"></a><span class="lineno"> 1360</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.x, v.z);</div>
<div class="line"><a name="l01361"></a><span class="lineno"> 1361</span>&#160;        }</div>
<div class="line"><a name="l01362"></a><span class="lineno"> 1362</span>&#160;</div>
<div class="line"><a name="l01363"></a><span class="lineno"> 1363</span>&#160;        <span class="comment">// yxxw</span></div>
<div class="line"><a name="l01364"></a><span class="lineno"> 1364</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01365"></a><span class="lineno"> 1365</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01366"></a><span class="lineno"> 1366</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.x, v.w);</div>
<div class="line"><a name="l01367"></a><span class="lineno"> 1367</span>&#160;        }</div>
<div class="line"><a name="l01368"></a><span class="lineno"> 1368</span>&#160;</div>
<div class="line"><a name="l01369"></a><span class="lineno"> 1369</span>&#160;        <span class="comment">// yxyx</span></div>
<div class="line"><a name="l01370"></a><span class="lineno"> 1370</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01371"></a><span class="lineno"> 1371</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxyx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01372"></a><span class="lineno"> 1372</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.y, v.x);</div>
<div class="line"><a name="l01373"></a><span class="lineno"> 1373</span>&#160;        }</div>
<div class="line"><a name="l01374"></a><span class="lineno"> 1374</span>&#160;</div>
<div class="line"><a name="l01375"></a><span class="lineno"> 1375</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01376"></a><span class="lineno"> 1376</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01377"></a><span class="lineno"> 1377</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.y, v.x);</div>
<div class="line"><a name="l01378"></a><span class="lineno"> 1378</span>&#160;        }</div>
<div class="line"><a name="l01379"></a><span class="lineno"> 1379</span>&#160;</div>
<div class="line"><a name="l01380"></a><span class="lineno"> 1380</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01381"></a><span class="lineno"> 1381</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01382"></a><span class="lineno"> 1382</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.y, v.x);</div>
<div class="line"><a name="l01383"></a><span class="lineno"> 1383</span>&#160;        }</div>
<div class="line"><a name="l01384"></a><span class="lineno"> 1384</span>&#160;</div>
<div class="line"><a name="l01385"></a><span class="lineno"> 1385</span>&#160;        <span class="comment">// yxyy</span></div>
<div class="line"><a name="l01386"></a><span class="lineno"> 1386</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01387"></a><span class="lineno"> 1387</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxyy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01388"></a><span class="lineno"> 1388</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.y, v.y);</div>
<div class="line"><a name="l01389"></a><span class="lineno"> 1389</span>&#160;        }</div>
<div class="line"><a name="l01390"></a><span class="lineno"> 1390</span>&#160;</div>
<div class="line"><a name="l01391"></a><span class="lineno"> 1391</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01392"></a><span class="lineno"> 1392</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01393"></a><span class="lineno"> 1393</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.y, v.y);</div>
<div class="line"><a name="l01394"></a><span class="lineno"> 1394</span>&#160;        }</div>
<div class="line"><a name="l01395"></a><span class="lineno"> 1395</span>&#160;</div>
<div class="line"><a name="l01396"></a><span class="lineno"> 1396</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01397"></a><span class="lineno"> 1397</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01398"></a><span class="lineno"> 1398</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.y, v.y);</div>
<div class="line"><a name="l01399"></a><span class="lineno"> 1399</span>&#160;        }</div>
<div class="line"><a name="l01400"></a><span class="lineno"> 1400</span>&#160;</div>
<div class="line"><a name="l01401"></a><span class="lineno"> 1401</span>&#160;        <span class="comment">// yxyz</span></div>
<div class="line"><a name="l01402"></a><span class="lineno"> 1402</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01403"></a><span class="lineno"> 1403</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01404"></a><span class="lineno"> 1404</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.y, v.z);</div>
<div class="line"><a name="l01405"></a><span class="lineno"> 1405</span>&#160;        }</div>
<div class="line"><a name="l01406"></a><span class="lineno"> 1406</span>&#160;</div>
<div class="line"><a name="l01407"></a><span class="lineno"> 1407</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01408"></a><span class="lineno"> 1408</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01409"></a><span class="lineno"> 1409</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.y, v.z);</div>
<div class="line"><a name="l01410"></a><span class="lineno"> 1410</span>&#160;        }</div>
<div class="line"><a name="l01411"></a><span class="lineno"> 1411</span>&#160;</div>
<div class="line"><a name="l01412"></a><span class="lineno"> 1412</span>&#160;        <span class="comment">// yxyw</span></div>
<div class="line"><a name="l01413"></a><span class="lineno"> 1413</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01414"></a><span class="lineno"> 1414</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01415"></a><span class="lineno"> 1415</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.y, v.w);</div>
<div class="line"><a name="l01416"></a><span class="lineno"> 1416</span>&#160;        }</div>
<div class="line"><a name="l01417"></a><span class="lineno"> 1417</span>&#160;</div>
<div class="line"><a name="l01418"></a><span class="lineno"> 1418</span>&#160;        <span class="comment">// yxzx</span></div>
<div class="line"><a name="l01419"></a><span class="lineno"> 1419</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01420"></a><span class="lineno"> 1420</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01421"></a><span class="lineno"> 1421</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.z, v.x);</div>
<div class="line"><a name="l01422"></a><span class="lineno"> 1422</span>&#160;        }</div>
<div class="line"><a name="l01423"></a><span class="lineno"> 1423</span>&#160;</div>
<div class="line"><a name="l01424"></a><span class="lineno"> 1424</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01425"></a><span class="lineno"> 1425</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01426"></a><span class="lineno"> 1426</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.z, v.x);</div>
<div class="line"><a name="l01427"></a><span class="lineno"> 1427</span>&#160;        }</div>
<div class="line"><a name="l01428"></a><span class="lineno"> 1428</span>&#160;</div>
<div class="line"><a name="l01429"></a><span class="lineno"> 1429</span>&#160;        <span class="comment">// yxzy</span></div>
<div class="line"><a name="l01430"></a><span class="lineno"> 1430</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01431"></a><span class="lineno"> 1431</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01432"></a><span class="lineno"> 1432</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.z, v.y);</div>
<div class="line"><a name="l01433"></a><span class="lineno"> 1433</span>&#160;        }</div>
<div class="line"><a name="l01434"></a><span class="lineno"> 1434</span>&#160;</div>
<div class="line"><a name="l01435"></a><span class="lineno"> 1435</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01436"></a><span class="lineno"> 1436</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01437"></a><span class="lineno"> 1437</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.z, v.y);</div>
<div class="line"><a name="l01438"></a><span class="lineno"> 1438</span>&#160;        }</div>
<div class="line"><a name="l01439"></a><span class="lineno"> 1439</span>&#160;</div>
<div class="line"><a name="l01440"></a><span class="lineno"> 1440</span>&#160;        <span class="comment">// yxzz</span></div>
<div class="line"><a name="l01441"></a><span class="lineno"> 1441</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01442"></a><span class="lineno"> 1442</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01443"></a><span class="lineno"> 1443</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.z, v.z);</div>
<div class="line"><a name="l01444"></a><span class="lineno"> 1444</span>&#160;        }</div>
<div class="line"><a name="l01445"></a><span class="lineno"> 1445</span>&#160;</div>
<div class="line"><a name="l01446"></a><span class="lineno"> 1446</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01447"></a><span class="lineno"> 1447</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01448"></a><span class="lineno"> 1448</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.z, v.z);</div>
<div class="line"><a name="l01449"></a><span class="lineno"> 1449</span>&#160;        }</div>
<div class="line"><a name="l01450"></a><span class="lineno"> 1450</span>&#160;</div>
<div class="line"><a name="l01451"></a><span class="lineno"> 1451</span>&#160;        <span class="comment">// yxzw</span></div>
<div class="line"><a name="l01452"></a><span class="lineno"> 1452</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01453"></a><span class="lineno"> 1453</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01454"></a><span class="lineno"> 1454</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.z, v.w);</div>
<div class="line"><a name="l01455"></a><span class="lineno"> 1455</span>&#160;        }</div>
<div class="line"><a name="l01456"></a><span class="lineno"> 1456</span>&#160;</div>
<div class="line"><a name="l01457"></a><span class="lineno"> 1457</span>&#160;        <span class="comment">// yxwx</span></div>
<div class="line"><a name="l01458"></a><span class="lineno"> 1458</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01459"></a><span class="lineno"> 1459</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01460"></a><span class="lineno"> 1460</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.w, v.x);</div>
<div class="line"><a name="l01461"></a><span class="lineno"> 1461</span>&#160;        }</div>
<div class="line"><a name="l01462"></a><span class="lineno"> 1462</span>&#160;</div>
<div class="line"><a name="l01463"></a><span class="lineno"> 1463</span>&#160;        <span class="comment">// yxwy</span></div>
<div class="line"><a name="l01464"></a><span class="lineno"> 1464</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01465"></a><span class="lineno"> 1465</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01466"></a><span class="lineno"> 1466</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.w, v.y);</div>
<div class="line"><a name="l01467"></a><span class="lineno"> 1467</span>&#160;        }</div>
<div class="line"><a name="l01468"></a><span class="lineno"> 1468</span>&#160;</div>
<div class="line"><a name="l01469"></a><span class="lineno"> 1469</span>&#160;        <span class="comment">// yxwz</span></div>
<div class="line"><a name="l01470"></a><span class="lineno"> 1470</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01471"></a><span class="lineno"> 1471</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01472"></a><span class="lineno"> 1472</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.w, v.z);</div>
<div class="line"><a name="l01473"></a><span class="lineno"> 1473</span>&#160;        }</div>
<div class="line"><a name="l01474"></a><span class="lineno"> 1474</span>&#160;</div>
<div class="line"><a name="l01475"></a><span class="lineno"> 1475</span>&#160;        <span class="comment">// yxww</span></div>
<div class="line"><a name="l01476"></a><span class="lineno"> 1476</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01477"></a><span class="lineno"> 1477</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yxww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01478"></a><span class="lineno"> 1478</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.x, v.w, v.w);</div>
<div class="line"><a name="l01479"></a><span class="lineno"> 1479</span>&#160;        }</div>
<div class="line"><a name="l01480"></a><span class="lineno"> 1480</span>&#160;</div>
<div class="line"><a name="l01481"></a><span class="lineno"> 1481</span>&#160;        <span class="comment">// yyxx</span></div>
<div class="line"><a name="l01482"></a><span class="lineno"> 1482</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01483"></a><span class="lineno"> 1483</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyxx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01484"></a><span class="lineno"> 1484</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.x, v.x);</div>
<div class="line"><a name="l01485"></a><span class="lineno"> 1485</span>&#160;        }</div>
<div class="line"><a name="l01486"></a><span class="lineno"> 1486</span>&#160;</div>
<div class="line"><a name="l01487"></a><span class="lineno"> 1487</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01488"></a><span class="lineno"> 1488</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01489"></a><span class="lineno"> 1489</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.x, v.x);</div>
<div class="line"><a name="l01490"></a><span class="lineno"> 1490</span>&#160;        }</div>
<div class="line"><a name="l01491"></a><span class="lineno"> 1491</span>&#160;</div>
<div class="line"><a name="l01492"></a><span class="lineno"> 1492</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01493"></a><span class="lineno"> 1493</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01494"></a><span class="lineno"> 1494</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.x, v.x);</div>
<div class="line"><a name="l01495"></a><span class="lineno"> 1495</span>&#160;        }</div>
<div class="line"><a name="l01496"></a><span class="lineno"> 1496</span>&#160;</div>
<div class="line"><a name="l01497"></a><span class="lineno"> 1497</span>&#160;        <span class="comment">// yyxy</span></div>
<div class="line"><a name="l01498"></a><span class="lineno"> 1498</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01499"></a><span class="lineno"> 1499</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyxy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01500"></a><span class="lineno"> 1500</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.x, v.y);</div>
<div class="line"><a name="l01501"></a><span class="lineno"> 1501</span>&#160;        }</div>
<div class="line"><a name="l01502"></a><span class="lineno"> 1502</span>&#160;</div>
<div class="line"><a name="l01503"></a><span class="lineno"> 1503</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01504"></a><span class="lineno"> 1504</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01505"></a><span class="lineno"> 1505</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.x, v.y);</div>
<div class="line"><a name="l01506"></a><span class="lineno"> 1506</span>&#160;        }</div>
<div class="line"><a name="l01507"></a><span class="lineno"> 1507</span>&#160;</div>
<div class="line"><a name="l01508"></a><span class="lineno"> 1508</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01509"></a><span class="lineno"> 1509</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01510"></a><span class="lineno"> 1510</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.x, v.y);</div>
<div class="line"><a name="l01511"></a><span class="lineno"> 1511</span>&#160;        }</div>
<div class="line"><a name="l01512"></a><span class="lineno"> 1512</span>&#160;</div>
<div class="line"><a name="l01513"></a><span class="lineno"> 1513</span>&#160;        <span class="comment">// yyxz</span></div>
<div class="line"><a name="l01514"></a><span class="lineno"> 1514</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01515"></a><span class="lineno"> 1515</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01516"></a><span class="lineno"> 1516</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.x, v.z);</div>
<div class="line"><a name="l01517"></a><span class="lineno"> 1517</span>&#160;        }</div>
<div class="line"><a name="l01518"></a><span class="lineno"> 1518</span>&#160;</div>
<div class="line"><a name="l01519"></a><span class="lineno"> 1519</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01520"></a><span class="lineno"> 1520</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01521"></a><span class="lineno"> 1521</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.x, v.z);</div>
<div class="line"><a name="l01522"></a><span class="lineno"> 1522</span>&#160;        }</div>
<div class="line"><a name="l01523"></a><span class="lineno"> 1523</span>&#160;</div>
<div class="line"><a name="l01524"></a><span class="lineno"> 1524</span>&#160;        <span class="comment">// yyxw</span></div>
<div class="line"><a name="l01525"></a><span class="lineno"> 1525</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01526"></a><span class="lineno"> 1526</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01527"></a><span class="lineno"> 1527</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.x, v.w);</div>
<div class="line"><a name="l01528"></a><span class="lineno"> 1528</span>&#160;        }</div>
<div class="line"><a name="l01529"></a><span class="lineno"> 1529</span>&#160;</div>
<div class="line"><a name="l01530"></a><span class="lineno"> 1530</span>&#160;        <span class="comment">// yyyx</span></div>
<div class="line"><a name="l01531"></a><span class="lineno"> 1531</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01532"></a><span class="lineno"> 1532</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyyx(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01533"></a><span class="lineno"> 1533</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.y, v.x);</div>
<div class="line"><a name="l01534"></a><span class="lineno"> 1534</span>&#160;        }</div>
<div class="line"><a name="l01535"></a><span class="lineno"> 1535</span>&#160;</div>
<div class="line"><a name="l01536"></a><span class="lineno"> 1536</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01537"></a><span class="lineno"> 1537</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01538"></a><span class="lineno"> 1538</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.y, v.x);</div>
<div class="line"><a name="l01539"></a><span class="lineno"> 1539</span>&#160;        }</div>
<div class="line"><a name="l01540"></a><span class="lineno"> 1540</span>&#160;</div>
<div class="line"><a name="l01541"></a><span class="lineno"> 1541</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01542"></a><span class="lineno"> 1542</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01543"></a><span class="lineno"> 1543</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.y, v.x);</div>
<div class="line"><a name="l01544"></a><span class="lineno"> 1544</span>&#160;        }</div>
<div class="line"><a name="l01545"></a><span class="lineno"> 1545</span>&#160;</div>
<div class="line"><a name="l01546"></a><span class="lineno"> 1546</span>&#160;        <span class="comment">// yyyy</span></div>
<div class="line"><a name="l01547"></a><span class="lineno"> 1547</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01548"></a><span class="lineno"> 1548</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyyy(<span class="keyword">const</span> glm::vec&lt;2, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01549"></a><span class="lineno"> 1549</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.y, v.y);</div>
<div class="line"><a name="l01550"></a><span class="lineno"> 1550</span>&#160;        }</div>
<div class="line"><a name="l01551"></a><span class="lineno"> 1551</span>&#160;</div>
<div class="line"><a name="l01552"></a><span class="lineno"> 1552</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01553"></a><span class="lineno"> 1553</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01554"></a><span class="lineno"> 1554</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.y, v.y);</div>
<div class="line"><a name="l01555"></a><span class="lineno"> 1555</span>&#160;        }</div>
<div class="line"><a name="l01556"></a><span class="lineno"> 1556</span>&#160;</div>
<div class="line"><a name="l01557"></a><span class="lineno"> 1557</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01558"></a><span class="lineno"> 1558</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01559"></a><span class="lineno"> 1559</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.y, v.y);</div>
<div class="line"><a name="l01560"></a><span class="lineno"> 1560</span>&#160;        }</div>
<div class="line"><a name="l01561"></a><span class="lineno"> 1561</span>&#160;</div>
<div class="line"><a name="l01562"></a><span class="lineno"> 1562</span>&#160;        <span class="comment">// yyyz</span></div>
<div class="line"><a name="l01563"></a><span class="lineno"> 1563</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01564"></a><span class="lineno"> 1564</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01565"></a><span class="lineno"> 1565</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.y, v.z);</div>
<div class="line"><a name="l01566"></a><span class="lineno"> 1566</span>&#160;        }</div>
<div class="line"><a name="l01567"></a><span class="lineno"> 1567</span>&#160;</div>
<div class="line"><a name="l01568"></a><span class="lineno"> 1568</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01569"></a><span class="lineno"> 1569</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01570"></a><span class="lineno"> 1570</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.y, v.z);</div>
<div class="line"><a name="l01571"></a><span class="lineno"> 1571</span>&#160;        }</div>
<div class="line"><a name="l01572"></a><span class="lineno"> 1572</span>&#160;</div>
<div class="line"><a name="l01573"></a><span class="lineno"> 1573</span>&#160;        <span class="comment">// yyyw</span></div>
<div class="line"><a name="l01574"></a><span class="lineno"> 1574</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01575"></a><span class="lineno"> 1575</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01576"></a><span class="lineno"> 1576</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.y, v.w);</div>
<div class="line"><a name="l01577"></a><span class="lineno"> 1577</span>&#160;        }</div>
<div class="line"><a name="l01578"></a><span class="lineno"> 1578</span>&#160;</div>
<div class="line"><a name="l01579"></a><span class="lineno"> 1579</span>&#160;        <span class="comment">// yyzx</span></div>
<div class="line"><a name="l01580"></a><span class="lineno"> 1580</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01581"></a><span class="lineno"> 1581</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01582"></a><span class="lineno"> 1582</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.z, v.x);</div>
<div class="line"><a name="l01583"></a><span class="lineno"> 1583</span>&#160;        }</div>
<div class="line"><a name="l01584"></a><span class="lineno"> 1584</span>&#160;</div>
<div class="line"><a name="l01585"></a><span class="lineno"> 1585</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01586"></a><span class="lineno"> 1586</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01587"></a><span class="lineno"> 1587</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.z, v.x);</div>
<div class="line"><a name="l01588"></a><span class="lineno"> 1588</span>&#160;        }</div>
<div class="line"><a name="l01589"></a><span class="lineno"> 1589</span>&#160;</div>
<div class="line"><a name="l01590"></a><span class="lineno"> 1590</span>&#160;        <span class="comment">// yyzy</span></div>
<div class="line"><a name="l01591"></a><span class="lineno"> 1591</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01592"></a><span class="lineno"> 1592</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01593"></a><span class="lineno"> 1593</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.z, v.y);</div>
<div class="line"><a name="l01594"></a><span class="lineno"> 1594</span>&#160;        }</div>
<div class="line"><a name="l01595"></a><span class="lineno"> 1595</span>&#160;</div>
<div class="line"><a name="l01596"></a><span class="lineno"> 1596</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01597"></a><span class="lineno"> 1597</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01598"></a><span class="lineno"> 1598</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.z, v.y);</div>
<div class="line"><a name="l01599"></a><span class="lineno"> 1599</span>&#160;        }</div>
<div class="line"><a name="l01600"></a><span class="lineno"> 1600</span>&#160;</div>
<div class="line"><a name="l01601"></a><span class="lineno"> 1601</span>&#160;        <span class="comment">// yyzz</span></div>
<div class="line"><a name="l01602"></a><span class="lineno"> 1602</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01603"></a><span class="lineno"> 1603</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01604"></a><span class="lineno"> 1604</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.z, v.z);</div>
<div class="line"><a name="l01605"></a><span class="lineno"> 1605</span>&#160;        }</div>
<div class="line"><a name="l01606"></a><span class="lineno"> 1606</span>&#160;</div>
<div class="line"><a name="l01607"></a><span class="lineno"> 1607</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01608"></a><span class="lineno"> 1608</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01609"></a><span class="lineno"> 1609</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.z, v.z);</div>
<div class="line"><a name="l01610"></a><span class="lineno"> 1610</span>&#160;        }</div>
<div class="line"><a name="l01611"></a><span class="lineno"> 1611</span>&#160;</div>
<div class="line"><a name="l01612"></a><span class="lineno"> 1612</span>&#160;        <span class="comment">// yyzw</span></div>
<div class="line"><a name="l01613"></a><span class="lineno"> 1613</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01614"></a><span class="lineno"> 1614</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01615"></a><span class="lineno"> 1615</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.z, v.w);</div>
<div class="line"><a name="l01616"></a><span class="lineno"> 1616</span>&#160;        }</div>
<div class="line"><a name="l01617"></a><span class="lineno"> 1617</span>&#160;</div>
<div class="line"><a name="l01618"></a><span class="lineno"> 1618</span>&#160;        <span class="comment">// yywx</span></div>
<div class="line"><a name="l01619"></a><span class="lineno"> 1619</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01620"></a><span class="lineno"> 1620</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yywx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01621"></a><span class="lineno"> 1621</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.w, v.x);</div>
<div class="line"><a name="l01622"></a><span class="lineno"> 1622</span>&#160;        }</div>
<div class="line"><a name="l01623"></a><span class="lineno"> 1623</span>&#160;</div>
<div class="line"><a name="l01624"></a><span class="lineno"> 1624</span>&#160;        <span class="comment">// yywy</span></div>
<div class="line"><a name="l01625"></a><span class="lineno"> 1625</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01626"></a><span class="lineno"> 1626</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yywy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01627"></a><span class="lineno"> 1627</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.w, v.y);</div>
<div class="line"><a name="l01628"></a><span class="lineno"> 1628</span>&#160;        }</div>
<div class="line"><a name="l01629"></a><span class="lineno"> 1629</span>&#160;</div>
<div class="line"><a name="l01630"></a><span class="lineno"> 1630</span>&#160;        <span class="comment">// yywz</span></div>
<div class="line"><a name="l01631"></a><span class="lineno"> 1631</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01632"></a><span class="lineno"> 1632</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yywz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01633"></a><span class="lineno"> 1633</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.w, v.z);</div>
<div class="line"><a name="l01634"></a><span class="lineno"> 1634</span>&#160;        }</div>
<div class="line"><a name="l01635"></a><span class="lineno"> 1635</span>&#160;</div>
<div class="line"><a name="l01636"></a><span class="lineno"> 1636</span>&#160;        <span class="comment">// yyww</span></div>
<div class="line"><a name="l01637"></a><span class="lineno"> 1637</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01638"></a><span class="lineno"> 1638</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yyww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01639"></a><span class="lineno"> 1639</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.y, v.w, v.w);</div>
<div class="line"><a name="l01640"></a><span class="lineno"> 1640</span>&#160;        }</div>
<div class="line"><a name="l01641"></a><span class="lineno"> 1641</span>&#160;</div>
<div class="line"><a name="l01642"></a><span class="lineno"> 1642</span>&#160;        <span class="comment">// yzxx</span></div>
<div class="line"><a name="l01643"></a><span class="lineno"> 1643</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01644"></a><span class="lineno"> 1644</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01645"></a><span class="lineno"> 1645</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.x, v.x);</div>
<div class="line"><a name="l01646"></a><span class="lineno"> 1646</span>&#160;        }</div>
<div class="line"><a name="l01647"></a><span class="lineno"> 1647</span>&#160;</div>
<div class="line"><a name="l01648"></a><span class="lineno"> 1648</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01649"></a><span class="lineno"> 1649</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01650"></a><span class="lineno"> 1650</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.x, v.x);</div>
<div class="line"><a name="l01651"></a><span class="lineno"> 1651</span>&#160;        }</div>
<div class="line"><a name="l01652"></a><span class="lineno"> 1652</span>&#160;</div>
<div class="line"><a name="l01653"></a><span class="lineno"> 1653</span>&#160;        <span class="comment">// yzxy</span></div>
<div class="line"><a name="l01654"></a><span class="lineno"> 1654</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01655"></a><span class="lineno"> 1655</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01656"></a><span class="lineno"> 1656</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.x, v.y);</div>
<div class="line"><a name="l01657"></a><span class="lineno"> 1657</span>&#160;        }</div>
<div class="line"><a name="l01658"></a><span class="lineno"> 1658</span>&#160;</div>
<div class="line"><a name="l01659"></a><span class="lineno"> 1659</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01660"></a><span class="lineno"> 1660</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01661"></a><span class="lineno"> 1661</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.x, v.y);</div>
<div class="line"><a name="l01662"></a><span class="lineno"> 1662</span>&#160;        }</div>
<div class="line"><a name="l01663"></a><span class="lineno"> 1663</span>&#160;</div>
<div class="line"><a name="l01664"></a><span class="lineno"> 1664</span>&#160;        <span class="comment">// yzxz</span></div>
<div class="line"><a name="l01665"></a><span class="lineno"> 1665</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01666"></a><span class="lineno"> 1666</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01667"></a><span class="lineno"> 1667</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.x, v.z);</div>
<div class="line"><a name="l01668"></a><span class="lineno"> 1668</span>&#160;        }</div>
<div class="line"><a name="l01669"></a><span class="lineno"> 1669</span>&#160;</div>
<div class="line"><a name="l01670"></a><span class="lineno"> 1670</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01671"></a><span class="lineno"> 1671</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01672"></a><span class="lineno"> 1672</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.x, v.z);</div>
<div class="line"><a name="l01673"></a><span class="lineno"> 1673</span>&#160;        }</div>
<div class="line"><a name="l01674"></a><span class="lineno"> 1674</span>&#160;</div>
<div class="line"><a name="l01675"></a><span class="lineno"> 1675</span>&#160;        <span class="comment">// yzxw</span></div>
<div class="line"><a name="l01676"></a><span class="lineno"> 1676</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01677"></a><span class="lineno"> 1677</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01678"></a><span class="lineno"> 1678</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.x, v.w);</div>
<div class="line"><a name="l01679"></a><span class="lineno"> 1679</span>&#160;        }</div>
<div class="line"><a name="l01680"></a><span class="lineno"> 1680</span>&#160;</div>
<div class="line"><a name="l01681"></a><span class="lineno"> 1681</span>&#160;        <span class="comment">// yzyx</span></div>
<div class="line"><a name="l01682"></a><span class="lineno"> 1682</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01683"></a><span class="lineno"> 1683</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01684"></a><span class="lineno"> 1684</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.y, v.x);</div>
<div class="line"><a name="l01685"></a><span class="lineno"> 1685</span>&#160;        }</div>
<div class="line"><a name="l01686"></a><span class="lineno"> 1686</span>&#160;</div>
<div class="line"><a name="l01687"></a><span class="lineno"> 1687</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01688"></a><span class="lineno"> 1688</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01689"></a><span class="lineno"> 1689</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.y, v.x);</div>
<div class="line"><a name="l01690"></a><span class="lineno"> 1690</span>&#160;        }</div>
<div class="line"><a name="l01691"></a><span class="lineno"> 1691</span>&#160;</div>
<div class="line"><a name="l01692"></a><span class="lineno"> 1692</span>&#160;        <span class="comment">// yzyy</span></div>
<div class="line"><a name="l01693"></a><span class="lineno"> 1693</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01694"></a><span class="lineno"> 1694</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01695"></a><span class="lineno"> 1695</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.y, v.y);</div>
<div class="line"><a name="l01696"></a><span class="lineno"> 1696</span>&#160;        }</div>
<div class="line"><a name="l01697"></a><span class="lineno"> 1697</span>&#160;</div>
<div class="line"><a name="l01698"></a><span class="lineno"> 1698</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01699"></a><span class="lineno"> 1699</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01700"></a><span class="lineno"> 1700</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.y, v.y);</div>
<div class="line"><a name="l01701"></a><span class="lineno"> 1701</span>&#160;        }</div>
<div class="line"><a name="l01702"></a><span class="lineno"> 1702</span>&#160;</div>
<div class="line"><a name="l01703"></a><span class="lineno"> 1703</span>&#160;        <span class="comment">// yzyz</span></div>
<div class="line"><a name="l01704"></a><span class="lineno"> 1704</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01705"></a><span class="lineno"> 1705</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01706"></a><span class="lineno"> 1706</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.y, v.z);</div>
<div class="line"><a name="l01707"></a><span class="lineno"> 1707</span>&#160;        }</div>
<div class="line"><a name="l01708"></a><span class="lineno"> 1708</span>&#160;</div>
<div class="line"><a name="l01709"></a><span class="lineno"> 1709</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01710"></a><span class="lineno"> 1710</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01711"></a><span class="lineno"> 1711</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.y, v.z);</div>
<div class="line"><a name="l01712"></a><span class="lineno"> 1712</span>&#160;        }</div>
<div class="line"><a name="l01713"></a><span class="lineno"> 1713</span>&#160;</div>
<div class="line"><a name="l01714"></a><span class="lineno"> 1714</span>&#160;        <span class="comment">// yzyw</span></div>
<div class="line"><a name="l01715"></a><span class="lineno"> 1715</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01716"></a><span class="lineno"> 1716</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01717"></a><span class="lineno"> 1717</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.y, v.w);</div>
<div class="line"><a name="l01718"></a><span class="lineno"> 1718</span>&#160;        }</div>
<div class="line"><a name="l01719"></a><span class="lineno"> 1719</span>&#160;</div>
<div class="line"><a name="l01720"></a><span class="lineno"> 1720</span>&#160;        <span class="comment">// yzzx</span></div>
<div class="line"><a name="l01721"></a><span class="lineno"> 1721</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01722"></a><span class="lineno"> 1722</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01723"></a><span class="lineno"> 1723</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.z, v.x);</div>
<div class="line"><a name="l01724"></a><span class="lineno"> 1724</span>&#160;        }</div>
<div class="line"><a name="l01725"></a><span class="lineno"> 1725</span>&#160;</div>
<div class="line"><a name="l01726"></a><span class="lineno"> 1726</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01727"></a><span class="lineno"> 1727</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01728"></a><span class="lineno"> 1728</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.z, v.x);</div>
<div class="line"><a name="l01729"></a><span class="lineno"> 1729</span>&#160;        }</div>
<div class="line"><a name="l01730"></a><span class="lineno"> 1730</span>&#160;</div>
<div class="line"><a name="l01731"></a><span class="lineno"> 1731</span>&#160;        <span class="comment">// yzzy</span></div>
<div class="line"><a name="l01732"></a><span class="lineno"> 1732</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01733"></a><span class="lineno"> 1733</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01734"></a><span class="lineno"> 1734</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.z, v.y);</div>
<div class="line"><a name="l01735"></a><span class="lineno"> 1735</span>&#160;        }</div>
<div class="line"><a name="l01736"></a><span class="lineno"> 1736</span>&#160;</div>
<div class="line"><a name="l01737"></a><span class="lineno"> 1737</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01738"></a><span class="lineno"> 1738</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01739"></a><span class="lineno"> 1739</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.z, v.y);</div>
<div class="line"><a name="l01740"></a><span class="lineno"> 1740</span>&#160;        }</div>
<div class="line"><a name="l01741"></a><span class="lineno"> 1741</span>&#160;</div>
<div class="line"><a name="l01742"></a><span class="lineno"> 1742</span>&#160;        <span class="comment">// yzzz</span></div>
<div class="line"><a name="l01743"></a><span class="lineno"> 1743</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01744"></a><span class="lineno"> 1744</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01745"></a><span class="lineno"> 1745</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.z, v.z);</div>
<div class="line"><a name="l01746"></a><span class="lineno"> 1746</span>&#160;        }</div>
<div class="line"><a name="l01747"></a><span class="lineno"> 1747</span>&#160;</div>
<div class="line"><a name="l01748"></a><span class="lineno"> 1748</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01749"></a><span class="lineno"> 1749</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01750"></a><span class="lineno"> 1750</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.z, v.z);</div>
<div class="line"><a name="l01751"></a><span class="lineno"> 1751</span>&#160;        }</div>
<div class="line"><a name="l01752"></a><span class="lineno"> 1752</span>&#160;</div>
<div class="line"><a name="l01753"></a><span class="lineno"> 1753</span>&#160;        <span class="comment">// yzzw</span></div>
<div class="line"><a name="l01754"></a><span class="lineno"> 1754</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01755"></a><span class="lineno"> 1755</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01756"></a><span class="lineno"> 1756</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.z, v.w);</div>
<div class="line"><a name="l01757"></a><span class="lineno"> 1757</span>&#160;        }</div>
<div class="line"><a name="l01758"></a><span class="lineno"> 1758</span>&#160;</div>
<div class="line"><a name="l01759"></a><span class="lineno"> 1759</span>&#160;        <span class="comment">// yzwx</span></div>
<div class="line"><a name="l01760"></a><span class="lineno"> 1760</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01761"></a><span class="lineno"> 1761</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01762"></a><span class="lineno"> 1762</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.w, v.x);</div>
<div class="line"><a name="l01763"></a><span class="lineno"> 1763</span>&#160;        }</div>
<div class="line"><a name="l01764"></a><span class="lineno"> 1764</span>&#160;</div>
<div class="line"><a name="l01765"></a><span class="lineno"> 1765</span>&#160;        <span class="comment">// yzwy</span></div>
<div class="line"><a name="l01766"></a><span class="lineno"> 1766</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01767"></a><span class="lineno"> 1767</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01768"></a><span class="lineno"> 1768</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.w, v.y);</div>
<div class="line"><a name="l01769"></a><span class="lineno"> 1769</span>&#160;        }</div>
<div class="line"><a name="l01770"></a><span class="lineno"> 1770</span>&#160;</div>
<div class="line"><a name="l01771"></a><span class="lineno"> 1771</span>&#160;        <span class="comment">// yzwz</span></div>
<div class="line"><a name="l01772"></a><span class="lineno"> 1772</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01773"></a><span class="lineno"> 1773</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01774"></a><span class="lineno"> 1774</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.w, v.z);</div>
<div class="line"><a name="l01775"></a><span class="lineno"> 1775</span>&#160;        }</div>
<div class="line"><a name="l01776"></a><span class="lineno"> 1776</span>&#160;</div>
<div class="line"><a name="l01777"></a><span class="lineno"> 1777</span>&#160;        <span class="comment">// yzww</span></div>
<div class="line"><a name="l01778"></a><span class="lineno"> 1778</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01779"></a><span class="lineno"> 1779</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; yzww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01780"></a><span class="lineno"> 1780</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.z, v.w, v.w);</div>
<div class="line"><a name="l01781"></a><span class="lineno"> 1781</span>&#160;        }</div>
<div class="line"><a name="l01782"></a><span class="lineno"> 1782</span>&#160;</div>
<div class="line"><a name="l01783"></a><span class="lineno"> 1783</span>&#160;        <span class="comment">// ywxx</span></div>
<div class="line"><a name="l01784"></a><span class="lineno"> 1784</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01785"></a><span class="lineno"> 1785</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01786"></a><span class="lineno"> 1786</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.x, v.x);</div>
<div class="line"><a name="l01787"></a><span class="lineno"> 1787</span>&#160;        }</div>
<div class="line"><a name="l01788"></a><span class="lineno"> 1788</span>&#160;</div>
<div class="line"><a name="l01789"></a><span class="lineno"> 1789</span>&#160;        <span class="comment">// ywxy</span></div>
<div class="line"><a name="l01790"></a><span class="lineno"> 1790</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01791"></a><span class="lineno"> 1791</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01792"></a><span class="lineno"> 1792</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.x, v.y);</div>
<div class="line"><a name="l01793"></a><span class="lineno"> 1793</span>&#160;        }</div>
<div class="line"><a name="l01794"></a><span class="lineno"> 1794</span>&#160;</div>
<div class="line"><a name="l01795"></a><span class="lineno"> 1795</span>&#160;        <span class="comment">// ywxz</span></div>
<div class="line"><a name="l01796"></a><span class="lineno"> 1796</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01797"></a><span class="lineno"> 1797</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01798"></a><span class="lineno"> 1798</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.x, v.z);</div>
<div class="line"><a name="l01799"></a><span class="lineno"> 1799</span>&#160;        }</div>
<div class="line"><a name="l01800"></a><span class="lineno"> 1800</span>&#160;</div>
<div class="line"><a name="l01801"></a><span class="lineno"> 1801</span>&#160;        <span class="comment">// ywxw</span></div>
<div class="line"><a name="l01802"></a><span class="lineno"> 1802</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01803"></a><span class="lineno"> 1803</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01804"></a><span class="lineno"> 1804</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.x, v.w);</div>
<div class="line"><a name="l01805"></a><span class="lineno"> 1805</span>&#160;        }</div>
<div class="line"><a name="l01806"></a><span class="lineno"> 1806</span>&#160;</div>
<div class="line"><a name="l01807"></a><span class="lineno"> 1807</span>&#160;        <span class="comment">// ywyx</span></div>
<div class="line"><a name="l01808"></a><span class="lineno"> 1808</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01809"></a><span class="lineno"> 1809</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01810"></a><span class="lineno"> 1810</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.y, v.x);</div>
<div class="line"><a name="l01811"></a><span class="lineno"> 1811</span>&#160;        }</div>
<div class="line"><a name="l01812"></a><span class="lineno"> 1812</span>&#160;</div>
<div class="line"><a name="l01813"></a><span class="lineno"> 1813</span>&#160;        <span class="comment">// ywyy</span></div>
<div class="line"><a name="l01814"></a><span class="lineno"> 1814</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01815"></a><span class="lineno"> 1815</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01816"></a><span class="lineno"> 1816</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.y, v.y);</div>
<div class="line"><a name="l01817"></a><span class="lineno"> 1817</span>&#160;        }</div>
<div class="line"><a name="l01818"></a><span class="lineno"> 1818</span>&#160;</div>
<div class="line"><a name="l01819"></a><span class="lineno"> 1819</span>&#160;        <span class="comment">// ywyz</span></div>
<div class="line"><a name="l01820"></a><span class="lineno"> 1820</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01821"></a><span class="lineno"> 1821</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01822"></a><span class="lineno"> 1822</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.y, v.z);</div>
<div class="line"><a name="l01823"></a><span class="lineno"> 1823</span>&#160;        }</div>
<div class="line"><a name="l01824"></a><span class="lineno"> 1824</span>&#160;</div>
<div class="line"><a name="l01825"></a><span class="lineno"> 1825</span>&#160;        <span class="comment">// ywyw</span></div>
<div class="line"><a name="l01826"></a><span class="lineno"> 1826</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01827"></a><span class="lineno"> 1827</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01828"></a><span class="lineno"> 1828</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.y, v.w);</div>
<div class="line"><a name="l01829"></a><span class="lineno"> 1829</span>&#160;        }</div>
<div class="line"><a name="l01830"></a><span class="lineno"> 1830</span>&#160;</div>
<div class="line"><a name="l01831"></a><span class="lineno"> 1831</span>&#160;        <span class="comment">// ywzx</span></div>
<div class="line"><a name="l01832"></a><span class="lineno"> 1832</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01833"></a><span class="lineno"> 1833</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01834"></a><span class="lineno"> 1834</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.z, v.x);</div>
<div class="line"><a name="l01835"></a><span class="lineno"> 1835</span>&#160;        }</div>
<div class="line"><a name="l01836"></a><span class="lineno"> 1836</span>&#160;</div>
<div class="line"><a name="l01837"></a><span class="lineno"> 1837</span>&#160;        <span class="comment">// ywzy</span></div>
<div class="line"><a name="l01838"></a><span class="lineno"> 1838</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01839"></a><span class="lineno"> 1839</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01840"></a><span class="lineno"> 1840</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.z, v.y);</div>
<div class="line"><a name="l01841"></a><span class="lineno"> 1841</span>&#160;        }</div>
<div class="line"><a name="l01842"></a><span class="lineno"> 1842</span>&#160;</div>
<div class="line"><a name="l01843"></a><span class="lineno"> 1843</span>&#160;        <span class="comment">// ywzz</span></div>
<div class="line"><a name="l01844"></a><span class="lineno"> 1844</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01845"></a><span class="lineno"> 1845</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01846"></a><span class="lineno"> 1846</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.z, v.z);</div>
<div class="line"><a name="l01847"></a><span class="lineno"> 1847</span>&#160;        }</div>
<div class="line"><a name="l01848"></a><span class="lineno"> 1848</span>&#160;</div>
<div class="line"><a name="l01849"></a><span class="lineno"> 1849</span>&#160;        <span class="comment">// ywzw</span></div>
<div class="line"><a name="l01850"></a><span class="lineno"> 1850</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01851"></a><span class="lineno"> 1851</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01852"></a><span class="lineno"> 1852</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.z, v.w);</div>
<div class="line"><a name="l01853"></a><span class="lineno"> 1853</span>&#160;        }</div>
<div class="line"><a name="l01854"></a><span class="lineno"> 1854</span>&#160;</div>
<div class="line"><a name="l01855"></a><span class="lineno"> 1855</span>&#160;        <span class="comment">// ywwx</span></div>
<div class="line"><a name="l01856"></a><span class="lineno"> 1856</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01857"></a><span class="lineno"> 1857</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01858"></a><span class="lineno"> 1858</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.w, v.x);</div>
<div class="line"><a name="l01859"></a><span class="lineno"> 1859</span>&#160;        }</div>
<div class="line"><a name="l01860"></a><span class="lineno"> 1860</span>&#160;</div>
<div class="line"><a name="l01861"></a><span class="lineno"> 1861</span>&#160;        <span class="comment">// ywwy</span></div>
<div class="line"><a name="l01862"></a><span class="lineno"> 1862</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01863"></a><span class="lineno"> 1863</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01864"></a><span class="lineno"> 1864</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.w, v.y);</div>
<div class="line"><a name="l01865"></a><span class="lineno"> 1865</span>&#160;        }</div>
<div class="line"><a name="l01866"></a><span class="lineno"> 1866</span>&#160;</div>
<div class="line"><a name="l01867"></a><span class="lineno"> 1867</span>&#160;        <span class="comment">// ywwz</span></div>
<div class="line"><a name="l01868"></a><span class="lineno"> 1868</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01869"></a><span class="lineno"> 1869</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01870"></a><span class="lineno"> 1870</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.w, v.z);</div>
<div class="line"><a name="l01871"></a><span class="lineno"> 1871</span>&#160;        }</div>
<div class="line"><a name="l01872"></a><span class="lineno"> 1872</span>&#160;</div>
<div class="line"><a name="l01873"></a><span class="lineno"> 1873</span>&#160;        <span class="comment">// ywww</span></div>
<div class="line"><a name="l01874"></a><span class="lineno"> 1874</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01875"></a><span class="lineno"> 1875</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; ywww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01876"></a><span class="lineno"> 1876</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.y, v.w, v.w, v.w);</div>
<div class="line"><a name="l01877"></a><span class="lineno"> 1877</span>&#160;        }</div>
<div class="line"><a name="l01878"></a><span class="lineno"> 1878</span>&#160;</div>
<div class="line"><a name="l01879"></a><span class="lineno"> 1879</span>&#160;        <span class="comment">// zxxx</span></div>
<div class="line"><a name="l01880"></a><span class="lineno"> 1880</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01881"></a><span class="lineno"> 1881</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01882"></a><span class="lineno"> 1882</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.x, v.x);</div>
<div class="line"><a name="l01883"></a><span class="lineno"> 1883</span>&#160;        }</div>
<div class="line"><a name="l01884"></a><span class="lineno"> 1884</span>&#160;</div>
<div class="line"><a name="l01885"></a><span class="lineno"> 1885</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01886"></a><span class="lineno"> 1886</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01887"></a><span class="lineno"> 1887</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.x, v.x);</div>
<div class="line"><a name="l01888"></a><span class="lineno"> 1888</span>&#160;        }</div>
<div class="line"><a name="l01889"></a><span class="lineno"> 1889</span>&#160;</div>
<div class="line"><a name="l01890"></a><span class="lineno"> 1890</span>&#160;        <span class="comment">// zxxy</span></div>
<div class="line"><a name="l01891"></a><span class="lineno"> 1891</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01892"></a><span class="lineno"> 1892</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01893"></a><span class="lineno"> 1893</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.x, v.y);</div>
<div class="line"><a name="l01894"></a><span class="lineno"> 1894</span>&#160;        }</div>
<div class="line"><a name="l01895"></a><span class="lineno"> 1895</span>&#160;</div>
<div class="line"><a name="l01896"></a><span class="lineno"> 1896</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01897"></a><span class="lineno"> 1897</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01898"></a><span class="lineno"> 1898</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.x, v.y);</div>
<div class="line"><a name="l01899"></a><span class="lineno"> 1899</span>&#160;        }</div>
<div class="line"><a name="l01900"></a><span class="lineno"> 1900</span>&#160;</div>
<div class="line"><a name="l01901"></a><span class="lineno"> 1901</span>&#160;        <span class="comment">// zxxz</span></div>
<div class="line"><a name="l01902"></a><span class="lineno"> 1902</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01903"></a><span class="lineno"> 1903</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01904"></a><span class="lineno"> 1904</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.x, v.z);</div>
<div class="line"><a name="l01905"></a><span class="lineno"> 1905</span>&#160;        }</div>
<div class="line"><a name="l01906"></a><span class="lineno"> 1906</span>&#160;</div>
<div class="line"><a name="l01907"></a><span class="lineno"> 1907</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01908"></a><span class="lineno"> 1908</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01909"></a><span class="lineno"> 1909</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.x, v.z);</div>
<div class="line"><a name="l01910"></a><span class="lineno"> 1910</span>&#160;        }</div>
<div class="line"><a name="l01911"></a><span class="lineno"> 1911</span>&#160;</div>
<div class="line"><a name="l01912"></a><span class="lineno"> 1912</span>&#160;        <span class="comment">// zxxw</span></div>
<div class="line"><a name="l01913"></a><span class="lineno"> 1913</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01914"></a><span class="lineno"> 1914</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01915"></a><span class="lineno"> 1915</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.x, v.w);</div>
<div class="line"><a name="l01916"></a><span class="lineno"> 1916</span>&#160;        }</div>
<div class="line"><a name="l01917"></a><span class="lineno"> 1917</span>&#160;</div>
<div class="line"><a name="l01918"></a><span class="lineno"> 1918</span>&#160;        <span class="comment">// zxyx</span></div>
<div class="line"><a name="l01919"></a><span class="lineno"> 1919</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01920"></a><span class="lineno"> 1920</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01921"></a><span class="lineno"> 1921</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.y, v.x);</div>
<div class="line"><a name="l01922"></a><span class="lineno"> 1922</span>&#160;        }</div>
<div class="line"><a name="l01923"></a><span class="lineno"> 1923</span>&#160;</div>
<div class="line"><a name="l01924"></a><span class="lineno"> 1924</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01925"></a><span class="lineno"> 1925</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01926"></a><span class="lineno"> 1926</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.y, v.x);</div>
<div class="line"><a name="l01927"></a><span class="lineno"> 1927</span>&#160;        }</div>
<div class="line"><a name="l01928"></a><span class="lineno"> 1928</span>&#160;</div>
<div class="line"><a name="l01929"></a><span class="lineno"> 1929</span>&#160;        <span class="comment">// zxyy</span></div>
<div class="line"><a name="l01930"></a><span class="lineno"> 1930</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01931"></a><span class="lineno"> 1931</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01932"></a><span class="lineno"> 1932</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.y, v.y);</div>
<div class="line"><a name="l01933"></a><span class="lineno"> 1933</span>&#160;        }</div>
<div class="line"><a name="l01934"></a><span class="lineno"> 1934</span>&#160;</div>
<div class="line"><a name="l01935"></a><span class="lineno"> 1935</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01936"></a><span class="lineno"> 1936</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01937"></a><span class="lineno"> 1937</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.y, v.y);</div>
<div class="line"><a name="l01938"></a><span class="lineno"> 1938</span>&#160;        }</div>
<div class="line"><a name="l01939"></a><span class="lineno"> 1939</span>&#160;</div>
<div class="line"><a name="l01940"></a><span class="lineno"> 1940</span>&#160;        <span class="comment">// zxyz</span></div>
<div class="line"><a name="l01941"></a><span class="lineno"> 1941</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01942"></a><span class="lineno"> 1942</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01943"></a><span class="lineno"> 1943</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.y, v.z);</div>
<div class="line"><a name="l01944"></a><span class="lineno"> 1944</span>&#160;        }</div>
<div class="line"><a name="l01945"></a><span class="lineno"> 1945</span>&#160;</div>
<div class="line"><a name="l01946"></a><span class="lineno"> 1946</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01947"></a><span class="lineno"> 1947</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01948"></a><span class="lineno"> 1948</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.y, v.z);</div>
<div class="line"><a name="l01949"></a><span class="lineno"> 1949</span>&#160;        }</div>
<div class="line"><a name="l01950"></a><span class="lineno"> 1950</span>&#160;</div>
<div class="line"><a name="l01951"></a><span class="lineno"> 1951</span>&#160;        <span class="comment">// zxyw</span></div>
<div class="line"><a name="l01952"></a><span class="lineno"> 1952</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01953"></a><span class="lineno"> 1953</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01954"></a><span class="lineno"> 1954</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.y, v.w);</div>
<div class="line"><a name="l01955"></a><span class="lineno"> 1955</span>&#160;        }</div>
<div class="line"><a name="l01956"></a><span class="lineno"> 1956</span>&#160;</div>
<div class="line"><a name="l01957"></a><span class="lineno"> 1957</span>&#160;        <span class="comment">// zxzx</span></div>
<div class="line"><a name="l01958"></a><span class="lineno"> 1958</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01959"></a><span class="lineno"> 1959</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01960"></a><span class="lineno"> 1960</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.z, v.x);</div>
<div class="line"><a name="l01961"></a><span class="lineno"> 1961</span>&#160;        }</div>
<div class="line"><a name="l01962"></a><span class="lineno"> 1962</span>&#160;</div>
<div class="line"><a name="l01963"></a><span class="lineno"> 1963</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01964"></a><span class="lineno"> 1964</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01965"></a><span class="lineno"> 1965</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.z, v.x);</div>
<div class="line"><a name="l01966"></a><span class="lineno"> 1966</span>&#160;        }</div>
<div class="line"><a name="l01967"></a><span class="lineno"> 1967</span>&#160;</div>
<div class="line"><a name="l01968"></a><span class="lineno"> 1968</span>&#160;        <span class="comment">// zxzy</span></div>
<div class="line"><a name="l01969"></a><span class="lineno"> 1969</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01970"></a><span class="lineno"> 1970</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01971"></a><span class="lineno"> 1971</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.z, v.y);</div>
<div class="line"><a name="l01972"></a><span class="lineno"> 1972</span>&#160;        }</div>
<div class="line"><a name="l01973"></a><span class="lineno"> 1973</span>&#160;</div>
<div class="line"><a name="l01974"></a><span class="lineno"> 1974</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01975"></a><span class="lineno"> 1975</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01976"></a><span class="lineno"> 1976</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.z, v.y);</div>
<div class="line"><a name="l01977"></a><span class="lineno"> 1977</span>&#160;        }</div>
<div class="line"><a name="l01978"></a><span class="lineno"> 1978</span>&#160;</div>
<div class="line"><a name="l01979"></a><span class="lineno"> 1979</span>&#160;        <span class="comment">// zxzz</span></div>
<div class="line"><a name="l01980"></a><span class="lineno"> 1980</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01981"></a><span class="lineno"> 1981</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01982"></a><span class="lineno"> 1982</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.z, v.z);</div>
<div class="line"><a name="l01983"></a><span class="lineno"> 1983</span>&#160;        }</div>
<div class="line"><a name="l01984"></a><span class="lineno"> 1984</span>&#160;</div>
<div class="line"><a name="l01985"></a><span class="lineno"> 1985</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01986"></a><span class="lineno"> 1986</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01987"></a><span class="lineno"> 1987</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.z, v.z);</div>
<div class="line"><a name="l01988"></a><span class="lineno"> 1988</span>&#160;        }</div>
<div class="line"><a name="l01989"></a><span class="lineno"> 1989</span>&#160;</div>
<div class="line"><a name="l01990"></a><span class="lineno"> 1990</span>&#160;        <span class="comment">// zxzw</span></div>
<div class="line"><a name="l01991"></a><span class="lineno"> 1991</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01992"></a><span class="lineno"> 1992</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01993"></a><span class="lineno"> 1993</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.z, v.w);</div>
<div class="line"><a name="l01994"></a><span class="lineno"> 1994</span>&#160;        }</div>
<div class="line"><a name="l01995"></a><span class="lineno"> 1995</span>&#160;</div>
<div class="line"><a name="l01996"></a><span class="lineno"> 1996</span>&#160;        <span class="comment">// zxwx</span></div>
<div class="line"><a name="l01997"></a><span class="lineno"> 1997</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l01998"></a><span class="lineno"> 1998</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l01999"></a><span class="lineno"> 1999</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.w, v.x);</div>
<div class="line"><a name="l02000"></a><span class="lineno"> 2000</span>&#160;        }</div>
<div class="line"><a name="l02001"></a><span class="lineno"> 2001</span>&#160;</div>
<div class="line"><a name="l02002"></a><span class="lineno"> 2002</span>&#160;        <span class="comment">// zxwy</span></div>
<div class="line"><a name="l02003"></a><span class="lineno"> 2003</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02004"></a><span class="lineno"> 2004</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02005"></a><span class="lineno"> 2005</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.w, v.y);</div>
<div class="line"><a name="l02006"></a><span class="lineno"> 2006</span>&#160;        }</div>
<div class="line"><a name="l02007"></a><span class="lineno"> 2007</span>&#160;</div>
<div class="line"><a name="l02008"></a><span class="lineno"> 2008</span>&#160;        <span class="comment">// zxwz</span></div>
<div class="line"><a name="l02009"></a><span class="lineno"> 2009</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02010"></a><span class="lineno"> 2010</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02011"></a><span class="lineno"> 2011</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.w, v.z);</div>
<div class="line"><a name="l02012"></a><span class="lineno"> 2012</span>&#160;        }</div>
<div class="line"><a name="l02013"></a><span class="lineno"> 2013</span>&#160;</div>
<div class="line"><a name="l02014"></a><span class="lineno"> 2014</span>&#160;        <span class="comment">// zxww</span></div>
<div class="line"><a name="l02015"></a><span class="lineno"> 2015</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02016"></a><span class="lineno"> 2016</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zxww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02017"></a><span class="lineno"> 2017</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.x, v.w, v.w);</div>
<div class="line"><a name="l02018"></a><span class="lineno"> 2018</span>&#160;        }</div>
<div class="line"><a name="l02019"></a><span class="lineno"> 2019</span>&#160;</div>
<div class="line"><a name="l02020"></a><span class="lineno"> 2020</span>&#160;        <span class="comment">// zyxx</span></div>
<div class="line"><a name="l02021"></a><span class="lineno"> 2021</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02022"></a><span class="lineno"> 2022</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02023"></a><span class="lineno"> 2023</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.x, v.x);</div>
<div class="line"><a name="l02024"></a><span class="lineno"> 2024</span>&#160;        }</div>
<div class="line"><a name="l02025"></a><span class="lineno"> 2025</span>&#160;</div>
<div class="line"><a name="l02026"></a><span class="lineno"> 2026</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02027"></a><span class="lineno"> 2027</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02028"></a><span class="lineno"> 2028</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.x, v.x);</div>
<div class="line"><a name="l02029"></a><span class="lineno"> 2029</span>&#160;        }</div>
<div class="line"><a name="l02030"></a><span class="lineno"> 2030</span>&#160;</div>
<div class="line"><a name="l02031"></a><span class="lineno"> 2031</span>&#160;        <span class="comment">// zyxy</span></div>
<div class="line"><a name="l02032"></a><span class="lineno"> 2032</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02033"></a><span class="lineno"> 2033</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02034"></a><span class="lineno"> 2034</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.x, v.y);</div>
<div class="line"><a name="l02035"></a><span class="lineno"> 2035</span>&#160;        }</div>
<div class="line"><a name="l02036"></a><span class="lineno"> 2036</span>&#160;</div>
<div class="line"><a name="l02037"></a><span class="lineno"> 2037</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02038"></a><span class="lineno"> 2038</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02039"></a><span class="lineno"> 2039</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.x, v.y);</div>
<div class="line"><a name="l02040"></a><span class="lineno"> 2040</span>&#160;        }</div>
<div class="line"><a name="l02041"></a><span class="lineno"> 2041</span>&#160;</div>
<div class="line"><a name="l02042"></a><span class="lineno"> 2042</span>&#160;        <span class="comment">// zyxz</span></div>
<div class="line"><a name="l02043"></a><span class="lineno"> 2043</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02044"></a><span class="lineno"> 2044</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02045"></a><span class="lineno"> 2045</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.x, v.z);</div>
<div class="line"><a name="l02046"></a><span class="lineno"> 2046</span>&#160;        }</div>
<div class="line"><a name="l02047"></a><span class="lineno"> 2047</span>&#160;</div>
<div class="line"><a name="l02048"></a><span class="lineno"> 2048</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02049"></a><span class="lineno"> 2049</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02050"></a><span class="lineno"> 2050</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.x, v.z);</div>
<div class="line"><a name="l02051"></a><span class="lineno"> 2051</span>&#160;        }</div>
<div class="line"><a name="l02052"></a><span class="lineno"> 2052</span>&#160;</div>
<div class="line"><a name="l02053"></a><span class="lineno"> 2053</span>&#160;        <span class="comment">// zyxw</span></div>
<div class="line"><a name="l02054"></a><span class="lineno"> 2054</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02055"></a><span class="lineno"> 2055</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02056"></a><span class="lineno"> 2056</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.x, v.w);</div>
<div class="line"><a name="l02057"></a><span class="lineno"> 2057</span>&#160;        }</div>
<div class="line"><a name="l02058"></a><span class="lineno"> 2058</span>&#160;</div>
<div class="line"><a name="l02059"></a><span class="lineno"> 2059</span>&#160;        <span class="comment">// zyyx</span></div>
<div class="line"><a name="l02060"></a><span class="lineno"> 2060</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02061"></a><span class="lineno"> 2061</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02062"></a><span class="lineno"> 2062</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.y, v.x);</div>
<div class="line"><a name="l02063"></a><span class="lineno"> 2063</span>&#160;        }</div>
<div class="line"><a name="l02064"></a><span class="lineno"> 2064</span>&#160;</div>
<div class="line"><a name="l02065"></a><span class="lineno"> 2065</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02066"></a><span class="lineno"> 2066</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02067"></a><span class="lineno"> 2067</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.y, v.x);</div>
<div class="line"><a name="l02068"></a><span class="lineno"> 2068</span>&#160;        }</div>
<div class="line"><a name="l02069"></a><span class="lineno"> 2069</span>&#160;</div>
<div class="line"><a name="l02070"></a><span class="lineno"> 2070</span>&#160;        <span class="comment">// zyyy</span></div>
<div class="line"><a name="l02071"></a><span class="lineno"> 2071</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02072"></a><span class="lineno"> 2072</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02073"></a><span class="lineno"> 2073</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.y, v.y);</div>
<div class="line"><a name="l02074"></a><span class="lineno"> 2074</span>&#160;        }</div>
<div class="line"><a name="l02075"></a><span class="lineno"> 2075</span>&#160;</div>
<div class="line"><a name="l02076"></a><span class="lineno"> 2076</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02077"></a><span class="lineno"> 2077</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02078"></a><span class="lineno"> 2078</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.y, v.y);</div>
<div class="line"><a name="l02079"></a><span class="lineno"> 2079</span>&#160;        }</div>
<div class="line"><a name="l02080"></a><span class="lineno"> 2080</span>&#160;</div>
<div class="line"><a name="l02081"></a><span class="lineno"> 2081</span>&#160;        <span class="comment">// zyyz</span></div>
<div class="line"><a name="l02082"></a><span class="lineno"> 2082</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02083"></a><span class="lineno"> 2083</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02084"></a><span class="lineno"> 2084</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.y, v.z);</div>
<div class="line"><a name="l02085"></a><span class="lineno"> 2085</span>&#160;        }</div>
<div class="line"><a name="l02086"></a><span class="lineno"> 2086</span>&#160;</div>
<div class="line"><a name="l02087"></a><span class="lineno"> 2087</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02088"></a><span class="lineno"> 2088</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02089"></a><span class="lineno"> 2089</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.y, v.z);</div>
<div class="line"><a name="l02090"></a><span class="lineno"> 2090</span>&#160;        }</div>
<div class="line"><a name="l02091"></a><span class="lineno"> 2091</span>&#160;</div>
<div class="line"><a name="l02092"></a><span class="lineno"> 2092</span>&#160;        <span class="comment">// zyyw</span></div>
<div class="line"><a name="l02093"></a><span class="lineno"> 2093</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02094"></a><span class="lineno"> 2094</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02095"></a><span class="lineno"> 2095</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.y, v.w);</div>
<div class="line"><a name="l02096"></a><span class="lineno"> 2096</span>&#160;        }</div>
<div class="line"><a name="l02097"></a><span class="lineno"> 2097</span>&#160;</div>
<div class="line"><a name="l02098"></a><span class="lineno"> 2098</span>&#160;        <span class="comment">// zyzx</span></div>
<div class="line"><a name="l02099"></a><span class="lineno"> 2099</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02100"></a><span class="lineno"> 2100</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02101"></a><span class="lineno"> 2101</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.z, v.x);</div>
<div class="line"><a name="l02102"></a><span class="lineno"> 2102</span>&#160;        }</div>
<div class="line"><a name="l02103"></a><span class="lineno"> 2103</span>&#160;</div>
<div class="line"><a name="l02104"></a><span class="lineno"> 2104</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02105"></a><span class="lineno"> 2105</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02106"></a><span class="lineno"> 2106</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.z, v.x);</div>
<div class="line"><a name="l02107"></a><span class="lineno"> 2107</span>&#160;        }</div>
<div class="line"><a name="l02108"></a><span class="lineno"> 2108</span>&#160;</div>
<div class="line"><a name="l02109"></a><span class="lineno"> 2109</span>&#160;        <span class="comment">// zyzy</span></div>
<div class="line"><a name="l02110"></a><span class="lineno"> 2110</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02111"></a><span class="lineno"> 2111</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02112"></a><span class="lineno"> 2112</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.z, v.y);</div>
<div class="line"><a name="l02113"></a><span class="lineno"> 2113</span>&#160;        }</div>
<div class="line"><a name="l02114"></a><span class="lineno"> 2114</span>&#160;</div>
<div class="line"><a name="l02115"></a><span class="lineno"> 2115</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02116"></a><span class="lineno"> 2116</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02117"></a><span class="lineno"> 2117</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.z, v.y);</div>
<div class="line"><a name="l02118"></a><span class="lineno"> 2118</span>&#160;        }</div>
<div class="line"><a name="l02119"></a><span class="lineno"> 2119</span>&#160;</div>
<div class="line"><a name="l02120"></a><span class="lineno"> 2120</span>&#160;        <span class="comment">// zyzz</span></div>
<div class="line"><a name="l02121"></a><span class="lineno"> 2121</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02122"></a><span class="lineno"> 2122</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02123"></a><span class="lineno"> 2123</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.z, v.z);</div>
<div class="line"><a name="l02124"></a><span class="lineno"> 2124</span>&#160;        }</div>
<div class="line"><a name="l02125"></a><span class="lineno"> 2125</span>&#160;</div>
<div class="line"><a name="l02126"></a><span class="lineno"> 2126</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02127"></a><span class="lineno"> 2127</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02128"></a><span class="lineno"> 2128</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.z, v.z);</div>
<div class="line"><a name="l02129"></a><span class="lineno"> 2129</span>&#160;        }</div>
<div class="line"><a name="l02130"></a><span class="lineno"> 2130</span>&#160;</div>
<div class="line"><a name="l02131"></a><span class="lineno"> 2131</span>&#160;        <span class="comment">// zyzw</span></div>
<div class="line"><a name="l02132"></a><span class="lineno"> 2132</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02133"></a><span class="lineno"> 2133</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02134"></a><span class="lineno"> 2134</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.z, v.w);</div>
<div class="line"><a name="l02135"></a><span class="lineno"> 2135</span>&#160;        }</div>
<div class="line"><a name="l02136"></a><span class="lineno"> 2136</span>&#160;</div>
<div class="line"><a name="l02137"></a><span class="lineno"> 2137</span>&#160;        <span class="comment">// zywx</span></div>
<div class="line"><a name="l02138"></a><span class="lineno"> 2138</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02139"></a><span class="lineno"> 2139</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zywx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02140"></a><span class="lineno"> 2140</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.w, v.x);</div>
<div class="line"><a name="l02141"></a><span class="lineno"> 2141</span>&#160;        }</div>
<div class="line"><a name="l02142"></a><span class="lineno"> 2142</span>&#160;</div>
<div class="line"><a name="l02143"></a><span class="lineno"> 2143</span>&#160;        <span class="comment">// zywy</span></div>
<div class="line"><a name="l02144"></a><span class="lineno"> 2144</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02145"></a><span class="lineno"> 2145</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zywy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02146"></a><span class="lineno"> 2146</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.w, v.y);</div>
<div class="line"><a name="l02147"></a><span class="lineno"> 2147</span>&#160;        }</div>
<div class="line"><a name="l02148"></a><span class="lineno"> 2148</span>&#160;</div>
<div class="line"><a name="l02149"></a><span class="lineno"> 2149</span>&#160;        <span class="comment">// zywz</span></div>
<div class="line"><a name="l02150"></a><span class="lineno"> 2150</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02151"></a><span class="lineno"> 2151</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zywz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02152"></a><span class="lineno"> 2152</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.w, v.z);</div>
<div class="line"><a name="l02153"></a><span class="lineno"> 2153</span>&#160;        }</div>
<div class="line"><a name="l02154"></a><span class="lineno"> 2154</span>&#160;</div>
<div class="line"><a name="l02155"></a><span class="lineno"> 2155</span>&#160;        <span class="comment">// zyww</span></div>
<div class="line"><a name="l02156"></a><span class="lineno"> 2156</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02157"></a><span class="lineno"> 2157</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zyww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02158"></a><span class="lineno"> 2158</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.y, v.w, v.w);</div>
<div class="line"><a name="l02159"></a><span class="lineno"> 2159</span>&#160;        }</div>
<div class="line"><a name="l02160"></a><span class="lineno"> 2160</span>&#160;</div>
<div class="line"><a name="l02161"></a><span class="lineno"> 2161</span>&#160;        <span class="comment">// zzxx</span></div>
<div class="line"><a name="l02162"></a><span class="lineno"> 2162</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02163"></a><span class="lineno"> 2163</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzxx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02164"></a><span class="lineno"> 2164</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.x, v.x);</div>
<div class="line"><a name="l02165"></a><span class="lineno"> 2165</span>&#160;        }</div>
<div class="line"><a name="l02166"></a><span class="lineno"> 2166</span>&#160;</div>
<div class="line"><a name="l02167"></a><span class="lineno"> 2167</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02168"></a><span class="lineno"> 2168</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02169"></a><span class="lineno"> 2169</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.x, v.x);</div>
<div class="line"><a name="l02170"></a><span class="lineno"> 2170</span>&#160;        }</div>
<div class="line"><a name="l02171"></a><span class="lineno"> 2171</span>&#160;</div>
<div class="line"><a name="l02172"></a><span class="lineno"> 2172</span>&#160;        <span class="comment">// zzxy</span></div>
<div class="line"><a name="l02173"></a><span class="lineno"> 2173</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02174"></a><span class="lineno"> 2174</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzxy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02175"></a><span class="lineno"> 2175</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.x, v.y);</div>
<div class="line"><a name="l02176"></a><span class="lineno"> 2176</span>&#160;        }</div>
<div class="line"><a name="l02177"></a><span class="lineno"> 2177</span>&#160;</div>
<div class="line"><a name="l02178"></a><span class="lineno"> 2178</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02179"></a><span class="lineno"> 2179</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02180"></a><span class="lineno"> 2180</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.x, v.y);</div>
<div class="line"><a name="l02181"></a><span class="lineno"> 2181</span>&#160;        }</div>
<div class="line"><a name="l02182"></a><span class="lineno"> 2182</span>&#160;</div>
<div class="line"><a name="l02183"></a><span class="lineno"> 2183</span>&#160;        <span class="comment">// zzxz</span></div>
<div class="line"><a name="l02184"></a><span class="lineno"> 2184</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02185"></a><span class="lineno"> 2185</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzxz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02186"></a><span class="lineno"> 2186</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.x, v.z);</div>
<div class="line"><a name="l02187"></a><span class="lineno"> 2187</span>&#160;        }</div>
<div class="line"><a name="l02188"></a><span class="lineno"> 2188</span>&#160;</div>
<div class="line"><a name="l02189"></a><span class="lineno"> 2189</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02190"></a><span class="lineno"> 2190</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02191"></a><span class="lineno"> 2191</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.x, v.z);</div>
<div class="line"><a name="l02192"></a><span class="lineno"> 2192</span>&#160;        }</div>
<div class="line"><a name="l02193"></a><span class="lineno"> 2193</span>&#160;</div>
<div class="line"><a name="l02194"></a><span class="lineno"> 2194</span>&#160;        <span class="comment">// zzxw</span></div>
<div class="line"><a name="l02195"></a><span class="lineno"> 2195</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02196"></a><span class="lineno"> 2196</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02197"></a><span class="lineno"> 2197</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.x, v.w);</div>
<div class="line"><a name="l02198"></a><span class="lineno"> 2198</span>&#160;        }</div>
<div class="line"><a name="l02199"></a><span class="lineno"> 2199</span>&#160;</div>
<div class="line"><a name="l02200"></a><span class="lineno"> 2200</span>&#160;        <span class="comment">// zzyx</span></div>
<div class="line"><a name="l02201"></a><span class="lineno"> 2201</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02202"></a><span class="lineno"> 2202</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzyx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02203"></a><span class="lineno"> 2203</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.y, v.x);</div>
<div class="line"><a name="l02204"></a><span class="lineno"> 2204</span>&#160;        }</div>
<div class="line"><a name="l02205"></a><span class="lineno"> 2205</span>&#160;</div>
<div class="line"><a name="l02206"></a><span class="lineno"> 2206</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02207"></a><span class="lineno"> 2207</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02208"></a><span class="lineno"> 2208</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.y, v.x);</div>
<div class="line"><a name="l02209"></a><span class="lineno"> 2209</span>&#160;        }</div>
<div class="line"><a name="l02210"></a><span class="lineno"> 2210</span>&#160;</div>
<div class="line"><a name="l02211"></a><span class="lineno"> 2211</span>&#160;        <span class="comment">// zzyy</span></div>
<div class="line"><a name="l02212"></a><span class="lineno"> 2212</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02213"></a><span class="lineno"> 2213</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzyy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02214"></a><span class="lineno"> 2214</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.y, v.y);</div>
<div class="line"><a name="l02215"></a><span class="lineno"> 2215</span>&#160;        }</div>
<div class="line"><a name="l02216"></a><span class="lineno"> 2216</span>&#160;</div>
<div class="line"><a name="l02217"></a><span class="lineno"> 2217</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02218"></a><span class="lineno"> 2218</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02219"></a><span class="lineno"> 2219</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.y, v.y);</div>
<div class="line"><a name="l02220"></a><span class="lineno"> 2220</span>&#160;        }</div>
<div class="line"><a name="l02221"></a><span class="lineno"> 2221</span>&#160;</div>
<div class="line"><a name="l02222"></a><span class="lineno"> 2222</span>&#160;        <span class="comment">// zzyz</span></div>
<div class="line"><a name="l02223"></a><span class="lineno"> 2223</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02224"></a><span class="lineno"> 2224</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzyz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02225"></a><span class="lineno"> 2225</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.y, v.z);</div>
<div class="line"><a name="l02226"></a><span class="lineno"> 2226</span>&#160;        }</div>
<div class="line"><a name="l02227"></a><span class="lineno"> 2227</span>&#160;</div>
<div class="line"><a name="l02228"></a><span class="lineno"> 2228</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02229"></a><span class="lineno"> 2229</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02230"></a><span class="lineno"> 2230</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.y, v.z);</div>
<div class="line"><a name="l02231"></a><span class="lineno"> 2231</span>&#160;        }</div>
<div class="line"><a name="l02232"></a><span class="lineno"> 2232</span>&#160;</div>
<div class="line"><a name="l02233"></a><span class="lineno"> 2233</span>&#160;        <span class="comment">// zzyw</span></div>
<div class="line"><a name="l02234"></a><span class="lineno"> 2234</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02235"></a><span class="lineno"> 2235</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02236"></a><span class="lineno"> 2236</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.y, v.w);</div>
<div class="line"><a name="l02237"></a><span class="lineno"> 2237</span>&#160;        }</div>
<div class="line"><a name="l02238"></a><span class="lineno"> 2238</span>&#160;</div>
<div class="line"><a name="l02239"></a><span class="lineno"> 2239</span>&#160;        <span class="comment">// zzzx</span></div>
<div class="line"><a name="l02240"></a><span class="lineno"> 2240</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02241"></a><span class="lineno"> 2241</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzzx(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02242"></a><span class="lineno"> 2242</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.z, v.x);</div>
<div class="line"><a name="l02243"></a><span class="lineno"> 2243</span>&#160;        }</div>
<div class="line"><a name="l02244"></a><span class="lineno"> 2244</span>&#160;</div>
<div class="line"><a name="l02245"></a><span class="lineno"> 2245</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02246"></a><span class="lineno"> 2246</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02247"></a><span class="lineno"> 2247</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.z, v.x);</div>
<div class="line"><a name="l02248"></a><span class="lineno"> 2248</span>&#160;        }</div>
<div class="line"><a name="l02249"></a><span class="lineno"> 2249</span>&#160;</div>
<div class="line"><a name="l02250"></a><span class="lineno"> 2250</span>&#160;        <span class="comment">// zzzy</span></div>
<div class="line"><a name="l02251"></a><span class="lineno"> 2251</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02252"></a><span class="lineno"> 2252</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzzy(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02253"></a><span class="lineno"> 2253</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.z, v.y);</div>
<div class="line"><a name="l02254"></a><span class="lineno"> 2254</span>&#160;        }</div>
<div class="line"><a name="l02255"></a><span class="lineno"> 2255</span>&#160;</div>
<div class="line"><a name="l02256"></a><span class="lineno"> 2256</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02257"></a><span class="lineno"> 2257</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02258"></a><span class="lineno"> 2258</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.z, v.y);</div>
<div class="line"><a name="l02259"></a><span class="lineno"> 2259</span>&#160;        }</div>
<div class="line"><a name="l02260"></a><span class="lineno"> 2260</span>&#160;</div>
<div class="line"><a name="l02261"></a><span class="lineno"> 2261</span>&#160;        <span class="comment">// zzzz</span></div>
<div class="line"><a name="l02262"></a><span class="lineno"> 2262</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02263"></a><span class="lineno"> 2263</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzzz(<span class="keyword">const</span> glm::vec&lt;3, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02264"></a><span class="lineno"> 2264</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.z, v.z);</div>
<div class="line"><a name="l02265"></a><span class="lineno"> 2265</span>&#160;        }</div>
<div class="line"><a name="l02266"></a><span class="lineno"> 2266</span>&#160;</div>
<div class="line"><a name="l02267"></a><span class="lineno"> 2267</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02268"></a><span class="lineno"> 2268</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02269"></a><span class="lineno"> 2269</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.z, v.z);</div>
<div class="line"><a name="l02270"></a><span class="lineno"> 2270</span>&#160;        }</div>
<div class="line"><a name="l02271"></a><span class="lineno"> 2271</span>&#160;</div>
<div class="line"><a name="l02272"></a><span class="lineno"> 2272</span>&#160;        <span class="comment">// zzzw</span></div>
<div class="line"><a name="l02273"></a><span class="lineno"> 2273</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02274"></a><span class="lineno"> 2274</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02275"></a><span class="lineno"> 2275</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.z, v.w);</div>
<div class="line"><a name="l02276"></a><span class="lineno"> 2276</span>&#160;        }</div>
<div class="line"><a name="l02277"></a><span class="lineno"> 2277</span>&#160;</div>
<div class="line"><a name="l02278"></a><span class="lineno"> 2278</span>&#160;        <span class="comment">// zzwx</span></div>
<div class="line"><a name="l02279"></a><span class="lineno"> 2279</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02280"></a><span class="lineno"> 2280</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02281"></a><span class="lineno"> 2281</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.w, v.x);</div>
<div class="line"><a name="l02282"></a><span class="lineno"> 2282</span>&#160;        }</div>
<div class="line"><a name="l02283"></a><span class="lineno"> 2283</span>&#160;</div>
<div class="line"><a name="l02284"></a><span class="lineno"> 2284</span>&#160;        <span class="comment">// zzwy</span></div>
<div class="line"><a name="l02285"></a><span class="lineno"> 2285</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02286"></a><span class="lineno"> 2286</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02287"></a><span class="lineno"> 2287</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.w, v.y);</div>
<div class="line"><a name="l02288"></a><span class="lineno"> 2288</span>&#160;        }</div>
<div class="line"><a name="l02289"></a><span class="lineno"> 2289</span>&#160;</div>
<div class="line"><a name="l02290"></a><span class="lineno"> 2290</span>&#160;        <span class="comment">// zzwz</span></div>
<div class="line"><a name="l02291"></a><span class="lineno"> 2291</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02292"></a><span class="lineno"> 2292</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02293"></a><span class="lineno"> 2293</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.w, v.z);</div>
<div class="line"><a name="l02294"></a><span class="lineno"> 2294</span>&#160;        }</div>
<div class="line"><a name="l02295"></a><span class="lineno"> 2295</span>&#160;</div>
<div class="line"><a name="l02296"></a><span class="lineno"> 2296</span>&#160;        <span class="comment">// zzww</span></div>
<div class="line"><a name="l02297"></a><span class="lineno"> 2297</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02298"></a><span class="lineno"> 2298</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zzww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02299"></a><span class="lineno"> 2299</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.z, v.w, v.w);</div>
<div class="line"><a name="l02300"></a><span class="lineno"> 2300</span>&#160;        }</div>
<div class="line"><a name="l02301"></a><span class="lineno"> 2301</span>&#160;</div>
<div class="line"><a name="l02302"></a><span class="lineno"> 2302</span>&#160;        <span class="comment">// zwxx</span></div>
<div class="line"><a name="l02303"></a><span class="lineno"> 2303</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02304"></a><span class="lineno"> 2304</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02305"></a><span class="lineno"> 2305</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.x, v.x);</div>
<div class="line"><a name="l02306"></a><span class="lineno"> 2306</span>&#160;        }</div>
<div class="line"><a name="l02307"></a><span class="lineno"> 2307</span>&#160;</div>
<div class="line"><a name="l02308"></a><span class="lineno"> 2308</span>&#160;        <span class="comment">// zwxy</span></div>
<div class="line"><a name="l02309"></a><span class="lineno"> 2309</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02310"></a><span class="lineno"> 2310</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02311"></a><span class="lineno"> 2311</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.x, v.y);</div>
<div class="line"><a name="l02312"></a><span class="lineno"> 2312</span>&#160;        }</div>
<div class="line"><a name="l02313"></a><span class="lineno"> 2313</span>&#160;</div>
<div class="line"><a name="l02314"></a><span class="lineno"> 2314</span>&#160;        <span class="comment">// zwxz</span></div>
<div class="line"><a name="l02315"></a><span class="lineno"> 2315</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02316"></a><span class="lineno"> 2316</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02317"></a><span class="lineno"> 2317</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.x, v.z);</div>
<div class="line"><a name="l02318"></a><span class="lineno"> 2318</span>&#160;        }</div>
<div class="line"><a name="l02319"></a><span class="lineno"> 2319</span>&#160;</div>
<div class="line"><a name="l02320"></a><span class="lineno"> 2320</span>&#160;        <span class="comment">// zwxw</span></div>
<div class="line"><a name="l02321"></a><span class="lineno"> 2321</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02322"></a><span class="lineno"> 2322</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02323"></a><span class="lineno"> 2323</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.x, v.w);</div>
<div class="line"><a name="l02324"></a><span class="lineno"> 2324</span>&#160;        }</div>
<div class="line"><a name="l02325"></a><span class="lineno"> 2325</span>&#160;</div>
<div class="line"><a name="l02326"></a><span class="lineno"> 2326</span>&#160;        <span class="comment">// zwyx</span></div>
<div class="line"><a name="l02327"></a><span class="lineno"> 2327</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02328"></a><span class="lineno"> 2328</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02329"></a><span class="lineno"> 2329</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.y, v.x);</div>
<div class="line"><a name="l02330"></a><span class="lineno"> 2330</span>&#160;        }</div>
<div class="line"><a name="l02331"></a><span class="lineno"> 2331</span>&#160;</div>
<div class="line"><a name="l02332"></a><span class="lineno"> 2332</span>&#160;        <span class="comment">// zwyy</span></div>
<div class="line"><a name="l02333"></a><span class="lineno"> 2333</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02334"></a><span class="lineno"> 2334</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02335"></a><span class="lineno"> 2335</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.y, v.y);</div>
<div class="line"><a name="l02336"></a><span class="lineno"> 2336</span>&#160;        }</div>
<div class="line"><a name="l02337"></a><span class="lineno"> 2337</span>&#160;</div>
<div class="line"><a name="l02338"></a><span class="lineno"> 2338</span>&#160;        <span class="comment">// zwyz</span></div>
<div class="line"><a name="l02339"></a><span class="lineno"> 2339</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02340"></a><span class="lineno"> 2340</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02341"></a><span class="lineno"> 2341</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.y, v.z);</div>
<div class="line"><a name="l02342"></a><span class="lineno"> 2342</span>&#160;        }</div>
<div class="line"><a name="l02343"></a><span class="lineno"> 2343</span>&#160;</div>
<div class="line"><a name="l02344"></a><span class="lineno"> 2344</span>&#160;        <span class="comment">// zwyw</span></div>
<div class="line"><a name="l02345"></a><span class="lineno"> 2345</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02346"></a><span class="lineno"> 2346</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02347"></a><span class="lineno"> 2347</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.y, v.w);</div>
<div class="line"><a name="l02348"></a><span class="lineno"> 2348</span>&#160;        }</div>
<div class="line"><a name="l02349"></a><span class="lineno"> 2349</span>&#160;</div>
<div class="line"><a name="l02350"></a><span class="lineno"> 2350</span>&#160;        <span class="comment">// zwzx</span></div>
<div class="line"><a name="l02351"></a><span class="lineno"> 2351</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02352"></a><span class="lineno"> 2352</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02353"></a><span class="lineno"> 2353</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.z, v.x);</div>
<div class="line"><a name="l02354"></a><span class="lineno"> 2354</span>&#160;        }</div>
<div class="line"><a name="l02355"></a><span class="lineno"> 2355</span>&#160;</div>
<div class="line"><a name="l02356"></a><span class="lineno"> 2356</span>&#160;        <span class="comment">// zwzy</span></div>
<div class="line"><a name="l02357"></a><span class="lineno"> 2357</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02358"></a><span class="lineno"> 2358</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02359"></a><span class="lineno"> 2359</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.z, v.y);</div>
<div class="line"><a name="l02360"></a><span class="lineno"> 2360</span>&#160;        }</div>
<div class="line"><a name="l02361"></a><span class="lineno"> 2361</span>&#160;</div>
<div class="line"><a name="l02362"></a><span class="lineno"> 2362</span>&#160;        <span class="comment">// zwzz</span></div>
<div class="line"><a name="l02363"></a><span class="lineno"> 2363</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02364"></a><span class="lineno"> 2364</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02365"></a><span class="lineno"> 2365</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.z, v.z);</div>
<div class="line"><a name="l02366"></a><span class="lineno"> 2366</span>&#160;        }</div>
<div class="line"><a name="l02367"></a><span class="lineno"> 2367</span>&#160;</div>
<div class="line"><a name="l02368"></a><span class="lineno"> 2368</span>&#160;        <span class="comment">// zwzw</span></div>
<div class="line"><a name="l02369"></a><span class="lineno"> 2369</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02370"></a><span class="lineno"> 2370</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02371"></a><span class="lineno"> 2371</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.z, v.w);</div>
<div class="line"><a name="l02372"></a><span class="lineno"> 2372</span>&#160;        }</div>
<div class="line"><a name="l02373"></a><span class="lineno"> 2373</span>&#160;</div>
<div class="line"><a name="l02374"></a><span class="lineno"> 2374</span>&#160;        <span class="comment">// zwwx</span></div>
<div class="line"><a name="l02375"></a><span class="lineno"> 2375</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02376"></a><span class="lineno"> 2376</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02377"></a><span class="lineno"> 2377</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.w, v.x);</div>
<div class="line"><a name="l02378"></a><span class="lineno"> 2378</span>&#160;        }</div>
<div class="line"><a name="l02379"></a><span class="lineno"> 2379</span>&#160;</div>
<div class="line"><a name="l02380"></a><span class="lineno"> 2380</span>&#160;        <span class="comment">// zwwy</span></div>
<div class="line"><a name="l02381"></a><span class="lineno"> 2381</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02382"></a><span class="lineno"> 2382</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02383"></a><span class="lineno"> 2383</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.w, v.y);</div>
<div class="line"><a name="l02384"></a><span class="lineno"> 2384</span>&#160;        }</div>
<div class="line"><a name="l02385"></a><span class="lineno"> 2385</span>&#160;</div>
<div class="line"><a name="l02386"></a><span class="lineno"> 2386</span>&#160;        <span class="comment">// zwwz</span></div>
<div class="line"><a name="l02387"></a><span class="lineno"> 2387</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02388"></a><span class="lineno"> 2388</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02389"></a><span class="lineno"> 2389</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.w, v.z);</div>
<div class="line"><a name="l02390"></a><span class="lineno"> 2390</span>&#160;        }</div>
<div class="line"><a name="l02391"></a><span class="lineno"> 2391</span>&#160;</div>
<div class="line"><a name="l02392"></a><span class="lineno"> 2392</span>&#160;        <span class="comment">// zwww</span></div>
<div class="line"><a name="l02393"></a><span class="lineno"> 2393</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02394"></a><span class="lineno"> 2394</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; zwww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02395"></a><span class="lineno"> 2395</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.z, v.w, v.w, v.w);</div>
<div class="line"><a name="l02396"></a><span class="lineno"> 2396</span>&#160;        }</div>
<div class="line"><a name="l02397"></a><span class="lineno"> 2397</span>&#160;</div>
<div class="line"><a name="l02398"></a><span class="lineno"> 2398</span>&#160;        <span class="comment">// wxxx</span></div>
<div class="line"><a name="l02399"></a><span class="lineno"> 2399</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02400"></a><span class="lineno"> 2400</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02401"></a><span class="lineno"> 2401</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.x, v.x);</div>
<div class="line"><a name="l02402"></a><span class="lineno"> 2402</span>&#160;        }</div>
<div class="line"><a name="l02403"></a><span class="lineno"> 2403</span>&#160;</div>
<div class="line"><a name="l02404"></a><span class="lineno"> 2404</span>&#160;        <span class="comment">// wxxy</span></div>
<div class="line"><a name="l02405"></a><span class="lineno"> 2405</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02406"></a><span class="lineno"> 2406</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02407"></a><span class="lineno"> 2407</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.x, v.y);</div>
<div class="line"><a name="l02408"></a><span class="lineno"> 2408</span>&#160;        }</div>
<div class="line"><a name="l02409"></a><span class="lineno"> 2409</span>&#160;</div>
<div class="line"><a name="l02410"></a><span class="lineno"> 2410</span>&#160;        <span class="comment">// wxxz</span></div>
<div class="line"><a name="l02411"></a><span class="lineno"> 2411</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02412"></a><span class="lineno"> 2412</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02413"></a><span class="lineno"> 2413</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.x, v.z);</div>
<div class="line"><a name="l02414"></a><span class="lineno"> 2414</span>&#160;        }</div>
<div class="line"><a name="l02415"></a><span class="lineno"> 2415</span>&#160;</div>
<div class="line"><a name="l02416"></a><span class="lineno"> 2416</span>&#160;        <span class="comment">// wxxw</span></div>
<div class="line"><a name="l02417"></a><span class="lineno"> 2417</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02418"></a><span class="lineno"> 2418</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02419"></a><span class="lineno"> 2419</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.x, v.w);</div>
<div class="line"><a name="l02420"></a><span class="lineno"> 2420</span>&#160;        }</div>
<div class="line"><a name="l02421"></a><span class="lineno"> 2421</span>&#160;</div>
<div class="line"><a name="l02422"></a><span class="lineno"> 2422</span>&#160;        <span class="comment">// wxyx</span></div>
<div class="line"><a name="l02423"></a><span class="lineno"> 2423</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02424"></a><span class="lineno"> 2424</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02425"></a><span class="lineno"> 2425</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.y, v.x);</div>
<div class="line"><a name="l02426"></a><span class="lineno"> 2426</span>&#160;        }</div>
<div class="line"><a name="l02427"></a><span class="lineno"> 2427</span>&#160;</div>
<div class="line"><a name="l02428"></a><span class="lineno"> 2428</span>&#160;        <span class="comment">// wxyy</span></div>
<div class="line"><a name="l02429"></a><span class="lineno"> 2429</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02430"></a><span class="lineno"> 2430</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02431"></a><span class="lineno"> 2431</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.y, v.y);</div>
<div class="line"><a name="l02432"></a><span class="lineno"> 2432</span>&#160;        }</div>
<div class="line"><a name="l02433"></a><span class="lineno"> 2433</span>&#160;</div>
<div class="line"><a name="l02434"></a><span class="lineno"> 2434</span>&#160;        <span class="comment">// wxyz</span></div>
<div class="line"><a name="l02435"></a><span class="lineno"> 2435</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02436"></a><span class="lineno"> 2436</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02437"></a><span class="lineno"> 2437</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.y, v.z);</div>
<div class="line"><a name="l02438"></a><span class="lineno"> 2438</span>&#160;        }</div>
<div class="line"><a name="l02439"></a><span class="lineno"> 2439</span>&#160;</div>
<div class="line"><a name="l02440"></a><span class="lineno"> 2440</span>&#160;        <span class="comment">// wxyw</span></div>
<div class="line"><a name="l02441"></a><span class="lineno"> 2441</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02442"></a><span class="lineno"> 2442</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02443"></a><span class="lineno"> 2443</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.y, v.w);</div>
<div class="line"><a name="l02444"></a><span class="lineno"> 2444</span>&#160;        }</div>
<div class="line"><a name="l02445"></a><span class="lineno"> 2445</span>&#160;</div>
<div class="line"><a name="l02446"></a><span class="lineno"> 2446</span>&#160;        <span class="comment">// wxzx</span></div>
<div class="line"><a name="l02447"></a><span class="lineno"> 2447</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02448"></a><span class="lineno"> 2448</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02449"></a><span class="lineno"> 2449</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.z, v.x);</div>
<div class="line"><a name="l02450"></a><span class="lineno"> 2450</span>&#160;        }</div>
<div class="line"><a name="l02451"></a><span class="lineno"> 2451</span>&#160;</div>
<div class="line"><a name="l02452"></a><span class="lineno"> 2452</span>&#160;        <span class="comment">// wxzy</span></div>
<div class="line"><a name="l02453"></a><span class="lineno"> 2453</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02454"></a><span class="lineno"> 2454</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02455"></a><span class="lineno"> 2455</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.z, v.y);</div>
<div class="line"><a name="l02456"></a><span class="lineno"> 2456</span>&#160;        }</div>
<div class="line"><a name="l02457"></a><span class="lineno"> 2457</span>&#160;</div>
<div class="line"><a name="l02458"></a><span class="lineno"> 2458</span>&#160;        <span class="comment">// wxzz</span></div>
<div class="line"><a name="l02459"></a><span class="lineno"> 2459</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02460"></a><span class="lineno"> 2460</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02461"></a><span class="lineno"> 2461</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.z, v.z);</div>
<div class="line"><a name="l02462"></a><span class="lineno"> 2462</span>&#160;        }</div>
<div class="line"><a name="l02463"></a><span class="lineno"> 2463</span>&#160;</div>
<div class="line"><a name="l02464"></a><span class="lineno"> 2464</span>&#160;        <span class="comment">// wxzw</span></div>
<div class="line"><a name="l02465"></a><span class="lineno"> 2465</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02466"></a><span class="lineno"> 2466</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02467"></a><span class="lineno"> 2467</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.z, v.w);</div>
<div class="line"><a name="l02468"></a><span class="lineno"> 2468</span>&#160;        }</div>
<div class="line"><a name="l02469"></a><span class="lineno"> 2469</span>&#160;</div>
<div class="line"><a name="l02470"></a><span class="lineno"> 2470</span>&#160;        <span class="comment">// wxwx</span></div>
<div class="line"><a name="l02471"></a><span class="lineno"> 2471</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02472"></a><span class="lineno"> 2472</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02473"></a><span class="lineno"> 2473</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.w, v.x);</div>
<div class="line"><a name="l02474"></a><span class="lineno"> 2474</span>&#160;        }</div>
<div class="line"><a name="l02475"></a><span class="lineno"> 2475</span>&#160;</div>
<div class="line"><a name="l02476"></a><span class="lineno"> 2476</span>&#160;        <span class="comment">// wxwy</span></div>
<div class="line"><a name="l02477"></a><span class="lineno"> 2477</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02478"></a><span class="lineno"> 2478</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02479"></a><span class="lineno"> 2479</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.w, v.y);</div>
<div class="line"><a name="l02480"></a><span class="lineno"> 2480</span>&#160;        }</div>
<div class="line"><a name="l02481"></a><span class="lineno"> 2481</span>&#160;</div>
<div class="line"><a name="l02482"></a><span class="lineno"> 2482</span>&#160;        <span class="comment">// wxwz</span></div>
<div class="line"><a name="l02483"></a><span class="lineno"> 2483</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02484"></a><span class="lineno"> 2484</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02485"></a><span class="lineno"> 2485</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.w, v.z);</div>
<div class="line"><a name="l02486"></a><span class="lineno"> 2486</span>&#160;        }</div>
<div class="line"><a name="l02487"></a><span class="lineno"> 2487</span>&#160;</div>
<div class="line"><a name="l02488"></a><span class="lineno"> 2488</span>&#160;        <span class="comment">// wxww</span></div>
<div class="line"><a name="l02489"></a><span class="lineno"> 2489</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02490"></a><span class="lineno"> 2490</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wxww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02491"></a><span class="lineno"> 2491</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.x, v.w, v.w);</div>
<div class="line"><a name="l02492"></a><span class="lineno"> 2492</span>&#160;        }</div>
<div class="line"><a name="l02493"></a><span class="lineno"> 2493</span>&#160;</div>
<div class="line"><a name="l02494"></a><span class="lineno"> 2494</span>&#160;        <span class="comment">// wyxx</span></div>
<div class="line"><a name="l02495"></a><span class="lineno"> 2495</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02496"></a><span class="lineno"> 2496</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02497"></a><span class="lineno"> 2497</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.x, v.x);</div>
<div class="line"><a name="l02498"></a><span class="lineno"> 2498</span>&#160;        }</div>
<div class="line"><a name="l02499"></a><span class="lineno"> 2499</span>&#160;</div>
<div class="line"><a name="l02500"></a><span class="lineno"> 2500</span>&#160;        <span class="comment">// wyxy</span></div>
<div class="line"><a name="l02501"></a><span class="lineno"> 2501</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02502"></a><span class="lineno"> 2502</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02503"></a><span class="lineno"> 2503</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.x, v.y);</div>
<div class="line"><a name="l02504"></a><span class="lineno"> 2504</span>&#160;        }</div>
<div class="line"><a name="l02505"></a><span class="lineno"> 2505</span>&#160;</div>
<div class="line"><a name="l02506"></a><span class="lineno"> 2506</span>&#160;        <span class="comment">// wyxz</span></div>
<div class="line"><a name="l02507"></a><span class="lineno"> 2507</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02508"></a><span class="lineno"> 2508</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02509"></a><span class="lineno"> 2509</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.x, v.z);</div>
<div class="line"><a name="l02510"></a><span class="lineno"> 2510</span>&#160;        }</div>
<div class="line"><a name="l02511"></a><span class="lineno"> 2511</span>&#160;</div>
<div class="line"><a name="l02512"></a><span class="lineno"> 2512</span>&#160;        <span class="comment">// wyxw</span></div>
<div class="line"><a name="l02513"></a><span class="lineno"> 2513</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02514"></a><span class="lineno"> 2514</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02515"></a><span class="lineno"> 2515</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.x, v.w);</div>
<div class="line"><a name="l02516"></a><span class="lineno"> 2516</span>&#160;        }</div>
<div class="line"><a name="l02517"></a><span class="lineno"> 2517</span>&#160;</div>
<div class="line"><a name="l02518"></a><span class="lineno"> 2518</span>&#160;        <span class="comment">// wyyx</span></div>
<div class="line"><a name="l02519"></a><span class="lineno"> 2519</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02520"></a><span class="lineno"> 2520</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02521"></a><span class="lineno"> 2521</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.y, v.x);</div>
<div class="line"><a name="l02522"></a><span class="lineno"> 2522</span>&#160;        }</div>
<div class="line"><a name="l02523"></a><span class="lineno"> 2523</span>&#160;</div>
<div class="line"><a name="l02524"></a><span class="lineno"> 2524</span>&#160;        <span class="comment">// wyyy</span></div>
<div class="line"><a name="l02525"></a><span class="lineno"> 2525</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02526"></a><span class="lineno"> 2526</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02527"></a><span class="lineno"> 2527</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.y, v.y);</div>
<div class="line"><a name="l02528"></a><span class="lineno"> 2528</span>&#160;        }</div>
<div class="line"><a name="l02529"></a><span class="lineno"> 2529</span>&#160;</div>
<div class="line"><a name="l02530"></a><span class="lineno"> 2530</span>&#160;        <span class="comment">// wyyz</span></div>
<div class="line"><a name="l02531"></a><span class="lineno"> 2531</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02532"></a><span class="lineno"> 2532</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02533"></a><span class="lineno"> 2533</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.y, v.z);</div>
<div class="line"><a name="l02534"></a><span class="lineno"> 2534</span>&#160;        }</div>
<div class="line"><a name="l02535"></a><span class="lineno"> 2535</span>&#160;</div>
<div class="line"><a name="l02536"></a><span class="lineno"> 2536</span>&#160;        <span class="comment">// wyyw</span></div>
<div class="line"><a name="l02537"></a><span class="lineno"> 2537</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02538"></a><span class="lineno"> 2538</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02539"></a><span class="lineno"> 2539</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.y, v.w);</div>
<div class="line"><a name="l02540"></a><span class="lineno"> 2540</span>&#160;        }</div>
<div class="line"><a name="l02541"></a><span class="lineno"> 2541</span>&#160;</div>
<div class="line"><a name="l02542"></a><span class="lineno"> 2542</span>&#160;        <span class="comment">// wyzx</span></div>
<div class="line"><a name="l02543"></a><span class="lineno"> 2543</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02544"></a><span class="lineno"> 2544</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02545"></a><span class="lineno"> 2545</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.z, v.x);</div>
<div class="line"><a name="l02546"></a><span class="lineno"> 2546</span>&#160;        }</div>
<div class="line"><a name="l02547"></a><span class="lineno"> 2547</span>&#160;</div>
<div class="line"><a name="l02548"></a><span class="lineno"> 2548</span>&#160;        <span class="comment">// wyzy</span></div>
<div class="line"><a name="l02549"></a><span class="lineno"> 2549</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02550"></a><span class="lineno"> 2550</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02551"></a><span class="lineno"> 2551</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.z, v.y);</div>
<div class="line"><a name="l02552"></a><span class="lineno"> 2552</span>&#160;        }</div>
<div class="line"><a name="l02553"></a><span class="lineno"> 2553</span>&#160;</div>
<div class="line"><a name="l02554"></a><span class="lineno"> 2554</span>&#160;        <span class="comment">// wyzz</span></div>
<div class="line"><a name="l02555"></a><span class="lineno"> 2555</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02556"></a><span class="lineno"> 2556</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02557"></a><span class="lineno"> 2557</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.z, v.z);</div>
<div class="line"><a name="l02558"></a><span class="lineno"> 2558</span>&#160;        }</div>
<div class="line"><a name="l02559"></a><span class="lineno"> 2559</span>&#160;</div>
<div class="line"><a name="l02560"></a><span class="lineno"> 2560</span>&#160;        <span class="comment">// wyzw</span></div>
<div class="line"><a name="l02561"></a><span class="lineno"> 2561</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02562"></a><span class="lineno"> 2562</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02563"></a><span class="lineno"> 2563</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.z, v.w);</div>
<div class="line"><a name="l02564"></a><span class="lineno"> 2564</span>&#160;        }</div>
<div class="line"><a name="l02565"></a><span class="lineno"> 2565</span>&#160;</div>
<div class="line"><a name="l02566"></a><span class="lineno"> 2566</span>&#160;        <span class="comment">// wywx</span></div>
<div class="line"><a name="l02567"></a><span class="lineno"> 2567</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02568"></a><span class="lineno"> 2568</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wywx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02569"></a><span class="lineno"> 2569</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.w, v.x);</div>
<div class="line"><a name="l02570"></a><span class="lineno"> 2570</span>&#160;        }</div>
<div class="line"><a name="l02571"></a><span class="lineno"> 2571</span>&#160;</div>
<div class="line"><a name="l02572"></a><span class="lineno"> 2572</span>&#160;        <span class="comment">// wywy</span></div>
<div class="line"><a name="l02573"></a><span class="lineno"> 2573</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02574"></a><span class="lineno"> 2574</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wywy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02575"></a><span class="lineno"> 2575</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.w, v.y);</div>
<div class="line"><a name="l02576"></a><span class="lineno"> 2576</span>&#160;        }</div>
<div class="line"><a name="l02577"></a><span class="lineno"> 2577</span>&#160;</div>
<div class="line"><a name="l02578"></a><span class="lineno"> 2578</span>&#160;        <span class="comment">// wywz</span></div>
<div class="line"><a name="l02579"></a><span class="lineno"> 2579</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02580"></a><span class="lineno"> 2580</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wywz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02581"></a><span class="lineno"> 2581</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.w, v.z);</div>
<div class="line"><a name="l02582"></a><span class="lineno"> 2582</span>&#160;        }</div>
<div class="line"><a name="l02583"></a><span class="lineno"> 2583</span>&#160;</div>
<div class="line"><a name="l02584"></a><span class="lineno"> 2584</span>&#160;        <span class="comment">// wyww</span></div>
<div class="line"><a name="l02585"></a><span class="lineno"> 2585</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02586"></a><span class="lineno"> 2586</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wyww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02587"></a><span class="lineno"> 2587</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.y, v.w, v.w);</div>
<div class="line"><a name="l02588"></a><span class="lineno"> 2588</span>&#160;        }</div>
<div class="line"><a name="l02589"></a><span class="lineno"> 2589</span>&#160;</div>
<div class="line"><a name="l02590"></a><span class="lineno"> 2590</span>&#160;        <span class="comment">// wzxx</span></div>
<div class="line"><a name="l02591"></a><span class="lineno"> 2591</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02592"></a><span class="lineno"> 2592</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02593"></a><span class="lineno"> 2593</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.x, v.x);</div>
<div class="line"><a name="l02594"></a><span class="lineno"> 2594</span>&#160;        }</div>
<div class="line"><a name="l02595"></a><span class="lineno"> 2595</span>&#160;</div>
<div class="line"><a name="l02596"></a><span class="lineno"> 2596</span>&#160;        <span class="comment">// wzxy</span></div>
<div class="line"><a name="l02597"></a><span class="lineno"> 2597</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02598"></a><span class="lineno"> 2598</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02599"></a><span class="lineno"> 2599</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.x, v.y);</div>
<div class="line"><a name="l02600"></a><span class="lineno"> 2600</span>&#160;        }</div>
<div class="line"><a name="l02601"></a><span class="lineno"> 2601</span>&#160;</div>
<div class="line"><a name="l02602"></a><span class="lineno"> 2602</span>&#160;        <span class="comment">// wzxz</span></div>
<div class="line"><a name="l02603"></a><span class="lineno"> 2603</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02604"></a><span class="lineno"> 2604</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02605"></a><span class="lineno"> 2605</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.x, v.z);</div>
<div class="line"><a name="l02606"></a><span class="lineno"> 2606</span>&#160;        }</div>
<div class="line"><a name="l02607"></a><span class="lineno"> 2607</span>&#160;</div>
<div class="line"><a name="l02608"></a><span class="lineno"> 2608</span>&#160;        <span class="comment">// wzxw</span></div>
<div class="line"><a name="l02609"></a><span class="lineno"> 2609</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02610"></a><span class="lineno"> 2610</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02611"></a><span class="lineno"> 2611</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.x, v.w);</div>
<div class="line"><a name="l02612"></a><span class="lineno"> 2612</span>&#160;        }</div>
<div class="line"><a name="l02613"></a><span class="lineno"> 2613</span>&#160;</div>
<div class="line"><a name="l02614"></a><span class="lineno"> 2614</span>&#160;        <span class="comment">// wzyx</span></div>
<div class="line"><a name="l02615"></a><span class="lineno"> 2615</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02616"></a><span class="lineno"> 2616</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02617"></a><span class="lineno"> 2617</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.y, v.x);</div>
<div class="line"><a name="l02618"></a><span class="lineno"> 2618</span>&#160;        }</div>
<div class="line"><a name="l02619"></a><span class="lineno"> 2619</span>&#160;</div>
<div class="line"><a name="l02620"></a><span class="lineno"> 2620</span>&#160;        <span class="comment">// wzyy</span></div>
<div class="line"><a name="l02621"></a><span class="lineno"> 2621</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02622"></a><span class="lineno"> 2622</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02623"></a><span class="lineno"> 2623</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.y, v.y);</div>
<div class="line"><a name="l02624"></a><span class="lineno"> 2624</span>&#160;        }</div>
<div class="line"><a name="l02625"></a><span class="lineno"> 2625</span>&#160;</div>
<div class="line"><a name="l02626"></a><span class="lineno"> 2626</span>&#160;        <span class="comment">// wzyz</span></div>
<div class="line"><a name="l02627"></a><span class="lineno"> 2627</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02628"></a><span class="lineno"> 2628</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02629"></a><span class="lineno"> 2629</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.y, v.z);</div>
<div class="line"><a name="l02630"></a><span class="lineno"> 2630</span>&#160;        }</div>
<div class="line"><a name="l02631"></a><span class="lineno"> 2631</span>&#160;</div>
<div class="line"><a name="l02632"></a><span class="lineno"> 2632</span>&#160;        <span class="comment">// wzyw</span></div>
<div class="line"><a name="l02633"></a><span class="lineno"> 2633</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02634"></a><span class="lineno"> 2634</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02635"></a><span class="lineno"> 2635</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.y, v.w);</div>
<div class="line"><a name="l02636"></a><span class="lineno"> 2636</span>&#160;        }</div>
<div class="line"><a name="l02637"></a><span class="lineno"> 2637</span>&#160;</div>
<div class="line"><a name="l02638"></a><span class="lineno"> 2638</span>&#160;        <span class="comment">// wzzx</span></div>
<div class="line"><a name="l02639"></a><span class="lineno"> 2639</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02640"></a><span class="lineno"> 2640</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02641"></a><span class="lineno"> 2641</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.z, v.x);</div>
<div class="line"><a name="l02642"></a><span class="lineno"> 2642</span>&#160;        }</div>
<div class="line"><a name="l02643"></a><span class="lineno"> 2643</span>&#160;</div>
<div class="line"><a name="l02644"></a><span class="lineno"> 2644</span>&#160;        <span class="comment">// wzzy</span></div>
<div class="line"><a name="l02645"></a><span class="lineno"> 2645</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02646"></a><span class="lineno"> 2646</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02647"></a><span class="lineno"> 2647</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.z, v.y);</div>
<div class="line"><a name="l02648"></a><span class="lineno"> 2648</span>&#160;        }</div>
<div class="line"><a name="l02649"></a><span class="lineno"> 2649</span>&#160;</div>
<div class="line"><a name="l02650"></a><span class="lineno"> 2650</span>&#160;        <span class="comment">// wzzz</span></div>
<div class="line"><a name="l02651"></a><span class="lineno"> 2651</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02652"></a><span class="lineno"> 2652</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02653"></a><span class="lineno"> 2653</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.z, v.z);</div>
<div class="line"><a name="l02654"></a><span class="lineno"> 2654</span>&#160;        }</div>
<div class="line"><a name="l02655"></a><span class="lineno"> 2655</span>&#160;</div>
<div class="line"><a name="l02656"></a><span class="lineno"> 2656</span>&#160;        <span class="comment">// wzzw</span></div>
<div class="line"><a name="l02657"></a><span class="lineno"> 2657</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02658"></a><span class="lineno"> 2658</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02659"></a><span class="lineno"> 2659</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.z, v.w);</div>
<div class="line"><a name="l02660"></a><span class="lineno"> 2660</span>&#160;        }</div>
<div class="line"><a name="l02661"></a><span class="lineno"> 2661</span>&#160;</div>
<div class="line"><a name="l02662"></a><span class="lineno"> 2662</span>&#160;        <span class="comment">// wzwx</span></div>
<div class="line"><a name="l02663"></a><span class="lineno"> 2663</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02664"></a><span class="lineno"> 2664</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02665"></a><span class="lineno"> 2665</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.w, v.x);</div>
<div class="line"><a name="l02666"></a><span class="lineno"> 2666</span>&#160;        }</div>
<div class="line"><a name="l02667"></a><span class="lineno"> 2667</span>&#160;</div>
<div class="line"><a name="l02668"></a><span class="lineno"> 2668</span>&#160;        <span class="comment">// wzwy</span></div>
<div class="line"><a name="l02669"></a><span class="lineno"> 2669</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02670"></a><span class="lineno"> 2670</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02671"></a><span class="lineno"> 2671</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.w, v.y);</div>
<div class="line"><a name="l02672"></a><span class="lineno"> 2672</span>&#160;        }</div>
<div class="line"><a name="l02673"></a><span class="lineno"> 2673</span>&#160;</div>
<div class="line"><a name="l02674"></a><span class="lineno"> 2674</span>&#160;        <span class="comment">// wzwz</span></div>
<div class="line"><a name="l02675"></a><span class="lineno"> 2675</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02676"></a><span class="lineno"> 2676</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02677"></a><span class="lineno"> 2677</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.w, v.z);</div>
<div class="line"><a name="l02678"></a><span class="lineno"> 2678</span>&#160;        }</div>
<div class="line"><a name="l02679"></a><span class="lineno"> 2679</span>&#160;</div>
<div class="line"><a name="l02680"></a><span class="lineno"> 2680</span>&#160;        <span class="comment">// wzww</span></div>
<div class="line"><a name="l02681"></a><span class="lineno"> 2681</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02682"></a><span class="lineno"> 2682</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wzww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02683"></a><span class="lineno"> 2683</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.z, v.w, v.w);</div>
<div class="line"><a name="l02684"></a><span class="lineno"> 2684</span>&#160;        }</div>
<div class="line"><a name="l02685"></a><span class="lineno"> 2685</span>&#160;</div>
<div class="line"><a name="l02686"></a><span class="lineno"> 2686</span>&#160;        <span class="comment">// wwxx</span></div>
<div class="line"><a name="l02687"></a><span class="lineno"> 2687</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02688"></a><span class="lineno"> 2688</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwxx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02689"></a><span class="lineno"> 2689</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.x, v.x);</div>
<div class="line"><a name="l02690"></a><span class="lineno"> 2690</span>&#160;        }</div>
<div class="line"><a name="l02691"></a><span class="lineno"> 2691</span>&#160;</div>
<div class="line"><a name="l02692"></a><span class="lineno"> 2692</span>&#160;        <span class="comment">// wwxy</span></div>
<div class="line"><a name="l02693"></a><span class="lineno"> 2693</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02694"></a><span class="lineno"> 2694</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwxy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02695"></a><span class="lineno"> 2695</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.x, v.y);</div>
<div class="line"><a name="l02696"></a><span class="lineno"> 2696</span>&#160;        }</div>
<div class="line"><a name="l02697"></a><span class="lineno"> 2697</span>&#160;</div>
<div class="line"><a name="l02698"></a><span class="lineno"> 2698</span>&#160;        <span class="comment">// wwxz</span></div>
<div class="line"><a name="l02699"></a><span class="lineno"> 2699</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02700"></a><span class="lineno"> 2700</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwxz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02701"></a><span class="lineno"> 2701</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.x, v.z);</div>
<div class="line"><a name="l02702"></a><span class="lineno"> 2702</span>&#160;        }</div>
<div class="line"><a name="l02703"></a><span class="lineno"> 2703</span>&#160;</div>
<div class="line"><a name="l02704"></a><span class="lineno"> 2704</span>&#160;        <span class="comment">// wwxw</span></div>
<div class="line"><a name="l02705"></a><span class="lineno"> 2705</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02706"></a><span class="lineno"> 2706</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwxw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02707"></a><span class="lineno"> 2707</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.x, v.w);</div>
<div class="line"><a name="l02708"></a><span class="lineno"> 2708</span>&#160;        }</div>
<div class="line"><a name="l02709"></a><span class="lineno"> 2709</span>&#160;</div>
<div class="line"><a name="l02710"></a><span class="lineno"> 2710</span>&#160;        <span class="comment">// wwyx</span></div>
<div class="line"><a name="l02711"></a><span class="lineno"> 2711</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02712"></a><span class="lineno"> 2712</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwyx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02713"></a><span class="lineno"> 2713</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.y, v.x);</div>
<div class="line"><a name="l02714"></a><span class="lineno"> 2714</span>&#160;        }</div>
<div class="line"><a name="l02715"></a><span class="lineno"> 2715</span>&#160;</div>
<div class="line"><a name="l02716"></a><span class="lineno"> 2716</span>&#160;        <span class="comment">// wwyy</span></div>
<div class="line"><a name="l02717"></a><span class="lineno"> 2717</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02718"></a><span class="lineno"> 2718</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwyy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02719"></a><span class="lineno"> 2719</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.y, v.y);</div>
<div class="line"><a name="l02720"></a><span class="lineno"> 2720</span>&#160;        }</div>
<div class="line"><a name="l02721"></a><span class="lineno"> 2721</span>&#160;</div>
<div class="line"><a name="l02722"></a><span class="lineno"> 2722</span>&#160;        <span class="comment">// wwyz</span></div>
<div class="line"><a name="l02723"></a><span class="lineno"> 2723</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02724"></a><span class="lineno"> 2724</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwyz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02725"></a><span class="lineno"> 2725</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.y, v.z);</div>
<div class="line"><a name="l02726"></a><span class="lineno"> 2726</span>&#160;        }</div>
<div class="line"><a name="l02727"></a><span class="lineno"> 2727</span>&#160;</div>
<div class="line"><a name="l02728"></a><span class="lineno"> 2728</span>&#160;        <span class="comment">// wwyw</span></div>
<div class="line"><a name="l02729"></a><span class="lineno"> 2729</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02730"></a><span class="lineno"> 2730</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwyw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02731"></a><span class="lineno"> 2731</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.y, v.w);</div>
<div class="line"><a name="l02732"></a><span class="lineno"> 2732</span>&#160;        }</div>
<div class="line"><a name="l02733"></a><span class="lineno"> 2733</span>&#160;</div>
<div class="line"><a name="l02734"></a><span class="lineno"> 2734</span>&#160;        <span class="comment">// wwzx</span></div>
<div class="line"><a name="l02735"></a><span class="lineno"> 2735</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02736"></a><span class="lineno"> 2736</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwzx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02737"></a><span class="lineno"> 2737</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.z, v.x);</div>
<div class="line"><a name="l02738"></a><span class="lineno"> 2738</span>&#160;        }</div>
<div class="line"><a name="l02739"></a><span class="lineno"> 2739</span>&#160;</div>
<div class="line"><a name="l02740"></a><span class="lineno"> 2740</span>&#160;        <span class="comment">// wwzy</span></div>
<div class="line"><a name="l02741"></a><span class="lineno"> 2741</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02742"></a><span class="lineno"> 2742</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwzy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02743"></a><span class="lineno"> 2743</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.z, v.y);</div>
<div class="line"><a name="l02744"></a><span class="lineno"> 2744</span>&#160;        }</div>
<div class="line"><a name="l02745"></a><span class="lineno"> 2745</span>&#160;</div>
<div class="line"><a name="l02746"></a><span class="lineno"> 2746</span>&#160;        <span class="comment">// wwzz</span></div>
<div class="line"><a name="l02747"></a><span class="lineno"> 2747</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02748"></a><span class="lineno"> 2748</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwzz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02749"></a><span class="lineno"> 2749</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.z, v.z);</div>
<div class="line"><a name="l02750"></a><span class="lineno"> 2750</span>&#160;        }</div>
<div class="line"><a name="l02751"></a><span class="lineno"> 2751</span>&#160;</div>
<div class="line"><a name="l02752"></a><span class="lineno"> 2752</span>&#160;        <span class="comment">// wwzw</span></div>
<div class="line"><a name="l02753"></a><span class="lineno"> 2753</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02754"></a><span class="lineno"> 2754</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwzw(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02755"></a><span class="lineno"> 2755</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.z, v.w);</div>
<div class="line"><a name="l02756"></a><span class="lineno"> 2756</span>&#160;        }</div>
<div class="line"><a name="l02757"></a><span class="lineno"> 2757</span>&#160;</div>
<div class="line"><a name="l02758"></a><span class="lineno"> 2758</span>&#160;        <span class="comment">// wwwx</span></div>
<div class="line"><a name="l02759"></a><span class="lineno"> 2759</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02760"></a><span class="lineno"> 2760</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwwx(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02761"></a><span class="lineno"> 2761</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.w, v.x);</div>
<div class="line"><a name="l02762"></a><span class="lineno"> 2762</span>&#160;        }</div>
<div class="line"><a name="l02763"></a><span class="lineno"> 2763</span>&#160;</div>
<div class="line"><a name="l02764"></a><span class="lineno"> 2764</span>&#160;        <span class="comment">// wwwy</span></div>
<div class="line"><a name="l02765"></a><span class="lineno"> 2765</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02766"></a><span class="lineno"> 2766</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwwy(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02767"></a><span class="lineno"> 2767</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.w, v.y);</div>
<div class="line"><a name="l02768"></a><span class="lineno"> 2768</span>&#160;        }</div>
<div class="line"><a name="l02769"></a><span class="lineno"> 2769</span>&#160;</div>
<div class="line"><a name="l02770"></a><span class="lineno"> 2770</span>&#160;        <span class="comment">// wwwz</span></div>
<div class="line"><a name="l02771"></a><span class="lineno"> 2771</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02772"></a><span class="lineno"> 2772</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwwz(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02773"></a><span class="lineno"> 2773</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.w, v.z);</div>
<div class="line"><a name="l02774"></a><span class="lineno"> 2774</span>&#160;        }</div>
<div class="line"><a name="l02775"></a><span class="lineno"> 2775</span>&#160;</div>
<div class="line"><a name="l02776"></a><span class="lineno"> 2776</span>&#160;        <span class="comment">// wwww</span></div>
<div class="line"><a name="l02777"></a><span class="lineno"> 2777</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l02778"></a><span class="lineno"> 2778</span>&#160;        GLM_INLINE glm::vec&lt;4, T, Q&gt; wwww(<span class="keyword">const</span> glm::vec&lt;4, T, Q&gt; &amp;v) {</div>
<div class="line"><a name="l02779"></a><span class="lineno"> 2779</span>&#160;                <span class="keywordflow">return</span> glm::vec&lt;4, T, Q&gt;(v.w, v.w, v.w, v.w);</div>
<div class="line"><a name="l02780"></a><span class="lineno"> 2780</span>&#160;        }</div>
<div class="line"><a name="l02781"></a><span class="lineno"> 2781</span>&#160;</div>
<div class="line"><a name="l02782"></a><span class="lineno"> 2782</span>&#160;}</div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
