#!/usr/bin/env python3
"""
简化的梯度检查测试 - 验证修复是否正确
"""

import os
import sys
import torch
import logging

# 添加项目路径
sys.path.append('.')
sys.path.append('./gaussian_splatting')

# 导入3DGS参数类
from gaussian_splatting.arguments import OptimizationParams

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gradient_check_fix():
    """测试梯度检查修复"""
    logger.info("🔥 测试梯度检查修复...")
    
    try:
        from scene.gaussian_model import GaussianModel
        
        # 1. 创建并加载高斯模型
        logger.info("   - 创建高斯模型...")
        gaussians = GaussianModel(sh_degree=0)  # 使用正确的sh_degree
        
        model_path = "stage1_results/garden_fixed_final/gaussians_with_concept_membership_enhanced.ply"
        gaussians.load_ply(model_path)
        logger.info(f"   ✅ 模型加载成功，点数: {gaussians.get_xyz.shape[0]}")
        
        # 2. 设置训练
        logger.info("   - 设置训练...")
        import argparse
        parser = argparse.ArgumentParser()
        opt_params = OptimizationParams(parser)
        
        args = argparse.Namespace()
        for attr_name in dir(opt_params):
            if not attr_name.startswith('_'):
                setattr(args, attr_name, getattr(opt_params, attr_name))
        
        training_args = opt_params.extract(args)
        gaussians.training_setup(training_args)
        logger.info("   ✅ 训练设置完成")
        
        # 3. 创建模拟损失
        logger.info("   - 创建模拟损失...")
        # 简单的L2损失
        target = torch.zeros_like(gaussians.get_xyz)
        loss = torch.nn.functional.mse_loss(gaussians.get_xyz, target)
        logger.info(f"   - 损失值: {loss.item():.6f}")
        
        # 4. 反向传播
        logger.info("   - 执行反向传播...")
        gaussians.optimizer.zero_grad()
        loss.backward()
        
        # 5. 检查梯度 - 使用修复后的逻辑
        logger.info("   - 检查梯度（使用修复后的逻辑）...")
        has_gradients = False
        gradient_info = []
        
        # 遍历GaussianModel的参数组
        for param_group in gaussians.params_list:
            param_name = param_group["name"]
            params = param_group["params"]
            
            for i, param in enumerate(params):
                if param.grad is not None:
                    grad_norm = param.grad.abs().sum().item()
                    if grad_norm > 0:
                        has_gradients = True
                        gradient_info.append(f"{param_name}[{i}]: {grad_norm:.6f}")
                    else:
                        gradient_info.append(f"{param_name}[{i}]: 0.0 (零梯度)")
                else:
                    gradient_info.append(f"{param_name}[{i}]: None (无梯度)")
        
        if has_gradients:
            logger.info("   ✅ 梯度检查成功！")
            logger.info(f"     梯度详情: {'; '.join(gradient_info[:3])}...")
        else:
            logger.warning("   ⚠️ 无梯度检测到")
            logger.warning(f"     参数状态: {'; '.join(gradient_info[:3])}...")
        
        # 6. 优化器步骤
        logger.info("   - 执行优化器步骤...")
        gaussians.optimizer.step()
        logger.info("   ✅ 优化器步骤完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🔥 梯度检查修复验证测试")
    logger.info("🎯 验证 named_parameters() 修复是否正确")
    logger.info("=" * 60)
    
    success = test_gradient_check_fix()
    
    if success:
        logger.info("🎉 梯度检查修复验证成功！")
        logger.info("💡 可以继续运行完整的端到端测试")
    else:
        logger.info("❌ 梯度检查修复验证失败")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
