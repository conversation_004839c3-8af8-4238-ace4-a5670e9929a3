# CISM阶段2依赖包
# 安装命令: pip install -r requirements_cism.txt

# 核心深度学习框架
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Stable Diffusion相关
diffusers>=0.21.0
transformers>=4.25.0
accelerate>=0.20.0

# 图像处理
Pillow>=9.0.0
opencv-python>=4.7.0

# 科学计算
numpy>=1.21.0
scipy>=1.9.0

# 配置文件处理
PyYAML>=6.0

# 日志和监控
tensorboard>=2.10.0
wandb>=0.13.0

# 工具库
tqdm>=4.64.0
pathlib2>=2.3.0

# 可选：CLIP评估
clip-by-openai>=1.0

# 可选：LPIPS评估
lpips>=0.1.4

# 可选：性能分析
psutil>=5.9.0
GPUtil>=1.4.0

# 开发工具
pytest>=7.0.0
black>=22.0.0
flake8>=5.0.0
