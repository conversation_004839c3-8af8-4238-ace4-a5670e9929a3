<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_vector_common</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_vector_common<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Exposes min and max functions for 3 to 4 vector parameters.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad66b6441f7200db16c9f341711733c56"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad66b6441f7200db16c9f341711733c56"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#gad66b6441f7200db16c9f341711733c56">fmax</a> (vec&lt; L, T, Q &gt; const &amp;a, T b)</td></tr>
<tr class="memdesc:gad66b6441f7200db16c9f341711733c56"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if x &lt; y; otherwise, it returns x.  <a href="a00267.html#gad66b6441f7200db16c9f341711733c56">More...</a><br /></td></tr>
<tr class="separator:gad66b6441f7200db16c9f341711733c56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8df4be3f48d6717c40ea788fd30deebf"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8df4be3f48d6717c40ea788fd30deebf"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#ga8df4be3f48d6717c40ea788fd30deebf">fmax</a> (vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b)</td></tr>
<tr class="memdesc:ga8df4be3f48d6717c40ea788fd30deebf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if x &lt; y; otherwise, it returns x.  <a href="a00267.html#ga8df4be3f48d6717c40ea788fd30deebf">More...</a><br /></td></tr>
<tr class="separator:ga8df4be3f48d6717c40ea788fd30deebf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0f04ba924294dae4234ca93ede23229a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0f04ba924294dae4234ca93ede23229a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#ga0f04ba924294dae4234ca93ede23229a">fmax</a> (vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c)</td></tr>
<tr class="memdesc:ga0f04ba924294dae4234ca93ede23229a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if x &lt; y; otherwise, it returns x.  <a href="a00267.html#ga0f04ba924294dae4234ca93ede23229a">More...</a><br /></td></tr>
<tr class="separator:ga0f04ba924294dae4234ca93ede23229a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4ed3eb250ccbe17bfe8ded8a6b72d230"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4ed3eb250ccbe17bfe8ded8a6b72d230"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#ga4ed3eb250ccbe17bfe8ded8a6b72d230">fmax</a> (vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;d)</td></tr>
<tr class="memdesc:ga4ed3eb250ccbe17bfe8ded8a6b72d230"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if x &lt; y; otherwise, it returns x.  <a href="a00267.html#ga4ed3eb250ccbe17bfe8ded8a6b72d230">More...</a><br /></td></tr>
<tr class="separator:ga4ed3eb250ccbe17bfe8ded8a6b72d230"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae989203363cff9eab5093630df4fe071"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae989203363cff9eab5093630df4fe071"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#gae989203363cff9eab5093630df4fe071">fmin</a> (vec&lt; L, T, Q &gt; const &amp;x, T y)</td></tr>
<tr class="memdesc:gae989203363cff9eab5093630df4fe071"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if y &lt; x; otherwise, it returns x.  <a href="a00267.html#gae989203363cff9eab5093630df4fe071">More...</a><br /></td></tr>
<tr class="separator:gae989203363cff9eab5093630df4fe071"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7c42e93cd778c9181d1cdeea4d3e43bd"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7c42e93cd778c9181d1cdeea4d3e43bd"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#ga7c42e93cd778c9181d1cdeea4d3e43bd">fmin</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga7c42e93cd778c9181d1cdeea4d3e43bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if y &lt; x; otherwise, it returns x.  <a href="a00267.html#ga7c42e93cd778c9181d1cdeea4d3e43bd">More...</a><br /></td></tr>
<tr class="separator:ga7c42e93cd778c9181d1cdeea4d3e43bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7e62739055b49189d9355471f78fe000"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7e62739055b49189d9355471f78fe000"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#ga7e62739055b49189d9355471f78fe000">fmin</a> (vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c)</td></tr>
<tr class="memdesc:ga7e62739055b49189d9355471f78fe000"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if y &lt; x; otherwise, it returns x.  <a href="a00267.html#ga7e62739055b49189d9355471f78fe000">More...</a><br /></td></tr>
<tr class="separator:ga7e62739055b49189d9355471f78fe000"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4a543dd7d22ad1f3b8b839f808a9d93c"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4a543dd7d22ad1f3b8b839f808a9d93c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#ga4a543dd7d22ad1f3b8b839f808a9d93c">fmin</a> (vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;d)</td></tr>
<tr class="memdesc:ga4a543dd7d22ad1f3b8b839f808a9d93c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if y &lt; x; otherwise, it returns x.  <a href="a00267.html#ga4a543dd7d22ad1f3b8b839f808a9d93c">More...</a><br /></td></tr>
<tr class="separator:ga4a543dd7d22ad1f3b8b839f808a9d93c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa45d34f6a2906f8bf58ab2ba5429234d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa45d34f6a2906f8bf58ab2ba5429234d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#gaa45d34f6a2906f8bf58ab2ba5429234d">max</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;z)</td></tr>
<tr class="memdesc:gaa45d34f6a2906f8bf58ab2ba5429234d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the maximum component-wise values of 3 inputs.  <a href="a00267.html#gaa45d34f6a2906f8bf58ab2ba5429234d">More...</a><br /></td></tr>
<tr class="separator:gaa45d34f6a2906f8bf58ab2ba5429234d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94d42b8da2b4ded5ddf7504fbdc6bf10"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga94d42b8da2b4ded5ddf7504fbdc6bf10"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#ga94d42b8da2b4ded5ddf7504fbdc6bf10">max</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;z, vec&lt; L, T, Q &gt; const &amp;w)</td></tr>
<tr class="memdesc:ga94d42b8da2b4ded5ddf7504fbdc6bf10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the maximum component-wise values of 4 inputs.  <a href="a00267.html#ga94d42b8da2b4ded5ddf7504fbdc6bf10">More...</a><br /></td></tr>
<tr class="separator:ga94d42b8da2b4ded5ddf7504fbdc6bf10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3cd83d80fd4f433d8e333593ec56dddf"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3cd83d80fd4f433d8e333593ec56dddf"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#ga3cd83d80fd4f433d8e333593ec56dddf">min</a> (vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c)</td></tr>
<tr class="memdesc:ga3cd83d80fd4f433d8e333593ec56dddf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the minimum component-wise values of 3 inputs.  <a href="a00267.html#ga3cd83d80fd4f433d8e333593ec56dddf">More...</a><br /></td></tr>
<tr class="separator:ga3cd83d80fd4f433d8e333593ec56dddf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab66920ed064ab518d6859c5a889c4be4"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab66920ed064ab518d6859c5a889c4be4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00267.html#gab66920ed064ab518d6859c5a889c4be4">min</a> (vec&lt; L, T, Q &gt; const &amp;a, vec&lt; L, T, Q &gt; const &amp;b, vec&lt; L, T, Q &gt; const &amp;c, vec&lt; L, T, Q &gt; const &amp;d)</td></tr>
<tr class="memdesc:gab66920ed064ab518d6859c5a889c4be4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the minimum component-wise values of 4 inputs.  <a href="a00267.html#gab66920ed064ab518d6859c5a889c4be4">More...</a><br /></td></tr>
<tr class="separator:gab66920ed064ab518d6859c5a889c4be4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Exposes min and max functions for 3 to 4 vector parameters. </p>
<p>Include &lt;<a class="el" href="a00197.html" title="GLM_EXT_vector_common ">glm/ext/vector_common.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd>core_common </dd>
<dd>
<a class="el" href="a00258.html" title="Exposes min and max functions for 3 to 4 scalar parameters. ">GLM_EXT_scalar_common</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gad66b6441f7200db16c9f341711733c56"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fmax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if x &lt; y; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmax">std::fmax documentation</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8df4be3f48d6717c40ea788fd30deebf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fmax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if x &lt; y; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmax">std::fmax documentation</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0f04ba924294dae4234ca93ede23229a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fmax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if x &lt; y; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmax">std::fmax documentation</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4ed3eb250ccbe17bfe8ded8a6b72d230"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fmax </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if x &lt; y; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmax">std::fmax documentation</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae989203363cff9eab5093630df4fe071"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fmin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if y &lt; x; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmin">std::fmin documentation</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7c42e93cd778c9181d1cdeea4d3e43bd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fmin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if y &lt; x; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmin">std::fmin documentation</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7e62739055b49189d9355471f78fe000"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fmin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if y &lt; x; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmin">std::fmin documentation</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4a543dd7d22ad1f3b8b839f808a9d93c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fmin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if y &lt; x; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://en.cppreference.com/w/cpp/numeric/math/fmin">std::fmin documentation</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa45d34f6a2906f8bf58ab2ba5429234d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; glm::max </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the maximum component-wise values of 3 inputs. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga94d42b8da2b4ded5ddf7504fbdc6bf10"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; glm::max </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the maximum component-wise values of 4 inputs. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga3cd83d80fd4f433d8e333593ec56dddf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; glm::min </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the minimum component-wise values of 3 inputs. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gab66920ed064ab518d6859c5a889c4be4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; glm::min </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>d</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the minimum component-wise values of 4 inputs. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
