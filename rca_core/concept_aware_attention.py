"""
RCA Core: 概念感知注意力处理器
阶段3.2: 基于MultiDreamer3D的RCA机制实现概念感知注意力

本模块实现了InFusion-Enhanced项目阶段3的RCA（区域概念注意力）核心功能，
通过修改UNet的注意力机制，实现对CISM引导信号在空间上的精确调制，
显著减少概念串扰，提升编辑的精准性。

主要功能：
- 概念感知注意力处理器（ConceptAwareAttnProcessor）
- 基于2D概念权重图的空间注意力调制
- 多概念独立注意力计算和聚合
- LoRA增强支持（可选）
- 完整的向后兼容性

技术特性：
- 使用Diffusers推荐的AttnProcessor架构
- 支持标准和概念感知两种模式
- 内存优化的批量处理
- 详细的调试信息和错误处理

参考实现：
- MultiDreamer3D/guidance/sd_utils_casd.py
- concept_forward, mod_forward, base_forward函数

作者: Claude Sonnet 4 (Anthropic 最先进模型)
版本: 阶段3 - RCA机制移植与适配
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, List, Union, Tuple
import logging
import warnings

# 抑制不重要的警告
warnings.filterwarnings("ignore", category=UserWarning, module="diffusers")

try:
    from diffusers.models.attention_processor import AttnProcessor, Attention
    from diffusers.utils import logging as diffusers_logging
    DIFFUSERS_AVAILABLE = True
    
    # 设置diffusers日志级别
    diffusers_logging.set_verbosity_error()
except ImportError:
    DIFFUSERS_AVAILABLE = False
    logging.warning("Diffusers not available for RCA attention processor")

class ConceptAwareAttnProcessor:
    """
    概念感知注意力处理器
    
    基于MultiDreamer3D的concept_forward实现，支持：
    1. 标准注意力计算（base_forward模式）
    2. 概念感知注意力计算（concept_forward模式）
    3. 2D概念权重图的空间调制
    4. 多概念独立注意力聚合
    """
    
    def __init__(
        self,
        enable_lora: bool = False,
        lora_scale: float = 1.0,
        debug_mode: bool = False
    ):
        """
        初始化概念感知注意力处理器
        
        Args:
            enable_lora: 是否启用LoRA增强
            lora_scale: LoRA缩放因子
            debug_mode: 是否启用调试模式
        """
        self.enable_lora = enable_lora
        self.lora_scale = lora_scale
        self.debug_mode = debug_mode
        
        if not DIFFUSERS_AVAILABLE:
            raise ImportError("Diffusers package is required for RCA attention processor")
        
        logging.info(f"ConceptAwareAttnProcessor initialized (LoRA: {enable_lora}, Debug: {debug_mode})")
    
    def __call__(
        self,
        attn: Attention,
        hidden_states: torch.FloatTensor,
        encoder_hidden_states: Optional[torch.FloatTensor] = None,
        attention_mask: Optional[torch.FloatTensor] = None,
        temb: Optional[torch.FloatTensor] = None,
        scale: float = 1.0,
        **kwargs
    ) -> torch.FloatTensor:
        """
        概念感知注意力处理器主入口
        
        根据cross_attention_kwargs中的concept_forward标志，
        动态选择标准注意力或概念感知注意力
        
        Args:
            attn: Diffusers Attention模块
            hidden_states: UNet中间特征 [batch_size, channels, height, width]
            encoder_hidden_states: 文本嵌入 [batch_size, seq_len, embed_dim]
            attention_mask: 注意力掩码
            temb: 时间嵌入
            scale: 缩放因子
            **kwargs: 包含cross_attention_kwargs
            
        Returns:
            attn_output: 注意力输出 [batch_size, channels, height, width]
        """
        # 检查是否启用概念感知注意力
        concept_forward = kwargs.get("concept_forward", False)
        
        if self.debug_mode:
            logging.debug(f"ConceptAwareAttnProcessor called with concept_forward={concept_forward}")
        
        if concept_forward:
            return self._concept_forward(
                attn, hidden_states, encoder_hidden_states, 
                attention_mask, temb, scale, **kwargs
            )
        else:
            return self._base_forward(
                attn, hidden_states, encoder_hidden_states,
                attention_mask, temb, scale, **kwargs
            )
    
    def _base_forward(
        self,
        attn: Attention,
        hidden_states: torch.FloatTensor,
        encoder_hidden_states: Optional[torch.FloatTensor] = None,
        attention_mask: Optional[torch.FloatTensor] = None,
        temb: Optional[torch.FloatTensor] = None,
        scale: float = 1.0,
        **kwargs
    ) -> torch.FloatTensor:
        """
        标准注意力计算（base_forward模式）
        
        完全等价于原生Diffusers Attention的forward方法，
        确保向后兼容性
        """
        if self.debug_mode:
            logging.debug("Using base_forward (standard attention)")
        
        # 使用标准AttnProcessor
        standard_processor = AttnProcessor()
        return standard_processor(
            attn, hidden_states, encoder_hidden_states,
            attention_mask, temb, scale
        )
    
    def _concept_forward(
        self,
        attn: Attention,
        hidden_states: torch.FloatTensor,
        encoder_hidden_states: Optional[torch.FloatTensor] = None,
        attention_mask: Optional[torch.FloatTensor] = None,
        temb: Optional[torch.FloatTensor] = None,
        scale: float = 1.0,
        **kwargs
    ) -> torch.FloatTensor:
        """
        概念感知注意力计算（concept_forward模式）
        
        基于MultiDreamer3D的concept_forward实现：
        1. 计算基础注意力
        2. 计算背景区域注意力
        3. 计算各概念区域注意力
        4. 按掩码权重聚合结果
        
        Args:
            **kwargs必须包含：
                mask: 概念权重图字典 {"bg": [B,1,H,W], "concept0": [B,1,H,W], ...}
                adapter_names: LoRA适配器名称列表 ["concept0", "concept1", ...]
                text_embeddings: 分概念文本嵌入 {"base": [B,N,C], "concept0": [B,N,C], ...}
                lora_scale: LoRA缩放因子
        """
        if self.debug_mode:
            logging.debug("Using concept_forward (RCA attention)")
        
        # 提取RCA参数
        concept_masks = kwargs.get("mask", {})
        adapter_names = kwargs.get("adapter_names", [])
        text_embeddings_dict = kwargs.get("text_embeddings", {})
        lora_scale = kwargs.get("lora_scale", self.lora_scale)
        
        # 验证输入参数
        if not concept_masks:
            logging.warning("No concept masks provided, falling back to base_forward")
            return self._base_forward(attn, hidden_states, encoder_hidden_states, attention_mask, temb, scale)
        
        if not text_embeddings_dict:
            logging.warning("No text embeddings dict provided, falling back to base_forward")
            return self._base_forward(attn, hidden_states, encoder_hidden_states, attention_mask, temb, scale)
        
        # 获取输入维度
        batch_size, channels, height, width = hidden_states.shape
        
        if self.debug_mode:
            logging.debug(f"Concept forward input shape: {hidden_states.shape}")
            logging.debug(f"Available masks: {list(concept_masks.keys())}")
            logging.debug(f"Available text embeddings: {list(text_embeddings_dict.keys())}")
        
        # 重塑hidden_states为注意力计算格式
        # [B, C, H, W] -> [B, H*W, C]
        hidden_states_reshaped = hidden_states.view(batch_size, channels, -1).transpose(1, 2)
        
        # 存储各概念的注意力结果
        attention_results = {}
        
        # 1. 计算基础注意力（使用base文本嵌入）
        base_text_embeddings = text_embeddings_dict.get("base", encoder_hidden_states)
        if base_text_embeddings is not None:
            base_attention = self._compute_attention(
                attn, hidden_states_reshaped, base_text_embeddings, attention_mask
            )
            attention_results["base"] = base_attention
            
            if self.debug_mode:
                logging.debug(f"Computed base attention: {base_attention.shape}")
        
        # 2. 计算背景注意力
        if "bg" in concept_masks and "base" in text_embeddings_dict:
            bg_mask = concept_masks["bg"]  # [B, 1, H, W]
            bg_attention = self._compute_masked_attention(
                attn, hidden_states_reshaped, text_embeddings_dict["base"], 
                bg_mask, attention_mask
            )
            attention_results["bg"] = bg_attention
            
            if self.debug_mode:
                logging.debug(f"Computed background attention: {bg_attention.shape}")
        
        # 3. 计算各概念注意力
        for adapter_name in adapter_names:
            concept_key = adapter_name  # 例如 "concept0"
            
            if concept_key in concept_masks and concept_key in text_embeddings_dict:
                concept_mask = concept_masks[concept_key]  # [B, 1, H, W]
                concept_text_embeddings = text_embeddings_dict[concept_key]
                
                # 计算概念专属的K, V（支持LoRA）
                concept_kv = self._compute_concept_kv(
                    attn, concept_text_embeddings, adapter_name, lora_scale
                )
                
                # 计算概念注意力
                concept_attention = self._compute_masked_attention_with_kv(
                    attn, hidden_states_reshaped, concept_kv, 
                    concept_mask, attention_mask
                )
                attention_results[concept_key] = concept_attention
                
                if self.debug_mode:
                    logging.debug(f"Computed {concept_key} attention: {concept_attention.shape}")
        
        # 4. 聚合所有注意力结果
        final_attention = self._aggregate_attention_results(
            attention_results, concept_masks, hidden_states.shape
        )
        
        # 重塑回原始格式 [B, H*W, C] -> [B, C, H, W]
        final_output = final_attention.transpose(1, 2).view(batch_size, channels, height, width)
        
        if self.debug_mode:
            logging.debug(f"Final concept attention output shape: {final_output.shape}")

        return final_output

    def _compute_attention(
        self,
        attn: Attention,
        hidden_states: torch.FloatTensor,
        encoder_hidden_states: torch.FloatTensor,
        attention_mask: Optional[torch.FloatTensor] = None
    ) -> torch.FloatTensor:
        """
        计算标准注意力

        Args:
            attn: Attention模块
            hidden_states: 查询特征 [B, H*W, C]
            encoder_hidden_states: 键值特征 [B, N, C]
            attention_mask: 注意力掩码

        Returns:
            attention_output: 注意力输出 [B, H*W, C]
        """
        batch_size, sequence_length, _ = hidden_states.shape

        # 计算Q, K, V
        query = attn.to_q(hidden_states)
        key = attn.to_k(encoder_hidden_states)
        value = attn.to_v(encoder_hidden_states)

        # 重塑为多头注意力格式
        query = attn.head_to_batch_dim(query)
        key = attn.head_to_batch_dim(key)
        value = attn.head_to_batch_dim(value)

        # 计算注意力权重
        attention_probs = attn.get_attention_scores(query, key, attention_mask)

        # 应用注意力权重
        hidden_states = torch.bmm(attention_probs, value)
        hidden_states = attn.batch_to_head_dim(hidden_states)

        # 输出投影
        hidden_states = attn.to_out[0](hidden_states)
        hidden_states = attn.to_out[1](hidden_states)

        return hidden_states

    def _compute_masked_attention(
        self,
        attn: Attention,
        hidden_states: torch.FloatTensor,
        encoder_hidden_states: torch.FloatTensor,
        spatial_mask: torch.FloatTensor,
        attention_mask: Optional[torch.FloatTensor] = None
    ) -> torch.FloatTensor:
        """
        计算空间掩码注意力

        Args:
            attn: Attention模块
            hidden_states: 查询特征 [B, H*W, C]
            encoder_hidden_states: 键值特征 [B, N, C]
            spatial_mask: 空间掩码 [B, 1, H, W]
            attention_mask: 注意力掩码

        Returns:
            masked_attention_output: 掩码注意力输出 [B, H*W, C]
        """
        batch_size, sequence_length, channels = hidden_states.shape

        # 重塑空间掩码 [B, 1, H, W] -> [B, H*W, 1]
        height_width = spatial_mask.shape[2] * spatial_mask.shape[3]
        mask_reshaped = spatial_mask.view(batch_size, 1, height_width).transpose(1, 2)

        # 应用空间掩码到查询特征
        masked_hidden_states = hidden_states * mask_reshaped

        # 计算注意力
        attention_output = self._compute_attention(
            attn, masked_hidden_states, encoder_hidden_states, attention_mask
        )

        return attention_output

    def _compute_concept_kv(
        self,
        attn: Attention,
        concept_text_embeddings: torch.FloatTensor,
        adapter_name: str,
        lora_scale: float
    ) -> Dict[str, torch.FloatTensor]:
        """
        计算概念专属的K, V（支持LoRA增强）

        Args:
            attn: Attention模块
            concept_text_embeddings: 概念文本嵌入 [B, N, C]
            adapter_name: LoRA适配器名称
            lora_scale: LoRA缩放因子

        Returns:
            concept_kv: {"key": K, "value": V}
        """
        if self.enable_lora:
            # LoRA增强的K, V计算
            # 注意：这里需要根据实际的LoRA实现进行调整
            try:
                # 尝试获取LoRA层
                if hasattr(attn.to_k, 'lora_A') and adapter_name in attn.to_k.lora_A:
                    # 基础Key计算
                    base_key = attn.to_k.base_layer(concept_text_embeddings)

                    # LoRA增强
                    lora_A_k = attn.to_k.lora_A[adapter_name]
                    lora_B_k = attn.to_k.lora_B[adapter_name]
                    lora_delta_k = lora_B_k(lora_A_k(concept_text_embeddings)) * lora_scale

                    concept_key = base_key + lora_delta_k
                else:
                    # 回退到标准计算
                    concept_key = attn.to_k(concept_text_embeddings)

                if hasattr(attn.to_v, 'lora_A') and adapter_name in attn.to_v.lora_A:
                    # 基础Value计算
                    base_value = attn.to_v.base_layer(concept_text_embeddings)

                    # LoRA增强
                    lora_A_v = attn.to_v.lora_A[adapter_name]
                    lora_B_v = attn.to_v.lora_B[adapter_name]
                    lora_delta_v = lora_B_v(lora_A_v(concept_text_embeddings)) * lora_scale

                    concept_value = base_value + lora_delta_v
                else:
                    # 回退到标准计算
                    concept_value = attn.to_v(concept_text_embeddings)

            except Exception as e:
                if self.debug_mode:
                    logging.warning(f"LoRA computation failed for {adapter_name}: {e}, using standard K/V")
                concept_key = attn.to_k(concept_text_embeddings)
                concept_value = attn.to_v(concept_text_embeddings)
        else:
            # 标准K, V计算
            concept_key = attn.to_k(concept_text_embeddings)
            concept_value = attn.to_v(concept_text_embeddings)

        return {
            "key": concept_key,
            "value": concept_value
        }

    def _compute_masked_attention_with_kv(
        self,
        attn: Attention,
        hidden_states: torch.FloatTensor,
        concept_kv: Dict[str, torch.FloatTensor],
        spatial_mask: torch.FloatTensor,
        attention_mask: Optional[torch.FloatTensor] = None
    ) -> torch.FloatTensor:
        """
        使用概念专属K/V计算空间掩码注意力

        Args:
            attn: Attention模块
            hidden_states: 查询特征 [B, H*W, C]
            concept_kv: 概念K/V字典 {"key": K, "value": V}
            spatial_mask: 空间掩码 [B, 1, H, W]
            attention_mask: 注意力掩码

        Returns:
            masked_attention_output: 掩码注意力输出 [B, H*W, C]
        """
        batch_size, sequence_length, channels = hidden_states.shape

        # 重塑空间掩码 [B, 1, H, W] -> [B, H*W, 1]
        height_width = spatial_mask.shape[2] * spatial_mask.shape[3]
        mask_reshaped = spatial_mask.view(batch_size, 1, height_width).transpose(1, 2)

        # 应用空间掩码到查询特征
        masked_hidden_states = hidden_states * mask_reshaped

        # 计算Q
        query = attn.to_q(masked_hidden_states)

        # 使用概念专属的K, V
        key = concept_kv["key"]
        value = concept_kv["value"]

        # 重塑为多头注意力格式
        query = attn.head_to_batch_dim(query)
        key = attn.head_to_batch_dim(key)
        value = attn.head_to_batch_dim(value)

        # 计算注意力权重
        attention_probs = attn.get_attention_scores(query, key, attention_mask)

        # 应用注意力权重
        hidden_states_out = torch.bmm(attention_probs, value)
        hidden_states_out = attn.batch_to_head_dim(hidden_states_out)

        # 输出投影
        hidden_states_out = attn.to_out[0](hidden_states_out)
        hidden_states_out = attn.to_out[1](hidden_states_out)

        return hidden_states_out

    def _aggregate_attention_results(
        self,
        attention_results: Dict[str, torch.FloatTensor],
        concept_masks: Dict[str, torch.FloatTensor],
        original_shape: Tuple[int, int, int, int]
    ) -> torch.FloatTensor:
        """
        聚合所有注意力结果

        基于MultiDreamer3D的聚合策略：
        1. 使用背景注意力作为基础
        2. 按概念掩码权重叠加各概念注意力
        3. 确保空间一致性

        Args:
            attention_results: 注意力结果字典 {"base": tensor, "bg": tensor, "concept0": tensor, ...}
            concept_masks: 概念掩码字典 {"bg": [B,1,H,W], "concept0": [B,1,H,W], ...}
            original_shape: 原始特征形状 (B, C, H, W)

        Returns:
            aggregated_attention: 聚合的注意力输出 [B, H*W, C]
        """
        batch_size, channels, height, width = original_shape
        sequence_length = height * width

        # 初始化聚合结果
        if "base" in attention_results:
            # 使用基础注意力作为起点
            aggregated = attention_results["base"].clone()
        elif "bg" in attention_results:
            # 使用背景注意力作为起点
            aggregated = attention_results["bg"].clone()
        else:
            # 创建零张量作为起点
            aggregated = torch.zeros(
                batch_size, sequence_length, channels,
                device=list(attention_results.values())[0].device,
                dtype=list(attention_results.values())[0].dtype
            )

        if self.debug_mode:
            logging.debug(f"Starting aggregation with base shape: {aggregated.shape}")

        # 聚合背景和概念注意力
        total_weight = torch.zeros(batch_size, sequence_length, 1, device=aggregated.device)
        weighted_sum = torch.zeros_like(aggregated)

        # 处理背景
        if "bg" in attention_results and "bg" in concept_masks:
            bg_mask = concept_masks["bg"]  # [B, 1, H, W]
            bg_mask_reshaped = bg_mask.view(batch_size, 1, -1).transpose(1, 2)  # [B, H*W, 1]

            weighted_sum += attention_results["bg"] * bg_mask_reshaped
            total_weight += bg_mask_reshaped

            if self.debug_mode:
                logging.debug(f"Added background attention with weight sum: {bg_mask_reshaped.sum()}")

        # 处理各个概念
        for concept_key, concept_attention in attention_results.items():
            if concept_key in ["base", "bg"]:
                continue  # 跳过已处理的

            if concept_key in concept_masks:
                concept_mask = concept_masks[concept_key]  # [B, 1, H, W]
                concept_mask_reshaped = concept_mask.view(batch_size, 1, -1).transpose(1, 2)  # [B, H*W, 1]

                weighted_sum += concept_attention * concept_mask_reshaped
                total_weight += concept_mask_reshaped

                if self.debug_mode:
                    logging.debug(f"Added {concept_key} attention with weight sum: {concept_mask_reshaped.sum()}")

        # 归一化聚合结果
        # 避免除零
        total_weight = torch.clamp(total_weight, min=1e-8)
        final_aggregated = weighted_sum / total_weight

        # 对于权重为零的区域，使用基础注意力
        zero_weight_mask = (total_weight < 1e-6).expand_as(final_aggregated)
        if "base" in attention_results:
            final_aggregated = torch.where(zero_weight_mask, attention_results["base"], final_aggregated)

        if self.debug_mode:
            logging.debug(f"Final aggregated attention shape: {final_aggregated.shape}")
            logging.debug(f"Total weight range: [{total_weight.min():.6f}, {total_weight.max():.6f}]")

        return final_aggregated
