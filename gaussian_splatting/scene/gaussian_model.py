#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#
import torchvision
import torch
import numpy as np
from utils.general_utils import inverse_sigmoid, get_expon_lr_func, build_rotation
from torch import nn
import os
from utils.system_utils import mkdir_p
from plyfile import PlyData, PlyElement
from utils.sh_utils import RGB2SH
from simple_knn._C import distCUDA2
from utils.graphics_utils import BasicPointCloud
from utils.general_utils import strip_symmetric, build_scaling_rotation
from pathlib import Path
from PIL import Image

class GaussianModel:

    def setup_functions(self):
        def build_covariance_from_scaling_rotation(scaling, scaling_modifier, rotation):
            L = build_scaling_rotation(scaling_modifier * scaling, rotation)
            actual_covariance = L @ L.transpose(1, 2)
            symm = strip_symmetric(actual_covariance)
            return symm
        
        self.scaling_activation = torch.exp
        self.scaling_inverse_activation = torch.log

        self.covariance_activation = build_covariance_from_scaling_rotation

        self.opacity_activation = torch.sigmoid
        self.inverse_opacity_activation = inverse_sigmoid

        self.rotation_activation = torch.nn.functional.normalize
        self.old_xyz = None
        self.old_features_dc=None

    def __init__(self, sh_degree : int,):
        self.active_sh_degree = 0
        self.max_sh_degree = sh_degree
        self._xyz = torch.empty(0)
        self._features_dc = torch.empty(0)
        self._features_rest = torch.empty(0)
        self._scaling = torch.empty(0)
        self._rotation = torch.empty(0)
        self._opacity = torch.empty(0)
        # ✅ 新增concept_membership支持 (方案B)
        self._concept_membership = torch.empty(0, device="cuda")  # (N, num_concepts)
        self.num_concepts = 3  # 背景、修复、边界
        self.max_radii2D = torch.empty(0)
        self.xyz_gradient_accum = torch.empty(0)
        self.denom = torch.empty(0)
        self.optimizer = None
        self.percent_dense = 0
        self.spatial_lr_scale = 0
        self.setup_functions()

    def capture(self):
        return (
            self.active_sh_degree,
            self._xyz,
            self._features_dc,
            self._features_rest,
            self._scaling,
            self._rotation,
            self._opacity,
            # ✅ 更新为concept_membership支持
            self._concept_membership,
            self.max_radii2D,
            self.xyz_gradient_accum,
            self.denom,
            self.optimizer.state_dict(),
            self.spatial_lr_scale,
        )
    
    def restore(self, model_args, training_args):
        (self.active_sh_degree,
        self._xyz,
        self._features_dc,
        self._features_rest,
        self._scaling,
        self._rotation,
        self._opacity,
        # ✅ 更新为concept_membership支持
        self._concept_membership,
        self.max_radii2D,
        xyz_gradient_accum,
        denom,
        opt_dict,
        self.spatial_lr_scale) = model_args
        self.training_setup(training_args)
        self.xyz_gradient_accum = xyz_gradient_accum
        self.denom = denom
        self.optimizer.load_state_dict(opt_dict)

    @property
    def get_scaling(self):
        return self.scaling_activation(self._scaling)
    
    @property
    def get_rotation(self):
        return self.rotation_activation(self._rotation)
    
    @property
    def get_xyz(self):
        return self._xyz
    
    @property
    def get_features(self):
        features_dc = self._features_dc
        features_rest = self._features_rest
        return torch.cat((features_dc, features_rest), dim=1)
    
    @property
    def get_opacity(self):
        return self.opacity_activation(self._opacity)

    # ✅ 更新concept属性访问器 (方案B)
    @property
    def get_concept_membership(self):
        return self._concept_membership

    @property 
    def get_dominant_concept_id(self):
        """获取每个点的主导概念ID (离散化)"""
        if self._concept_membership.numel() == 0:
            return torch.empty(0, dtype=torch.long, device="cuda")
        return torch.argmax(self._concept_membership, dim=1)

    @property
    def get_concept_confidence(self):
        """从概念隶属度获取置信度 (最大值)"""
        if self._concept_membership.numel() == 0:
            return torch.empty(0, dtype=torch.float, device="cuda")
        return torch.max(self._concept_membership, dim=1)[0]

    def get_covariance(self, scaling_modifier = 1):
        return self.covariance_activation(self.get_scaling, scaling_modifier, self._rotation)

    def oneupSHdegree(self):
        if self.active_sh_degree < self.max_sh_degree:
            self.active_sh_degree += 1

    def create_from_pcd(self, pcd : BasicPointCloud, spatial_lr_scale : float):
        self.spatial_lr_scale = spatial_lr_scale
        fused_point_cloud = torch.tensor(np.asarray(pcd.points)).float().cuda()
        fused_color = RGB2SH(torch.tensor(np.asarray(pcd.colors)).float().cuda())
        features = torch.zeros((fused_color.shape[0], 3, (self.max_sh_degree + 1) ** 2)).float().cuda()
        features[:, :3, 0 ] = fused_color
        features[:, 3:, 1:] = 0.0

        print("Number of points at initialisation : ", fused_point_cloud.shape[0])

        dist2 = torch.clamp_min(distCUDA2(fused_point_cloud).float().cuda(), 0.0000001)
        scales = torch.log(torch.sqrt(dist2))[...,None].repeat(1, 3)
        rots = torch.zeros((fused_point_cloud.shape[0], 4), device="cuda")
        rots[:, 0] = 1

        opacities = inverse_sigmoid(0.1 * torch.ones((fused_point_cloud.shape[0], 1), dtype=torch.float, device="cuda"))

        self._xyz = nn.Parameter(fused_point_cloud.requires_grad_(True))
        self._features_dc = nn.Parameter(features[:,:,0:1].transpose(1, 2).contiguous().requires_grad_(True))
        self._features_rest = nn.Parameter(features[:,:,1:].transpose(1, 2).contiguous().requires_grad_(True))
        self._scaling = nn.Parameter(scales.requires_grad_(True))
        self._rotation = nn.Parameter(rots.requires_grad_(True))
        self._opacity = nn.Parameter(opacities.requires_grad_(True))
        self.max_radii2D = torch.zeros((self.get_xyz.shape[0]), device="cuda")

        # 初始化 concept_membership (N, num_concepts)，默认为背景
        # 确保在创建时就设置 requires_grad=False
        num_points = fused_point_cloud.shape[0]
        concept_membership_init = torch.zeros((num_points, self.num_concepts), device="cuda")
        concept_membership_init[:, 0] = 1.0  # 默认为背景概念
        self._concept_membership = torch.nn.Parameter(concept_membership_init, requires_grad=False)
        print(f"   Initialized _concept_membership with shape: {self._concept_membership.shape}, requires_grad: {self._concept_membership.requires_grad}")

        # 🔥 新增：concept映射状态跟踪
        self._concept_mapping_initialized = False
        self._concept_mapping_stats = {
            'total_mapped_points': 0,
            'concept_distribution': {},
            'mapping_quality_score': 0.0
        }

    def training_setup(self, training_args):
        self.percent_dense = training_args.percent_dense
        self.xyz_gradient_accum = torch.zeros((self.get_xyz.shape[0], 1), device="cuda")
        self.denom = torch.zeros((self.get_xyz.shape[0], 1), device="cuda")
        scale = 1
        scale_small = 1
        l = [
            {'params': [self._xyz], 'lr': training_args.position_lr_init * self.spatial_lr_scale * scale_small, "name": "xyz"},
            {'params': [self._features_dc], 'lr': training_args.feature_lr* scale, "name": "f_dc"},
            {'params': [self._features_rest], 'lr': training_args.feature_lr * scale/ 20.0, "name": "f_rest"},
            {'params': [self._opacity], 'lr': training_args.opacity_lr* scale, "name": "opacity"},
            {'params': [self._scaling], 'lr': training_args.scaling_lr* scale, "name": "scaling"},
            {'params': [self._rotation], 'lr': training_args.rotation_lr* scale_small, "name": "rotation"},
            # ✅ 移除concept_membership - 它现在是固定的语义布局
        ]
        self.params_list = l
        self.optimizer = torch.optim.Adam(l, lr=0.0, eps=1e-15)
        self.xyz_scheduler_args = get_expon_lr_func(lr_init=training_args.position_lr_init*self.spatial_lr_scale,
                                                    lr_final=training_args.position_lr_final*self.spatial_lr_scale,
                                                    lr_delay_mult=training_args.position_lr_delay_mult,
                                                    max_steps=training_args.iterations)

    def update_learning_rate(self, iteration):
        ''' Learning rate scheduling per step '''
        for param_group in self.optimizer.param_groups:
            if param_group["name"] == "xyz":
                lr = self.xyz_scheduler_args(iteration)
                param_group['lr'] = lr
                return lr

    def update_feature_learning_rate(self, iteration):
        ''' Learning rate scheduling per step '''
        for param_group in self.optimizer.param_groups:
            if param_group["name"] == "f_dc":
                lr = self.feature_scheduler_args(iteration)
                param_group['lr'] = lr
                return lr

    def update_rotation_learning_rate(self, iteration):
        ''' Learning rate scheduling per step '''
        for param_group in self.optimizer.param_groups:
            if param_group["name"] == "rotation":
                lr = self.rotation_scheduler_args(iteration)
                param_group['lr'] = lr
                return lr

    def update_scaling_learning_rate(self, iteration):
        ''' Learning rate scheduling per step '''
        for param_group in self.optimizer.param_groups:
            if param_group["name"] == "scaling":
                lr = self.scaling_scheduler_args(iteration)
                param_group['lr'] = lr
                return lr
    def construct_list_of_attributes(self):
        l = ['x', 'y', 'z', 'nx', 'ny', 'nz']
        # All channels except the 3 DC
        for i in range(self._features_dc.shape[1]*self._features_dc.shape[2]):
            l.append('f_dc_{}'.format(i))
        for i in range(self._features_rest.shape[1]*self._features_rest.shape[2]):
            l.append('f_rest_{}'.format(i))
        l.append('opacity')
        for i in range(self._scaling.shape[1]):
            l.append('scale_{}'.format(i))
        for i in range(self._rotation.shape[1]):
            l.append('rot_{}'.format(i))
        # ✅ 新增concept_membership属性 (方案B)
        for i in range(self.num_concepts):
            l.append(f'concept_{i}')
        return l

    def save_ply(self, path):
        mkdir_p(os.path.dirname(path))

        xyz = self._xyz.detach().cpu().numpy()
        normals = np.zeros_like(xyz)
        f_dc = self._features_dc.detach().transpose(1, 2).flatten(start_dim=1).contiguous().cpu().numpy()
        f_rest = self._features_rest.detach().transpose(1, 2).flatten(start_dim=1).contiguous().cpu().numpy()
        opacities = self._opacity.detach().cpu().numpy()
        scale = self._scaling.detach().cpu().numpy()
        rotation = self._rotation.detach().cpu().numpy()
        
        # ✅ 新增concept_membership数据准备 (方案B)
        concept_membership = self._concept_membership.detach().cpu().numpy()  # (N, num_concepts)

        dtype_full = [(attribute, 'f4') for attribute in self.construct_list_of_attributes()]

        elements = np.empty(xyz.shape[0], dtype=dtype_full)
        # ✅ 更新attributes拼接，添加concept_membership数据
        attributes = np.concatenate((xyz, normals, f_dc, f_rest, opacities, scale, rotation, concept_membership), axis=1)
        elements[:] = list(map(tuple, attributes))
        el = PlyElement.describe(elements, 'vertex')
        PlyData([el]).write(path)

    def reset_opacity(self):
        opacities_new = inverse_sigmoid(torch.min(self.get_opacity, torch.ones_like(self.get_opacity)*0.01))
        optimizable_tensors = self.replace_tensor_to_optimizer(opacities_new, "opacity")
        self._opacity = optimizable_tensors["opacity"]

    def load_ply(self, path):
        plydata = PlyData.read(path)

        xyz = np.stack((np.asarray(plydata.elements[0]["x"]),
                        np.asarray(plydata.elements[0]["y"]),
                        np.asarray(plydata.elements[0]["z"])),  axis=1)
        opacities = np.asarray(plydata.elements[0]["opacity"])[..., np.newaxis]

        features_dc = np.zeros((xyz.shape[0], 3, 1))
        features_dc[:, 0, 0] = np.asarray(plydata.elements[0]["f_dc_0"])
        features_dc[:, 1, 0] = np.asarray(plydata.elements[0]["f_dc_1"])
        features_dc[:, 2, 0] = np.asarray(plydata.elements[0]["f_dc_2"])

        extra_f_names = [p.name for p in plydata.elements[0].properties if p.name.startswith("f_rest_")]
        extra_f_names = sorted(extra_f_names, key = lambda x: int(x.split('_')[-1]))
        assert len(extra_f_names)==3*(self.max_sh_degree + 1) ** 2 - 3
        features_extra = np.zeros((xyz.shape[0], len(extra_f_names)))
        for idx, attr_name in enumerate(extra_f_names):
            features_extra[:, idx] = np.asarray(plydata.elements[0][attr_name])
        # Reshape (P,F*SH_coeffs) to (P, F, SH_coeffs except DC)
        features_extra = features_extra.reshape((features_extra.shape[0], 3, (self.max_sh_degree + 1) ** 2 - 1))

        scale_names = [p.name for p in plydata.elements[0].properties if p.name.startswith("scale_")]
        scale_names = sorted(scale_names, key = lambda x: int(x.split('_')[-1]))
        scales = np.zeros((xyz.shape[0], len(scale_names)))
        for idx, attr_name in enumerate(scale_names):
            scales[:, idx] = np.asarray(plydata.elements[0][attr_name])

        rot_names = [p.name for p in plydata.elements[0].properties if p.name.startswith("rot")]
        rot_names = sorted(rot_names, key = lambda x: int(x.split('_')[-1]))
        rots = np.zeros((xyz.shape[0], len(rot_names)))
        for idx, attr_name in enumerate(rot_names):
            rots[:, idx] = np.asarray(plydata.elements[0][attr_name])

        self._xyz = nn.Parameter(torch.tensor(xyz, dtype=torch.float, device="cuda").requires_grad_(True))
        self._features_dc = nn.Parameter(torch.tensor(features_dc, dtype=torch.float, device="cuda").transpose(1, 2).contiguous().requires_grad_(True))
        self._features_rest = nn.Parameter(torch.tensor(features_extra, dtype=torch.float, device="cuda").transpose(1, 2).contiguous().requires_grad_(True))
        self._opacity = nn.Parameter(torch.tensor(opacities, dtype=torch.float, device="cuda").requires_grad_(True))
        self._scaling = nn.Parameter(torch.tensor(scales, dtype=torch.float, device="cuda").requires_grad_(True))
        self._rotation = nn.Parameter(torch.tensor(rots, dtype=torch.float, device="cuda").requires_grad_(True))

        # ✅ 新增concept_membership数据加载（向后兼容，固定张量版本）
        num_points = xyz.shape[0]
        
        # 检查PLY文件中是否包含concept_membership信息
        property_names = [p.name for p in plydata.elements[0].properties]
        concept_attrs = [name for name in property_names if name.startswith('concept_')]
        
        if len(concept_attrs) >= self.num_concepts:
            # 从PLY文件加载concept_membership数据
            concept_membership_data = np.zeros((num_points, self.num_concepts), dtype=np.float32)
            for i in range(self.num_concepts):
                attr_name = f'concept_{i}'
                if attr_name in property_names:
                    concept_membership_data[:, i] = np.asarray(plydata.elements[0][attr_name])
            
            # ✅ 修复：作为固定张量，不需要梯度
            self._concept_membership = torch.tensor(concept_membership_data, dtype=torch.float, device="cuda").requires_grad_(False)
            
            print(f"✅ 从PLY文件加载concept_membership数据: {concept_membership_data.shape}")
            
        elif 'concept_id' in property_names:
            # 向后兼容：从旧的concept_id转换为concept_membership
            print("🔄 检测到旧格式concept_id，转换为concept_membership")
            concept_ids_data = np.asarray(plydata.elements[0]["concept_id"]).astype(np.int64)
            
            # 转换为one-hot编码
            concept_membership_data = np.zeros((num_points, self.num_concepts), dtype=np.float32)
            for i, concept_id in enumerate(concept_ids_data):
                if 0 <= concept_id < self.num_concepts:
                    concept_membership_data[i, concept_id] = 1.0
                else:
                    concept_membership_data[i, 0] = 1.0  # 默认背景
            
            self._concept_membership = torch.tensor(concept_membership_data, dtype=torch.float, device="cuda").requires_grad_(False)
            
            print(f"✅ 从concept_id转换为concept_membership: {concept_membership_data.shape}")
            
        else:
            # 完全向后兼容：PLY文件不包含任何concept信息，使用默认初始化
            print("⚠️ PLY文件不包含concept信息，使用默认初始化")
            concept_membership = torch.zeros((num_points, self.num_concepts), dtype=torch.float, device="cuda")
            concept_membership[:, 0] = 1.0  # 默认背景概念
            self._concept_membership = concept_membership.requires_grad_(False)

        self.active_sh_degree = self.max_sh_degree
        self.max_radii2D = torch.zeros((self.get_xyz.shape[0]), device="cuda")

    def replace_tensor_to_optimizer(self, tensor, name):
        optimizable_tensors = {}
        for group in self.optimizer.param_groups:
            if group["name"] == name:
                stored_state = self.optimizer.state.get(group['params'][0], None)
                stored_state["exp_avg"] = torch.zeros_like(tensor)
                stored_state["exp_avg_sq"] = torch.zeros_like(tensor)

                del self.optimizer.state[group['params'][0]]
                group["params"][0] = nn.Parameter(tensor.requires_grad_(True))
                self.optimizer.state[group['params'][0]] = stored_state

                optimizable_tensors[group["name"]] = group["params"][0]
        return optimizable_tensors

    def _prune_optimizer(self, mask):
        optimizable_tensors = {}
        for group in self.optimizer.param_groups:
            stored_state = self.optimizer.state.get(group['params'][0], None)
            if stored_state is not None:
                stored_state["exp_avg"] = stored_state["exp_avg"][mask]
                stored_state["exp_avg_sq"] = stored_state["exp_avg_sq"][mask]

                del self.optimizer.state[group['params'][0]]
                group["params"][0] = nn.Parameter((group["params"][0][mask].requires_grad_(True)))
                self.optimizer.state[group['params'][0]] = stored_state

                optimizable_tensors[group["name"]] = group["params"][0]
            else:
                group["params"][0] = nn.Parameter(group["params"][0][mask].requires_grad_(True))
                optimizable_tensors[group["name"]] = group["params"][0]
        return optimizable_tensors

    def prune_points(self, mask):
        valid_points_mask = ~mask
        optimizable_tensors = self._prune_optimizer(valid_points_mask)

        self._xyz = optimizable_tensors["xyz"]
        self._features_dc = optimizable_tensors["f_dc"]
        self._features_rest = optimizable_tensors["f_rest"]
        self._opacity = optimizable_tensors["opacity"]
        self._scaling = optimizable_tensors["scaling"]
        self._rotation = optimizable_tensors["rotation"]

        # ✅ 修复：concept_membership现在是普通张量，直接索引即可
        self._concept_membership = self._concept_membership[valid_points_mask]

        self.xyz_gradient_accum = self.xyz_gradient_accum[valid_points_mask]
        self.denom = self.denom[valid_points_mask]
        self.max_radii2D = self.max_radii2D[valid_points_mask]

    
    def cat_tensors_to_optimizer(self, tensors_dict):
        optimizable_tensors = {}
        for group in self.optimizer.param_groups:
            assert len(group["params"]) == 1
            extension_tensor = tensors_dict[group["name"]]
            stored_state = self.optimizer.state.get(group['params'][0], None)
            if stored_state is not None:

                stored_state["exp_avg"] = torch.cat((stored_state["exp_avg"], torch.zeros_like(extension_tensor)), dim=0)
                stored_state["exp_avg_sq"] = torch.cat((stored_state["exp_avg_sq"], torch.zeros_like(extension_tensor)), dim=0)

                del self.optimizer.state[group['params'][0]]
                group["params"][0] = nn.Parameter(torch.cat((group["params"][0], extension_tensor), dim=0).requires_grad_(True))
                self.optimizer.state[group['params'][0]] = stored_state

                optimizable_tensors[group["name"]] = group["params"][0]
            else:
                group["params"][0] = nn.Parameter(torch.cat((group["params"][0], extension_tensor), dim=0).requires_grad_(True))
                optimizable_tensors[group["name"]] = group["params"][0]

        return optimizable_tensors

    def densification_postfix(self, new_xyz, new_features_dc, new_features_rest, new_opacities, new_scaling, new_rotation, new_concept_memberships=None):
        d = {"xyz": new_xyz,
        "f_dc": new_features_dc,
        "f_rest": new_features_rest,
        "opacity": new_opacities,
        "scaling" : new_scaling,
        "rotation" : new_rotation}

        optimizable_tensors = self.cat_tensors_to_optimizer(d)
        self._xyz = optimizable_tensors["xyz"]
        self._features_dc = optimizable_tensors["f_dc"]
        self._features_rest = optimizable_tensors["f_rest"]
        self._opacity = optimizable_tensors["opacity"]
        self._scaling = optimizable_tensors["scaling"]
        self._rotation = optimizable_tensors["rotation"]

        # ✅ 修复：concept_membership现在是普通张量的拼接
        if new_concept_memberships is not None:
            self._concept_membership = torch.cat([self._concept_membership, new_concept_memberships], dim=0)
        else:
            # 为新增的点分配默认值（背景概念one-hot编码）
            num_new_points = new_xyz.shape[0]
            new_concept_memberships_default = torch.zeros((num_new_points, self.num_concepts), dtype=torch.float, device="cuda")
            new_concept_memberships_default[:, 0] = 1.0  # 默认背景概念

            self._concept_membership = torch.cat([self._concept_membership, new_concept_memberships_default], dim=0)

        self.xyz_gradient_accum = torch.zeros((self.get_xyz.shape[0], 1), device="cuda")
        self.denom = torch.zeros((self.get_xyz.shape[0], 1), device="cuda")
        self.max_radii2D = torch.zeros((self.get_xyz.shape[0]), device="cuda")

    def densify_and_split(self, grads, grad_threshold, scene_extent, N=2):
        n_init_points = self.get_xyz.shape[0]
        # Extract points that satisfy the gradient condition
        padded_grad = torch.zeros((n_init_points), device="cuda")
        padded_grad[:grads.shape[0]] = grads.squeeze()
        selected_pts_mask = torch.where(padded_grad >= grad_threshold, True, False)
        selected_pts_mask = torch.logical_and(selected_pts_mask,
                                              torch.max(self.get_scaling, dim=1).values > self.percent_dense*scene_extent)
        stds = self.get_scaling[selected_pts_mask].repeat(N,1)
        means =torch.zeros((stds.size(0), 3),device="cuda")
        samples = torch.normal(mean=means, std=stds)
        rots = build_rotation(self._rotation[selected_pts_mask]).repeat(N,1,1)
        new_xyz = torch.bmm(rots, samples.unsqueeze(-1)).squeeze(-1) + self.get_xyz[selected_pts_mask].repeat(N, 1)
        new_scaling = self.scaling_inverse_activation(self.get_scaling[selected_pts_mask].repeat(N,1) / (0.8*N))
        new_rotation = self._rotation[selected_pts_mask].repeat(N,1)
        new_features_dc = self._features_dc[selected_pts_mask].repeat(N,1,1)
        new_features_rest = self._features_rest[selected_pts_mask].repeat(N,1,1)
        new_opacity = self._opacity[selected_pts_mask].repeat(N,1)

        # ✅ 处理concept_membership的分裂
        new_concept_memberships = self._concept_membership[selected_pts_mask].repeat(N, 1)

        self.densification_postfix(new_xyz, new_features_dc, new_features_rest, new_opacity, new_scaling, new_rotation, new_concept_memberships)

        prune_filter = torch.cat((selected_pts_mask, torch.zeros(N * selected_pts_mask.sum(), device="cuda", dtype=bool)))
        self.prune_points(prune_filter)
    def densify_and_clone(self, grads, grad_threshold, scene_extent):
        # Extract points that satisfy the gradient condition
        selected_pts_mask = torch.where(torch.norm(grads, dim=-1) >= grad_threshold, True, False)
        selected_pts_mask = torch.logical_and(selected_pts_mask,
                                              torch.max(self.get_scaling, dim=1).values <= self.percent_dense*scene_extent)
        new_xyz = self._xyz[selected_pts_mask]
        new_features_dc = self._features_dc[selected_pts_mask]
        new_features_rest = self._features_rest[selected_pts_mask]
        new_opacities = self._opacity[selected_pts_mask]
        new_scaling = self._scaling[selected_pts_mask]
        new_rotation = self._rotation[selected_pts_mask]

        # ✅ 正确修复：处理concept_membership的克隆（真正的1对1复制）
        new_concept_memberships = self._concept_membership[selected_pts_mask]  # 无需repeat

        self.densification_postfix(new_xyz, new_features_dc, new_features_rest, new_opacities, new_scaling, new_rotation, new_concept_memberships)

    def densify_and_prune(self, max_grad, min_opacity, extent, max_screen_size):
        grads = self.xyz_gradient_accum / self.denom
        grads[grads.isnan()] = 0.0

        self.densify_and_clone(grads, max_grad, extent)
        self.densify_and_split(grads, max_grad, extent)

        prune_mask = (self.get_opacity < min_opacity).squeeze()
        if max_screen_size:
            big_points_vs = self.max_radii2D > max_screen_size
            big_points_ws = self.get_scaling.max(dim=1).values > 0.1 * extent
            prune_mask = torch.logical_or(torch.logical_or(prune_mask, big_points_vs), big_points_ws)
        self.prune_points(prune_mask)

        torch.cuda.empty_cache()

    # 🔥 任务1: 完善的3D到2D投影函数
    def project_points_to_camera(self, camera):
        """
        将所有3D高斯点投影到给定相机的2D像素坐标。
        返回像素坐标和可见性掩码。

        根据InFusion相机矩阵约定：
        - camera.world_view_transform: W2C矩阵，已转置，适合列向量 P_cam = W2C @ P_world
        - camera.projection_matrix: 投影矩阵，已转置，适合列向量 P_clip = P @ P_cam
        - camera.full_proj_transform: 完整变换矩阵 P_clip = Full @ P_world

        变换流程：
        1. 世界坐标 -> 相机坐标 (使用world_view_transform)
        2. 相机坐标 -> 裁剪空间 (使用projection_matrix)
        3. 裁剪空间 -> NDC坐标 (透视除法)
        4. NDC坐标 -> 像素坐标 (视口变换)
        """
        return self._project_points_to_camera_view(self.get_xyz, camera)

    def _project_points_to_camera_view(self, xyz_world, camera):
        """
        核心投影函数：将3D世界坐标点投影到2D像素坐标

        Args:
            xyz_world: (N, 3) 世界坐标系中的3D点
            camera: Camera对象，包含相机参数

        Returns:
            pixel_coords: (N, 2) 像素坐标 [x, y]
            in_screen_mask: (N,) 布尔掩码，表示点是否在屏幕内且可见
        """
        N = xyz_world.shape[0]
        device = xyz_world.device
        dtype = xyz_world.dtype

        # 步骤1: 转换为齐次坐标 (N, 4)
        ones = torch.ones(N, 1, device=device, dtype=dtype)
        xyz_world_homo = torch.cat([xyz_world, ones], dim=1)

        # 步骤2: 使用完整投影变换：世界坐标 -> NDC坐标
        # camera.full_proj_transform 是 world_view_transform @ projection_matrix 的结果
        # 直接应用完整变换，避免分步骤可能的错误
        clip_coords = camera.full_proj_transform @ xyz_world_homo.T  # (4, N)
        clip_coords = clip_coords.T  # (N, 4)

        # 步骤3: 深度剔除（在相机坐标系中进行）
        # 将世界坐标转换到相机坐标系进行深度检查
        camera_coords = camera.world_view_transform @ xyz_world_homo.T  # (4, N)
        camera_coords = camera_coords.T  # (N, 4)
        camera_z = camera_coords[:, 2]  # 相机坐标系Z分量

        # 在相机坐标系中，相机看向-Z方向，所以可见点的Z应该为负值
        # 而且应该在[znear, zfar]范围内
        znear, zfar = camera.znear, camera.zfar
        depth_valid_mask = (camera_z <= -znear) & (camera_z >= -zfar)

        # 步骤4: 透视除法：裁剪空间 -> NDC坐标
        w = clip_coords[:, 3:4] + 1e-7  # 防除零保护
        ndc_coords = clip_coords[:, :3] / w  # (N, 3)

        # 步骤5: NDC视锥剔除
        # OpenGL约定：NDC坐标范围为[-1, 1]
        ndc_x, ndc_y, ndc_z = ndc_coords[:, 0], ndc_coords[:, 1], ndc_coords[:, 2]
        ndc_valid_mask = (
            (ndc_x >= -1.0) & (ndc_x <= 1.0) &
            (ndc_y >= -1.0) & (ndc_y <= 1.0) &
            (ndc_z >= -1.0) & (ndc_z <= 1.0)
        )

        # 步骤6: NDC到像素坐标转换
        # NDC坐标 [-1, 1] -> 像素坐标 [0, width/height]
        # 注意：Y轴需要翻转，因为NDC中+Y向上，但像素坐标中+Y向下
        H, W = camera.image_height, camera.image_width
        pixel_x = (ndc_x + 1.0) * 0.5 * W
        pixel_y = (1.0 - ndc_y) * 0.5 * H

        pixel_coords = torch.stack([pixel_x, pixel_y], dim=1)  # (N, 2)

        # 步骤7: 像素边界剔除
        screen_valid_mask = (
            (pixel_x >= 0) & (pixel_x < W) &
            (pixel_y >= 0) & (pixel_y < H)
        )

        # 步骤8: 综合所有剔除条件
        in_screen_mask = depth_valid_mask & ndc_valid_mask & screen_valid_mask

        return pixel_coords, in_screen_mask

    def _debug_projection_failure(self, xyz, camera):
        """调试投影失败的原因"""
        N = xyz.shape[0]
        device = xyz.device
        dtype = xyz.dtype

        # 步骤1: 转换为齐次坐标
        ones = torch.ones(N, 1, device=device, dtype=dtype)
        xyz_world_homo = torch.cat([xyz, ones], dim=1)

        # 步骤2: 世界坐标 -> 相机坐标
        xyz_camera_homo = xyz_world_homo @ camera.world_view_transform.T
        xyz_camera = xyz_camera_homo[:, :3]

        print(f"      📍 相机坐标范围:")
        print(f"         X: [{xyz_camera[:, 0].min():.2f}, {xyz_camera[:, 0].max():.2f}]")
        print(f"         Y: [{xyz_camera[:, 1].min():.2f}, {xyz_camera[:, 1].max():.2f}]")
        print(f"         Z: [{xyz_camera[:, 2].min():.2f}, {xyz_camera[:, 2].max():.2f}]")

        # 步骤3: 深度剔除
        depth_valid_mask = (xyz_camera[:, 2] < -0.01) & (xyz_camera[:, 2] > -1000.0)
        print(f"      ✂️ 深度剔除: {depth_valid_mask.sum()}/{N} 点在深度范围内")

        if depth_valid_mask.sum() == 0:
            print(f"      ❌ 深度剔除失败！")
            z_values = xyz_camera[:, 2]
            in_front = (z_values < 0).sum()
            print(f"         在相机前方的点: {in_front}/{N}")
            print(f"         Z值分布: [{z_values.min():.2f}, {z_values.max():.2f}]")
            return

        # 步骤4: 相机坐标 -> 裁剪空间
        clip_space_homo = xyz_camera_homo @ camera.projection_matrix.T

        # 步骤5: 透视除法
        w = clip_space_homo[:, 3:4]
        w_safe = torch.where(torch.abs(w) < 1e-6, torch.sign(w) * 1e-6, w)
        ndc_coords = clip_space_homo[:, :3] / w_safe

        print(f"      📍 NDC坐标范围:")
        print(f"         X: [{ndc_coords[:, 0].min():.2f}, {ndc_coords[:, 0].max():.2f}]")
        print(f"         Y: [{ndc_coords[:, 1].min():.2f}, {ndc_coords[:, 1].max():.2f}]")
        print(f"         Z: [{ndc_coords[:, 2].min():.2f}, {ndc_coords[:, 2].max():.2f}]")

        # 步骤6: NDC剔除
        ndc_valid_mask = (
            (ndc_coords[:, 0] >= -1.0) & (ndc_coords[:, 0] <= 1.0) &
            (ndc_coords[:, 1] >= -1.0) & (ndc_coords[:, 1] <= 1.0) &
            (ndc_coords[:, 2] >= -1.0) & (ndc_coords[:, 2] <= 1.0)
        )
        print(f"      ✂️ NDC剔除: {ndc_valid_mask.sum()}/{N} 点在NDC范围内")

        # 综合可见性
        final_valid = depth_valid_mask & ndc_valid_mask
        print(f"      👁️ 最终可见: {final_valid.sum()}/{N} 点")

    # 🔥 修改：核心 2D→3D concept_membership 映射方法，使用手动投影和动态掩码加载
    def initialize_concept_memberships_from_masks(self, concept_masks_root_path, cameras, render_func=None): # render_func 仍保留但不再用于means2D
        """
        从2D概念掩码初始化3D高斯点的concept_membership
        使用精确的像素级投影投票机制，动态加载每个相机的掩码

        Args:
            concept_masks_root_path: 概念掩码的根目录 (例如: "stage1_results/concept_masks_expanded/")
            cameras: 相机列表
            render_func: 渲染函数 (不再用于获取means2D，但可能用于其他渲染结果)
        """
        print("🔥 开始精确像素级concept_membership映射...")
        print(f"   概念掩码根目录: {concept_masks_root_path}")
        concept_masks_root_path = Path(concept_masks_root_path)

        # 概念名称与ID的映射，用于构建路径
        concept_id_to_name = {
            0: "concept_0_background",
            1: "concept_1_repair",
            2: "concept_2_boundary",
            # 根据需要添加更多概念
        }

        num_points = self.get_xyz.shape[0]
        # 累积每个点的概念投票向量 (N, num_concepts)
        concept_votes = torch.zeros((num_points, self.num_concepts), device="cuda")
        total_visibility_per_point = torch.zeros(num_points, device="cuda") # 记录每个点被多少相机视为可见

        # 对每个相机视角进行精确投票
        for cam_idx, camera in enumerate(cameras):
            print(f"处理相机 {cam_idx+1}/{len(cameras)} (图像: {camera.image_name})")

            # 使用经过任务1验证的手动投影获取像素坐标和可见性
            with torch.no_grad():
                pixel_coords_all, visible_mask_manual = self._project_points_to_camera_view(self.get_xyz, camera)

                # 添加详细调试信息
                print(f"   📊 投影结果: {visible_mask_manual.sum()}/{len(visible_mask_manual)} 点可见")
                if cam_idx < 3:  # 只对前3个相机显示详细信息
                    print(f"   📍 相机参数: H={camera.image_height}, W={camera.image_width}")
                    print(f"   📍 相机中心: [{camera.camera_center[0]:.2f}, {camera.camera_center[1]:.2f}, {camera.camera_center[2]:.2f}]")
                    print(f"   📍 znear={camera.znear:.3f}, zfar={camera.zfar:.3f}")
                    print(f"   📍 世界坐标范围: X[{self.get_xyz[:, 0].min():.2f}, {self.get_xyz[:, 0].max():.2f}]")
                    print(f"   📍 世界坐标范围: Y[{self.get_xyz[:, 1].min():.2f}, {self.get_xyz[:, 1].max():.2f}]")
                    print(f"   📍 世界坐标范围: Z[{self.get_xyz[:, 2].min():.2f}, {self.get_xyz[:, 2].max():.2f}]")

                    # 如果没有可见点，进行详细分析
                    if visible_mask_manual.sum() == 0:
                        print(f"   🔍 详细分析投影失败原因...")
                        self._debug_projection_failure(self.get_xyz, camera)

                if visible_mask_manual.sum() == 0:
                    print(f"   ⚠️ 相机 {cam_idx+1} ({camera.image_name}) 无可见点（手动投影），跳过")
                    continue
                
                # 获取可见点的像素坐标和对应的原始点索引
                visible_pixel_coords = pixel_coords_all[visible_mask_manual]
                visible_original_indices = torch.where(visible_mask_manual)[0]
                
                # 图像尺寸
                H, W = camera.image_height, camera.image_width
                
                # 将2D坐标转换为整数像素坐标并裁剪到图像范围内
                pixel_coords_int = visible_pixel_coords.round().long()
                pixel_coords_int[:, 0] = torch.clamp(pixel_coords_int[:, 0], 0, W-1)  # x坐标
                pixel_coords_int[:, 1] = torch.clamp(pixel_coords_int[:, 1], 0, H-1)  # y坐标
                
                # 动态加载当前相机的概念掩码
                current_view_concept_masks = {}
                masks_loaded_for_this_view = 0

                # 从camera.image_name提取基础文件名（去掉扩展名）
                base_image_name = Path(camera.image_name).stem  # 例如: "DSC07956.JPG" -> "DSC07956"

                for concept_id in range(self.num_concepts):
                    concept_dir_name = concept_id_to_name.get(concept_id, f"concept_{concept_id}")
                    # 构建概念掩码文件名: concept_{id}_{base_name}.png
                    mask_filename = f"concept_{concept_id}_{base_image_name}.png"
                    mask_file_path = concept_masks_root_path / concept_dir_name / mask_filename
                    
                    if mask_file_path.exists():
                        try:
                            # 加载掩码，并确保转换为灰度图和目标分辨率
                            mask_image_pil = Image.open(mask_file_path).convert('L') # 强制转换为灰度图 (单通道)
                            mask_np = np.array(mask_image_pil, dtype=np.float32) / 255.0 # 归一化到0-1
                            
                            mask_tensor = torch.from_numpy(mask_np).to(pixel_coords_int.device)
                            
                            # 如果掩码分辨率不匹配相机分辨率，进行插值
                            if mask_tensor.shape[-2:] != (H, W):
                                mask_tensor = torch.nn.functional.interpolate(
                                    mask_tensor.unsqueeze(0).unsqueeze(0), # (1,1,H,W)
                                    size=(H, W), mode='bilinear', align_corners=False
                                ).squeeze() # (H,W)
                            
                            current_view_concept_masks[concept_id] = mask_tensor
                            masks_loaded_for_this_view += 1
                        except Exception as e:
                            print(f"   ❌ 相机 {cam_idx+1} ({camera.image_name}): 加载概念{concept_id}掩码 {mask_file_path} 失败: {e}")
                    else:
                        # print(f"   ⚠️ 相机 {cam_idx+1} ({camera.image_name}): 概念{concept_id}掩码 {mask_file_path} 不存在，跳过此概念的投票。")
                        pass # 静默跳过，因为不是所有视图都有所有概念的掩码

                if masks_loaded_for_this_view == 0:
                    print(f"   ⚠️ 相机 {cam_idx+1} ({camera.image_name}): 未能加载任何概念掩码，跳过此视图的投票。")
                    continue
                
                # 累积可见点的投票次数
                total_visibility_per_point[visible_mask_manual] += 1.0

                # 为每个概念收集投票
                current_vote = torch.zeros((visible_original_indices.shape[0], self.num_concepts), device="cuda")
                
                for concept_id, mask_tensor in current_view_concept_masks.items():
                    # 从掩码中采样每个投影点的概念值
                    concept_values = mask_tensor[pixel_coords_int[:, 1], pixel_coords_int[:, 0]]  # (N_visible,)
                    current_vote[:, concept_id] = concept_values
                
                # 累积到全局投票中
                concept_votes[visible_original_indices] += current_vote

        print(f"📊 投票统计完成，开始生成concept_membership...")

        # 生成最终的concept_membership
        with torch.no_grad():
            # 只对有足够可见性的点进行分配
            valid_points = total_visibility_per_point >= 1.0  # 至少在一个视角中可见
            
            if valid_points.sum() > 0:
                print(f"   📍 有效点数: {valid_points.sum().item()}/{num_points}")
                
                # 获取有效点的投票结果
                raw_votes = concept_votes[valid_points]  # (N_valid, num_concepts)
                
                # 计算投票强度（每个点的总投票数）
                vote_strength = raw_votes.sum(dim=1, keepdim=True)  # (N_valid, 1)
                
                # 处理不同投票强度的情况
                strong_vote_mask = vote_strength.squeeze() > 0.1  # 有明显投票的点
                weak_vote_mask = ~strong_vote_mask  # 投票很弱的点
                
                # 初始化隶属度
                assigned_memberships = torch.zeros_like(raw_votes)
                
                if strong_vote_mask.sum() > 0:
                    # 对于有强投票的点，进行softmax归一化
                    strong_votes = raw_votes[strong_vote_mask]
                    temperature = 1.5  # 调节分布尖锐度
                    assigned_memberships[strong_vote_mask] = torch.softmax(strong_votes / temperature, dim=1)
                
                if weak_vote_mask.sum() > 0:
                    # 对于弱投票的点，默认为背景概念
                    assigned_memberships[weak_vote_mask, 0] = 1.0  # 背景概念
                
                # 更新concept_membership
                valid_indices = torch.where(valid_points)[0]
                self._concept_membership.data[valid_indices] = assigned_memberships
                
                print(f"   ✅ 强投票点: {strong_vote_mask.sum().item()}")
                print(f"   ⚠️ 弱投票点: {weak_vote_mask.sum().item()}")
            
            else:
                print("   ⚠️ 没有找到有效的投影点")

        # 更新映射统计
        self._concept_mapping_initialized = True
        
        # 使用主导概念ID进行统计
        dominant_ids = self.get_dominant_concept_id
        unique_concepts, counts = torch.unique(dominant_ids, return_counts=True)
        
        # 计算平均置信度作为质量分数
        avg_confidence = self.get_concept_confidence.mean().item()
        
        # 计算隶属度分布的统计信息
        concept_distribution_detailed = {}
        for i in range(self.num_concepts):
            concept_distribution_detailed[f'concept_{i}_avg'] = self._concept_membership[:, i].mean().item()
        
        self._concept_mapping_stats = {
            'total_mapped_points': valid_points.sum().item(),
            'concept_distribution': dict(zip(unique_concepts.cpu().numpy(), counts.cpu().numpy())),\
            'concept_distribution_detailed': concept_distribution_detailed,\
            'mapping_quality_score': avg_confidence
        }

        print(f"✅ concept_membership映射完成:")
        print(f"   - 总映射点数: {self._concept_mapping_stats['total_mapped_points']}")
        print(f"   - 主导概念分布: {self._concept_mapping_stats['concept_distribution']}")
        print(f"   - 平均隶属度: {self._concept_mapping_stats['concept_distribution_detailed']}")
        print(f"   - 映射质量分数: {self._concept_mapping_stats['mapping_quality_score']:.3f}")

        return self._concept_mapping_stats

    def add_densification_stats(self, viewspace_point_tensor, update_filter):
        self.xyz_gradient_accum[update_filter] += torch.norm(viewspace_point_tensor.grad[update_filter,:2], dim=-1, keepdim=True)
        self.denom[update_filter] += 1