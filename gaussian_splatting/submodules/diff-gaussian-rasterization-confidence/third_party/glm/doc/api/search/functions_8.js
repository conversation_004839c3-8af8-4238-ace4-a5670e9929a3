var searchData=
[
  ['identity',['identity',['../a00247.html#ga81696f2b8d1db02ea1aff8da8f269314',1,'glm']]],
  ['imulextended',['imulExtended',['../a00370.html#gac0c510a70e852f57594a9141848642e3',1,'glm']]],
  ['infiniteperspective',['infinitePerspective',['../a00243.html#ga44fa38a18349450325cae2661bb115ca',1,'glm']]],
  ['infiniteperspectivelh',['infinitePerspectiveLH',['../a00243.html#ga3201b30f5b3ea0f933246d87bfb992a9',1,'glm']]],
  ['infiniteperspectiverh',['infinitePerspectiveRH',['../a00243.html#ga99672ffe5714ef478dab2437255fe7e1',1,'glm']]],
  ['intbitstofloat',['intBitsToFloat',['../a00241.html#ga4fb7c21c2dce064b26fd9ccdaf9adcd4',1,'glm::intBitsToFloat(int const &amp;v)'],['../a00241.html#ga7a0a8291a1cf3e1c2aee33030a1bd7b0',1,'glm::intBitsToFloat(vec&lt; L, int, Q &gt; const &amp;v)']]],
  ['intermediate',['intermediate',['../a00352.html#gacc5cd5f3e78de61d141c2355417424de',1,'glm']]],
  ['interpolate',['interpolate',['../a00337.html#ga4e67863d150724b10c1ac00972dc958c',1,'glm']]],
  ['intersectlinesphere',['intersectLineSphere',['../a00331.html#ga9c68139f3d8a4f3d7fe45f9dbc0de5b7',1,'glm']]],
  ['intersectlinetriangle',['intersectLineTriangle',['../a00331.html#ga9d29b9b3acb504d43986502f42740df4',1,'glm']]],
  ['intersectrayplane',['intersectRayPlane',['../a00331.html#gad3697a9700ea379739a667ea02573488',1,'glm']]],
  ['intersectraysphere',['intersectRaySphere',['../a00331.html#gac88f8cd84c4bcb5b947d56acbbcfa56e',1,'glm::intersectRaySphere(genType const &amp;rayStarting, genType const &amp;rayNormalizedDirection, genType const &amp;sphereCenter, typename genType::value_type const sphereRadiusSquared, typename genType::value_type &amp;intersectionDistance)'],['../a00331.html#gad28c00515b823b579c608aafa1100c1d',1,'glm::intersectRaySphere(genType const &amp;rayStarting, genType const &amp;rayNormalizedDirection, genType const &amp;sphereCenter, const typename genType::value_type sphereRadius, genType &amp;intersectionPosition, genType &amp;intersectionNormal)']]],
  ['intersectraytriangle',['intersectRayTriangle',['../a00331.html#ga65bf2c594482f04881c36bc761f9e946',1,'glm']]],
  ['inverse',['inverse',['../a00248.html#gab41da854ae678e23e114b598cbca4065',1,'glm::inverse(qua&lt; T, Q &gt; const &amp;q)'],['../a00317.html#ga070f521a953f6461af4ab4cf8ccbf27e',1,'glm::inverse(tdualquat&lt; T, Q &gt; const &amp;q)'],['../a00371.html#gaed509fe8129b01e4f20a6d0de5690091',1,'glm::inverse(mat&lt; C, R, T, Q &gt; const &amp;m)']]],
  ['inversesqrt',['inversesqrt',['../a00242.html#ga523dd6bd0ad9f75ae2d24c8e4b017b7a',1,'glm']]],
  ['inversetranspose',['inverseTranspose',['../a00295.html#gab213cd0e3ead5f316d583f99d6312008',1,'glm']]],
  ['iround',['iround',['../a00292.html#ga57824268ebe13a922f1d69a5d37f637f',1,'glm']]],
  ['iscompnull',['isCompNull',['../a00368.html#gaf6ec1688eab7442fe96fe4941d5d4e76',1,'glm']]],
  ['isdenormal',['isdenormal',['../a00314.html#ga74aa7c7462245d83bd5a9edf9c6c2d91',1,'glm']]],
  ['isfinite',['isfinite',['../a00315.html#gaf4b04dcd3526996d68c1bfe17bfc8657',1,'glm::isfinite(genType const &amp;x)'],['../a00315.html#gac3b12b8ac3014418fe53c299478b6603',1,'glm::isfinite(const vec&lt; 1, T, Q &gt; &amp;x)'],['../a00315.html#ga8e76dc3e406ce6a4155c2b12a2e4b084',1,'glm::isfinite(const vec&lt; 2, T, Q &gt; &amp;x)'],['../a00315.html#ga929ef27f896d902c1771a2e5e150fc97',1,'glm::isfinite(const vec&lt; 3, T, Q &gt; &amp;x)'],['../a00315.html#ga19925badbe10ce61df1d0de00be0b5ad',1,'glm::isfinite(const vec&lt; 4, T, Q &gt; &amp;x)']]],
  ['isidentity',['isIdentity',['../a00340.html#gaee935d145581c82e82b154ccfd78ad91',1,'glm']]],
  ['isinf',['isinf',['../a00241.html#ga2885587c23a106301f20443896365b62',1,'glm::isinf(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00248.html#ga45722741ea266b4e861938b365c5f362',1,'glm::isinf(qua&lt; T, Q &gt; const &amp;x)']]],
  ['ismultiple',['isMultiple',['../a00261.html#gaec593d33956a8fe43f78fccc63ddde9a',1,'glm::isMultiple(genIUType v, genIUType Multiple)'],['../a00274.html#ga354caf634ef333d9cb4844407416256a',1,'glm::isMultiple(vec&lt; L, T, Q &gt; const &amp;v, T Multiple)'],['../a00274.html#gabb4360e38c0943d8981ba965dead519d',1,'glm::isMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)']]],
  ['isnan',['isnan',['../a00241.html#ga29ef934c00306490de837b4746b4e14d',1,'glm::isnan(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00248.html#ga1bb55f8963616502e96dc564384d8a03',1,'glm::isnan(qua&lt; T, Q &gt; const &amp;x)']]],
  ['isnormalized',['isNormalized',['../a00340.html#gae785af56f47ce220a1609f7f84aa077a',1,'glm::isNormalized(mat&lt; 2, 2, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00340.html#gaa068311695f28f5f555f5f746a6a66fb',1,'glm::isNormalized(mat&lt; 3, 3, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00340.html#ga4d9bb4d0465df49fedfad79adc6ce4ad',1,'glm::isNormalized(mat&lt; 4, 4, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00368.html#gac3c974f459fd75453134fad7ae89a39e',1,'glm::isNormalized(vec&lt; L, T, Q &gt; const &amp;v, T const &amp;epsilon)']]],
  ['isnull',['isNull',['../a00340.html#ga9790ec222ce948c0ff0d8ce927340dba',1,'glm::isNull(mat&lt; 2, 2, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00340.html#gae14501c6b14ccda6014cc5350080103d',1,'glm::isNull(mat&lt; 3, 3, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00340.html#ga2b98bb30a9fefa7cdea5f1dcddba677b',1,'glm::isNull(mat&lt; 4, 4, T, Q &gt; const &amp;m, T const &amp;epsilon)'],['../a00368.html#gab4a3637dbcb4bb42dc55caea7a1e0495',1,'glm::isNull(vec&lt; L, T, Q &gt; const &amp;v, T const &amp;epsilon)']]],
  ['isorthogonal',['isOrthogonal',['../a00340.html#ga58f3289f74dcab653387dd78ad93ca40',1,'glm']]],
  ['ispoweroftwo',['isPowerOfTwo',['../a00261.html#gadf491730354aa7da67fbe23d4d688763',1,'glm::isPowerOfTwo(genIUType v)'],['../a00274.html#gabf2b61ded7049bcb13e25164f832a290',1,'glm::isPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)']]]
];
