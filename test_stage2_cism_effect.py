#!/usr/bin/env python3
"""
InFusion-Enhanced 阶段2 CISM语义引导效果验证测试

本脚本专门用于验证CISM系统的实际语义引导效果：
1. 立即激活CISM损失（从第1次迭代开始）
2. 使用与背景差异巨大的概念（熔岩池 vs 花园）
3. 定期保存渲染图像以观察视觉变化
4. 详细记录损失变化和梯度流

作者: Claude 4.0 Sonnet
目标: 验证CISM的实际语义引导能力
"""

import os
import sys
import torch
import logging
import time
import yaml
import argparse
from pathlib import Path
import numpy as np
from PIL import Image

# 添加项目路径
sys.path.append('.')
sys.path.append('./gaussian_splatting')

# 导入3DGS组件
from scene.gaussian_model import GaussianModel
from gaussian_splatting.arguments import OptimizationParams
from gaussian_splatting.utils.loss_utils import l1_loss

# 导入CISM组件
from cism_core.cism_trainer import CISMTrainer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stage2_cism_effect_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class CISMEffectValidator:
    """
    CISM语义引导效果验证器
    
    专门用于验证CISM系统是否能够产生实际的语义引导效果
    """
    
    def __init__(self, config_path: str = "configs/cism_training_config.yaml"):
        """初始化验证器"""
        self.config_path = config_path
        self.config = self._load_config()
        self.device = self.config.get('device', 'cuda')
        
        # 创建输出目录
        self.output_dir = Path("output/cism_effect_test")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.images_dir = self.output_dir / "rendered_images"
        self.images_dir.mkdir(exist_ok=True)
        
        # 训练组件
        self.gaussians = None
        self.cism_trainer = None
        
        # 验证统计
        self.validation_stats = {
            'iterations': [],
            'total_losses': [],
            'base_losses': [],
            'cism_losses': [],
            'gradient_norms': [],
            'cism_applied_count': 0
        }
        
        logger.info("🔥 CISM效果验证器初始化完成")
    
    def _load_config(self) -> dict:
        """加载配置"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 确保CISM立即激活
        config['training_schedule']['cism_start_iter'] = 1
        config['training_schedule']['cism_interval'] = 1
        
        logger.info(f"✅ 配置加载成功，CISM将从第1次迭代开始")
        logger.info(f"🎯 目标概念: {config.get('user_concept', 'N/A')}")
        
        return config
    
    def setup_components(self) -> bool:
        """设置所有组件"""
        try:
            # 1. 加载3DGS模型
            logger.info("📦 加载3DGS模型...")
            self.gaussians = GaussianModel(sh_degree=0)
            model_path = "stage1_results/garden_fixed_final/gaussians_with_concept_membership_enhanced.ply"
            self.gaussians.load_ply(model_path)
            
            logger.info(f"   ✅ 模型加载成功，高斯点数: {self.gaussians.get_xyz.shape[0]}")
            
            # 2. 设置3DGS优化器
            logger.info("⚙️ 设置3DGS优化器...")
            parser = argparse.ArgumentParser()
            opt_params = OptimizationParams(parser)
            
            args = argparse.Namespace()
            for attr_name in dir(opt_params):
                if not attr_name.startswith('_'):
                    setattr(args, attr_name, getattr(opt_params, attr_name))
            
            training_args = opt_params.extract(args)
            self.gaussians.training_setup(training_args)
            
            # 3. 初始化CISM训练器
            logger.info("🚀 初始化CISM训练器...")
            self.cism_trainer = CISMTrainer(self.config)
            
            logger.info("✅ 所有组件设置完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 组件设置失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_mock_camera(self):
        """创建模拟相机"""
        device = self.device
        
        class MockCamera:
            def __init__(self):
                self.image_width = 512
                self.image_height = 512
                self.FoVx = 1.2
                self.FoVy = 1.2
                self.znear = 0.01
                self.zfar = 100.0
                self.world_view_transform = torch.eye(4, device=device)
                self.projection_matrix = torch.eye(4, device=device)
                self.full_proj_transform = torch.eye(4, device=device)
                self.camera_center = torch.zeros(3, device=device)
                
                # 模拟原始图像（花园场景）
                self.original_image = torch.rand(3, 512, 512, device=device) * 0.5 + 0.3
        
        return MockCamera()
    
    def render_scene(self, camera) -> torch.Tensor:
        """渲染场景（确保梯度连接）"""
        xyz = self.gaussians.get_xyz
        features = self.gaussians.get_features
        opacity = self.gaussians.get_opacity
        
        # 简化的可微渲染
        batch_size = 1
        height, width = 256, 256  # 使用中等分辨率
        
        rendered_images = torch.ones(batch_size, 3, height, width, device=self.device) * 0.5
        
        # 让图像依赖于3DGS参数
        xyz_mean = xyz.mean(dim=0)
        features_mean = features[:, 0, :].mean(dim=0)
        opacity_mean = opacity.mean()
        
        # 应用参数影响
        for i in range(3):
            rendered_images[:, i, :, :] += xyz_mean[i] * 0.02
            rendered_images[:, i, :, :] += features_mean[i] * 0.1
        
        rendered_images = rendered_images * (0.3 + opacity_mean * 0.7)
        rendered_images = torch.clamp(rendered_images, 0.0, 1.0)
        
        return rendered_images
    
    def save_rendered_image(self, rendered_image: torch.Tensor, iteration: int):
        """保存渲染图像"""
        try:
            # 转换为PIL图像
            image_np = rendered_image.squeeze(0).detach().cpu().numpy()
            image_np = (image_np * 255).astype(np.uint8)
            image_np = np.transpose(image_np, (1, 2, 0))
            
            # 保存图像
            image_path = self.images_dir / f"iteration_{iteration:04d}.png"
            Image.fromarray(image_np).save(image_path)
            
            logger.info(f"   💾 保存渲染图像: {image_path}")
            
        except Exception as e:
            logger.warning(f"   ⚠️ 保存图像失败: {e}")
    
    def check_gradients(self) -> dict:
        """检查梯度"""
        gradient_info = {
            'has_gradients': False,
            'total_norm': 0.0,
            'param_norms': {}
        }
        
        total_norm = 0.0
        param_count = 0
        
        for param_group in self.gaussians.params_list:
            param_name = param_group["name"]
            params = param_group["params"]
            
            for i, param in enumerate(params):
                if param.grad is not None:
                    grad_norm = param.grad.norm().item()
                    if grad_norm > 0:
                        gradient_info['has_gradients'] = True
                        gradient_info['param_norms'][f"{param_name}[{i}]"] = grad_norm
                        total_norm += grad_norm
                        param_count += 1
        
        gradient_info['total_norm'] = total_norm
        gradient_info['avg_norm'] = total_norm / max(param_count, 1)
        
        return gradient_info

    def run_cism_effect_validation(self, num_iterations: int = 100, save_interval: int = 10) -> bool:
        """运行CISM效果验证"""
        try:
            logger.info("🔥 开始CISM语义引导效果验证")
            logger.info(f"🎯 目标: 验证'{self.config.get('user_concept', 'N/A')}'的语义引导效果")
            logger.info(f"📊 训练设置: {num_iterations}次迭代，每{save_interval}次保存图像")
            logger.info("=" * 80)

            # 创建相机
            camera = self.create_mock_camera()

            # 保存初始状态
            logger.info("📸 保存初始渲染状态...")
            initial_render = self.render_scene(camera)
            self.save_rendered_image(initial_render, 0)

            # 训练循环
            for iteration in range(1, num_iterations + 1):
                iter_start = time.time()

                # 清零梯度
                self.gaussians.optimizer.zero_grad()

                # 渲染场景
                rendered_images = self.render_scene(camera)

                # 计算基础损失（降低权重以突出CISM效果）
                gt_image = camera.original_image.unsqueeze(0)
                if gt_image.shape != rendered_images.shape:
                    gt_image = torch.nn.functional.interpolate(
                        gt_image, size=rendered_images.shape[-2:], mode='bilinear', align_corners=False
                    )

                # 使用较小的基础损失权重
                base_loss = l1_loss(rendered_images, gt_image) * 0.1  # 降低基础损失权重
                total_loss = base_loss

                # 应用CISM损失
                cism_loss_value = 0.0
                cism_applied = False

                should_apply_cism = self.cism_trainer.should_apply_cism(iteration)

                if should_apply_cism:
                    try:
                        # 调用CISM训练步骤
                        cism_result = self.cism_trainer.training_step(
                            rendered_images,
                            camera,
                            iteration
                        )

                        if cism_result is not None:
                            cism_loss, cism_info = cism_result
                            cism_loss_value = cism_loss.item()
                            total_loss = base_loss + cism_loss * 2.0  # 增强CISM损失权重
                            cism_applied = True
                            self.validation_stats['cism_applied_count'] += 1

                    except Exception as e:
                        logger.warning(f"   ⚠️ 迭代 {iteration}: CISM损失计算失败: {e}")

                # 反向传播
                total_loss.backward()

                # 检查梯度
                gradient_info = self.check_gradients()

                # 优化器步骤
                if gradient_info['has_gradients']:
                    self.gaussians.optimizer.step()

                # 记录统计信息
                iter_time = time.time() - iter_start

                self.validation_stats['iterations'].append(iteration)
                self.validation_stats['total_losses'].append(total_loss.item())
                self.validation_stats['base_losses'].append(base_loss.item())
                self.validation_stats['cism_losses'].append(cism_loss_value)
                self.validation_stats['gradient_norms'].append(gradient_info['total_norm'])

                # 打印进度
                status = "🔥 CISM应用" if cism_applied else "⚪ 基础训练"
                logger.info(f"迭代 {iteration:3d}/{num_iterations}: {status}")
                logger.info(f"   📊 总损失: {total_loss.item():.6f} | 基础: {base_loss.item():.6f} | CISM: {cism_loss_value:.6f}")
                logger.info(f"   🔧 梯度范数: {gradient_info['total_norm']:.6f} | 时间: {iter_time:.2f}s")

                # 定期保存图像
                if iteration % save_interval == 0:
                    logger.info(f"   💾 保存第{iteration}次迭代的渲染结果...")
                    self.save_rendered_image(rendered_images, iteration)

                # 每25次迭代打印详细统计
                if iteration % 25 == 0:
                    self._print_progress_summary(iteration, num_iterations)

            # 保存最终状态
            logger.info("📸 保存最终渲染状态...")
            final_render = self.render_scene(camera)
            self.save_rendered_image(final_render, num_iterations)

            # 生成验证报告
            self._generate_validation_report(num_iterations)

            return True

        except Exception as e:
            logger.error(f"❌ CISM效果验证失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _print_progress_summary(self, current_iter: int, total_iter: int):
        """打印进度摘要"""
        recent_losses = self.validation_stats['total_losses'][-25:]
        recent_cism_losses = self.validation_stats['cism_losses'][-25:]

        avg_total_loss = sum(recent_losses) / len(recent_losses)
        avg_cism_loss = sum(recent_cism_losses) / len(recent_cism_losses)
        cism_active_rate = sum(1 for x in recent_cism_losses if x > 0) / len(recent_cism_losses)

        progress = current_iter / total_iter * 100

        logger.info("📈 进度摘要:")
        logger.info(f"   - 完成进度: {progress:.1f}%")
        logger.info(f"   - 近期平均总损失: {avg_total_loss:.6f}")
        logger.info(f"   - 近期平均CISM损失: {avg_cism_loss:.6f}")
        logger.info(f"   - CISM激活率: {cism_active_rate:.1%}")

    def _generate_validation_report(self, num_iterations: int):
        """生成验证报告"""
        logger.info("\n" + "=" * 80)
        logger.info("📋 CISM语义引导效果验证报告")
        logger.info("=" * 80)

        # 基本统计
        total_cism_applied = self.validation_stats['cism_applied_count']
        cism_activation_rate = total_cism_applied / num_iterations * 100

        final_total_loss = self.validation_stats['total_losses'][-1]
        final_cism_loss = self.validation_stats['cism_losses'][-1]

        avg_total_loss = sum(self.validation_stats['total_losses']) / len(self.validation_stats['total_losses'])
        avg_cism_loss = sum(x for x in self.validation_stats['cism_losses'] if x > 0)
        if total_cism_applied > 0:
            avg_cism_loss = avg_cism_loss / total_cism_applied
        else:
            avg_cism_loss = 0.0

        logger.info(f"🎯 目标概念: {self.config.get('user_concept', 'N/A')}")
        logger.info(f"📊 训练统计:")
        logger.info(f"   - 总迭代数: {num_iterations}")
        logger.info(f"   - CISM激活次数: {total_cism_applied}")
        logger.info(f"   - CISM激活率: {cism_activation_rate:.1f}%")
        logger.info(f"📈 损失分析:")
        logger.info(f"   - 最终总损失: {final_total_loss:.6f}")
        logger.info(f"   - 最终CISM损失: {final_cism_loss:.6f}")
        logger.info(f"   - 平均总损失: {avg_total_loss:.6f}")
        logger.info(f"   - 平均CISM损失: {avg_cism_loss:.6f}")

        # 效果评估指导
        logger.info(f"🔍 效果评估指导:")
        logger.info(f"   📁 渲染图像保存在: {self.images_dir}")
        logger.info(f"   📸 请检查图像序列:")
        logger.info(f"      - iteration_0000.png (初始状态)")
        logger.info(f"      - iteration_00XX.png (中间过程)")
        logger.info(f"      - iteration_{num_iterations:04d}.png (最终状态)")

        if cism_activation_rate >= 80:
            logger.info("✅ CISM激活率正常 (≥80%)")
            if avg_cism_loss > 0.001:
                logger.info("✅ CISM损失在合理范围内")
                logger.info("🎉 CISM系统运行正常！")
                logger.info("👀 请检查渲染图像是否出现熔岩池特征:")
                logger.info("   - 橙红色调增加")
                logger.info("   - 发光效果")
                logger.info("   - 纹理变化")
            else:
                logger.warning("⚠️ CISM损失过小，可能需要调整参数")
        else:
            logger.warning("⚠️ CISM激活率过低，请检查配置")

        logger.info("=" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CISM语义引导效果验证测试")
    parser.add_argument("--iterations", type=int, default=100, help="训练迭代次数")
    parser.add_argument("--save-interval", type=int, default=10, help="图像保存间隔")
    parser.add_argument("--config", type=str, default="configs/cism_training_config.yaml", help="配置文件路径")

    args = parser.parse_args()

    logger.info("🚀 InFusion-Enhanced 阶段2 CISM语义引导效果验证")
    logger.info("🎯 目标: 验证CISM系统的实际语义引导能力")
    logger.info("👨‍💻 开发者: Claude 4.0 Sonnet")
    logger.info("=" * 80)

    try:
        # 初始化验证器
        validator = CISMEffectValidator(config_path=args.config)

        # 设置组件
        if not validator.setup_components():
            logger.error("❌ 组件设置失败")
            return False

        # 运行验证
        success = validator.run_cism_effect_validation(
            num_iterations=args.iterations,
            save_interval=args.save_interval
        )

        if success:
            logger.info("🎉 CISM效果验证完成！")
            logger.info("💡 请检查输出图像以评估语义引导效果")
            return True
        else:
            logger.error("❌ CISM效果验证失败")
            return False

    except Exception as e:
        logger.error(f"❌ 验证过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
