"""
RCA训练器：集成RCA到CISM+3DGS训练流程
阶段3: 完整的RCA区域控制训练系统
"""

import torch
import torch.nn as nn
from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path
import time

from .spatial_attention_3d import SpatialAttention3D
from .concept_weight_renderer import ConceptWeightRenderer
from .spatial_modulation import SpatialModulation

# 导入CISM组件
try:
    from cism_core import DiffusionEngine, ConceptGuidance, SDSLoss
    CISM_AVAILABLE = True
except ImportError:
    CISM_AVAILABLE = False
    logging.warning("CISM components not available")

class RCATrainer:
    """
    RCA训练器
    
    功能：
    1. 集成RCA到CISM+3DGS训练流程
    2. 管理空间注意力网络训练
    3. 协调概念权重渲染和空间调制
    4. 监控训练进度和质量指标
    """
    
    def __init__(
        self,
        gaussians,
        config: Dict,
        device: str = "cuda"
    ):
        """
        初始化RCA训练器
        
        Args:
            gaussians: 3D高斯模型
            config: 训练配置
            device: 计算设备
        """
        self.gaussians = gaussians
        self.config = config
        self.device = device
        
        # 初始化RCA组件
        self._init_rca_components()
        
        # 初始化CISM组件（如果可用）
        if CISM_AVAILABLE:
            self._init_cism_components()
        
        # 训练状态
        self.iteration = 0
        self.training_stats = {
            'rca_losses': [],
            'spatial_consistency_losses': [],
            'concept_separation_losses': [],
            'modulation_quality_scores': [],
            'training_times': []
        }
        
        logging.info("RCATrainer initialized")
    
    def _init_rca_components(self):
        """初始化RCA核心组件"""
        # 空间注意力网络
        self.spatial_attention = SpatialAttention3D(
            input_dim=self.config.get('spatial_attention', {}).get('input_dim', 13),
            hidden_dim=self.config.get('spatial_attention', {}).get('hidden_dim', 256),
            num_concepts=self.config.get('num_concepts', 3),
            dropout_rate=self.config.get('spatial_attention', {}).get('dropout_rate', 0.1),
            use_residual=self.config.get('spatial_attention', {}).get('use_residual', True)
        ).to(self.device)
        
        # 概念权重渲染器
        self.weight_renderer = ConceptWeightRenderer(
            device=self.device,
            enable_sh=self.config.get('weight_renderer', {}).get('enable_sh', False)
        )
        
        # 空间调制器
        self.spatial_modulator = SpatialModulation(
            device=self.device,
            enable_boundary_smoothing=self.config.get('spatial_modulation', {}).get('enable_boundary_smoothing', True),
            smoothing_kernel_size=self.config.get('spatial_modulation', {}).get('smoothing_kernel_size', 5),
            smoothing_sigma=self.config.get('spatial_modulation', {}).get('smoothing_sigma', 1.0)
        )
        
        # 设置空间注意力网络的优化器
        self.spatial_optimizer = torch.optim.Adam(
            self.spatial_attention.parameters(),
            lr=self.config.get('spatial_attention', {}).get('learning_rate', 0.001),
            weight_decay=self.config.get('spatial_attention', {}).get('weight_decay', 1e-4)
        )
        
        logging.info("RCA components initialized")
    
    def _init_cism_components(self):
        """初始化CISM组件"""
        if not CISM_AVAILABLE:
            logging.warning("CISM components not available, skipping CISM initialization")
            return
        
        # 扩散模型引擎
        self.diffusion_engine = DiffusionEngine(
            model_id=self.config.get('cism', {}).get('model_id', 'runwayml/stable-diffusion-v1-5'),
            device=self.device,
            half_precision=self.config.get('cism', {}).get('half_precision', True)
        )
        
        # 概念引导系统
        concept_config_path = self.config.get('cism', {}).get('concept_config_path', 'configs/concept_prompts.yaml')
        self.concept_guidance = ConceptGuidance(
            self.diffusion_engine,
            concept_config_path,
            self.device
        )
        
        # SDS损失计算器
        self.sds_loss = SDSLoss(
            self.diffusion_engine,
            self.concept_guidance,
            device=self.device
        )
        
        # 设置用户概念
        user_concept = self.config.get('cism', {}).get('user_concept')
        if user_concept:
            self.concept_guidance.set_user_concept(1, user_concept)
            logging.info(f"Set user concept: {user_concept}")
        
        logging.info("CISM components initialized")
    
    def setup_training_schedule(self):
        """设置训练调度"""
        self.rca_start_iter = self.config.get('training_schedule', {}).get('rca_start_iter', 2000)
        self.rca_end_iter = self.config.get('training_schedule', {}).get('rca_end_iter', 20000)
        self.rca_interval = self.config.get('training_schedule', {}).get('rca_interval', 5)
        
        # RCA损失权重调度
        self.rca_weight_schedule = self._create_rca_weight_schedule()
        
        logging.info(f"RCA training: iter {self.rca_start_iter}-{self.rca_end_iter}, interval {self.rca_interval}")
    
    def _create_rca_weight_schedule(self) -> Dict:
        """创建RCA损失权重调度"""
        schedule = {
            'spatial_consistency_weight': self.config.get('loss_weights', {}).get('spatial_consistency', 0.1),
            'concept_separation_weight': self.config.get('loss_weights', {}).get('concept_separation', 0.05),
            'modulation_quality_weight': self.config.get('loss_weights', {}).get('modulation_quality', 0.02),
            'rca_total_weight': self.config.get('loss_weights', {}).get('rca_total', 1.0)
        }
        return schedule
    
    def should_apply_rca(self, iteration: int) -> bool:
        """判断是否应该应用RCA"""
        if iteration < self.rca_start_iter or iteration > self.rca_end_iter:
            return False
        
        return (iteration - self.rca_start_iter) % self.rca_interval == 0
    
    def compute_rca_loss(
        self,
        rendered_images: torch.Tensor,
        viewpoint_camera,
        iteration: int
    ) -> Tuple[torch.Tensor, Dict]:
        """
        计算RCA损失
        
        Args:
            rendered_images: 渲染图像 [batch_size, 3, H, W]
            viewpoint_camera: 视点相机
            iteration: 当前迭代
            
        Returns:
            loss: RCA损失
            info: 损失信息
        """
        start_time = time.time()
        
        # 1. 使用空间注意力网络预测概念权重
        spatial_results = self.spatial_attention(self.gaussians, use_global_attention=True)
        concept_weights_3d = spatial_results['concept_weights']  # [N, num_concepts]
        
        # 2. 渲染概念权重图
        weight_render_results = self.weight_renderer.render_concept_weights(
            viewpoint_camera, self.gaussians, None, concept_weights_3d
        )
        weight_maps = weight_render_results['normalized_weights']  # [H, W, num_concepts]
        
        # 3. 计算空间一致性损失
        spatial_consistency_loss = self.spatial_attention.compute_spatial_consistency_loss(
            concept_weights_3d, self.gaussians, neighbor_radius=0.1
        )
        
        # 4. 计算概念分离损失
        concept_separation_loss = self._compute_concept_separation_loss(concept_weights_3d)
        
        # 5. 如果CISM可用，计算空间调制的CISM损失
        modulation_loss = torch.tensor(0.0, device=self.device)
        modulation_quality_score = 0.0
        
        if CISM_AVAILABLE and hasattr(self, 'sds_loss'):
            # 计算原始CISM引导
            concept_ids = self.config.get('active_concepts', [0, 1, 2])
            delta_epsilons = {}
            
            # 编码图像到潜在空间
            latents = self.sds_loss.encode_images_to_latents(rendered_images)
            timesteps = self.sds_loss.sample_timesteps(rendered_images.shape[0])
            noise = torch.randn_like(latents)
            noisy_latents = self.diffusion_engine.add_noise(latents, noise, timesteps)
            
            # 计算每个概念的引导差异
            for concept_id in concept_ids:
                delta_epsilon = self.concept_guidance.compute_guidance_delta(
                    noisy_latents, timesteps, concept_id, guidance_scale=7.5
                )
                delta_epsilons[concept_id] = delta_epsilon
            
            # 空间调制
            modulated_guidance = self.spatial_modulator.spatially_modulated_guidance(
                delta_epsilons, weight_maps, modulation_strength=1.0
            )
            
            # 计算调制后的SDS损失
            w_t = self.sds_loss.time_weight(timesteps)
            target = (latents - latents.detach())
            loss_per_sample = torch.sum(modulated_guidance * target, dim=[1, 2, 3])
            modulation_loss = (w_t * loss_per_sample).mean()
            
            # 计算调制质量指标
            original_guidance = sum(delta_epsilons.values()) / len(delta_epsilons)
            quality_metrics = self.spatial_modulator.compute_modulation_quality_metrics(
                original_guidance, modulated_guidance, weight_maps
            )
            modulation_quality_score = quality_metrics['overall_quality']
        
        # 6. 组合所有损失
        weights = self.rca_weight_schedule
        
        total_rca_loss = (
            weights['spatial_consistency_weight'] * spatial_consistency_loss +
            weights['concept_separation_weight'] * concept_separation_loss +
            weights['modulation_quality_weight'] * modulation_loss
        ) * weights['rca_total_weight']
        
        # 计算时间
        compute_time = time.time() - start_time
        
        # 收集信息
        info = {
            'spatial_consistency_loss': spatial_consistency_loss.item(),
            'concept_separation_loss': concept_separation_loss.item(),
            'modulation_loss': modulation_loss.item(),
            'modulation_quality_score': modulation_quality_score,
            'total_rca_loss': total_rca_loss.item(),
            'compute_time': compute_time,
            'concept_weights_stats': self.spatial_attention.get_concept_statistics(concept_weights_3d)
        }
        
        return total_rca_loss, info
    
    def _compute_concept_separation_loss(self, concept_weights: torch.Tensor) -> torch.Tensor:
        """
        计算概念分离损失，鼓励不同概念的清晰分离
        
        Args:
            concept_weights: 概念权重 [N, num_concepts]
            
        Returns:
            separation_loss: 概念分离损失
        """
        # 计算权重的熵，低熵表示权重集中（好的分离）
        entropy = -torch.sum(concept_weights * torch.log(concept_weights + 1e-8), dim=-1)
        
        # 我们希望熵较低，所以损失是熵的平均值
        separation_loss = entropy.mean()
        
        return separation_loss
    
    def training_step(
        self,
        rendered_images: torch.Tensor,
        viewpoint_camera,
        iteration: int
    ) -> Optional[Tuple[torch.Tensor, Dict]]:
        """
        RCA训练步骤
        
        Args:
            rendered_images: 渲染图像
            viewpoint_camera: 视点相机
            iteration: 当前迭代
            
        Returns:
            loss和信息，如果不应用RCA则返回None
        """
        self.iteration = iteration
        
        if not self.should_apply_rca(iteration):
            return None
        
        try:
            # 计算RCA损失
            rca_loss, info = self.compute_rca_loss(rendered_images, viewpoint_camera, iteration)
            
            # 更新空间注意力网络
            self.spatial_optimizer.zero_grad()
            rca_loss.backward(retain_graph=True)
            
            # 梯度裁剪
            max_grad_norm = self.config.get('spatial_attention', {}).get('max_grad_norm', 1.0)
            torch.nn.utils.clip_grad_norm_(self.spatial_attention.parameters(), max_grad_norm)
            
            self.spatial_optimizer.step()
            
            # 更新统计信息
            self._update_training_stats(info)
            
            return rca_loss, info
            
        except Exception as e:
            logging.error(f"RCA training step failed at iteration {iteration}: {e}")
            return None
    
    def _update_training_stats(self, info: Dict):
        """更新训练统计信息"""
        self.training_stats['rca_losses'].append(info['total_rca_loss'])
        self.training_stats['spatial_consistency_losses'].append(info['spatial_consistency_loss'])
        self.training_stats['concept_separation_losses'].append(info['concept_separation_loss'])
        self.training_stats['modulation_quality_scores'].append(info['modulation_quality_score'])
        self.training_stats['training_times'].append(info['compute_time'])
        
        # 限制统计历史长度
        max_history = 1000
        for key in self.training_stats:
            if len(self.training_stats[key]) > max_history:
                self.training_stats[key] = self.training_stats[key][-max_history:]
    
    def get_training_stats(self) -> Dict:
        """获取训练统计信息"""
        stats = self.training_stats.copy()
        
        if stats['rca_losses']:
            stats['avg_rca_loss'] = sum(stats['rca_losses']) / len(stats['rca_losses'])
            stats['avg_spatial_consistency_loss'] = sum(stats['spatial_consistency_losses']) / len(stats['spatial_consistency_losses'])
            stats['avg_concept_separation_loss'] = sum(stats['concept_separation_losses']) / len(stats['concept_separation_losses'])
            stats['avg_modulation_quality'] = sum(stats['modulation_quality_scores']) / len(stats['modulation_quality_scores'])
            stats['avg_compute_time'] = sum(stats['training_times']) / len(stats['training_times'])
        else:
            stats['avg_rca_loss'] = 0.0
            stats['avg_spatial_consistency_loss'] = 0.0
            stats['avg_concept_separation_loss'] = 0.0
            stats['avg_modulation_quality'] = 0.0
            stats['avg_compute_time'] = 0.0
        
        stats['total_rca_steps'] = len(stats['rca_losses'])
        
        return stats
    
    def save_training_state(self, save_path: str):
        """保存训练状态"""
        state = {
            'iteration': self.iteration,
            'config': self.config,
            'training_stats': self.training_stats,
            'spatial_attention_state': self.spatial_attention.state_dict(),
            'spatial_optimizer_state': self.spatial_optimizer.state_dict()
        }
        
        torch.save(state, save_path)
        logging.info(f"RCA training state saved to {save_path}")
    
    def load_training_state(self, load_path: str):
        """加载训练状态"""
        if Path(load_path).exists():
            state = torch.load(load_path, map_location=self.device)
            self.iteration = state.get('iteration', 0)
            self.training_stats = state.get('training_stats', {
                'rca_losses': [], 'spatial_consistency_losses': [], 
                'concept_separation_losses': [], 'modulation_quality_scores': [], 'training_times': []
            })
            
            # 恢复网络状态
            if 'spatial_attention_state' in state:
                self.spatial_attention.load_state_dict(state['spatial_attention_state'])
            if 'spatial_optimizer_state' in state:
                self.spatial_optimizer.load_state_dict(state['spatial_optimizer_state'])
            
            logging.info(f"RCA training state loaded from {load_path}")
        else:
            logging.warning(f"Training state file not found: {load_path}")
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'diffusion_engine'):
            self.diffusion_engine.cleanup()
        if hasattr(self, 'concept_guidance'):
            self.concept_guidance.cleanup()
        
        torch.cuda.empty_cache()
        logging.info("RCATrainer cleaned up")
