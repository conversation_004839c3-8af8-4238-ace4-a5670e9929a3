<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_transform</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_transform<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00158.html" title="GLM_GTX_transform ">glm/gtx/transform.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaf599be4c0e9d99be1f9cddba79b6018b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf599be4c0e9d99be1f9cddba79b6018b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00362.html#gaf599be4c0e9d99be1f9cddba79b6018b">rotate</a> (T angle, vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaf599be4c0e9d99be1f9cddba79b6018b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a rotation 4 * 4 matrix created from an axis of 3 scalars and an angle expressed in radians.  <a href="a00362.html#gaf599be4c0e9d99be1f9cddba79b6018b">More...</a><br /></td></tr>
<tr class="separator:gaf599be4c0e9d99be1f9cddba79b6018b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafbeefee8fec884d566e4ada0049174d7"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafbeefee8fec884d566e4ada0049174d7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00362.html#gafbeefee8fec884d566e4ada0049174d7">scale</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gafbeefee8fec884d566e4ada0049174d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a scale 4 * 4 matrix created from a vector of 3 components.  <a href="a00362.html#gafbeefee8fec884d566e4ada0049174d7">More...</a><br /></td></tr>
<tr class="separator:gafbeefee8fec884d566e4ada0049174d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga309a30e652e58c396e2c3d4db3ee7658"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga309a30e652e58c396e2c3d4db3ee7658"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00362.html#ga309a30e652e58c396e2c3d4db3ee7658">translate</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga309a30e652e58c396e2c3d4db3ee7658"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a matrix with a translation 4 * 4 matrix created from 3 scalars.  <a href="a00362.html#ga309a30e652e58c396e2c3d4db3ee7658">More...</a><br /></td></tr>
<tr class="separator:ga309a30e652e58c396e2c3d4db3ee7658"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00158.html" title="GLM_GTX_transform ">glm/gtx/transform.hpp</a>&gt; to use the features of this extension. </p>
<p>Add transformation matrices </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaf599be4c0e9d99be1f9cddba79b6018b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a rotation 4 * 4 matrix created from an axis of 3 scalars and an angle expressed in radians. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00296.html" title="Include <glm/gtc/matrix_transform.hpp> to use the features of this extension. ">GLM_GTC_matrix_transform</a> </dd>
<dd>
<a class="el" href="a00362.html" title="Include <glm/gtx/transform.hpp> to use the features of this extension. ">GLM_GTX_transform</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafbeefee8fec884d566e4ada0049174d7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::scale </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a matrix with a scale 4 * 4 matrix created from a vector of 3 components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00296.html" title="Include <glm/gtc/matrix_transform.hpp> to use the features of this extension. ">GLM_GTC_matrix_transform</a> </dd>
<dd>
<a class="el" href="a00362.html" title="Include <glm/gtx/transform.hpp> to use the features of this extension. ">GLM_GTX_transform</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga309a30e652e58c396e2c3d4db3ee7658"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::translate </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a matrix with a translation 4 * 4 matrix created from 3 scalars. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00296.html" title="Include <glm/gtc/matrix_transform.hpp> to use the features of this extension. ">GLM_GTC_matrix_transform</a> </dd>
<dd>
<a class="el" href="a00362.html" title="Include <glm/gtx/transform.hpp> to use the features of this extension. ">GLM_GTX_transform</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
