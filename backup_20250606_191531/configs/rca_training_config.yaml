# RCA训练配置文件
# 阶段3: RCA区域控制系统训练参数

# 基本配置
experiment_name: "rca_enhanced_garden"
output_dir: "output/rca_experiments"
device: "cuda"
num_concepts: 3

# 空间注意力网络配置
spatial_attention:
  input_dim: 13  # 3(xyz) + 3(color) + 1(opacity) + 3(scaling) + 3(concept_onehot)
  hidden_dim: 256
  dropout_rate: 0.1
  use_residual: true
  learning_rate: 0.001
  weight_decay: 0.0001
  max_grad_norm: 1.0

# 概念权重渲染器配置
weight_renderer:
  enable_sh: false
  background_color: [0.0, 0.0, 0.0]

# 空间调制器配置
spatial_modulation:
  enable_boundary_smoothing: true
  smoothing_kernel_size: 5
  smoothing_sigma: 1.0

# CISM集成配置
cism:
  model_id: "runwayml/stable-diffusion-v1-5"
  half_precision: true
  concept_config_path: "configs/concept_prompts.yaml"
  user_concept: "modern minimalist garden with geometric patterns, clean lines, contemporary landscape design"

# RCA训练调度
training_schedule:
  rca_start_iter: 2000       # RCA开始迭代（在CISM稳定后）
  rca_end_iter: 20000        # RCA结束迭代
  rca_interval: 5            # RCA应用间隔（比CISM更频繁）

# 损失权重配置
loss_weights:
  spatial_consistency: 0.1   # 空间一致性损失权重
  concept_separation: 0.05   # 概念分离损失权重
  modulation_quality: 0.02   # 调制质量损失权重
  rca_total: 1.0            # RCA总权重

# 活跃概念配置
active_concepts: [0, 1, 2]  # 背景、修复、边界

# 训练优化
optimization:
  # 3DGS基础学习率（保持原有设置）
  position_lr_init: 0.00016
  position_lr_final: 0.0000016
  feature_lr: 0.0025
  opacity_lr: 0.05
  scaling_lr: 0.005
  rotation_lr: 0.001
  
  # concept_confidence学习率
  concept_confidence_lr: 0.005

# 渲染配置
rendering:
  image_resolution: 512
  batch_size: 1
  random_background: true

# 监控和日志
monitoring:
  log_interval: 50           # 更频繁的日志输出
  save_interval: 1000
  eval_interval: 500
  
  # 可视化
  save_training_images: true
  save_concept_visualizations: true
  save_weight_maps: true
  save_modulation_visualizations: true

# 内存管理
memory:
  gradient_accumulation_steps: 1
  max_memory_usage: 0.9
  clear_cache_interval: 50   # 更频繁的缓存清理

# 高级设置
advanced:
  # 空间一致性
  spatial_consistency_radius: 0.1
  spatial_consistency_threshold: 0.05
  
  # 概念分离
  concept_separation_target_entropy: 0.5
  concept_separation_margin: 0.1
  
  # 边界处理
  boundary_detection_threshold: 0.1
  boundary_smoothing_iterations: 3
  
  # 自适应调制
  adaptive_modulation: true
  modulation_strength_range: [0.5, 1.5]
  
  # 质量评估
  quality_assessment_interval: 100
  quality_metrics: ["fidelity", "spatial_consistency", "concept_separation"]

# 实验变体配置
experiment_variants:
  # 基础RCA
  basic:
    spatial_attention:
      hidden_dim: 128
    loss_weights:
      spatial_consistency: 0.05
      concept_separation: 0.02
  
  # 高精度RCA
  high_precision:
    spatial_attention:
      hidden_dim: 512
      dropout_rate: 0.05
    loss_weights:
      spatial_consistency: 0.2
      concept_separation: 0.1
    training_schedule:
      rca_interval: 3
  
  # 快速RCA
  fast:
    spatial_attention:
      hidden_dim: 128
      learning_rate: 0.002
    training_schedule:
      rca_interval: 10
    monitoring:
      log_interval: 100

# 数据路径
data_paths:
  # 输入数据（基于CISM增强结果）
  gaussians_path: "output/cism_experiments/enhanced_gaussians.ply"
  cameras_dir: "output/garden/"
  concept_masks_dir: "data/garden/concept_masks/"
  
  # 输出路径
  output_gaussians: "output/rca_experiments/rca_enhanced_gaussians.ply"
  training_logs: "output/rca_experiments/logs/"
  visualizations: "output/rca_experiments/visualizations/"
  weight_maps: "output/rca_experiments/weight_maps/"

# 评估配置
evaluation:
  # 评估指标
  metrics: ["psnr", "ssim", "lpips", "concept_consistency", "spatial_coherence"]
  
  # 评估视角
  eval_cameras: "test"
  
  # RCA质量评估
  rca_evaluation:
    enabled: true
    spatial_consistency_threshold: 0.8
    concept_separation_threshold: 0.7
    modulation_quality_threshold: 0.75
    
  # 对比评估
  comparison_baselines: ["original_3dgs", "cism_only", "rca_enhanced"]

# 调试和开发
debug:
  enable_debug_mode: false
  save_intermediate_results: true
  profile_performance: false
  verbose_logging: false
  
  # 可视化调试
  visualize_attention_weights: false
  visualize_concept_weights: true
  visualize_modulation_process: true

# 使用说明
usage_instructions: |
  1. 确保已完成阶段2的CISM训练
  2. 检查CISM增强模型路径
  3. 配置RCA参数和用户概念
  4. 运行RCA增强训练：
     python train_rca.py --config configs/rca_training_config.yaml
  
  5. 监控训练进度：
     tensorboard --logdir output/rca_experiments/logs/
  
  6. 评估结果：
     python evaluate_rca.py --model_path output/rca_experiments/rca_enhanced_gaussians.ply

# 性能优化建议
performance_tips:
  - 使用较小的rca_interval以获得更好的空间控制
  - 调整spatial_consistency权重以平衡质量和训练速度
  - 启用boundary_smoothing以获得更好的边界质量
  - 使用adaptive_modulation以获得更智能的空间调制

# 故障排除
troubleshooting:
  memory_issues:
    - 减少spatial_attention的hidden_dim
    - 增加clear_cache_interval频率
    - 启用gradient_accumulation_steps
  
  convergence_issues:
    - 降低spatial_attention的learning_rate
    - 调整loss_weights的平衡
    - 增加rca_start_iter延迟RCA介入
  
  quality_issues:
    - 增加spatial_consistency权重
    - 启用boundary_smoothing
    - 调整concept_separation_target_entropy
