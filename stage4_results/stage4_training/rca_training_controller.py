# -*- coding: utf-8 -*-
"""
InFusion 阶段4: RCA训练控制器
专门负责RCA网络训练过程的精细控制和监管
"""

import torch
import torch.nn as nn
import numpy as np
import os
import time
import json
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
import logging
from collections import defaultdict, deque
import matplotlib.pyplot as plt
from pathlib import Path

# 导入相关组件
from .training_config_manager import TrainingConfigManager, Stage4TrainingConfig
from .rca_training_data_generator import RCATrainingDataGenerator
from .rca_loss_functions import RCALossFunction
from .rca_end_to_end_optimizer import EndToEndOptimizerConfig, GradientManager, LearningRateScheduler

# 导入RCA网络
from ..stage3_rca.rca_spatial_attention import RCASpatialAttentionNetwork

@dataclass
class TrainingState:
    """训练状态跟踪"""
    current_epoch: int = 0
    current_iteration: int = 0
    best_loss: float = float('inf')
    best_epoch: int = -1
    patience_counter: int = 0
    
    # 性能监控
    recent_losses: deque = None
    recent_accuracies: deque = None
    gradient_norms: deque = None
    
    # 训练标志
    is_training: bool = False
    early_stopping_triggered: bool = False
    convergence_detected: bool = False
    
    def __post_init__(self):
        if self.recent_losses is None:
            self.recent_losses = deque(maxlen=100)
        if self.recent_accuracies is None:
            self.recent_accuracies = deque(maxlen=100)
        if self.gradient_norms is None:
            self.gradient_norms = deque(maxlen=100)

@dataclass
class TrainingMetrics:
    """训练指标"""
    # 损失指标
    total_loss: float = 0.0
    classification_loss: float = 0.0
    regularization_loss: float = 0.0
    consistency_loss: float = 0.0
    boundary_loss: float = 0.0
    
    # 准确率指标
    overall_accuracy: float = 0.0
    per_concept_accuracy: Dict[int, float] = None
    
    # 其他指标
    gradient_norm: float = 0.0
    learning_rate: float = 0.0
    batch_processing_time: float = 0.0
    
    def __post_init__(self):
        if self.per_concept_accuracy is None:
            self.per_concept_accuracy = {}

class RCATrainingController:
    """
    🔥 RCA训练控制器
    
    功能:
    - 精细控制RCA网络训练过程
    - 实时监控训练状态和性能
    - 自适应调整训练策略
    - 提供训练过程的可视化和分析
    """
    
    def __init__(self, 
                 config_manager: TrainingConfigManager,
                 rca_network: RCASpatialAttentionNetwork,
                 data_generator: RCATrainingDataGenerator,
                 loss_function: RCALossFunction):
        """
        Args:
            config_manager: 配置管理器
            rca_network: RCA网络
            data_generator: 数据生成器
            loss_function: 损失函数
        """
        self.config_manager = config_manager
        self.config = config_manager.get_config()
        self.rca_network = rca_network
        self.data_generator = data_generator
        self.loss_function = loss_function
        
        # 设备设置
        self.device = torch.device(self.config.training.device)
        
        # 训练状态
        self.training_state = TrainingState()
        
        # 历史记录
        self.training_history = {
            'epoch_metrics': [],
            'batch_metrics': [],
            'learning_rate_history': [],
            'gradient_norm_history': [],
            'validation_history': []
        }
        
        # 优化器和调度器
        self.optimizers = {}
        self.schedulers = {}
        self.gradient_manager = None
        
        # 监控和分析
        self.performance_monitor = PerformanceMonitor()
        self.convergence_analyzer = ConvergenceAnalyzer()
        
        # 检查点管理
        self.checkpoint_manager = CheckpointManager(
            checkpoint_dir=self.config.experiment.checkpoint_dir,
            keep_n_checkpoints=self.config.training.keep_n_checkpoints
        )
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        print(f"🎯 RCA训练控制器初始化完成")
    
    def setup_training_components(self):
        """
        🔥 设置训练组件
        初始化优化器、调度器、梯度管理器等
        """
        print("🔧 设置训练组件...")
        
        # 1. 设置优化器
        self._setup_optimizers()
        
        # 2. 设置学习率调度器
        self._setup_schedulers()
        
        # 3. 设置梯度管理器
        optimizer_config = EndToEndOptimizerConfig(
            rca_learning_rate=self.config.optimizer.rca_learning_rate,
            optimizer_type=self.config.optimizer.optimizer_type,
            weight_decay=self.config.optimizer.weight_decay,
            gradient_clip_norm=self.config.optimizer.gradient_clip_norm,
            use_mixed_precision=self.config.training.use_mixed_precision,
            loss_scale=self.config.training.loss_scale
        )
        
        self.gradient_manager = GradientManager(optimizer_config)
        
        # 4. 设置性能监控
        self.performance_monitor.setup(
            window_size=100,
            metrics=['loss', 'accuracy', 'gradient_norm', 'lr']
        )
        
        print("✅ 训练组件设置完成")
    
    def _setup_optimizers(self):
        """设置优化器"""
        if self.config.optimizer.optimizer_type == "adamw":
            optimizer = torch.optim.AdamW(
                self.rca_network.parameters(),
                lr=self.config.optimizer.rca_learning_rate,
                weight_decay=self.config.optimizer.weight_decay,
                betas=(self.config.optimizer.beta1, self.config.optimizer.beta2),
                eps=self.config.optimizer.eps
            )
        elif self.config.optimizer.optimizer_type == "adam":
            optimizer = torch.optim.Adam(
                self.rca_network.parameters(),
                lr=self.config.optimizer.rca_learning_rate,
                weight_decay=self.config.optimizer.weight_decay,
                betas=(self.config.optimizer.beta1, self.config.optimizer.beta2),
                eps=self.config.optimizer.eps
            )
        else:  # sgd
            optimizer = torch.optim.SGD(
                self.rca_network.parameters(),
                lr=self.config.optimizer.rca_learning_rate,
                weight_decay=self.config.optimizer.weight_decay,
                momentum=self.config.optimizer.momentum
            )
        
        self.optimizers['rca'] = optimizer
        
        print(f"   优化器类型: {self.config.optimizer.optimizer_type}")
        print(f"   学习率: {self.config.optimizer.rca_learning_rate}")
    
    def _setup_schedulers(self):
        """设置学习率调度器"""
        optimizer = self.optimizers['rca']
        
        if self.config.optimizer.scheduler_type == "cosine":
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=self.config.training.max_epochs,
                eta_min=self.config.optimizer.rca_learning_rate * 0.01
            )
        elif self.config.optimizer.scheduler_type == "step":
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                step_size=self.config.optimizer.step_size,
                gamma=self.config.optimizer.gamma
            )
        else:  # plateau
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='min',
                patience=self.config.optimizer.patience,
                factor=self.config.optimizer.gamma,
                verbose=True
            )
        
        self.schedulers['rca'] = scheduler
        
        print(f"   调度器类型: {self.config.optimizer.scheduler_type}")
    
    def train_epoch(self, epoch: int) -> TrainingMetrics:
        """
        🔥 训练一个epoch
        
        Args:
            epoch: 当前epoch数
            
        Returns:
            TrainingMetrics: 训练指标
        """
        self.training_state.current_epoch = epoch
        self.training_state.is_training = True
        self.rca_network.train()
        
        epoch_metrics = TrainingMetrics()
        batch_count = 0
        total_samples = 0
        
        # 预热学习率
        if epoch < self.config.optimizer.warmup_steps:
            self._apply_warmup_lr(epoch)
        
        print(f"\n📚 开始训练 Epoch {epoch + 1}/{self.config.training.max_epochs}")
        
        # 计算batch数量
        total_points = self.data_generator.data_stats['total_points']
        num_batches = max(1, total_points // self.config.data.points_per_batch)
        
        for batch_idx in range(num_batches):
            batch_start_time = time.time()
            
            try:
                # 生成训练批次
                batch_data = self.data_generator.generate_training_batch(
                    batch_size=self.config.data.points_per_batch
                )
                
                # 移动到设备
                batch_data = self._move_batch_to_device(batch_data)
                
                # 训练一个批次
                batch_metrics = self._train_one_batch(batch_data)
                
                # 累积指标
                self._accumulate_metrics(epoch_metrics, batch_metrics, batch_count)
                batch_count += 1
                total_samples += batch_data['features'].shape[0]
                
                # 记录批次指标
                batch_metrics.batch_processing_time = time.time() - batch_start_time
                self.training_history['batch_metrics'].append(batch_metrics)
                
                # 更新训练状态
                self.training_state.current_iteration += 1
                self.training_state.recent_losses.append(batch_metrics.total_loss)
                self.training_state.recent_accuracies.append(batch_metrics.overall_accuracy)
                self.training_state.gradient_norms.append(batch_metrics.gradient_norm)
                
                # 性能监控
                self.performance_monitor.update(batch_metrics)
                
                # 打印训练进度
                if (batch_idx + 1) % max(1, num_batches // 10) == 0:
                    self._print_batch_progress(batch_idx + 1, num_batches, batch_metrics)
                
            except Exception as e:
                self.logger.warning(f"批次 {batch_idx} 训练失败: {str(e)}")
                continue
        
        # 计算epoch平均指标
        if batch_count > 0:
            self._finalize_epoch_metrics(epoch_metrics, batch_count)
        
        # 记录epoch指标
        self.training_history['epoch_metrics'].append(epoch_metrics)
        
        # 更新学习率历史
        current_lr = self.optimizers['rca'].param_groups[0]['lr']
        self.training_history['learning_rate_history'].append(current_lr)
        epoch_metrics.learning_rate = current_lr
        
        # 检查收敛性
        self.convergence_analyzer.update(epoch_metrics)
        if self.convergence_analyzer.is_converged():
            self.training_state.convergence_detected = True
            self.logger.info(f"检测到收敛，epoch: {epoch + 1}")
        
        self.training_state.is_training = False
        
        print(f"📊 Epoch {epoch + 1} 完成")
        print(f"   平均损失: {epoch_metrics.total_loss:.6f}")
        print(f"   平均准确率: {epoch_metrics.overall_accuracy:.4f}")
        print(f"   梯度范数: {epoch_metrics.gradient_norm:.6f}")
        print(f"   学习率: {epoch_metrics.learning_rate:.8f}")
        
        return epoch_metrics
    
    def _move_batch_to_device(self, batch_data: Dict) -> Dict:
        """将批次数据移动到设备"""
        for key, value in batch_data.items():
            if torch.is_tensor(value):
                batch_data[key] = value.to(self.device)
            elif isinstance(value, dict):
                batch_data[key] = self._move_batch_to_device(value)
        return batch_data
    
    def _train_one_batch(self, batch_data: Dict) -> TrainingMetrics:
        """训练一个批次"""
        # 前向传播
        features = batch_data['features']
        predictions = self.rca_network(features)
        
        # 计算损失
        loss_dict = self.loss_function(
            predictions=predictions,
            targets=batch_data['labels'],
            weight_maps=batch_data.get('weight_maps'),
            positions_3d=batch_data.get('positions'),
            network_parameters=list(self.rca_network.parameters())
        )
        
        total_loss = loss_dict['total_loss']
        
        # 反向传播和优化
        grad_info = self.gradient_manager.backward_and_step(
            loss=total_loss,
            optimizers=self.optimizers,
            step=self.training_state.current_iteration
        )
        
        # 计算准确率
        accuracy_metrics = self._compute_batch_accuracy(predictions, batch_data['labels'])
        
        # 创建批次指标
        batch_metrics = TrainingMetrics(
            total_loss=total_loss.item(),
            classification_loss=loss_dict['classification_loss'].item(),
            regularization_loss=loss_dict['regularization_loss'].item(),
            consistency_loss=loss_dict['consistency_loss'].item(),
            boundary_loss=loss_dict.get('boundary_loss', torch.tensor(0.0)).item(),
            overall_accuracy=accuracy_metrics['overall_accuracy'],
            per_concept_accuracy=accuracy_metrics['per_concept_accuracy'],
            gradient_norm=grad_info.get('rca_grad_norm', 0.0),
            learning_rate=self.optimizers['rca'].param_groups[0]['lr']
        )
        
        return batch_metrics
    
    def _compute_batch_accuracy(self, predictions: torch.Tensor, labels: Dict) -> Dict:
        """计算批次准确率"""
        predicted_labels = torch.argmax(predictions, dim=1)
        true_labels = labels['concept_ids']
        
        # 总体准确率
        overall_accuracy = (predicted_labels == true_labels).float().mean().item()
        
        # 每个概念的准确率
        per_concept_accuracy = {}
        for concept_id in range(self.config.model.num_concepts):
            concept_mask = (true_labels == concept_id)
            if concept_mask.sum() > 0:
                concept_acc = (predicted_labels[concept_mask] == true_labels[concept_mask]).float().mean().item()
                per_concept_accuracy[concept_id] = concept_acc
            else:
                per_concept_accuracy[concept_id] = 0.0
        
        return {
            'overall_accuracy': overall_accuracy,
            'per_concept_accuracy': per_concept_accuracy
        }
    
    def _apply_warmup_lr(self, epoch: int):
        """应用预热学习率"""
        warmup_factor = self.config.optimizer.warmup_factor
        target_lr = self.config.optimizer.rca_learning_rate
        
        warmup_lr = warmup_factor + (1.0 - warmup_factor) * epoch / self.config.optimizer.warmup_steps
        current_lr = target_lr * warmup_lr
        
        for param_group in self.optimizers['rca'].param_groups:
            param_group['lr'] = current_lr
    
    def _accumulate_metrics(self, epoch_metrics: TrainingMetrics, batch_metrics: TrainingMetrics, batch_count: int):
        """累积指标"""
        # 使用移动平均累积指标
        alpha = 1.0 / (batch_count + 1)
        
        epoch_metrics.total_loss = (1 - alpha) * epoch_metrics.total_loss + alpha * batch_metrics.total_loss
        epoch_metrics.classification_loss = (1 - alpha) * epoch_metrics.classification_loss + alpha * batch_metrics.classification_loss
        epoch_metrics.regularization_loss = (1 - alpha) * epoch_metrics.regularization_loss + alpha * batch_metrics.regularization_loss
        epoch_metrics.consistency_loss = (1 - alpha) * epoch_metrics.consistency_loss + alpha * batch_metrics.consistency_loss
        epoch_metrics.boundary_loss = (1 - alpha) * epoch_metrics.boundary_loss + alpha * batch_metrics.boundary_loss
        epoch_metrics.overall_accuracy = (1 - alpha) * epoch_metrics.overall_accuracy + alpha * batch_metrics.overall_accuracy
        epoch_metrics.gradient_norm = (1 - alpha) * epoch_metrics.gradient_norm + alpha * batch_metrics.gradient_norm
        
        # 累积每个概念的准确率
        if epoch_metrics.per_concept_accuracy is None:
            epoch_metrics.per_concept_accuracy = {}
        
        for concept_id, acc in batch_metrics.per_concept_accuracy.items():
            if concept_id in epoch_metrics.per_concept_accuracy:
                epoch_metrics.per_concept_accuracy[concept_id] = (1 - alpha) * epoch_metrics.per_concept_accuracy[concept_id] + alpha * acc
            else:
                epoch_metrics.per_concept_accuracy[concept_id] = acc
    
    def _finalize_epoch_metrics(self, epoch_metrics: TrainingMetrics, batch_count: int):
        """完成epoch指标计算"""
        # 这里指标已经通过移动平均累积，无需额外处理
        pass
    
    def _print_batch_progress(self, batch_idx: int, total_batches: int, batch_metrics: TrainingMetrics):
        """打印批次进度"""
        progress = batch_idx / total_batches * 100
        print(f"   批次 {batch_idx}/{total_batches} ({progress:.1f}%) - "
              f"损失: {batch_metrics.total_loss:.4f}, "
              f"准确率: {batch_metrics.overall_accuracy:.3f}, "
              f"梯度: {batch_metrics.gradient_norm:.4f}")
    
    def validate_epoch(self, epoch: int) -> TrainingMetrics:
        """
        🔥 验证一个epoch
        
        Args:
            epoch: 当前epoch数
            
        Returns:
            TrainingMetrics: 验证指标
        """
        self.rca_network.eval()
        val_metrics = TrainingMetrics()
        batch_count = 0
        
        print(f"🔍 开始验证 Epoch {epoch + 1}")
        
        with torch.no_grad():
            # 生成验证批次
            val_batch_size = max(1, int(self.config.data.points_per_batch * self.config.training.validation_split))
            num_val_batches = max(1, val_batch_size // self.config.training.batch_size)
            
            for batch_idx in range(num_val_batches):
                try:
                    # 生成验证批次
                    batch_data = self.data_generator.generate_training_batch(
                        batch_size=self.config.training.batch_size
                    )
                    
                    # 移动到设备
                    batch_data = self._move_batch_to_device(batch_data)
                    
                    # 前向传播
                    features = batch_data['features']
                    predictions = self.rca_network(features)
                    
                    # 计算损失
                    loss_dict = self.loss_function(
                        predictions=predictions,
                        targets=batch_data['labels'],
                        weight_maps=batch_data.get('weight_maps'),
                        positions_3d=batch_data.get('positions')
                    )
                    
                    # 计算准确率
                    accuracy_metrics = self._compute_batch_accuracy(predictions, batch_data['labels'])
                    
                    # 创建批次指标
                    batch_metrics = TrainingMetrics(
                        total_loss=loss_dict['total_loss'].item(),
                        classification_loss=loss_dict['classification_loss'].item(),
                        regularization_loss=loss_dict['regularization_loss'].item(),
                        consistency_loss=loss_dict['consistency_loss'].item(),
                        overall_accuracy=accuracy_metrics['overall_accuracy'],
                        per_concept_accuracy=accuracy_metrics['per_concept_accuracy']
                    )
                    
                    # 累积指标
                    self._accumulate_metrics(val_metrics, batch_metrics, batch_count)
                    batch_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"验证批次 {batch_idx} 失败: {str(e)}")
                    continue
        
        # 完成验证指标
        if batch_count > 0:
            self._finalize_epoch_metrics(val_metrics, batch_count)
        
        # 记录验证历史
        self.training_history['validation_history'].append(val_metrics)
        
        print(f"✅ 验证完成 - 损失: {val_metrics.total_loss:.6f}, 准确率: {val_metrics.overall_accuracy:.4f}")
        
        return val_metrics
    
    def update_learning_rate(self, val_metrics: TrainingMetrics = None):
        """更新学习率"""
        if self.config.optimizer.scheduler_type == "plateau":
            if val_metrics is not None:
                self.schedulers['rca'].step(val_metrics.total_loss)
        else:
            self.schedulers['rca'].step()
    
    def check_early_stopping(self, val_metrics: TrainingMetrics) -> bool:
        """检查早停条件"""
        current_loss = val_metrics.total_loss
        
        if current_loss < self.training_state.best_loss - self.config.training.early_stopping_min_delta:
            self.training_state.best_loss = current_loss
            self.training_state.best_epoch = self.training_state.current_epoch
            self.training_state.patience_counter = 0
            return False
        else:
            self.training_state.patience_counter += 1
            
            if self.training_state.patience_counter >= self.config.training.early_stopping_patience:
                self.training_state.early_stopping_triggered = True
                self.logger.info(f"早停触发，patience: {self.training_state.patience_counter}")
                return True
        
        return False
    
    def save_checkpoint(self, epoch: int, checkpoint_type: str = 'regular'):
        """保存检查点"""
        checkpoint_data = {
            'epoch': epoch,
            'training_state': self.training_state,
            'rca_network_state_dict': self.rca_network.state_dict(),
            'optimizer_state_dict': self.optimizers['rca'].state_dict(),
            'scheduler_state_dict': self.schedulers['rca'].state_dict(),
            'training_history': self.training_history,
            'config': self.config
        }
        
        self.checkpoint_manager.save_checkpoint(checkpoint_data, epoch, checkpoint_type)
    
    def load_checkpoint(self, checkpoint_path: str) -> bool:
        """加载检查点"""
        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            # 恢复网络状态
            self.rca_network.load_state_dict(checkpoint['rca_network_state_dict'])
            
            # 恢复优化器状态
            if 'optimizer_state_dict' in checkpoint:
                self.optimizers['rca'].load_state_dict(checkpoint['optimizer_state_dict'])
            
            # 恢复调度器状态
            if 'scheduler_state_dict' in checkpoint:
                self.schedulers['rca'].load_state_dict(checkpoint['scheduler_state_dict'])
            
            # 恢复训练状态
            if 'training_state' in checkpoint:
                self.training_state = checkpoint['training_state']
            
            # 恢复训练历史
            if 'training_history' in checkpoint:
                self.training_history = checkpoint['training_history']
            
            self.logger.info(f"检查点加载成功: {checkpoint_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"检查点加载失败: {str(e)}")
            return False
    
    def get_training_summary(self) -> Dict[str, Any]:
        """获取训练摘要"""
        if not self.training_history['epoch_metrics']:
            return {}
        
        recent_epochs = min(10, len(self.training_history['epoch_metrics']))
        recent_metrics = self.training_history['epoch_metrics'][-recent_epochs:]
        
        summary = {
            'current_epoch': self.training_state.current_epoch,
            'total_iterations': self.training_state.current_iteration,
            'best_loss': self.training_state.best_loss,
            'best_epoch': self.training_state.best_epoch,
            'early_stopping_triggered': self.training_state.early_stopping_triggered,
            'convergence_detected': self.training_state.convergence_detected,
            
            'recent_performance': {
                'avg_loss': np.mean([m.total_loss for m in recent_metrics]),
                'avg_accuracy': np.mean([m.overall_accuracy for m in recent_metrics]),
                'avg_gradient_norm': np.mean([m.gradient_norm for m in recent_metrics]),
                'loss_trend': self._compute_trend([m.total_loss for m in recent_metrics]),
                'accuracy_trend': self._compute_trend([m.overall_accuracy for m in recent_metrics])
            },
            
            'training_stability': {
                'loss_variance': np.var([m.total_loss for m in recent_metrics]),
                'accuracy_variance': np.var([m.overall_accuracy for m in recent_metrics]),
                'gradient_stability': self._compute_gradient_stability()
            }
        }
        
        return summary
    
    def _compute_trend(self, values: List[float]) -> str:
        """计算趋势"""
        if len(values) < 2:
            return "stable"
        
        # 简单线性回归斜率
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0]
        
        if slope > 0.01:
            return "increasing"
        elif slope < -0.01:
            return "decreasing"
        else:
            return "stable"
    
    def _compute_gradient_stability(self) -> float:
        """计算梯度稳定性"""
        if len(self.training_state.gradient_norms) < 10:
            return 0.0
        
        recent_norms = list(self.training_state.gradient_norms)[-10:]
        return 1.0 / (1.0 + np.var(recent_norms))
    
    def visualize_training_progress(self, save_path: str = None):
        """可视化训练进度"""
        if not self.training_history['epoch_metrics']:
            print("暂无训练历史数据")
            return
        
        epochs = range(1, len(self.training_history['epoch_metrics']) + 1)
        train_losses = [m.total_loss for m in self.training_history['epoch_metrics']]
        train_accuracies = [m.overall_accuracy for m in self.training_history['epoch_metrics']]
        
        val_losses = [m.total_loss for m in self.training_history['validation_history']]
        val_accuracies = [m.overall_accuracy for m in self.training_history['validation_history']]
        
        learning_rates = self.training_history['learning_rate_history']
        
        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        ax1.plot(epochs, train_losses, label='训练损失', color='blue')
        if val_losses:
            val_epochs = np.linspace(1, len(epochs), len(val_losses))
            ax1.plot(val_epochs, val_losses, label='验证损失', color='red')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('损失')
        ax1.set_title('训练/验证损失')
        ax1.legend()
        ax1.grid(True)
        
        # 准确率曲线
        ax2.plot(epochs, train_accuracies, label='训练准确率', color='blue')
        if val_accuracies:
            val_epochs = np.linspace(1, len(epochs), len(val_accuracies))
            ax2.plot(val_epochs, val_accuracies, label='验证准确率', color='red')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('准确率')
        ax2.set_title('训练/验证准确率')
        ax2.legend()
        ax2.grid(True)
        
        # 学习率曲线
        ax3.plot(epochs, learning_rates, label='学习率', color='green')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('学习率')
        ax3.set_title('学习率变化')
        ax3.legend()
        ax3.grid(True)
        ax3.set_yscale('log')
        
        # 梯度范数
        gradient_norms = [m.gradient_norm for m in self.training_history['epoch_metrics']]
        ax4.plot(epochs, gradient_norms, label='梯度范数', color='orange')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('梯度范数')
        ax4.set_title('梯度范数变化')
        ax4.legend()
        ax4.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"训练进度图保存至: {save_path}")
        else:
            plt.show()
    
    def generate_training_report(self) -> Dict[str, Any]:
        """生成训练报告"""
        summary = self.get_training_summary()
        
        report = {
            'experiment_info': {
                'experiment_name': self.config.experiment.experiment_name,
                'total_epochs': self.training_state.current_epoch + 1,
                'total_iterations': self.training_state.current_iteration,
                'completion_status': 'completed' if self.training_state.convergence_detected or self.training_state.early_stopping_triggered else 'in_progress'
            },
            
            'performance_summary': summary,
            
            'model_info': {
                'total_parameters': sum(p.numel() for p in self.rca_network.parameters()),
                'trainable_parameters': sum(p.numel() for p in self.rca_network.parameters() if p.requires_grad),
                'model_size_mb': sum(p.numel() * p.element_size() for p in self.rca_network.parameters()) / (1024 * 1024)
            },
            
            'training_config': {
                'optimizer': self.config.optimizer.optimizer_type,
                'learning_rate': self.config.optimizer.rca_learning_rate,
                'scheduler': self.config.optimizer.scheduler_type,
                'batch_size': self.config.data.points_per_batch,
                'mixed_precision': self.config.training.use_mixed_precision
            },
            
            'convergence_analysis': self.convergence_analyzer.get_analysis_report(),
            
            'performance_metrics': self.performance_monitor.get_performance_summary()
        }
        
        return report


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics_history = defaultdict(list)
        self.window_size = 100
        self.metrics_config = {}
    
    def setup(self, window_size: int = 100, metrics: List[str] = None):
        """设置监控参数"""
        self.window_size = window_size
        if metrics:
            for metric in metrics:
                self.metrics_config[metric] = {'enabled': True, 'threshold': None}
    
    def update(self, metrics: TrainingMetrics):
        """更新性能指标"""
        self.metrics_history['loss'].append(metrics.total_loss)
        self.metrics_history['accuracy'].append(metrics.overall_accuracy)
        self.metrics_history['gradient_norm'].append(metrics.gradient_norm)
        self.metrics_history['lr'].append(metrics.learning_rate)
        
        # 保持窗口大小
        for key in self.metrics_history:
            if len(self.metrics_history[key]) > self.window_size:
                self.metrics_history[key] = self.metrics_history[key][-self.window_size:]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {}
        
        for metric, values in self.metrics_history.items():
            if values:
                summary[metric] = {
                    'current': values[-1],
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'trend': self._compute_trend(values)
                }
        
        return summary
    
    def _compute_trend(self, values: List[float]) -> str:
        """计算趋势"""
        if len(values) < 5:
            return "insufficient_data"
        
        recent = values[-5:]
        slope = np.polyfit(range(len(recent)), recent, 1)[0]
        
        if slope > 0.001:
            return "increasing"
        elif slope < -0.001:
            return "decreasing"
        else:
            return "stable"


class ConvergenceAnalyzer:
    """收敛分析器"""
    
    def __init__(self):
        self.loss_history = deque(maxlen=50)
        self.accuracy_history = deque(maxlen=50)
        self.convergence_threshold = 1e-4
        self.stability_window = 10
        
    def update(self, metrics: TrainingMetrics):
        """更新收敛分析"""
        self.loss_history.append(metrics.total_loss)
        self.accuracy_history.append(metrics.overall_accuracy)
    
    def is_converged(self) -> bool:
        """检查是否收敛"""
        if len(self.loss_history) < self.stability_window:
            return False
        
        # 检查损失稳定性
        recent_losses = list(self.loss_history)[-self.stability_window:]
        loss_variance = np.var(recent_losses)
        
        # 检查准确率稳定性
        recent_accuracies = list(self.accuracy_history)[-self.stability_window:]
        accuracy_variance = np.var(recent_accuracies)
        
        return (loss_variance < self.convergence_threshold and 
                accuracy_variance < self.convergence_threshold)
    
    def get_analysis_report(self) -> Dict[str, Any]:
        """获取收敛分析报告"""
        if len(self.loss_history) < 5:
            return {'status': 'insufficient_data'}
        
        # 计算收敛指标
        loss_trend = self._compute_slope(list(self.loss_history))
        accuracy_trend = self._compute_slope(list(self.accuracy_history))
        
        # 计算稳定性
        loss_stability = 1.0 / (1.0 + np.var(list(self.loss_history)[-10:]))
        accuracy_stability = 1.0 / (1.0 + np.var(list(self.accuracy_history)[-10:]))
        
        return {
            'status': 'converged' if self.is_converged() else 'training',
            'loss_trend': loss_trend,
            'accuracy_trend': accuracy_trend,
            'loss_stability': loss_stability,
            'accuracy_stability': accuracy_stability,
            'convergence_score': (loss_stability + accuracy_stability) / 2
        }
    
    def _compute_slope(self, values: List[float]) -> float:
        """计算斜率"""
        if len(values) < 2:
            return 0.0
        
        x = np.arange(len(values))
        return np.polyfit(x, values, 1)[0]


class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, checkpoint_dir: str, keep_n_checkpoints: int = 5):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.keep_n_checkpoints = keep_n_checkpoints
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # 跟踪检查点
        self.checkpoints = {'regular': [], 'best': []}
    
    def save_checkpoint(self, checkpoint_data: Dict, epoch: int, checkpoint_type: str = 'regular'):
        """保存检查点"""
        checkpoint_filename = f"{checkpoint_type}_epoch_{epoch:04d}.pt"
        checkpoint_path = self.checkpoint_dir / checkpoint_filename
        
        # 保存检查点
        torch.save(checkpoint_data, checkpoint_path)
        
        # 更新检查点记录
        self.checkpoints[checkpoint_type].append({
            'epoch': epoch,
            'path': checkpoint_path,
            'timestamp': time.time()
        })
        
        # 清理旧检查点
        self._cleanup_old_checkpoints(checkpoint_type)
        
        print(f"💾 保存{checkpoint_type}检查点: {checkpoint_path}")
    
    def _cleanup_old_checkpoints(self, checkpoint_type: str):
        """清理旧检查点"""
        checkpoints = self.checkpoints[checkpoint_type]
        
        if len(checkpoints) > self.keep_n_checkpoints:
            # 按epoch排序
            checkpoints.sort(key=lambda x: x['epoch'])
            
            # 删除旧的检查点
            old_checkpoints = checkpoints[:-self.keep_n_checkpoints]
            for old_checkpoint in old_checkpoints:
                try:
                    if old_checkpoint['path'].exists():
                        old_checkpoint['path'].unlink()
                except Exception as e:
                    print(f"删除旧检查点失败: {e}")
            
            # 更新记录
            self.checkpoints[checkpoint_type] = checkpoints[-self.keep_n_checkpoints:]
    
    def get_latest_checkpoint(self, checkpoint_type: str = 'regular') -> Optional[Path]:
        """获取最新检查点路径"""
        checkpoints = self.checkpoints[checkpoint_type]
        if not checkpoints:
            return None
        
        latest = max(checkpoints, key=lambda x: x['epoch'])
        return latest['path'] if latest['path'].exists() else None
    
    def list_checkpoints(self) -> Dict[str, List[Dict]]:
        """列出所有检查点"""
        return self.checkpoints.copy()


# 使用示例函数
def create_training_controller(config_manager: TrainingConfigManager,
                              rca_network: RCASpatialAttentionNetwork,
                              data_generator: RCATrainingDataGenerator,
                              loss_function: RCALossFunction) -> RCATrainingController:
    """
    创建训练控制器的便捷函数
    
    Args:
        config_manager: 配置管理器
        rca_network: RCA网络
        data_generator: 数据生成器
        loss_function: 损失函数
        
    Returns:
        RCATrainingController: 训练控制器
    """
    controller = RCATrainingController(
        config_manager=config_manager,
        rca_network=rca_network,
        data_generator=data_generator,
        loss_function=loss_function
    )
    
    controller.setup_training_components()
    return controller


def run_training_loop(controller: RCATrainingController) -> Dict[str, Any]:
    """
    运行完整训练循环的便捷函数
    
    Args:
        controller: 训练控制器
        
    Returns:
        Dict[str, Any]: 训练结果
    """
    config = controller.config
    max_epochs = config.training.max_epochs
    validation_interval = config.training.validation_interval
    save_interval = config.training.save_interval
    
    print(f"🚀 开始训练循环，最大轮次: {max_epochs}")
    
    for epoch in range(max_epochs):
        # 训练一个epoch
        train_metrics = controller.train_epoch(epoch)
        
        # 验证
        if (epoch + 1) % validation_interval == 0 or epoch == max_epochs - 1:
            val_metrics = controller.validate_epoch(epoch)
            
            # 更新学习率
            controller.update_learning_rate(val_metrics)
            
            # 检查早停
            if controller.check_early_stopping(val_metrics):
                print(f"🛑 早停触发，在epoch {epoch + 1}")
                break
            
            # 保存最佳模型
            if val_metrics.total_loss < controller.training_state.best_loss:
                controller.save_checkpoint(epoch, 'best')
        
        # 定期保存检查点
        if (epoch + 1) % save_interval == 0:
            controller.save_checkpoint(epoch, 'regular')
        
        # 检查收敛
        if controller.training_state.convergence_detected:
            print(f"🎯 检测到收敛，在epoch {epoch + 1}")
            break
    
    # 生成最终报告
    final_report = controller.generate_training_report()
    
    # 可视化训练进度
    viz_path = os.path.join(config.experiment.output_dir, 'training_progress.png')
    controller.visualize_training_progress(viz_path)
    
    print(f"✅ 训练循环完成")
    
    return {
        'final_report': final_report,
        'training_controller': controller,
        'completion_status': 'success'
    }


if __name__ == "__main__":
    # 示例使用
    from .training_config_manager import TrainingConfigManager
    from .rca_training_data_generator import RCATrainingDataGenerator
    from .rca_loss_functions import RCALossFunction
    from ..stage3_rca.rca_spatial_attention import RCASpatialAttentionNetwork, RCASpatialAttentionConfig
    
    # 创建配置
    config_manager = TrainingConfigManager()
    
    # 创建组件（示例）
    rca_config = RCASpatialAttentionConfig()
    rca_network = RCASpatialAttentionNetwork(rca_config)
    data_generator = RCATrainingDataGenerator()
    loss_function = RCALossFunction()
    
    # 创建训练控制器
    controller = create_training_controller(
        config_manager, rca_network, data_generator, loss_function
    )
    
    # 运行训练
    results = run_training_loop(controller)
    
    print("🎉 训练完成！")