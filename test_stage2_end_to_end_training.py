#!/usr/bin/env python3
"""
InFusion-Enhanced 阶段2 端到端训练测试
测试完整的 CISM 训练流程：渲染->损失计算->反向传播->参数更新

作者: Claude 4.0 Sonnet
目标: 验证阶段2 CISM 系统的实际运行能力
"""

import os
import sys
import torch
import logging
import time
from pathlib import Path

# 添加项目路径
sys.path.append('.')
sys.path.append('./gaussian_splatting')

# 导入3DGS参数类
from gaussian_splatting.arguments import OptimizationParams

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_environment_setup():
    """测试环境设置"""
    logger.info("🔥 测试环境设置...")
    
    try:
        # 检查CUDA
        cuda_available = torch.cuda.is_available()
        device_count = torch.cuda.device_count() if cuda_available else 0
        logger.info(f"   - CUDA可用: {cuda_available}")
        logger.info(f"   - GPU数量: {device_count}")
        
        # 检查必要文件
        required_files = [
            "stage1_results/garden_fixed_final/gaussians_with_concept_membership_enhanced.ply",
            "configs/cism_training_config.yaml",
            "configs/concept_prompts.yaml"
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                logger.info(f"   ✅ 文件存在: {file_path}")
            else:
                logger.warning(f"   ⚠️ 文件缺失: {file_path}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 环境设置失败: {e}")
        return False

def test_load_enhanced_gaussians():
    """测试加载增强的高斯模型"""
    logger.info("🔥 测试加载增强高斯模型...")

    try:
        from scene.gaussian_model import GaussianModel

        # 创建高斯模型 - 使用与保存时相同的sh_degree=0
        # 注意：stage1_enhanced_concept_initializer.py中使用的是sh_degree=0
        CORRECT_SH_DEGREE = 0
        gaussians = GaussianModel(sh_degree=CORRECT_SH_DEGREE)
        logger.info(f"   - 使用sh_degree={CORRECT_SH_DEGREE}（与保存时一致）")
        
        # 加载阶段1的增强模型
        model_path = "stage1_results/garden_fixed_final/gaussians_with_concept_membership_enhanced.ply"
        gaussians.load_ply(model_path)
        
        logger.info(f"   ✅ 高斯模型加载成功")
        logger.info(f"   - 高斯点数: {gaussians.get_xyz.shape[0]}")
        
        # 检查concept_membership
        if hasattr(gaussians, '_concept_membership'):
            concept_membership = gaussians.get_concept_membership
            logger.info(f"   ✅ concept_membership存在: {concept_membership.shape}")
            logger.info(f"   - 概念分布: {concept_membership.mean(dim=0)}")
        else:
            logger.warning("   ⚠️ concept_membership不存在")
            return False, None
        
        return True, gaussians
        
    except Exception as e:
        logger.error(f"❌ 高斯模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_cism_trainer_initialization():
    """测试CISM训练器初始化"""
    logger.info("🔥 测试CISM训练器初始化...")
    
    try:
        from cism_core.cism_trainer import CISMTrainer
        import yaml
        
        # 加载配置
        with open("configs/cism_training_config.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 修改配置用于测试
        config['device'] = 'cuda'
        config['half_precision'] = True
        config['training_schedule']['cism_start_iter'] = 1
        config['training_schedule']['cism_end_iter'] = 10
        config['training_schedule']['cism_interval'] = 2
        
        logger.info("   - 创建CISM训练器...")
        cism_trainer = CISMTrainer(config)
        
        logger.info("   ✅ CISM训练器初始化成功")
        return True, cism_trainer, config
        
    except Exception as e:
        logger.error(f"❌ CISM训练器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def create_mock_camera():
    """创建模拟相机"""
    class MockCamera:
        def __init__(self):
            self.image_width = 512
            self.image_height = 512
            self.FoVx = 1.2
            self.FoVy = 1.2
            self.znear = 0.01
            self.zfar = 100.0
            self.world_view_transform = torch.eye(4, device='cuda')
            self.projection_matrix = torch.eye(4, device='cuda')
            self.full_proj_transform = torch.eye(4, device='cuda')
            self.camera_center = torch.zeros(3, device='cuda')
            
            # 模拟原始图像
            self.original_image = torch.rand(3, 512, 512, device='cuda')
    
    return MockCamera()

def test_end_to_end_training_loop():
    """测试端到端训练循环"""
    logger.info("🔥 测试端到端训练循环...")
    
    try:
        # 1. 加载增强高斯模型
        success, gaussians = test_load_enhanced_gaussians()
        if not success:
            return False
        
        # 2. 初始化CISM训练器
        success, cism_trainer, config = test_cism_trainer_initialization()
        if not success:
            return False
        
        # 3. 设置优化器
        logger.info("   - 设置优化器...")
        # 使用OptimizationParams创建完整的训练配置
        import argparse
        parser = argparse.ArgumentParser()
        opt_params = OptimizationParams(parser)

        # 创建一个简单的args对象来提取配置
        args = argparse.Namespace()
        # 设置所有必要的参数（使用默认值）
        for attr_name in dir(opt_params):
            if not attr_name.startswith('_'):
                setattr(args, attr_name, getattr(opt_params, attr_name))

        # 提取完整的训练配置
        training_args = opt_params.extract(args)

        logger.info(f"   - 使用完整的OptimizationParams配置")
        logger.info(f"   - percent_dense: {training_args.percent_dense}")

        # 设置训练
        gaussians.training_setup(training_args)
        
        # 4. 创建模拟相机
        camera = create_mock_camera()
        
        # 5. 训练循环测试
        logger.info("   - 开始训练循环测试...")
        num_iterations = 10
        losses = []
        
        for iteration in range(1, num_iterations + 1):
            iter_start = time.time()
            
            # 清零梯度
            gaussians.optimizer.zero_grad()
            
            # 模拟渲染（简化版）
            logger.info(f"   - 迭代 {iteration}: 开始渲染...")
            
            # 创建模拟渲染结果
            rendered_image = torch.rand(3, 512, 512, device='cuda', requires_grad=True)
            gt_image = camera.original_image
            
            # 计算基础损失
            l1_loss = torch.nn.functional.l1_loss(rendered_image, gt_image)
            base_loss = l1_loss
            
            logger.info(f"   - 迭代 {iteration}: 基础损失 = {base_loss.item():.6f}")
            
            # 应用CISM增强
            total_loss = base_loss
            cism_applied = False
            
            # 检查是否应该应用CISM
            should_apply_cism = cism_trainer.should_apply_cism(iteration)
            
            if should_apply_cism:
                logger.info(f"   - 迭代 {iteration}: 应用CISM损失...")
                
                try:
                    # 调用CISM训练步骤
                    cism_result = cism_trainer.training_step(
                        rendered_image.unsqueeze(0),  # 添加batch维度
                        camera,
                        iteration
                    )
                    
                    if cism_result is not None:
                        cism_loss, cism_info = cism_result
                        total_loss = base_loss + cism_loss
                        cism_applied = True
                        
                        logger.info(f"   - 迭代 {iteration}: CISM损失 = {cism_loss.item():.6f}")
                        logger.info(f"   - 迭代 {iteration}: 总损失 = {total_loss.item():.6f}")
                    
                except Exception as e:
                    logger.warning(f"   - 迭代 {iteration}: CISM损失计算失败: {e}")
                    # 继续使用基础损失
            
            # 反向传播
            logger.info(f"   - 迭代 {iteration}: 反向传播...")
            total_loss.backward()
            
            # 检查梯度
            has_gradients = False
            for name, param in gaussians.named_parameters():
                if param.grad is not None and param.grad.abs().sum() > 0:
                    has_gradients = True
                    break
            
            if has_gradients:
                logger.info(f"   - 迭代 {iteration}: ✅ 梯度存在")
            else:
                logger.warning(f"   - 迭代 {iteration}: ⚠️ 无梯度")
            
            # 优化器步骤
            gaussians.optimizer.step()
            
            # 记录损失
            losses.append(total_loss.item())
            
            iter_time = time.time() - iter_start
            status = "CISM应用" if cism_applied else "基础训练"
            logger.info(f"   ✅ 迭代 {iteration} 完成: {status}, 损失={total_loss.item():.6f}, 时间={iter_time:.2f}s")
        
        # 6. 分析结果
        logger.info("   - 训练循环分析:")
        logger.info(f"     - 总迭代数: {num_iterations}")
        logger.info(f"     - 平均损失: {sum(losses)/len(losses):.6f}")
        logger.info(f"     - 损失范围: [{min(losses):.6f}, {max(losses):.6f}]")
        
        # 检查损失是否合理
        if any(torch.isnan(torch.tensor(loss)) for loss in losses):
            logger.error("   ❌ 检测到NaN损失")
            return False
        
        if any(torch.isinf(torch.tensor(loss)) for loss in losses):
            logger.error("   ❌ 检测到Inf损失")
            return False
        
        logger.info("   ✅ 端到端训练循环测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 端到端训练循环失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🔥 InFusion-Enhanced 阶段2 端到端训练测试")
    logger.info("🎯 验证完整的 CISM 训练流程")
    logger.info("👨‍💻 测试者: Claude 4.0 Sonnet")
    logger.info("=" * 80)
    
    tests = [
        ("环境设置", test_environment_setup),
        ("端到端训练循环", test_end_to_end_training_loop)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 {test_name}")
        logger.info("-" * 60)
        
        try:
            if test_func():
                passed_tests += 1
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    # 总结
    success_rate = passed_tests / total_tests
    logger.info(f"\n🎯 总体状态: {passed_tests}/{total_tests} 测试通过 ({success_rate:.1%})")
    
    if success_rate >= 0.8:
        logger.info("🎉 阶段2 CISM 端到端训练测试成功！")
        logger.info("💡 建议: 可以进入阶段3 RCA 集成")
    else:
        logger.info("⚠️ 阶段2 需要进一步调试")
    
    return success_rate >= 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
