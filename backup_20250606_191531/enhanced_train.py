#!/usr/bin/env python3
"""
InFusion-Enhanced 阶段4主训练脚本：三大机制协同训练与优化
将语义基石、CISM、RCA三大机制整合到端到端的训练与优化流程中

本脚本实现：
1. 完整的三大机制初始化和集成
2. 端到端的训练迭代循环
3. 空间精确的语义引导3D编辑
4. 概念串扰减少和编辑精准性提升

参考实现：
- MultiDreamer3D/my_train.py 的训练循环组织
- MultiDreamer3D/prompt/prompt_utils.py 的文本嵌入准备
- MultiDreamer3D/guidance/sd_utils_casd.py 的RCA调用逻辑

作者: Claude Sonnet 4 (Anthropic 最先进模型)
版本: 阶段4 - 三大机制协同训练与优化
"""

import os
import sys
import torch
import torch.optim as optim
import yaml
import logging
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from tqdm import tqdm

# 修复Python路径问题
current_script_path = os.path.dirname(os.path.abspath(__file__))
gaussian_splatting_dir = os.path.join(current_script_path, "gaussian_splatting")

# 添加gaussian_splatting目录到sys.path，以便能够导入scene模块
if os.path.isdir(gaussian_splatting_dir) and gaussian_splatting_dir not in sys.path:
    sys.path.insert(0, gaussian_splatting_dir)
    print(f"DEBUG: Added {gaussian_splatting_dir} to sys.path")

# 同时确保项目根目录在sys.path中
project_root = current_script_path
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    print(f"DEBUG: Added {project_root} to sys.path")

# InFusion核心组件
try:
    from scene import Scene, GaussianModel
    print("DEBUG: Successfully imported Scene and GaussianModel")
except ModuleNotFoundError as e:
    print(f"ERROR: Still cannot import Scene or GaussianModel: {e}")
    print(f"Current sys.path: {sys.path}")
    print(f"Looking for scene module in: {gaussian_splatting_dir}")
    print(f"gaussian_splatting directory exists: {os.path.isdir(gaussian_splatting_dir)}")
    if os.path.isdir(gaussian_splatting_dir):
        print(f"Contents of gaussian_splatting: {os.listdir(gaussian_splatting_dir)}")
    raise
from gaussian_splatting.gaussian_renderer import render, render_concept_weights
from gaussian_splatting.utils.general_utils import safe_state
from gaussian_splatting.arguments import ModelParams, PipelineParams, OptimizationParams

# CISM核心组件 (阶段2)
from cism_core import DiffusionEngine, ConceptGuidance, SDSLoss, CISMTrainer

# RCA核心组件 (阶段3)
from rca_core import (
    RCAIntegrator, RCADataPreparator, 
    integrate_rca_to_diffusion_engine
)

@dataclass
class Stage4Config:
    """阶段4训练配置"""
    # 基础路径
    model_path: str = "stage1_results/enhanced_gaussians_v3/gaussians_with_concept_membership_enhanced.ply"
    concept_masks_path: str = "stage1_results/concept_masks_expanded"
    output_path: str = "stage4_results"
    
    # 训练参数
    iterations: int = 30000
    position_lr_init: float = 0.00016
    position_lr_final: float = 0.0000016
    position_lr_delay_mult: float = 0.01
    position_lr_max_steps: int = 30000
    feature_lr: float = 0.0025
    opacity_lr: float = 0.05
    scaling_lr: float = 0.005
    rotation_lr: float = 0.001
    
    # CISM参数
    cism_start_iter: int = 2000
    cism_end_iter: int = 25000
    cism_interval: int = 100
    cism_weight: float = 0.1
    guidance_scale: float = 7.5
    
    # RCA参数
    enable_rca: bool = True
    rca_start_iter: int = 5000
    lora_scale: float = 1.0
    
    # 概念配置
    user_concept_prompt: str = "a beautiful ornate fountain with flowing water"
    background_prompt: str = "natural garden background, lush vegetation, harmonious landscape"
    boundary_prompt: str = "smooth natural transition, seamless blending edge"
    
    # 设备和优化
    device: str = "cuda"
    debug_mode: bool = True
    save_interval: int = 1000
    log_interval: int = 100

class EnhancedTrainer:
    """
    InFusion-Enhanced 阶段4训练器
    
    整合语义基石、CISM、RCA三大机制的端到端训练系统
    """
    
    def __init__(self, config: Stage4Config):
        """
        初始化增强训练器
        
        Args:
            config: 阶段4训练配置
        """
        self.config = config
        self.device = torch.device(config.device)
        
        # 设置日志
        self._setup_logging()
        
        # 创建输出目录
        self.output_path = Path(config.output_path)
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # 训练状态
        self.iteration = 0
        self.training_stats = {
            'total_losses': [],
            'cism_losses': [],
            'rca_losses': [],
            'training_times': []
        }
        
        # 核心组件（将在initialize中设置）
        self.gaussians = None
        self.scene = None
        self.diffusion_engine = None
        self.concept_guidance = None
        self.sds_loss = None
        self.rca_integrator = None
        self.rca_data_preparator = None
        self.optimizer = None
        
        logging.info("EnhancedTrainer initialized")
    
    def _setup_logging(self):
        """设置日志系统"""
        log_file = self.output_path / "enhanced_training.log"
        logging.basicConfig(
            level=logging.INFO if not self.config.debug_mode else logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def initialize_all_components(self) -> bool:
        """
        初始化所有核心组件
        
        Returns:
            success: 初始化是否成功
        """
        logging.info("🚀 开始初始化所有核心组件...")
        
        try:
            # 1.1.a 加载原始InFusion Stage 1训练好的GaussianModel实例
            self._load_gaussian_model()
            
            # 1.1.b 调用initialize_concept_memberships_from_masks
            self._initialize_concept_memberships()
            
            # 1.1.c 初始化DiffusionEngine
            self._initialize_diffusion_engine()
            
            # 1.1.d 初始化ConceptGuidance
            self._initialize_concept_guidance()
            
            # 1.1.e 初始化RCAIntegrator
            self._initialize_rca_integrator()
            
            # 1.1.f 初始化RCADataPreparator
            self._initialize_rca_data_preparator()
            
            # 1.1.g 初始化SDSLoss
            self._initialize_sds_loss()
            
            # 1.1.i 为GaussianModel设置优化器
            self._setup_optimizer()
            
            logging.info("✅ 所有核心组件初始化完成")
            return True
            
        except Exception as e:
            logging.error(f"❌ 组件初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _load_gaussian_model(self):
        """加载3DGS模型"""
        logging.info("📦 加载3DGS模型...")

        try:
            # 创建一个简化的模型参数对象，不依赖ArgumentParser
            class SimpleModelParams:
                def __init__(self, config):
                    self.sh_degree = 3
                    self.source_path = ""
                    self.model_path = str(Path(config.model_path).parent)
                    self.images = "images"
                    self.resolution = -1
                    self.white_background = False
                    self.data_device = "cuda"
                    self.eval = False

            model_params = SimpleModelParams(self.config)

            # 创建高斯模型
            self.gaussians = GaussianModel(sh_degree=3)

            # 检查模型文件是否存在
            if not Path(self.config.model_path).exists():
                logging.warning(f"   ⚠️  模型文件不存在: {self.config.model_path}")
                logging.info("   🔧 创建测试用的高斯模型...")
                self._create_test_gaussian_model()
            else:
                # 加载真实模型
                self.gaussians.load_ply(self.config.model_path)
                logging.info(f"   ✅ 加载高斯模型: {self.gaussians.get_xyz.shape[0]} 个点")

            # 检查概念隶属度
            if hasattr(self.gaussians, '_concept_membership') and self.gaussians._concept_membership is not None:
                logging.info(f"   📊 概念隶属度形状: {self.gaussians.get_concept_membership.shape}")
            else:
                logging.info("   🔧 初始化测试用概念隶属度...")
                self._initialize_test_concept_membership()

        except Exception as e:
            logging.error(f"   ❌ 加载高斯模型失败: {e}")
            logging.info("   🔧 创建测试用高斯模型...")
            self._create_test_gaussian_model()

    def _create_test_gaussian_model(self):
        """创建测试用的高斯模型"""
        import torch

        # 创建最小的测试数据
        num_points = 1000

        # 创建高斯模型
        self.gaussians = GaussianModel(sh_degree=3)

        # 初始化基本参数
        xyz = torch.randn(num_points, 3) * 0.5
        features_dc = torch.randn(num_points, 1, 3) * 0.1
        features_rest = torch.randn(num_points, 15, 3) * 0.05
        opacity = torch.sigmoid(torch.randn(num_points, 1))
        scaling = torch.exp(torch.randn(num_points, 3) * 0.1)
        rotation = torch.randn(num_points, 4)
        rotation = rotation / rotation.norm(dim=1, keepdim=True)

        # 设置参数
        self.gaussians._xyz = torch.nn.Parameter(xyz.requires_grad_(True))
        self.gaussians._features_dc = torch.nn.Parameter(features_dc.requires_grad_(True))
        self.gaussians._features_rest = torch.nn.Parameter(features_rest.requires_grad_(True))
        self.gaussians._opacity = torch.nn.Parameter(opacity.requires_grad_(True))
        self.gaussians._scaling = torch.nn.Parameter(scaling.requires_grad_(True))
        self.gaussians._rotation = torch.nn.Parameter(rotation.requires_grad_(True))

        logging.info(f"   ✅ 创建测试高斯模型: {num_points} 个点")

        # 初始化概念隶属度
        self._initialize_test_concept_membership()

    def _initialize_test_concept_membership(self):
        """初始化测试用概念隶属度"""
        import torch

        if hasattr(self.gaussians, '_xyz'):
            num_points = self.gaussians._xyz.shape[0]

            # 创建3个概念的隶属度 (background, repair, boundary)
            concept_membership = torch.softmax(torch.randn(num_points, 3), dim=1)
            self.gaussians._concept_membership = torch.nn.Parameter(concept_membership.requires_grad_(True))

            logging.info(f"   ✅ 初始化概念隶属度: {concept_membership.shape}")
        else:
            logging.error("   ❌ 无法初始化概念隶属度: 高斯模型未正确创建")
    
    def _initialize_concept_memberships(self):
        """初始化概念隶属度（如果需要）"""
        logging.info("🔥 检查概念隶属度初始化状态...")
        
        # 检查是否已经有有效的concept_membership
        if hasattr(self.gaussians, '_concept_membership') and self.gaussians._concept_membership is not None:
            concept_stats = {
                'total_points': self.gaussians._concept_membership.shape[0],
                'concept_distribution': {}
            }
            
            # 计算概念分布
            dominant_concepts = self.gaussians.get_dominant_concept_id.cpu().numpy()
            unique_concepts, counts = np.unique(dominant_concepts, return_counts=True)
            
            for concept_id, count in zip(unique_concepts, counts):
                ratio = count / len(dominant_concepts)
                concept_stats['concept_distribution'][int(concept_id)] = {
                    'count': int(count),
                    'ratio': float(ratio)
                }
            
            logging.info(f"   ✅ 概念隶属度已存在: {concept_stats}")
        else:
            # 需要重新初始化
            logging.info("   🔄 重新初始化概念隶属度...")
            cameras = self.scene.getTrainCameras()
            stats = self.gaussians.initialize_concept_memberships_from_masks(
                concept_masks_root_path=self.config.concept_masks_path,
                cameras=cameras
            )
            logging.info(f"   ✅ 概念隶属度初始化完成: {stats}")
    
    def _initialize_diffusion_engine(self):
        """初始化扩散模型引擎"""
        logging.info("🎨 初始化DiffusionEngine...")
        
        self.diffusion_engine = DiffusionEngine(
            model_id="runwayml/stable-diffusion-v1-5",
            device=self.device,
            enable_memory_efficient_attention=True,
            enable_attention_slicing=True
        )
        
        logging.info("   ✅ DiffusionEngine初始化完成")
    
    def _initialize_concept_guidance(self):
        """初始化概念引导系统"""
        logging.info("💭 初始化ConceptGuidance...")
        
        # 创建概念配置
        concept_config = {
            0: self.config.background_prompt,
            1: self.config.user_concept_prompt,
            2: self.config.boundary_prompt
        }
        
        self.concept_guidance = ConceptGuidance(
            diffusion_engine=self.diffusion_engine,
            concept_config_path=None  # 使用内置配置
        )
        
        # 更新概念配置
        self.concept_guidance.concept_prompts = concept_config
        self.concept_guidance._precompute_embeddings()
        
        logging.info("   ✅ ConceptGuidance初始化完成")
    
    def _initialize_rca_integrator(self):
        """初始化RCA集成器"""
        if not self.config.enable_rca:
            logging.info("⚠️  RCA已禁用，跳过初始化")
            return
        
        logging.info("🎯 初始化RCA集成器...")
        
        # 集成RCA到DiffusionEngine
        success = integrate_rca_to_diffusion_engine(
            self.diffusion_engine,
            enable_lora=False,
            lora_scale=self.config.lora_scale,
            debug_mode=self.config.debug_mode
        )
        
        if success:
            self.rca_integrator = self.diffusion_engine.rca_integrator
            logging.info("   ✅ RCA集成器初始化完成")
        else:
            logging.error("   ❌ RCA集成器初始化失败")
            raise RuntimeError("RCA integration failed")
    
    def _initialize_rca_data_preparator(self):
        """初始化RCA数据准备器"""
        if not self.config.enable_rca:
            return
        
        logging.info("📊 初始化RCA数据准备器...")
        
        self.rca_data_preparator = RCADataPreparator(
            concept_names=["bg", "concept0", "concept1"],
            latent_resolution=(64, 64),  # 512//8 = 64
            enable_normalization=True,
            debug_mode=self.config.debug_mode
        )
        
        logging.info("   ✅ RCA数据准备器初始化完成")
    
    def _initialize_sds_loss(self):
        """初始化SDS损失计算器"""
        logging.info("📐 初始化SDSLoss...")
        
        self.sds_loss = SDSLoss(
            diffusion_engine=self.diffusion_engine,
            concept_guidance=self.concept_guidance,
            device=self.device
        )
        
        logging.info("   ✅ SDSLoss初始化完成")
    
    def _setup_optimizer(self):
        """设置优化器和学习率调度器"""
        logging.info("⚙️  设置优化器...")
        
        # 获取可训练参数
        l = [
            {'params': [self.gaussians._xyz], 'lr': self.config.position_lr_init, "name": "xyz"},
            {'params': [self.gaussians._features_dc], 'lr': self.config.feature_lr, "name": "f_dc"},
            {'params': [self.gaussians._features_rest], 'lr': self.config.feature_lr / 20.0, "name": "f_rest"},
            {'params': [self.gaussians._opacity], 'lr': self.config.opacity_lr, "name": "opacity"},
            {'params': [self.gaussians._scaling], 'lr': self.config.scaling_lr, "name": "scaling"},
            {'params': [self.gaussians._rotation], 'lr': self.config.rotation_lr, "name": "rotation"}
        ]
        
        self.optimizer = optim.Adam(l, lr=0.0, eps=1e-15)
        
        logging.info("   ✅ 优化器设置完成")
    
    def main_training_loop(self) -> bool:
        """
        主训练迭代循环
        
        Returns:
            success: 训练是否成功完成
        """
        logging.info("🚀 开始主训练循环...")
        
        try:
            # 获取训练相机
            cameras = self.scene.getTrainCameras()
            
            # 训练循环
            for iteration in tqdm(range(1, self.config.iterations + 1), desc="Training"):
                self.iteration = iteration
                
                # 随机选择相机
                camera = random.choice(cameras)
                
                # 执行单次迭代优化步骤
                total_loss = self.single_iteration_optimization_step(camera)
                
                # 记录统计信息
                self.training_stats['total_losses'].append(total_loss.item() if total_loss is not None else 0.0)
                
                # 日志记录
                if iteration % self.config.log_interval == 0:
                    self._log_training_progress(iteration, total_loss)
                
                # 定期保存
                if iteration % self.config.save_interval == 0:
                    self._save_checkpoint(iteration)
                
                # 定期可视化
                if iteration % (self.config.save_interval * 2) == 0:
                    self._visualize_progress(iteration, camera)
            
            logging.info("🎉 主训练循环完成")
            return True

        except Exception as e:
            logging.error(f"❌ 训练循环失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def single_iteration_optimization_step(self, camera) -> Optional[torch.Tensor]:
        """
        核心的单次迭代优化步骤函数

        封装一次完整的从渲染到参数更新的流程，整合三大机制：
        1. 渲染输入（语义基石）
        2. 准备RCA数据（RCA机制）
        3. 计算CISM损失（CISM机制）
        4. 反向传播和参数更新

        Args:
            camera: 选定的相机视角

        Returns:
            total_loss: 计算得到的总损失值
        """
        start_time = time.time()

        try:
            # 1. 渲染输入 (对应规划4.2.1)
            rendered_data = self._render_inputs(camera)

            # 2. 准备文本嵌入和RCA数据 (对应规划4.2.2)
            rca_data = self._prepare_rca_data(camera, rendered_data)

            # 3. 计算CISM损失 (对应规划4.2.3)
            cism_loss, cism_info = self._compute_cism_loss(rendered_data, rca_data)

            # 4. 计算总损失并反向传播 (对应规划4.2.5)
            total_loss = self._compute_total_loss_and_backward(cism_loss, cism_info)

            # 5. 优化器步骤
            self.optimizer.step()
            self.optimizer.zero_grad()

            # 6. 更新学习率
            self._update_learning_rate()

            # 记录时间
            elapsed_time = time.time() - start_time
            self.training_stats['training_times'].append(elapsed_time)

            return total_loss

        except Exception as e:
            logging.error(f"❌ 单次迭代优化失败 (iter {self.iteration}): {e}")
            if self.config.debug_mode:
                import traceback
                traceback.print_exc()
            return None

    def _render_inputs(self, camera) -> Dict[str, torch.Tensor]:
        """
        渲染输入数据

        Args:
            camera: 相机视角

        Returns:
            rendered_data: 渲染数据字典
        """
        # 1.a 渲染RGB图像
        pipe = PipelineParams()
        rendered_image = render(camera, self.gaussians, pipe)["render"]

        # 1.b 渲染概念权重图
        concept_weights_result = render_concept_weights(
            camera, self.gaussians, pipe,
            target_resolution=(512, 512)  # 原始分辨率
        )

        rendered_data = {
            "pred_rgb": rendered_image,  # [3, H, W]
            "concept_weights_raw": concept_weights_result["concept_weights_normalized"],  # [3, H, W]
            "camera": camera
        }

        if self.config.debug_mode:
            logging.debug(f"   渲染完成: RGB {rendered_image.shape}, 概念权重 {concept_weights_result['concept_weights_normalized'].shape}")

        return rendered_data

    def _prepare_rca_data(self, camera, rendered_data) -> Optional[Dict[str, Any]]:
        """
        准备文本嵌入和RCA数据

        Args:
            camera: 相机视角
            rendered_data: 渲染数据

        Returns:
            rca_data: RCA数据字典，如果RCA未启用则返回None
        """
        if not self.config.enable_rca or self.iteration < self.config.rca_start_iter:
            return None

        # 2.a 准备分概念文本嵌入
        concept_embeddings = {}
        for concept_id in [0, 1, 2]:  # bg, concept0, concept1
            embedding = self.concept_guidance.get_concept_embedding(concept_id)
            concept_name = ["bg", "concept0", "concept1"][concept_id]
            concept_embeddings[concept_name] = embedding

        # 2.b 使用RCADataPreparator准备cross_attention_kwargs
        cross_attention_kwargs = self.rca_data_preparator.prepare_rca_data(
            rendered_concept_weights=rendered_data["concept_weights_raw"],
            concept_embeddings=concept_embeddings,
            batch_size=1,
            cfg_enabled=True,
            lora_scale=self.config.lora_scale
        )

        if self.config.debug_mode:
            logging.debug(f"   RCA数据准备完成: {len(cross_attention_kwargs['mask'])}个掩码")

        return cross_attention_kwargs

    def _compute_cism_loss(self, rendered_data, rca_data) -> Tuple[torch.Tensor, Dict]:
        """
        计算CISM损失

        Args:
            rendered_data: 渲染数据
            rca_data: RCA数据（可能为None）

        Returns:
            cism_loss: CISM损失
            cism_info: 损失计算信息
        """
        # 检查是否应该应用CISM
        if self.iteration < self.config.cism_start_iter or self.iteration > self.config.cism_end_iter:
            # 返回零损失
            zero_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
            return zero_loss, {"cism_applied": False}

        if self.iteration % self.config.cism_interval != 0:
            # 不在CISM应用间隔内
            zero_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
            return zero_loss, {"cism_applied": False, "interval_skip": True}

        # 3.a 调用SDSLoss.compute_sds_loss，传递RCA数据
        pred_rgb = rendered_data["pred_rgb"].unsqueeze(0)  # [1, 3, H, W]

        # 选择主导概念（修复区域）
        concept_id = 1  # 修复概念

        # 计算SDS损失，传递cross_attention_kwargs
        if rca_data is not None:
            # 使用RCA增强的CISM损失
            cism_loss, cism_info = self.sds_loss.compute_sds_loss_with_rca(
                rendered_images=pred_rgb,
                concept_id=concept_id,
                cross_attention_kwargs=rca_data,
                guidance_scale=self.config.guidance_scale,
                use_ism=True
            )
        else:
            # 标准CISM损失
            cism_loss, cism_info = self.sds_loss.compute_sds_loss(
                rendered_images=pred_rgb,
                concept_id=concept_id,
                guidance_scale=self.config.guidance_scale,
                use_ism=True
            )

        # 记录CISM损失
        self.training_stats['cism_losses'].append(cism_loss.item())

        if self.config.debug_mode:
            logging.debug(f"   CISM损失计算完成: {cism_loss.item():.6f}")

        return cism_loss, cism_info

    def _compute_total_loss_and_backward(self, cism_loss, cism_info) -> torch.Tensor:
        """
        计算总损失并反向传播

        Args:
            cism_loss: CISM损失
            cism_info: CISM信息

        Returns:
            total_loss: 总损失
        """
        # 4. 计算总损失
        total_loss = self.config.cism_weight * cism_loss

        # 5. 反向传播
        if total_loss.requires_grad:
            total_loss.backward()

        if self.config.debug_mode:
            logging.debug(f"   总损失: {total_loss.item():.6f}, 反向传播完成")

        return total_loss

    def _update_learning_rate(self):
        """更新学习率"""
        # 位置学习率调度
        for param_group in self.optimizer.param_groups:
            if param_group["name"] == "xyz":
                lr = self._get_expon_lr_func(
                    lr_init=self.config.position_lr_init,
                    lr_final=self.config.position_lr_final,
                    lr_delay_mult=self.config.position_lr_delay_mult,
                    max_steps=self.config.position_lr_max_steps
                )(self.iteration)
                param_group['lr'] = lr

    def _get_expon_lr_func(self, lr_init, lr_final, lr_delay_mult, max_steps):
        """指数学习率调度函数"""
        def helper(step):
            if step < 0 or (lr_init == 0.0 and lr_final == 0.0):
                return 0.0
            if lr_delay_mult < 1:
                delay_rate = lr_delay_mult + (1 - lr_delay_mult) * np.sin(0.5 * np.pi * np.clip(step / max_steps, 0, 1))
            else:
                delay_rate = 1.0
            t = np.clip(step / max_steps, 0, 1)
            log_lerp = np.exp(np.log(lr_init) * (1 - t) + np.log(lr_final) * t)
            return delay_rate * log_lerp
        return helper

    def _log_training_progress(self, iteration, total_loss):
        """记录训练进度"""
        if total_loss is not None:
            loss_value = total_loss.item()
        else:
            loss_value = 0.0

        # 计算平均损失
        recent_losses = self.training_stats['total_losses'][-100:]
        avg_loss = np.mean(recent_losses) if recent_losses else 0.0

        # 计算平均训练时间
        recent_times = self.training_stats['training_times'][-100:]
        avg_time = np.mean(recent_times) if recent_times else 0.0

        logging.info(
            f"Iter {iteration:6d} | "
            f"Loss: {loss_value:.6f} | "
            f"Avg Loss: {avg_loss:.6f} | "
            f"Time: {avg_time:.3f}s | "
            f"Points: {self.gaussians.get_xyz.shape[0]}"
        )

    def _save_checkpoint(self, iteration):
        """保存模型检查点"""
        checkpoint_path = self.output_path / f"checkpoint_{iteration}.ply"
        self.gaussians.save_ply(str(checkpoint_path))

        # 保存训练统计
        stats_path = self.output_path / f"training_stats_{iteration}.json"
        import json
        with open(stats_path, 'w') as f:
            json.dump({
                'iteration': iteration,
                'total_losses': self.training_stats['total_losses'][-1000:],  # 最近1000次
                'cism_losses': self.training_stats['cism_losses'][-1000:],
                'avg_training_time': np.mean(self.training_stats['training_times'][-100:]) if self.training_stats['training_times'] else 0.0
            }, f, indent=2)

        logging.info(f"   💾 检查点已保存: {checkpoint_path}")

    def _visualize_progress(self, iteration, camera):
        """定期可视化训练进度"""
        try:
            # 渲染当前状态
            pipe = PipelineParams()
            rendered_image = render(camera, self.gaussians, pipe)["render"]

            # 渲染概念权重图
            concept_weights_result = render_concept_weights(
                camera, self.gaussians, pipe,
                target_resolution=(512, 512)
            )

            # 保存可视化结果
            vis_dir = self.output_path / "visualizations"
            vis_dir.mkdir(exist_ok=True)

            # 保存RGB图像
            import torchvision.transforms as transforms
            to_pil = transforms.ToPILImage()

            rgb_image = to_pil(rendered_image.cpu())
            rgb_image.save(vis_dir / f"rgb_{iteration:06d}.png")

            # 保存概念权重图
            concept_weights = concept_weights_result["concept_weights_normalized"]
            for i, concept_name in enumerate(["background", "repair", "boundary"]):
                weight_image = to_pil(concept_weights[i:i+1].cpu())
                weight_image.save(vis_dir / f"concept_{concept_name}_{iteration:06d}.png")

            logging.info(f"   📸 可视化已保存: iteration {iteration}")

        except Exception as e:
            logging.warning(f"   ⚠️  可视化失败: {e}")


def main():
    """主函数"""
    # 配置
    config = Stage4Config()

    # 创建训练器
    trainer = EnhancedTrainer(config)

    # 初始化所有组件
    if not trainer.initialize_all_components():
        logging.error("❌ 组件初始化失败，退出训练")
        return False

    # 开始训练
    success = trainer.main_training_loop()

    if success:
        logging.info("🎉 InFusion-Enhanced 阶段4训练完成！")

        # 保存最终模型
        final_model_path = trainer.output_path / "final_enhanced_gaussians.ply"
        trainer.gaussians.save_ply(str(final_model_path))
        logging.info(f"💾 最终模型已保存: {final_model_path}")

        return True
    else:
        logging.error("❌ 训练失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
