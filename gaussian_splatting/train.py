#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.d<PERSON><PERSON>@inria.fr
#
from datetime import datetime
import numpy as np
import cv2
import torch.nn.functional as F
from PIL import Image
import torchvision
import os
from PIL import Image
import torch
from random import randint
from utils.loss_utils import l1_loss, ssim
from gaussian_renderer import render, network_gui
import sys
from scene import Scene, GaussianModel
from utils.general_utils import safe_state
import uuid
from tqdm import tqdm
from utils.image_utils import psnr
from argparse import ArgumentParser, Namespace
from arguments import ModelParams, PipelineParams, OptimizationParams
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_FOUND = True 
except ImportError:
    TENSORBOARD_FOUND = False

def training(dataset, opt, pipe, testing_iterations, saving_iterations, checkpoint_iterations, checkpoint, debug_from, load_iteration, mask_training, color_aug, concept_training=False, concept_masks_path=None, cism_training=False, cism_start_iter=2000, cism_end_iter=25000, cism_interval=100, cism_weight=0.1, concept_prompts_path=None):
    first_iter = 0
    tb_writer = prepare_output_and_logger(dataset)
    gaussians = GaussianModel(dataset.sh_degree)
    if load_iteration != 0:
        scene = Scene(dataset, gaussians,load_iteration)
    else:
        scene = Scene(dataset, gaussians)
    gaussians.training_setup(opt)
    if checkpoint:
        (model_params, first_iter) = torch.load(checkpoint)
        gaussians.restore(model_params, opt)

    # 🔥 新增：concept_id训练集成
    concept_initialized = False
    if concept_training and concept_masks_path:
        print("🔥 启用 concept_id 训练模式")
        try:
            # 添加路径以找到 stage1_core_concept_initializer 模块
            import sys
            import os
            # 确保能找到项目根目录下的模块
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
            if project_root not in sys.path:
                sys.path.append(project_root)

            # 在训练开始后初始化concept_id
            from stage1_core_concept_initializer import ConceptIDInitializer
            initializer = ConceptIDInitializer(
                model_path=dataset.model_path,
                concept_masks_path=concept_masks_path,
                output_path=f"{dataset.model_path}/concept_initialization"
            )
            concept_masks = initializer.load_concept_masks()

            # 延迟初始化concept_id（在一些迭代后）
            concept_init_iteration = 1000
            print(f"📅 将在第 {concept_init_iteration} 次迭代后初始化concept_id")
        except Exception as e:
            print(f"⚠️ concept_id初始化准备失败: {e}")
            concept_training = False

    # 🔥 新增：CISM训练集成
    cism_trainer = None
    if cism_training:
        try:
            print("🔥 初始化CISM语义引导系统...")
            # 导入CISM核心组件
            sys.path.append('./stage2_results')
            from cism_core.cism_trainer import CISMTrainer

            # 创建CISM配置
            cism_config = {
                'model_id': 'runwayml/stable-diffusion-v1-5',
                'half_precision': True,
                'attention_slicing': True,
                'memory_efficient_attention': True,
                'concept_config_path': concept_prompts_path or 'configs/concept_prompts.yaml',
                'cism_start_iter': cism_start_iter,
                'cism_end_iter': cism_end_iter,
                'cism_interval': cism_interval,
                'cism_weight': cism_weight,
                'guidance_scale': 7.5,
                'timestep_range': [0.02, 0.98],
                'device': 'cuda'
            }

            # 初始化CISM训练器
            cism_trainer = CISMTrainer(gaussians, cism_config)
            print("✅ CISM系统初始化完成")

        except Exception as e:
            print(f"⚠️ CISM初始化失败: {e}")
            cism_training = False

    bg_color = [1, 1, 1] if dataset.white_background else [0, 0, 0]
    background = torch.tensor(bg_color, dtype=torch.float32, device="cuda")

    iter_start = torch.cuda.Event(enable_timing = True)
    iter_end = torch.cuda.Event(enable_timing = True)
    viewpoint_stack = None
    ema_loss_for_log = 0.0
    progress_bar = tqdm(range(first_iter, opt.iterations), desc="Training progress")
    first_iter += 1
     
    for iteration in range(first_iter, opt.iterations + 1):  
        if network_gui.conn == None:
            network_gui.try_connect()
        while network_gui.conn != None:
            try:
                net_image_bytes = None
                custom_cam, do_training, pipe.convert_SHs_python, pipe.compute_cov3D_python, keep_alive, scaling_modifer = network_gui.receive()
                if custom_cam != None:
                    net_image = render(custom_cam, gaussians, pipe, background, scaling_modifer)["render"]
                    net_image_bytes = memoryview((torch.clamp(net_image, min=0, max=1.0) * 255).byte().permute(1, 2, 0).contiguous().cpu().numpy())
                network_gui.send(net_image_bytes, dataset.source_path)
                if do_training and ((iteration < int(opt.iterations)) or not keep_alive):
                    break
            except Exception as e:
                network_gui.conn = None

        iter_start.record()

        gaussians.update_learning_rate(iteration)
        if iteration % 1000 == 0:
            gaussians.oneupSHdegree()

        # 🔥 新增：concept_id初始化逻辑
        if concept_training and not concept_initialized and iteration == concept_init_iteration:
            print(f"🔥 第 {iteration} 次迭代：开始初始化concept_id")
            try:
                def render_func(camera, gaussians_model):
                    bg_color = torch.tensor([0, 0, 0], dtype=torch.float32, device="cuda")
                    return render(camera, gaussians_model, pipe, bg_color)

                # 初始化concept_id
                stats = gaussians.initialize_concept_ids_from_masks(
                    concept_masks, scene.getTrainCameras()[:5], render_func
                )
                concept_initialized = True
                print(f"✅ concept_id初始化完成: {stats}")

            except Exception as e:
                print(f"❌ concept_id初始化失败: {e}")
                concept_training = False

        # Pick a random Camera
        if not viewpoint_stack:
            train_cameras = scene.getTrainCameras()
            print(f"🔍 调试信息: 训练相机数量 = {len(train_cameras)}")
            if len(train_cameras) == 0:
                print("❌ 错误: 没有找到训练相机！")
                print(f"   场景路径: {scene.model_path}")
                print(f"   数据集路径: {dataset.source_path}")
                break
            viewpoint_stack = train_cameras.copy()
        viewpoint_cam = viewpoint_stack.pop(randint(0, len(viewpoint_stack)-1))

        # Render
        if (iteration - 1) == debug_from:
            pipe.debug = True
        render_pkg = render(viewpoint_cam, gaussians, pipe, background, random_color=color_aug)
        image, viewspace_point_tensor, visibility_filter, radii = render_pkg["render"], render_pkg["viewspace_points"], render_pkg["visibility_filter"], render_pkg["radii"]

        # Loss
        gt_image = viewpoint_cam.original_image.cuda()
        if mask_training:
            kernel_size = 10
            image_mask = cv2.dilate(viewpoint_cam.original_image_mask, np.ones((kernel_size, kernel_size), dtype=np.uint8), iterations=1)


            image_m = image*torch.tensor(1-image_mask).cuda().repeat(3,1,1)
            gt_image_m = gt_image *torch.tensor(1-image_mask).cuda().repeat(3,1,1)

            Ll1 = l1_loss(image_m, gt_image_m,mask = viewpoint_cam.original_image_mask)
            loss = (1.0 - opt.lambda_dssim) * Ll1 + opt.lambda_dssim * (1.0 - ssim(image_m, gt_image_m))
        else:
            Ll1 = l1_loss(image, gt_image,mask = viewpoint_cam.original_image_mask)
            loss = (1.0 - opt.lambda_dssim) * Ll1 + opt.lambda_dssim * (1.0 - ssim(image, gt_image))

        # 🔥 新增：CISM损失计算
        cism_loss = None
        if cism_training and cism_trainer is not None and concept_initialized:
            try:
                # 应用CISM训练步骤
                cism_result = cism_trainer.training_step(image, viewpoint_cam, iteration)
                if cism_result is not None:
                    cism_loss, cism_info = cism_result
                    # 将CISM损失加入总损失
                    total_loss = loss + cism_weight * cism_loss
                    total_loss.backward()

                    # 记录CISM损失信息
                    if iteration % 100 == 0:
                        print(f"🔥 CISM损失 [{iteration}]: {cism_loss.item():.6f}, 权重: {cism_weight}")
                else:
                    loss.backward()
            except Exception as e:
                print(f"⚠️ CISM训练步骤失败 [{iteration}]: {e}")
                loss.backward()
        else:
            loss.backward()
        iter_end.record()

        with torch.no_grad():
            # Progress bar
            ema_loss_for_log = 0.4 * loss.item() + 0.6 * ema_loss_for_log
            if iteration % 10 == 0:
                progress_bar.set_postfix({"Loss": f"{ema_loss_for_log:.{7}f}"})
                progress_bar.update(10)
            if iteration == opt.iterations:
                progress_bar.close()


            # Densification
      
            if iteration < opt.densify_until_iter:
                gaussians.max_radii2D[visibility_filter] = torch.max(gaussians.max_radii2D[visibility_filter], radii[visibility_filter])

                gaussians.add_densification_stats(viewspace_point_tensor, visibility_filter)

                if iteration > opt.densify_from_iter and iteration % opt.densification_interval == 0:
                    size_threshold = 20 if iteration > opt.opacity_reset_interval else None
                    gaussians.densify_and_prune(opt.densify_grad_threshold, 0.005, scene.cameras_extent, size_threshold)
    
                
                if iteration % opt.opacity_reset_interval == 0 or (dataset.white_background and iteration == opt.densify_from_iter):
                    gaussians.reset_opacity()
           
            if iteration < opt.iterations:
                gaussians.optimizer.step()
                gaussians.optimizer.zero_grad(set_to_none = True)
                
            if (iteration in saving_iterations):
                print("\n[ITER {}] Saving Gaussians".format(iteration))
                scene.save(iteration)

            if (iteration in checkpoint_iterations):
                print("\n[ITER {}] Saving Checkpoint".format(iteration))
                torch.save((gaussians.capture(), iteration), scene.model_path + "/chkpnt" + str(iteration) + ".pth")

def prepare_output_and_logger(args):    
    if not args.model_path:
        if os.getenv('OAR_JOB_ID'):
            unique_str=os.getenv('OAR_JOB_ID')
        else:
            unique_str = str(uuid.uuid4())
        args.model_path = os.path.join("./result/bear", unique_str[0:10])
        
    # Set up output folder
    print("Output folder: {}".format(args.model_path))
    os.makedirs(args.model_path, exist_ok = True)
    with open(os.path.join(args.model_path, "cfg_args"), 'w') as cfg_log_f:
        cfg_log_f.write(str(Namespace(**vars(args))))

    # Create Tensorboard writer
    tb_writer = None
    if TENSORBOARD_FOUND:
        tb_writer = SummaryWriter(args.model_path)
    else:
        print("Tensorboard not available: not logging progress")
    return tb_writer

def training_report(tb_writer, iteration, Ll1, loss, l1_loss, elapsed, testing_iterations, scene : Scene, renderFunc, renderArgs):
    if tb_writer:
        tb_writer.add_scalar('train_loss_patches/l1_loss', Ll1.item(), iteration)
        tb_writer.add_scalar('train_loss_patches/total_loss', loss.item(), iteration)
        tb_writer.add_scalar('iter_time', elapsed, iteration)

    # Report test and samples of training set
    if iteration in testing_iterations:
        torch.cuda.empty_cache()
        validation_configs = ({'name': 'test', 'cameras' : scene.getTestCameras()}, 
                              {'name': 'train', 'cameras' : [scene.getTrainCameras()[idx % len(scene.getTrainCameras())] for idx in range(5, 30, 5)]})

        for config in validation_configs:
            if config['cameras'] and len(config['cameras']) > 0:
                l1_test = 0.0
                psnr_test = 0.0
                for idx, viewpoint in enumerate(config['cameras']):
                    image = torch.clamp(renderFunc(viewpoint, scene.gaussians, *renderArgs)["render"], 0.0, 1.0)
                    gt_image = torch.clamp(viewpoint.original_image.to("cuda"), 0.0, 1.0)
                    if tb_writer and (idx < 5):
                        tb_writer.add_images(config['name'] + "_view_{}/render".format(viewpoint.image_name), image[None], global_step=iteration)
                        if iteration == testing_iterations[0]:
                            tb_writer.add_images(config['name'] + "_view_{}/ground_truth".format(viewpoint.image_name), gt_image[None], global_step=iteration)
                    l1_test += l1_loss(image, gt_image).mean().double()
                    psnr_test += psnr(image, gt_image).mean().double()
                psnr_test /= len(config['cameras'])
                l1_test /= len(config['cameras'])          
                print("\n[ITER {}] Evaluating {}: L1 {} PSNR {}".format(iteration, config['name'], l1_test, psnr_test))
                if tb_writer:
                    tb_writer.add_scalar(config['name'] + '/loss_viewpoint - l1_loss', l1_test, iteration)
                    tb_writer.add_scalar(config['name'] + '/loss_viewpoint - psnr', psnr_test, iteration)

        if tb_writer:
            tb_writer.add_histogram("scene/opacity_histogram", scene.gaussians.get_opacity, iteration)
            tb_writer.add_scalar('total_points', scene.gaussians.get_xyz.shape[0], iteration)
        torch.cuda.empty_cache()

if __name__ == "__main__":
    # Set up command line argument parser
    parser = ArgumentParser(description="Training script parameters")
    lp = ModelParams(parser)
    op = OptimizationParams(parser)
    pp = PipelineParams(parser)
    # update parameters for these three 
    parser.add_argument('--ip', type=str, default="***********")
    parser.add_argument('--port', type=int, default=6009)
    parser.add_argument('--debug_from', type=int, default=-1)
    parser.add_argument('--load_iteration', type=int, default=0)
    parser.add_argument('--detect_anomaly', action='store_true', default=False)
    parser.add_argument('--mask_training', action='store_true', default=False)
    parser.add_argument('--color_aug', action='store_true', default=False)
    # 🔥 新增：concept_id训练参数
    parser.add_argument('--concept_training', action='store_true', default=False, help='启用concept_id训练模式')
    parser.add_argument('--concept_masks_path', type=str, default='stage1_results/concept_masks', help='概念掩码路径')
    # 🔥 新增：CISM训练参数
    parser.add_argument('--cism_training', action='store_true', default=False, help='启用CISM语义引导训练')
    parser.add_argument('--cism_start_iter', type=int, default=2000, help='CISM开始迭代')
    parser.add_argument('--cism_end_iter', type=int, default=25000, help='CISM结束迭代')
    parser.add_argument('--cism_interval', type=int, default=100, help='CISM应用间隔')
    parser.add_argument('--cism_weight', type=float, default=0.1, help='CISM损失权重')
    parser.add_argument('--concept_prompts_path', type=str, default='configs/concept_prompts.yaml', help='概念提示配置路径')
    parser.add_argument("--test_iterations", nargs="+", type=int, default=[7_000, 30_000])
    parser.add_argument("--save_iterations", nargs="+", type=int, default=[7_000, 30_000])
    parser.add_argument("--quiet", action="store_true")
    parser.add_argument("--checkpoint_iterations", nargs="+", type=int, default=[])
    parser.add_argument("--start_checkpoint", type=str, default = None)
    args = parser.parse_args(sys.argv[1:])
    args.save_iterations.append(args.iterations)
    
    print("Optimizing " + args.model_path)

    # Initialize system state (RNG)
    safe_state(args.quiet)

    # Start GUI server, configure and run training
    network_gui.init(args.ip, args.port)
    torch.autograd.set_detect_anomaly(args.detect_anomaly)
    training(lp.extract(args), op.extract(args), pp.extract(args), args.test_iterations, args.save_iterations, args.checkpoint_iterations, args.start_checkpoint, args.debug_from, args.load_iteration, args.mask_training, args.color_aug, args.concept_training, args.concept_masks_path, args.cism_training, args.cism_start_iter, args.cism_end_iter, args.cism_interval, args.cism_weight, args.concept_prompts_path)

    # All done
    print("\nTraining complete.")
