# -*- coding: utf-8 -*-
"""
InFusion 阶段3配置管理器
管理RCA系统的所有配置参数
"""

import os
import json
import torch
from typing import Dict, Any, Optional

class Stage3ConfigManager:
    """阶段3配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.config_path = config_path
        self.config = self._load_default_config()
        
        if config_path and os.path.exists(config_path):
            self._load_config_file(config_path)
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            # === 基础路径配置 ===
            'stage2_output_dir': './output/stage2',
            'output_dir': './output/stage3', 
            'checkpoint_dir': './output/stage3/checkpoints',
            'log_dir': './output/stage3/logs',
            'visualization_dir': './output/stage3/visualizations',
            
            # === RCA网络配置 ===
            'num_concepts': 3,  # 背景、修复区域、边界过渡
            'hidden_dim': 256,
            'num_attention_heads': 8,
            'dropout_rate': 0.1,
            'position_encoding_levels': 10,
            'feature_encoding_levels': 6,
            
            # === 训练配置 ===
            'rca_learning_rate': 0.001,
            'gaussians_learning_rate': 0.0001,
            'rca_training_epochs': 1000,
            'optimization_iterations': 5000,
            'batch_size': 4,  # 每批处理的视角数
            'gradient_clip_norm': 1.0,
            
            # === 损失权重配置 ===
            'concept_separation_weight': 1.0,
            'spatial_smoothness_weight': 0.5,
            'consistency_weight': 2.0,
            'cism_weight': 1.0,
            'spatial_consistency_weight': 0.3,
            'scale_regularization': 0.01,
            'rotation_regularization': 0.01,
            
            # === 权重渲染配置 ===
            'weight_render_resolution': (512, 512),
            'gaussian_opacity_threshold': 0.005,
            'weight_smoothing_kernel_size': 5,
            'weight_smoothing_sigma': 1.0,
            
            # === CISM集成配置 ===
            'modulation_strategy': 'adaptive',  # 'pixel', 'region', 'adaptive'
            'adaptive_threshold': 0.1,
            'region_erosion_size': 3,
            'region_dilation_size': 5,
            
            # === 设备和性能配置 ===
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'mixed_precision': True,
            'num_workers': 4,
            'pin_memory': True,
            
            # === 日志和保存配置 ===
            'log_interval': 100,
            'checkpoint_interval': 500,
            'validation_interval': 1000,
            'visualization_interval': 500,
            'save_intermediate_renders': True,
            
            # === 验证配置 ===
            'validation_views': [0, 25, 50, 75],  # 用于验证的视角索引
            'validation_metrics': ['mse', 'psnr', 'ssim'],
            
            # === 调试配置 ===
            'debug_mode': False,
            'verbose_logging': True,
            'save_attention_maps': False,
            'save_weight_maps': True,
        }
    
    def _load_config_file(self, config_path: str):
        """从文件加载配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            # 递归更新配置
            self._update_config_recursive(self.config, file_config)
            print(f"已加载配置文件: {config_path}")
            
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            print("使用默认配置")
    
    def _update_config_recursive(self, base_config: Dict, update_config: Dict):
        """递归更新配置"""
        for key, value in update_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._update_config_recursive(base_config[key], value)
            else:
                base_config[key] = value
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self.config.copy()
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置配置项"""
        self.config[key] = value
    
    def save_config(self, save_path: str):
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            print(f"配置已保存到: {save_path}")
            
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        try:
            # 检查必需的路径
            required_paths = ['stage2_output_dir']
            for path_key in required_paths:
                if not os.path.exists(self.config[path_key]):
                    print(f"错误: 路径不存在 - {path_key}: {self.config[path_key]}")
                    return False
            
            # 检查数值范围
            if self.config['num_concepts'] < 2:
                print("错误: num_concepts必须至少为2")
                return False
            
            if self.config['hidden_dim'] < 64:
                print("错误: hidden_dim太小，建议至少64")
                return False
            
            if self.config['num_attention_heads'] < 1:
                print("错误: num_attention_heads必须至少为1")
                return False
            
            # 检查设备可用性
            if self.config['device'] == 'cuda' and not torch.cuda.is_available():
                print("警告: CUDA不可用，将使用CPU")
                self.config['device'] = 'cpu'
            
            print("配置验证通过")
            return True
            
        except Exception as e:
            print(f"配置验证失败: {str(e)}")
            return False
    
    def create_output_directories(self):
        """创建所有输出目录"""
        try:
            directories = [
                self.config['output_dir'],
                self.config['checkpoint_dir'], 
                self.config['log_dir'],
                self.config['visualization_dir'],
                os.path.join(self.config['output_dir'], 'final_renders'),
                os.path.join(self.config['output_dir'], 'weight_maps'),
                os.path.join(self.config['output_dir'], 'attention_maps'),
            ]
            
            for directory in directories:
                os.makedirs(directory, exist_ok=True)
            
            print("输出目录创建完成")
            
        except Exception as e:
            print(f"创建输出目录失败: {str(e)}")
    
    def get_device(self) -> torch.device:
        """获取PyTorch设备"""
        return torch.device(self.config['device'])
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("\n=== 阶段3配置摘要 ===")
        print(f"输出目录: {self.config['output_dir']}")
        print(f"阶段2结果目录: {self.config['stage2_output_dir']}")
        print(f"概念数量: {self.config['num_concepts']}")
        print(f"隐藏维度: {self.config['hidden_dim']}")
        print(f"注意力头数: {self.config['num_attention_heads']}")
        print(f"RCA训练轮数: {self.config['rca_training_epochs']}")
        print(f"优化迭代次数: {self.config['optimization_iterations']}")
        print(f"设备: {self.config['device']}")
        print(f"混合精度: {self.config['mixed_precision']}")
        print("====================\n")

# 创建全局配置实例
_global_config_manager = None

def get_global_config() -> Stage3ConfigManager:
    """获取全局配置管理器实例"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = Stage3ConfigManager()
    return _global_config_manager

def init_global_config(config_path: Optional[str] = None) -> Stage3ConfigManager:
    """初始化全局配置管理器"""
    global _global_config_manager
    _global_config_manager = Stage3ConfigManager(config_path)
    return _global_config_manager