#!/usr/bin/env python3
"""
SpecifyGradient类 - 用于SDS损失的正确梯度计算

基于MultiDreamer3D的实现，确保梯度正确传播到3DGS参数
"""

import torch
import torch.nn as nn
from typing import Optional


class SpecifyGradient(torch.autograd.Function):
    """
    自定义梯度函数，用于SDS损失计算
    
    这个类实现了SDS论文中的梯度技巧：
    - 前向传播：直接返回输入张量
    - 反向传播：使用指定的梯度而不是自动计算的梯度
    
    用法：
        loss = SpecifyGradient.apply(latents, grad)
    其中：
        latents: 需要梯度的张量（连接到3DGS参数）
        grad: 预计算的梯度（来自SDS公式）
    """
    
    @staticmethod
    def forward(ctx, input_tensor: torch.Tensor, gradient: torch.Tensor) -> torch.Tensor:
        """
        前向传播：直接返回输入张量
        
        Args:
            ctx: 上下文对象
            input_tensor: 输入张量（通常是latents）
            gradient: 指定的梯度
            
        Returns:
            input_tensor: 原样返回输入张量
        """
        # 保存梯度用于反向传播
        ctx.save_for_backward(gradient)
        return input_tensor
    
    @staticmethod
    def backward(ctx, grad_output: torch.Tensor) -> tuple:
        """
        反向传播：使用指定的梯度
        
        Args:
            ctx: 上下文对象
            grad_output: 来自后续层的梯度
            
        Returns:
            tuple: (input_tensor的梯度, gradient的梯度)
        """
        # 获取保存的梯度
        specified_gradient, = ctx.saved_tensors
        
        # 返回指定的梯度（忽略grad_output）
        # 第二个None表示gradient参数不需要梯度
        return specified_gradient, None


def sds_loss_with_specify_gradient(
    latents: torch.Tensor,
    pred_noise: torch.Tensor,
    target_noise: torch.Tensor,
    timesteps: torch.Tensor,
    alphas: torch.Tensor,
    grad_scale: float = 1.0,
    weight_type: str = "dreamfusion"
) -> torch.Tensor:
    """
    使用SpecifyGradient的正确SDS损失计算
    
    Args:
        latents: 潜在表示 [batch_size, 4, H//8, W//8]（需要梯度）
        pred_noise: UNet预测的噪声 [batch_size, 4, H//8, W//8]（无梯度）
        target_noise: 目标噪声 [batch_size, 4, H//8, W//8]（无梯度）
        timesteps: 时间步 [batch_size]
        alphas: alpha值 [num_timesteps]
        grad_scale: 梯度缩放因子
        weight_type: 权重类型
        
    Returns:
        loss: SDS损失张量
    """
    # 确保pred_noise和target_noise没有梯度
    pred_noise = pred_noise.detach()
    target_noise = target_noise.detach()
    
    # 计算时间步权重
    if weight_type == "dreamfusion":
        # DreamFusion权重: w(t) = σ_t^2 = (1-α_t)/α_t
        alpha_t = alphas[timesteps]
        w_t = (1 - alpha_t) / alpha_t
    elif weight_type == "sjc":
        # SJC权重: w(t) = σ_t
        alpha_t = alphas[timesteps]
        w_t = ((1 - alpha_t) / alpha_t) ** 0.5
    else:
        # 均匀权重
        w_t = torch.ones_like(timesteps, dtype=torch.float32)
    
    # 扩展权重维度以匹配latents
    w_t = w_t.view(-1, 1, 1, 1)  # [batch_size, 1, 1, 1]
    
    # 计算SDS梯度: grad = w(t) * (pred_noise - target_noise)
    sds_grad = w_t * (pred_noise - target_noise)
    
    # 应用梯度缩放
    sds_grad = grad_scale * sds_grad
    
    # 处理NaN和Inf
    sds_grad = torch.nan_to_num(sds_grad, nan=0.0, posinf=0.0, neginf=0.0)
    
    # 使用SpecifyGradient应用梯度
    loss = SpecifyGradient.apply(latents, sds_grad)
    
    # 返回标量损失（用于日志记录）
    return loss.mean()


def compute_time_weights(
    timesteps: torch.Tensor,
    alphas: torch.Tensor,
    weight_type: str = "dreamfusion"
) -> torch.Tensor:
    """
    计算时间步权重
    
    Args:
        timesteps: 时间步 [batch_size]
        alphas: alpha值 [num_timesteps]
        weight_type: 权重类型
        
    Returns:
        weights: 时间步权重 [batch_size]
    """
    alpha_t = alphas[timesteps]
    
    if weight_type == "dreamfusion":
        # DreamFusion权重: w(t) = σ_t^2 = (1-α_t)/α_t
        weights = (1 - alpha_t) / alpha_t
    elif weight_type == "sjc":
        # SJC权重: w(t) = σ_t
        weights = ((1 - alpha_t) / alpha_t) ** 0.5
    elif weight_type == "uniform":
        # 均匀权重
        weights = torch.ones_like(timesteps, dtype=torch.float32)
    else:
        raise ValueError(f"Unknown weight_type: {weight_type}")
    
    return weights


def check_gradient_flow(tensor: torch.Tensor, name: str = "tensor") -> bool:
    """
    检查张量是否具有梯度流
    
    Args:
        tensor: 要检查的张量
        name: 张量名称（用于日志）
        
    Returns:
        bool: 是否具有梯度流
    """
    if not isinstance(tensor, torch.Tensor):
        print(f"❌ {name} is not a torch.Tensor")
        return False
    
    if not tensor.requires_grad:
        print(f"⚠️ {name} does not require gradients")
        return False
    
    if tensor.grad_fn is None:
        print(f"⚠️ {name} has no grad_fn (likely a leaf tensor)")
        return True  # 叶子张量是正常的
    
    print(f"✅ {name} has gradient flow (grad_fn: {tensor.grad_fn})")
    return True


def debug_sds_computation(
    latents: torch.Tensor,
    pred_noise: torch.Tensor,
    target_noise: torch.Tensor,
    timesteps: torch.Tensor
) -> dict:
    """
    调试SDS计算过程
    
    Args:
        latents: 潜在表示
        pred_noise: 预测噪声
        target_noise: 目标噪声
        timesteps: 时间步
        
    Returns:
        dict: 调试信息
    """
    debug_info = {}
    
    # 检查张量形状
    debug_info['latents_shape'] = latents.shape
    debug_info['pred_noise_shape'] = pred_noise.shape
    debug_info['target_noise_shape'] = target_noise.shape
    debug_info['timesteps_shape'] = timesteps.shape
    
    # 检查梯度流
    debug_info['latents_requires_grad'] = latents.requires_grad
    debug_info['pred_noise_requires_grad'] = pred_noise.requires_grad
    debug_info['target_noise_requires_grad'] = target_noise.requires_grad
    
    # 检查数值范围
    debug_info['latents_range'] = (latents.min().item(), latents.max().item())
    debug_info['pred_noise_range'] = (pred_noise.min().item(), pred_noise.max().item())
    debug_info['target_noise_range'] = (target_noise.min().item(), target_noise.max().item())
    
    # 检查NaN和Inf
    debug_info['latents_has_nan'] = torch.isnan(latents).any().item()
    debug_info['pred_noise_has_nan'] = torch.isnan(pred_noise).any().item()
    debug_info['target_noise_has_nan'] = torch.isnan(target_noise).any().item()
    
    # 计算噪声差异
    noise_diff = pred_noise - target_noise
    debug_info['noise_diff_norm'] = noise_diff.norm().item()
    debug_info['noise_diff_mean'] = noise_diff.mean().item()
    debug_info['noise_diff_std'] = noise_diff.std().item()
    
    return debug_info
