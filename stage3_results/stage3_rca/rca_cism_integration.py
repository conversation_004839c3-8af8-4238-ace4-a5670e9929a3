import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np

from .rca_spatial_attention import RCASpatialAttentionNetwork, RCASpatialAttentionConfig
from .rca_weight_renderer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WeightRendererConfig

@dataclass
class RCACISMIntegrationConfig:
    """RCA-CISM集成配置"""
    # RCA网络配置
    rca_config: RCASpatialAttentionConfig = None
    
    # 权重渲染配置
    renderer_config: WeightRendererConfig = None
    
    # 调制策略
    modulation_strategy: str = "pixel_wise"  # "pixel_wise", "region_wise", "adaptive"
    guidance_routing_method: str = "weighted_average"  # "weighted_average", "winner_take_all", "collaborative"
    
    # 边界处理
    boundary_smoothing: bool = True
    boundary_feather_radius: int = 5
    boundary_transition_power: float = 2.0
    
    # 多概念融合
    concept_fusion_method: str = "linear_combination"  # "linear_combination", "competitive", "collaborative"
    fusion_temperature: float = 1.0
    adaptive_temperature: bool = True
    
    # 空间一致性
    spatial_consistency_weight: float = 0.1
    temporal_consistency_weight: float = 0.05
    
    # 优化参数
    gradient_accumulation: bool = True
    memory_efficient: bool = True
    enable_checkpointing: bool = False

class WeightModulator(nn.Module):
    """
    🔥 权重调制器
    
    功能:
    - 使用RCA权重图调制CISM引导信号
    - 实现精准的空间语义控制
    """
    
    def __init__(self, config: RCACISMIntegrationConfig = None):
        super().__init__()
        self.config = config or RCACISMIntegrationConfig()
        
        # 边界平滑核
        if self.config.boundary_smoothing:
            self._setup_boundary_kernel()
        
        print(f"🎯 权重调制器初始化完成")
        print(f"   调制策略: {self.config.modulation_strategy}")
        print(f"   引导路由: {self.config.guidance_routing_method}")
    
    def _setup_boundary_kernel(self):
        """设置边界平滑核"""
        radius = self.config.boundary_feather_radius
        kernel_size = 2 * radius + 1
        
        # 创建距离权重核
        center = radius
        y, x = torch.meshgrid(torch.arange(kernel_size), torch.arange(kernel_size), indexing='ij')
        distances = torch.sqrt((x - center)**2 + (y - center)**2)
        
        # 羽化权重 (距离越远权重越小)
        feather_weights = torch.clamp(1.0 - distances / radius, min=0.0)
        feather_weights = feather_weights ** self.config.boundary_transition_power
        
        # 归一化
        feather_weights = feather_weights / feather_weights.sum()
        
        self.register_buffer('boundary_kernel', feather_weights.unsqueeze(0).unsqueeze(0))
    
    def modulate_guidance_signals(self,
                                 cism_guidance_signals: Dict[int, torch.Tensor],
                                 weight_maps: torch.Tensor,
                                 concept_ids: List[int]) -> torch.Tensor:
        """
        🔥 调制引导信号
        
        Args:
            cism_guidance_signals: CISM引导信号字典 {concept_id: delta_epsilon}
                每个delta_epsilon形状: [batch_size, 4, H//8, W//8]
            weight_maps: [num_concepts, H, W] RCA权重图
            concept_ids: 概念ID列表
            
        Returns:
            modulated_guidance: [batch_size, 4, H//8, W//8] 调制后的引导信号
        """
        device = weight_maps.device
        num_concepts, H, W = weight_maps.shape
        
        # 获取第一个引导信号的形状
        first_signal = next(iter(cism_guidance_signals.values()))
        batch_size, channels, latent_h, latent_w = first_signal.shape
        
        # 1. 🔥 权重图下采样到潜在空间尺寸
        latent_weight_maps = F.interpolate(
            weight_maps.unsqueeze(0),  # [1, num_concepts, H, W]
            size=(latent_h, latent_w),
            mode='bilinear',
            align_corners=False
        ).squeeze(0)  # [num_concepts, latent_h, latent_w]
        
        # 2. 🔥 边界平滑处理
        if self.config.boundary_smoothing:
            latent_weight_maps = self._apply_boundary_smoothing(latent_weight_maps)
        
        # 3. 🔥 引导信号调制
        if self.config.modulation_strategy == "pixel_wise":
            modulated_guidance = self._pixel_wise_modulation(
                cism_guidance_signals, latent_weight_maps, concept_ids, batch_size, channels
            )
        elif self.config.modulation_strategy == "region_wise":
            modulated_guidance = self._region_wise_modulation(
                cism_guidance_signals, latent_weight_maps, concept_ids, batch_size, channels
            )
        else:
            modulated_guidance = self._adaptive_modulation(
                cism_guidance_signals, latent_weight_maps, concept_ids, batch_size, channels
            )
        
        return modulated_guidance
    
    def _apply_boundary_smoothing(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """应用边界平滑"""
        num_concepts, H, W = weight_maps.shape
        smoothed_maps = torch.zeros_like(weight_maps)
        
        for i in range(num_concepts):
            # 对每个概念权重图进行平滑
            weight_channel = weight_maps[i:i+1].unsqueeze(0)  # [1, 1, H, W]
            smoothed_channel = F.conv2d(
                weight_channel,
                self.boundary_kernel,
                padding=self.config.boundary_feather_radius
            )
            smoothed_maps[i] = smoothed_channel.squeeze()
        
        return smoothed_maps
    
    def _pixel_wise_modulation(self,
                              guidance_signals: Dict[int, torch.Tensor],
                              weight_maps: torch.Tensor,
                              concept_ids: List[int],
                              batch_size: int,
                              channels: int) -> torch.Tensor:
        """逐像素权重调制"""
        device = weight_maps.device
        num_concepts, latent_h, latent_w = weight_maps.shape
        
        # 初始化输出
        modulated_guidance = torch.zeros(batch_size, channels, latent_h, latent_w, device=device)
        
        # 为每个概念应用权重调制
        for i, concept_id in enumerate(concept_ids):
            if concept_id in guidance_signals:
                guidance_signal = guidance_signals[concept_id]  # [batch_size, channels, latent_h, latent_w]
                weight_map = weight_maps[i]  # [latent_h, latent_w]
                
                # 扩展权重图维度
                weight_map_expanded = weight_map.unsqueeze(0).unsqueeze(0)  # [1, 1, latent_h, latent_w]
                weight_map_expanded = weight_map_expanded.expand(batch_size, channels, -1, -1)
                
                # 逐像素调制
                modulated_signal = guidance_signal * weight_map_expanded
                modulated_guidance += modulated_signal
        
        return modulated_guidance
    
    def _region_wise_modulation(self,
                               guidance_signals: Dict[int, torch.Tensor],
                               weight_maps: torch.Tensor,
                               concept_ids: List[int],
                               batch_size: int,
                               channels: int) -> torch.Tensor:
        """区域级权重调制"""
        device = weight_maps.device
        num_concepts, latent_h, latent_w = weight_maps.shape
        
        # 使用最强概念作为主导
        dominant_concept_map = torch.argmax(weight_maps, dim=0)  # [latent_h, latent_w]
        
        # 初始化输出
        modulated_guidance = torch.zeros(batch_size, channels, latent_h, latent_w, device=device)
        
        # 为每个像素分配对应概念的引导信号
        for i, concept_id in enumerate(concept_ids):
            if concept_id in guidance_signals:
                # 创建该概念的掩码
                concept_mask = (dominant_concept_map == i).float()  # [latent_h, latent_w]
                
                if concept_mask.sum() > 0:  # 如果该概念有像素
                    guidance_signal = guidance_signals[concept_id]  # [batch_size, channels, latent_h, latent_w]
                    
                    # 扩展掩码维度
                    mask_expanded = concept_mask.unsqueeze(0).unsqueeze(0)  # [1, 1, latent_h, latent_w]
                    mask_expanded = mask_expanded.expand(batch_size, channels, -1, -1)
                    
                    # 应用掩码
                    masked_signal = guidance_signal * mask_expanded
                    modulated_guidance += masked_signal
        
        return modulated_guidance
    
    def _adaptive_modulation(self,
                            guidance_signals: Dict[int, torch.Tensor],
                            weight_maps: torch.Tensor,
                            concept_ids: List[int],
                            batch_size: int,
                            channels: int) -> torch.Tensor:
        """自适应权重调制"""
        # 结合像素级和区域级的优点
        pixel_modulated = self._pixel_wise_modulation(
            guidance_signals, weight_maps, concept_ids, batch_size, channels
        )
        region_modulated = self._region_wise_modulation(
            guidance_signals, weight_maps, concept_ids, batch_size, channels
        )
        
        # 自适应混合 (基于权重的信息量)
        weight_entropy = self._compute_weight_entropy(weight_maps)  # [latent_h, latent_w]
        
        # 熵高的区域使用像素级，熵低的区域使用区域级
        entropy_threshold = 0.5
        pixel_mask = (weight_entropy > entropy_threshold).float()
        region_mask = 1.0 - pixel_mask
        
        # 扩展掩码
        pixel_mask_expanded = pixel_mask.unsqueeze(0).unsqueeze(0).expand(batch_size, channels, -1, -1)
        region_mask_expanded = region_mask.unsqueeze(0).unsqueeze(0).expand(batch_size, channels, -1, -1)
        
        # 自适应混合
        adaptive_modulated = (
            pixel_modulated * pixel_mask_expanded + 
            region_modulated * region_mask_expanded
        )
        
        return adaptive_modulated
    
    def _compute_weight_entropy(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """计算权重熵 (衡量不确定性)"""
        # weight_maps: [num_concepts, H, W]
        eps = 1e-8
        
        # 沿概念维度计算熵
        entropy = -torch.sum(weight_maps * torch.log(weight_maps + eps), dim=0)  # [H, W]
        
        # 归一化到[0, 1]
        max_entropy = torch.log(torch.tensor(weight_maps.shape[0], dtype=torch.float, device=weight_maps.device))
        normalized_entropy = entropy / max_entropy
        
        return normalized_entropy

class RCACISMIntegrator(nn.Module):
    """
    🔥 RCA-CISM集成器主控制器
    
    功能:
    - 整合RCA空间注意力和CISM语义引导
    - 实现端到端的空间化语义控制
    - 管理多概念协同优化
    """
    
    def __init__(self, config: RCACISMIntegrationConfig = None):
        super().__init__()
        self.config = config or RCACISMIntegrationConfig()
        
        # 设置默认子配置
        if self.config.rca_config is None:
            self.config.rca_config = RCASpatialAttentionConfig()
        if self.config.renderer_config is None:
            self.config.renderer_config = WeightRendererConfig()
        
        # 核心组件
        self.rca_network = RCASpatialAttentionNetwork(self.config.rca_config)
        self.weight_renderer = RCAWeightRenderer(self.config.renderer_config)
        self.weight_modulator = WeightModulator(self.config)
        
        # 训练状态
        self.training_stats = {
            'total_forward_passes': 0,
            'concept_weight_history': [],
            'modulation_effectiveness': []
        }
        
        print(f"🎯 RCA-CISM集成器初始化完成")
    
    def forward(self,
                gaussian_data: Dict[str, torch.Tensor],
                cism_guidance_signals: Dict[int, torch.Tensor],
                camera_params: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        🔥 RCA-CISM集成前向传播
        
        Args:
            gaussian_data: 3D高斯数据
            cism_guidance_signals: CISM引导信号字典
            camera_params: 相机参数
            
        Returns:
            integration_result: 集成结果
                - 'modulated_guidance': 调制后的引导信号
                - 'concept_weights': 概念权重
                - 'weight_maps': 权重图
                - 'render_result': 渲染结果
        """
        # 1. 🔥 RCA网络预测概念权重
        concept_weights = self.rca_network(gaussian_data)  # [N, num_concepts]
        
        # 2. 🔥 渲染权重图
        render_result = self.weight_renderer.render_concept_weights(
            gaussian_data, concept_weights, camera_params
        )
        weight_maps = render_result['weight_maps']  # [num_concepts, H, W]
        
        # 3. 🔥 空间化引导调制
        concept_ids = list(range(self.config.rca_config.num_concepts))
        modulated_guidance = self.weight_modulator.modulate_guidance_signals(
            cism_guidance_signals, weight_maps, concept_ids
        )
        
        # 4. 🔥 更新统计
        self._update_training_stats(concept_weights, weight_maps, modulated_guidance)
        
        # 5. 🔥 组装结果
        result = {
            'modulated_guidance': modulated_guidance,
            'concept_weights': concept_weights,
            'weight_maps': weight_maps,
            'render_result': render_result,
            'rca_network_output': concept_weights,
            'spatial_attention_applied': True
        }
        
        return result
    
    def compute_spatial_consistency_loss(self, 
                                       weight_maps: torch.Tensor,
                                       prev_weight_maps: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        🔥 计算空间一致性损失
        
        Args:
            weight_maps: [num_concepts, H, W] 当前权重图
            prev_weight_maps: [num_concepts, H, W] 前一帧权重图 (可选)
            
        Returns:
            consistency_loss: 一致性损失
        """
        # 空间平滑性损失 (Total Variation Loss)
        spatial_loss = self._compute_total_variation_loss(weight_maps)
        
        total_loss = self.config.spatial_consistency_weight * spatial_loss
        
        # 时间一致性损失 (如果有前一帧)
        if prev_weight_maps is not None and self.config.temporal_consistency_weight > 0:
            temporal_loss = F.mse_loss(weight_maps, prev_weight_maps)
            total_loss += self.config.temporal_consistency_weight * temporal_loss
        
        return total_loss
    
    def _compute_total_variation_loss(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """计算总变差损失 (TV Loss)"""
        # weight_maps: [num_concepts, H, W]
        
        # 水平方向的差分
        h_diff = torch.abs(weight_maps[:, :, 1:] - weight_maps[:, :, :-1])
        
        # 垂直方向的差分
        v_diff = torch.abs(weight_maps[:, 1:, :] - weight_maps[:, :-1, :])
        
        # 总变差
        tv_loss = torch.mean(h_diff) + torch.mean(v_diff)
        
        return tv_loss
    
    def _update_training_stats(self,
                              concept_weights: torch.Tensor,
                              weight_maps: torch.Tensor,
                              modulated_guidance: torch.Tensor):
        """更新训练统计"""
        self.training_stats['total_forward_passes'] += 1
        
        # 记录概念权重分布
        concept_dist = concept_weights.mean(dim=0).cpu().numpy()
        self.training_stats['concept_weight_history'].append(concept_dist)
        
        # 计算调制有效性 (权重图的变化程度)
        weight_variance = torch.var(weight_maps).item()
        self.training_stats['modulation_effectiveness'].append(weight_variance)
        
        # 保持历史记录长度
        max_history = 1000
        for key in ['concept_weight_history', 'modulation_effectiveness']:
            if len(self.training_stats[key]) > max_history:
                self.training_stats[key] = self.training_stats[key][-max_history:]
    
    def get_integration_statistics(self) -> Dict[str, Any]:
        """获取集成统计信息"""
        stats = self.training_stats.copy()
        
        if stats['concept_weight_history']:
            # 计算概念权重的稳定性
            recent_weights = np.array(stats['concept_weight_history'][-100:])
            weight_stability = 1.0 / (np.std(recent_weights, axis=0).mean() + 1e-6)
            stats['concept_weight_stability'] = weight_stability
        
        if stats['modulation_effectiveness']:
            # 计算调制有效性趋势
            recent_effectiveness = stats['modulation_effectiveness'][-100:]
            effectiveness_trend = np.mean(recent_effectiveness[-10:]) - np.mean(recent_effectiveness[-50:-10])
            stats['modulation_effectiveness_trend'] = effectiveness_trend
        
        return stats
    
    def visualize_integration_results(self, 
                                    integration_result: Dict[str, torch.Tensor]) -> Dict[str, np.ndarray]:
        """
        🔥 可视化集成结果
        
        Args:
            integration_result: 集成结果
            
        Returns:
            visualization: 可视化字典
        """
        # 基础权重图可视化
        weight_visualization = self.weight_renderer.visualize_weight_maps(
            integration_result['render_result']
        )
        
        # 引导信号可视化
        modulated_guidance = integration_result['modulated_guidance']  # [batch_size, 4, H//8, W//8]
        
        # 将引导信号转换为可视化格式
        guidance_magnitude = torch.norm(modulated_guidance, dim=1).cpu().numpy()  # [batch_size, H//8, W//8]
        
        # 上采样到原始分辨率
        guidance_vis = F.interpolate(
            torch.from_numpy(guidance_magnitude).unsqueeze(1),
            size=(weight_visualization['colored_weight_map'].shape[0], 
                  weight_visualization['colored_weight_map'].shape[1]),
            mode='bilinear',
            align_corners=False
        ).squeeze().numpy()
        
        # 归一化
        if guidance_vis.max() > 0:
            guidance_vis = guidance_vis / guidance_vis.max()
        
        # 组合可视化
        visualization = weight_visualization.copy()
        visualization.update({
            'guidance_magnitude': guidance_vis,           # [H, W] 引导信号强度
            'integration_overlay': weight_visualization['colored_weight_map'] * 0.7 + 
                                 np.stack([guidance_vis] * 3, axis=2) * 0.3,  # 叠加显示
        })
        
        return visualization

# 使用示例和测试
if __name__ == "__main__":
    # 创建配置
    rca_config = RCASpatialAttentionConfig(num_concepts=3, hidden_dims=[256, 512, 256])
    renderer_config = WeightRendererConfig(num_concepts=3, image_height=512, image_width=512)
    integration_config = RCACISMIntegrationConfig(
        rca_config=rca_config,
        renderer_config=renderer_config
    )
    
    # 创建集成器
    integrator = RCACISMIntegrator(integration_config)
    
    # 模拟数据
    N = 1000
    gaussian_data = {
        'xyz': torch.randn(N, 3),
        'rgb': torch.rand(N, 3),
        'opacity': torch.rand(N, 1),
        'scales': torch.rand(N, 3) * 0.1 + 0.01,
        'rotations': F.normalize(torch.randn(N, 4), dim=1),
        'concept_id': torch.randint(0, 3, (N,))
    }
    
    # 模拟CISM引导信号
    cism_guidance_signals = {
        0: torch.randn(1, 4, 64, 64),  # background guidance
        1: torch.randn(1, 4, 64, 64),  # repair guidance
        2: torch.randn(1, 4, 64, 64),  # boundary guidance
    }
    
    # 模拟相机参数
    camera_params = {
        'view_matrix': torch.eye(4),
        'projection_matrix': torch.tensor([
            [2.0, 0.0, 0.0, 0.0],
            [0.0, 2.0, 0.0, 0.0],
            [0.0, 0.0, -1.0, -1.0],
            [0.0, 0.0, -2.0, 0.0]
        ]),
        'focal_length': torch.tensor(500.0)
    }
    
    # 测试集成
    print("🧪 测试RCA-CISM集成...")
    result = integrator(gaussian_data, cism_guidance_signals, camera_params)
    
    print(f"   调制后引导信号形状: {result['modulated_guidance'].shape}")
    print(f"   概念权重形状: {result['concept_weights'].shape}")
    print(f"   权重图形状: {result['weight_maps'].shape}")
    
    # 测试一致性损失
    consistency_loss = integrator.compute_spatial_consistency_loss(result['weight_maps'])
    print(f"   空间一致性损失: {consistency_loss.item():.6f}")
    
    # 测试可视化
    visualization = integrator.visualize_integration_results(result)
    print(f"   可视化结果键: {list(visualization.keys())}")
    
    # 测试统计
    stats = integrator.get_integration_statistics()
    print(f"📊 集成统计: {stats}")
    
    print("🎉 RCA-CISM集成测试完成")