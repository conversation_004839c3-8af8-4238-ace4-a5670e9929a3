#!/usr/bin/env python3
"""
诊断enhanced_train.py导入问题
"""

import os
import sys
import traceback

def setup_paths():
    """设置路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    gaussian_splatting_dir = os.path.join(current_dir, "gaussian_splatting")
    
    if gaussian_splatting_dir not in sys.path:
        sys.path.insert(0, gaussian_splatting_dir)
        print(f"添加路径: {gaussian_splatting_dir}")
    
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
        print(f"添加路径: {current_dir}")

def test_individual_imports():
    """逐个测试导入"""
    print("\n🔍 逐个测试导入...")
    
    imports_to_test = [
        ("torch", "import torch"),
        ("numpy", "import numpy as np"),
        ("pathlib", "from pathlib import Path"),
        ("dataclasses", "from dataclasses import dataclass"),
        ("scene", "from scene import Scene, GaussianModel"),
        ("cism_core", "from cism_core import DiffusionEngine"),
        ("rca_core", "from rca_core import RCAIntegrator"),
    ]
    
    for name, import_stmt in imports_to_test:
        try:
            exec(import_stmt)
            print(f"✅ {name}: 成功")
        except Exception as e:
            print(f"❌ {name}: 失败 - {e}")

def test_enhanced_train_step_by_step():
    """逐步测试enhanced_train.py"""
    print("\n🔍 逐步测试enhanced_train.py...")
    
    try:
        print("1. 测试基本导入...")
        import os
        import sys
        import torch
        import numpy as np
        from pathlib import Path
        from dataclasses import dataclass
        print("   ✅ 基本导入成功")
        
        print("2. 测试路径设置...")
        current_script_path = os.path.dirname(os.path.abspath(__file__))
        gaussian_splatting_dir = os.path.join(current_script_path, "gaussian_splatting")
        if gaussian_splatting_dir not in sys.path:
            sys.path.insert(0, gaussian_splatting_dir)
        print("   ✅ 路径设置成功")
        
        print("3. 测试scene导入...")
        from scene import Scene, GaussianModel
        print("   ✅ scene导入成功")
        
        print("4. 测试cism_core导入...")
        from cism_core import DiffusionEngine, ConceptGuidance, SDSLoss
        print("   ✅ cism_core导入成功")
        
        print("5. 测试rca_core导入...")
        from rca_core import RCAIntegrator, RCADataPreparator
        print("   ✅ rca_core导入成功")
        
        print("6. 测试Stage4Config创建...")
        @dataclass
        class Stage4Config:
            model_path: str = "./output/point_cloud/iteration_30000/point_cloud.ply"
            iterations: int = 1000
            device: str = "cuda"
            enable_rca: bool = True
        
        config = Stage4Config()
        print("   ✅ Stage4Config创建成功")
        
        print("7. 测试EnhancedTrainer基本结构...")
        class EnhancedTrainer:
            def __init__(self, config):
                self.config = config
                self.device = torch.device(config.device)
        
        trainer = EnhancedTrainer(config)
        print("   ✅ EnhancedTrainer基本结构成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 步骤失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 开始诊断enhanced_train.py...")
    print("=" * 50)
    
    # 设置路径
    setup_paths()
    
    # 测试个别导入
    test_individual_imports()
    
    # 逐步测试
    if test_enhanced_train_step_by_step():
        print("\n✅ 所有步骤都成功！")
        print("📝 enhanced_train.py的核心组件都可以正常工作")
        
        # 现在尝试导入实际的enhanced_train
        print("\n🔍 尝试导入实际的enhanced_train.py...")
        try:
            import enhanced_train
            print("✅ enhanced_train.py导入成功！")
            
            config = enhanced_train.Stage4Config()
            print("✅ Stage4Config创建成功！")
            print(f"   模型路径: {config.model_path}")
            print(f"   迭代次数: {config.iterations}")
            
        except Exception as e:
            print(f"❌ enhanced_train.py导入失败: {e}")
            traceback.print_exc()
    else:
        print("\n❌ 存在导入问题")

if __name__ == "__main__":
    main()
