#!/usr/bin/env python3
"""
🔥 InFusion-Enhanced 阶段1 - 高质量概念掩码生成器

这个脚本负责从原始二值分割掩码生成高质量的三概念2D掩码：
- Concept 0: 背景区域
- Concept 1: 核心修复区域  
- Concept 2: 边界过渡区域

作者: Claude Sonnet 4 (Anthropic)
"""

import os
import sys
import cv2
import numpy as np
from PIL import Image
from pathlib import Path
import argparse
import json
from typing import Dict, Tuple
import logging

class EnhancedConceptMaskGenerator:
    """高质量概念掩码生成器"""
    
    def __init__(self, 
                 original_seg_path: str,
                 output_path: str = "stage1_results/concept_masks_enhanced",
                 boundary_width: int = 10):
        """
        初始化概念掩码生成器
        
        Args:
            original_seg_path: 原始二值分割掩码路径
            output_path: 输出路径
            boundary_width: 边界区域宽度（像素）
        """
        self.original_seg_path = Path(original_seg_path)
        self.output_path = Path(output_path)
        self.boundary_width = boundary_width
        
        # 创建输出目录
        self.concept_dirs = {
            0: self.output_path / "concept_0_background",
            1: self.output_path / "concept_1_repair", 
            2: self.output_path / "concept_2_boundary"
        }
        
        for concept_dir in self.concept_dirs.values():
            concept_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.output_path / 'mask_generation.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_original_mask(self, mask_path: Path) -> np.ndarray:
        """加载原始二值掩码"""
        try:
            # 尝试不同的图像格式
            if mask_path.exists():
                mask_image = Image.open(mask_path).convert('L')
                mask_array = np.array(mask_image, dtype=np.float32) / 255.0
                return mask_array
            else:
                self.logger.warning(f"掩码文件不存在: {mask_path}")
                return None
        except Exception as e:
            self.logger.error(f"加载掩码失败 {mask_path}: {e}")
            return None
    
    def generate_concept_masks(self, original_mask: np.ndarray) -> Dict[int, np.ndarray]:
        """
        从原始二值掩码生成三概念掩码
        
        Args:
            original_mask: 原始二值掩码 (0-1)
            
        Returns:
            concept_masks: {concept_id: mask_array} 字典
        """
        H, W = original_mask.shape
        
        # 将原始掩码转换为二值掩码 (阈值化)
        binary_mask = (original_mask > 0.5).astype(np.uint8)
        
        # 1. 定义核心修复区域 (Concept 1)
        # 对原始修复区域进行轻微腐蚀，确保核心区域不直接接触边界
        kernel_erode = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        core_repair_mask = cv2.erode(binary_mask, kernel_erode, iterations=1)
        
        # 2. 定义边界过渡区域 (Concept 2)
        # 对核心修复区域进行膨胀，然后减去核心区域本身
        kernel_dilate = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, 
                                                 (self.boundary_width*2, self.boundary_width*2))
        expanded_mask = cv2.dilate(core_repair_mask, kernel_dilate, iterations=1)
        boundary_mask = expanded_mask - core_repair_mask
        
        # 3. 定义背景区域 (Concept 0)
        # 全图减去核心修复区域和边界区域
        background_mask = 1 - (core_repair_mask + boundary_mask)
        background_mask = np.clip(background_mask, 0, 1)
        
        # 确保互斥性和完整覆盖
        concept_masks = {
            0: background_mask.astype(np.float32),
            1: core_repair_mask.astype(np.float32),
            2: boundary_mask.astype(np.float32)
        }
        
        # 验证互斥性和完整性
        total_mask = sum(concept_masks.values())
        if not np.allclose(total_mask, 1.0, atol=1e-6):
            self.logger.warning("概念掩码不完全互斥或不完整覆盖")
        
        return concept_masks
    
    def save_concept_mask(self, mask: np.ndarray, concept_id: int, image_name: str):
        """保存单个概念掩码"""
        # 转换为0-255范围的单通道图像
        mask_uint8 = (mask * 255).astype(np.uint8)
        
        # 构建输出文件名
        output_filename = f"concept_{concept_id}_{image_name}"
        if not output_filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            output_filename += '.png'
        
        output_path = self.concept_dirs[concept_id] / output_filename
        
        # 保存为PNG格式（无损）
        mask_image = Image.fromarray(mask_uint8, mode='L')
        mask_image.save(output_path, 'PNG')
        
        return output_path
    
    def process_single_image(self, mask_file: Path) -> Dict:
        """处理单个图像的掩码生成"""
        self.logger.info(f"处理图像: {mask_file.name}")
        
        # 加载原始掩码
        original_mask = self.load_original_mask(mask_file)
        if original_mask is None:
            return {"success": False, "error": "无法加载原始掩码"}
        
        # 生成概念掩码
        concept_masks = self.generate_concept_masks(original_mask)
        
        # 保存概念掩码
        saved_files = {}
        for concept_id, mask in concept_masks.items():
            try:
                saved_path = self.save_concept_mask(mask, concept_id, mask_file.stem)
                saved_files[concept_id] = str(saved_path)
            except Exception as e:
                self.logger.error(f"保存概念{concept_id}掩码失败: {e}")
                return {"success": False, "error": f"保存失败: {e}"}
        
        # 统计信息
        stats = {}
        for concept_id, mask in concept_masks.items():
            pixel_count = np.sum(mask > 0.5)
            total_pixels = mask.size
            ratio = pixel_count / total_pixels
            stats[f'concept_{concept_id}'] = {
                'pixel_count': int(pixel_count),
                'ratio': float(ratio)
            }
        
        return {
            "success": True,
            "saved_files": saved_files,
            "stats": stats,
            "image_shape": original_mask.shape
        }
    
    def process_all_images(self) -> Dict:
        """处理所有图像"""
        self.logger.info(f"开始处理所有图像，原始掩码路径: {self.original_seg_path}")
        
        # 查找所有掩码文件
        mask_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.JPG', '*.JPEG', '*.PNG']:
            mask_files.extend(list(self.original_seg_path.glob(ext)))
        
        if not mask_files:
            self.logger.error(f"在 {self.original_seg_path} 中未找到掩码文件")
            return {"success": False, "error": "未找到掩码文件"}
        
        self.logger.info(f"找到 {len(mask_files)} 个掩码文件")
        
        # 处理每个文件
        results = {}
        success_count = 0
        
        for mask_file in mask_files:
            result = self.process_single_image(mask_file)
            results[mask_file.name] = result
            
            if result["success"]:
                success_count += 1
            else:
                self.logger.error(f"处理 {mask_file.name} 失败: {result.get('error', '未知错误')}")
        
        # 生成总体统计
        overall_stats = {
            "total_images": len(mask_files),
            "success_count": success_count,
            "failure_count": len(mask_files) - success_count,
            "success_rate": success_count / len(mask_files) if mask_files else 0,
            "boundary_width": self.boundary_width,
            "output_path": str(self.output_path)
        }
        
        # 保存处理结果
        results_file = self.output_path / "generation_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                "overall_stats": overall_stats,
                "individual_results": results
            }, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"处理完成: {success_count}/{len(mask_files)} 成功")
        self.logger.info(f"结果保存到: {results_file}")
        
        return {
            "success": True,
            "overall_stats": overall_stats,
            "results_file": str(results_file)
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成高质量概念掩码")
    parser.add_argument("--seg_path", type=str, default="data/garden/seg", 
                       help="原始分割掩码路径")
    parser.add_argument("--output_path", type=str, default="stage1_results/concept_masks_enhanced",
                       help="输出路径")
    parser.add_argument("--boundary_width", type=int, default=10,
                       help="边界区域宽度（像素）")
    
    args = parser.parse_args()
    
    print("🔥 InFusion-Enhanced 概念掩码生成器")
    print(f"原始掩码路径: {args.seg_path}")
    print(f"输出路径: {args.output_path}")
    print(f"边界宽度: {args.boundary_width} 像素")
    
    # 创建生成器并运行
    generator = EnhancedConceptMaskGenerator(
        original_seg_path=args.seg_path,
        output_path=args.output_path,
        boundary_width=args.boundary_width
    )
    
    result = generator.process_all_images()
    
    if result["success"]:
        print("🎉 概念掩码生成成功！")
        print(f"📊 统计信息: {result['overall_stats']}")
    else:
        print("❌ 概念掩码生成失败！")
        print(f"错误: {result.get('error', '未知错误')}")
        sys.exit(1)

if __name__ == "__main__":
    main()
