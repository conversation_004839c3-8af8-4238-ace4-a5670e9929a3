<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_fast_trigonometry</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_fast_trigonometry<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00033.html" title="GLM_GTX_fast_trigonometry ">glm/gtx/fast_trigonometry.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga9721d63356e5d94fdc4b393a426ab26b"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga9721d63356e5d94fdc4b393a426ab26b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00325.html#ga9721d63356e5d94fdc4b393a426ab26b">fastAcos</a> (T angle)</td></tr>
<tr class="memdesc:ga9721d63356e5d94fdc4b393a426ab26b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common acos function but less accurate.  <a href="a00325.html#ga9721d63356e5d94fdc4b393a426ab26b">More...</a><br /></td></tr>
<tr class="separator:ga9721d63356e5d94fdc4b393a426ab26b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga562cb62c51fbfe7fac7db0bce706b81f"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga562cb62c51fbfe7fac7db0bce706b81f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00325.html#ga562cb62c51fbfe7fac7db0bce706b81f">fastAsin</a> (T angle)</td></tr>
<tr class="memdesc:ga562cb62c51fbfe7fac7db0bce706b81f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common asin function but less accurate.  <a href="a00325.html#ga562cb62c51fbfe7fac7db0bce706b81f">More...</a><br /></td></tr>
<tr class="separator:ga562cb62c51fbfe7fac7db0bce706b81f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d197c6ef564f5e5d59af3b3f8adcc2c"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga8d197c6ef564f5e5d59af3b3f8adcc2c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00325.html#ga8d197c6ef564f5e5d59af3b3f8adcc2c">fastAtan</a> (T y, T x)</td></tr>
<tr class="memdesc:ga8d197c6ef564f5e5d59af3b3f8adcc2c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common atan function but less accurate.  <a href="a00325.html#ga8d197c6ef564f5e5d59af3b3f8adcc2c">More...</a><br /></td></tr>
<tr class="separator:ga8d197c6ef564f5e5d59af3b3f8adcc2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae25de86a968490ff56856fa425ec9d30"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae25de86a968490ff56856fa425ec9d30"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00325.html#gae25de86a968490ff56856fa425ec9d30">fastAtan</a> (T angle)</td></tr>
<tr class="memdesc:gae25de86a968490ff56856fa425ec9d30"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common atan function but less accurate.  <a href="a00325.html#gae25de86a968490ff56856fa425ec9d30">More...</a><br /></td></tr>
<tr class="separator:gae25de86a968490ff56856fa425ec9d30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab34c8b45c23c0165a64dcecfcc3b302a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gab34c8b45c23c0165a64dcecfcc3b302a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00325.html#gab34c8b45c23c0165a64dcecfcc3b302a">fastCos</a> (T angle)</td></tr>
<tr class="memdesc:gab34c8b45c23c0165a64dcecfcc3b302a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common cos function but less accurate.  <a href="a00325.html#gab34c8b45c23c0165a64dcecfcc3b302a">More...</a><br /></td></tr>
<tr class="separator:gab34c8b45c23c0165a64dcecfcc3b302a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0aab3257bb3b628d10a1e0483e2c6915"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga0aab3257bb3b628d10a1e0483e2c6915"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00325.html#ga0aab3257bb3b628d10a1e0483e2c6915">fastSin</a> (T angle)</td></tr>
<tr class="memdesc:ga0aab3257bb3b628d10a1e0483e2c6915"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common sin function but less accurate.  <a href="a00325.html#ga0aab3257bb3b628d10a1e0483e2c6915">More...</a><br /></td></tr>
<tr class="separator:ga0aab3257bb3b628d10a1e0483e2c6915"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf29b9c1101a10007b4f79ee89df27ba2"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaf29b9c1101a10007b4f79ee89df27ba2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00325.html#gaf29b9c1101a10007b4f79ee89df27ba2">fastTan</a> (T angle)</td></tr>
<tr class="memdesc:gaf29b9c1101a10007b4f79ee89df27ba2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common tan function but less accurate.  <a href="a00325.html#gaf29b9c1101a10007b4f79ee89df27ba2">More...</a><br /></td></tr>
<tr class="separator:gaf29b9c1101a10007b4f79ee89df27ba2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga069527c6dbd64f53435b8ebc4878b473"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga069527c6dbd64f53435b8ebc4878b473"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00325.html#ga069527c6dbd64f53435b8ebc4878b473">wrapAngle</a> (T angle)</td></tr>
<tr class="memdesc:ga069527c6dbd64f53435b8ebc4878b473"><td class="mdescLeft">&#160;</td><td class="mdescRight">Wrap an angle to [0 2pi[ From GLM_GTX_fast_trigonometry extension.  <a href="a00325.html#ga069527c6dbd64f53435b8ebc4878b473">More...</a><br /></td></tr>
<tr class="separator:ga069527c6dbd64f53435b8ebc4878b473"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00033.html" title="GLM_GTX_fast_trigonometry ">glm/gtx/fast_trigonometry.hpp</a>&gt; to use the features of this extension. </p>
<p>Fast but less accurate implementations of trigonometric functions. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga9721d63356e5d94fdc4b393a426ab26b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastAcos </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common acos function but less accurate. </p>
<p>Defined between -2pi and 2pi. From GLM_GTX_fast_trigonometry extension. </p>

</div>
</div>
<a class="anchor" id="ga562cb62c51fbfe7fac7db0bce706b81f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastAsin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common asin function but less accurate. </p>
<p>Defined between -2pi and 2pi. From GLM_GTX_fast_trigonometry extension. </p>

</div>
</div>
<a class="anchor" id="ga8d197c6ef564f5e5d59af3b3f8adcc2c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastAtan </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common atan function but less accurate. </p>
<p>Defined between -2pi and 2pi. From GLM_GTX_fast_trigonometry extension. </p>

</div>
</div>
<a class="anchor" id="gae25de86a968490ff56856fa425ec9d30"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastAtan </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common atan function but less accurate. </p>
<p>Defined between -2pi and 2pi. From GLM_GTX_fast_trigonometry extension. </p>

</div>
</div>
<a class="anchor" id="gab34c8b45c23c0165a64dcecfcc3b302a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastCos </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common cos function but less accurate. </p>
<p>From GLM_GTX_fast_trigonometry extension. </p>

</div>
</div>
<a class="anchor" id="ga0aab3257bb3b628d10a1e0483e2c6915"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastSin </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common sin function but less accurate. </p>
<p>From GLM_GTX_fast_trigonometry extension. </p>

</div>
</div>
<a class="anchor" id="gaf29b9c1101a10007b4f79ee89df27ba2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastTan </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common tan function but less accurate. </p>
<p>Defined between -2pi and 2pi. From GLM_GTX_fast_trigonometry extension. </p>

</div>
</div>
<a class="anchor" id="ga069527c6dbd64f53435b8ebc4878b473"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::wrapAngle </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Wrap an angle to [0 2pi[ From GLM_GTX_fast_trigonometry extension. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
