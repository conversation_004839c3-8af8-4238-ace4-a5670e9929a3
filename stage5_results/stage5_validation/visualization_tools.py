import os
import torch
import numpy as np
from typing import Dict, List, Any
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
from PIL import Image, ImageDraw, ImageFont
import time

class ValidationVisualizer:
    """
    🎯 验证结果可视化工具
    为各类验证结果生成可视化报告
    """
    
    def __init__(self, output_dir: str, config: Dict = None):
        """
        Args:
            output_dir: 输出目录
            config: 可视化配置
        """
        self.output_dir = os.path.join(output_dir, 'visualizations')
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 默认配置
        self.config = {
            'heatmap_alpha': 0.7,
            'save_formats': ['png', 'mp4'],
            'include_3d': True
        }
        
        # 更新配置
        if config:
            self.config.update(config)
    
    def visualize_concept_separation(self, concept_results: Dict) -> Dict[str, str]:
        """
        可视化概念分离效果
        
        Args:
            concept_results: 概念分离验证结果
            
        Returns:
            可视化输出路径
        """
        print("   📊 生成概念分离可视化...")
        
        # 创建输出目录
        concept_vis_dir = os.path.join(self.output_dir, 'concept_separation')
        os.makedirs(concept_vis_dir, exist_ok=True)
        
        # 1. 单概念测试可视化
        single_concept_path = os.path.join(concept_vis_dir, 'single_concept_test.png')
        self._visualize_single_concept_tests(concept_results, single_concept_path)
        
        # 2. 对比测试可视化
        comparison_path = os.path.join(concept_vis_dir, 'concept_comparison.png')
        self._visualize_concept_comparison(concept_results, comparison_path)
        
        # 3. 消融实验可视化
        ablation_path = os.path.join(concept_vis_dir, 'ablation_study.png')
        self._visualize_ablation_study(concept_results, ablation_path)
        
        # 4. 质量指标可视化
        metrics_path = os.path.join(concept_vis_dir, 'quality_metrics.png')
        self._visualize_quality_metrics(concept_results, metrics_path)
        
        # 返回可视化路径
        return {
            'single_concept': single_concept_path,
            'comparison': comparison_path,
            'ablation': ablation_path,
            'metrics': metrics_path
        }
    
    def visualize_spatial_control(self, spatial_results: Dict) -> Dict[str, str]:
        """
        可视化空间控制精度
        
        Args:
            spatial_results: 空间控制验证结果
            
        Returns:
            可视化输出路径
        """
        print("   📊 生成空间控制可视化...")
        
        # 创建输出目录
        spatial_vis_dir = os.path.join(self.output_dir, 'spatial_control')
        os.makedirs(spatial_vis_dir, exist_ok=True)
        
        # 1. 权重图可视化
        weight_vis_path = os.path.join(spatial_vis_dir, 'weight_visualization.png')
        self._visualize_weight_maps(spatial_results, weight_vis_path)
        
        # 2. 边界精度可视化
        boundary_path = os.path.join(spatial_vis_dir, 'boundary_precision.png')
        self._visualize_boundary_precision(spatial_results, boundary_path)
        
        # 3. 控制精度可视化
        control_path = os.path.join(spatial_vis_dir, 'control_precision.png')
        self._visualize_control_precision(spatial_results, control_path)
        
        # 返回可视化路径
        return {
            'weight_maps': weight_vis_path,
            'boundary_precision': boundary_path,
            'control_precision': control_path
        }
    
    def visualize_performance_metrics(self, performance_results: Dict) -> Dict[str, str]:
        """
        可视化性能指标
        
        Args:
            performance_results: 系统性能评估结果
            
        Returns:
            可视化输出路径
        """
        print("   📊 生成性能指标可视化...")
        
        # 创建输出目录
        perf_vis_dir = os.path.join(self.output_dir, 'performance')
        os.makedirs(perf_vis_dir, exist_ok=True)
        
                # 1. 计算性能可视化
        compute_path = os.path.join(perf_vis_dir, 'compute_performance.png')
        self._visualize_compute_performance(performance_results, compute_path)
        
        # 2. 可扩展性可视化
        scalability_path = os.path.join(perf_vis_dir, 'scalability.png')
        self._visualize_scalability(performance_results, scalability_path)
        
        # 3. 系统稳定性可视化
        stability_path = os.path.join(perf_vis_dir, 'system_stability.png')
        self._visualize_system_stability(performance_results, stability_path)
        
        # 4. 性能总结可视化
        summary_path = os.path.join(perf_vis_dir, 'performance_summary.png')
        self._visualize_performance_summary(performance_results, summary_path)
        
        # 返回可视化路径
        return {
            'compute_performance': compute_path,
            'scalability': scalability_path,
            'system_stability': stability_path,
            'summary': summary_path
        }
    
    def create_summary_visualization(self, 
                                   concept_results: Dict, 
                                   spatial_results: Dict,
                                   performance_results: Dict) -> Dict[str, str]:
        """
        创建综合可视化报告
        
        Args:
            concept_results: 概念分离验证结果
            spatial_results: 空间控制验证结果
            performance_results: 系统性能评估结果
            
        Returns:
            可视化输出路径
        """
        print("   📊 生成综合验证报告...")
        
        # 创建摘要报告目录
        summary_dir = os.path.join(self.output_dir, 'summary')
        os.makedirs(summary_dir, exist_ok=True)
        
        # 1. 创建总结报告
        summary_report_path = os.path.join(summary_dir, 'validation_summary.png')
        self._create_summary_report(
            concept_results, spatial_results, performance_results,
            summary_report_path
        )
        
        # 2. 创建关键指标仪表盘
        dashboard_path = os.path.join(summary_dir, 'metrics_dashboard.png')
        self._create_metrics_dashboard(
            concept_results, spatial_results, performance_results,
            dashboard_path
        )
        
        # 3. 创建比较矩阵 (如果有消融实验数据)
        comparison_matrix_path = os.path.join(summary_dir, 'comparison_matrix.png')
        if 'ablation_studies' in concept_results:
            self._create_comparison_matrix(concept_results, comparison_matrix_path)
        
        # 4. 创建HTML报告 (汇总所有可视化)
        html_report_path = os.path.join(summary_dir, 'validation_report.html')
        self._create_html_report(
            concept_results, spatial_results, performance_results,
            html_report_path
        )
        
        # 返回可视化路径
        return {
            'summary_report': summary_report_path,
            'dashboard': dashboard_path,
            'comparison_matrix': comparison_matrix_path,
            'html_report': html_report_path
        }
    
    def _visualize_single_concept_tests(self, concept_results: Dict, output_path: str):
        """可视化单概念测试结果"""
        # 检查是否有单概念测试结果
        if 'single_concept_results' not in concept_results or not concept_results['single_concept_results'].get('test_cases'):
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No single concept test data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        test_cases = concept_results['single_concept_results']['test_cases']
        
        # 创建网格布局
        fig = plt.figure(figsize=(15, 5 * len(test_cases)))
        
        for i, case in enumerate(test_cases):
            # 获取案例名称和相关指标
            name = case['name']
            concept_id = case['active_concept']
            metrics = case['metrics']
            
            # 添加案例标题
            ax_title = plt.subplot(len(test_cases), 3, i*3 + 1)
            ax_title.text(0.5, 0.5, f"Concept {concept_id}: {name}\n{case['prompt']}", 
                        ha='center', va='center', fontsize=12)
            ax_title.axis('off')
            
            # 添加随机示例图像
            ax_image = plt.subplot(len(test_cases), 3, i*3 + 2)
            # 创建随机图像
            img = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
            ax_image.imshow(img)
            ax_image.set_title(f"Example Render")
            ax_image.axis('off')
            
            # 添加指标条形图
            ax_metrics = plt.subplot(len(test_cases), 3, i*3 + 3)
            metric_names = list(metrics.keys())
            metric_values = list(metrics.values())
            y_pos = np.arange(len(metric_names))
            
            bars = ax_metrics.barh(y_pos, metric_values, align='center')
            ax_metrics.set_yticks(y_pos)
            ax_metrics.set_yticklabels(metric_names)
            ax_metrics.set_xlim([0, 1.0 if max(metric_values) < 1.0 else max(metric_values) * 1.1])
            ax_metrics.set_title("Quality Metrics")
            
            # 添加数值标签
            for bar, val in zip(bars, metric_values):
                ax_metrics.text(bar.get_width() + 0.05, bar.get_y() + bar.get_height()/2, 
                              f'{val:.2f}', va='center')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_concept_comparison(self, concept_results: Dict, output_path: str):
        """可视化概念对比测试结果"""
        # 检查是否有对比测试结果
        if 'comparative_results' not in concept_results or not concept_results['comparative_results'].get('comparisons'):
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No concept comparison data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        comparisons = concept_results['comparative_results']['comparisons']
        
        # 创建一个大的图像
        plt.figure(figsize=(15, 10))
        
        # 遍历比较类型
        for i, comp in enumerate(comparisons):
            plt.subplot(len(comparisons), 1, i+1)
            
            if comp['name'] == 'text_variation':
                # 文本变化对比
                metrics = comp['metrics']
                metric_names = list(metrics.keys())
                metric_values = list(metrics.values())
                
                plt.bar(metric_names, metric_values)
                plt.title(f"Text Variation Comparison: {comp['description']}")
                plt.ylim([0, 1.0 if max(metric_values) < 1.0 else max(metric_values) * 1.1])
                
                # 添加数值标签
                for j, v in enumerate(metric_values):
                    plt.text(j, v + 0.02, f'{v:.2f}', ha='center')
                
            elif comp['name'] == 'concept_strength':
                # 概念强度对比
                strengths = comp['metrics']['strength_values']
                impacts = comp['metrics']['impact_values']
                
                plt.plot(strengths, impacts, 'o-')
                plt.axvline(x=comp['metrics']['optimal_strength'], color='r', linestyle='--', 
                          label=f"Optimal: {comp['metrics']['optimal_strength']}")
                plt.axhline(y=impacts[strengths.index(comp['metrics']['optimal_strength'])], 
                          color='r', linestyle='--')
                
                plt.title(f"Concept Strength Comparison: {comp['description']}")
                plt.xlabel("Strength Value")
                plt.ylabel("Impact")
                plt.legend()
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_ablation_study(self, concept_results: Dict, output_path: str):
        """可视化消融实验结果"""
        # 检查是否有消融实验结果
        if 'ablation_results' not in concept_results or not concept_results['ablation_results'].get('ablation_modes'):
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No ablation study data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        ablation_results = concept_results['ablation_results']
        modes = [mode['name'] for mode in ablation_results['ablation_modes']]
        
        # 创建两个子图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 1. 各模式指标比较
        if 'metrics' in ablation_results:
            metrics = ablation_results['metrics']
            
            # 提取PSNR和SSIM
            psnr_values = [metrics[mode].get('psnr', 0) for mode in modes]
            ssim_values = [metrics[mode].get('ssim', 0) for mode in modes]
            
            x = np.arange(len(modes))
            width = 0.35
            
            rects1 = ax1.bar(x - width/2, psnr_values, width, label='PSNR (dB)')
            rects2 = ax1.bar(x + width/2, ssim_values, width, label='SSIM')
            
            ax1.set_title('Quality Metrics by Mode')
            ax1.set_xlabel('Mode')
            ax1.set_xticks(x)
            ax1.set_xticklabels(modes)
            ax1.legend()
            
            # 添加数值标签
            for rect in rects1:
                height = rect.get_height()
                ax1.annotate(f'{height:.1f}',
                           xy=(rect.get_x() + rect.get_width()/2, height),
                           xytext=(0, 3),
                           textcoords="offset points",
                           ha='center', va='bottom')
            
            for rect in rects2:
                height = rect.get_height()
                ax1.annotate(f'{height:.2f}',
                           xy=(rect.get_x() + rect.get_width()/2, height),
                           xytext=(0, 3),
                           textcoords="offset points",
                           ha='center', va='bottom')
        
        # 2. 改进百分比
        if 'comparisons' in ablation_results:
            comparisons = ablation_results['comparisons']
            
            # 提取与基准的比较结果
            comparison_modes = list(comparisons.keys())
            psnr_diffs = [comparisons[mode].get('psnr_diff', 0) for mode in comparison_modes]
            ssim_diffs = [comparisons[mode].get('ssim_diff', 0) for mode in comparison_modes]
            overall_diffs = [comparisons[mode].get('overall_quality_reduction', 0) for mode in comparison_modes]
            
            # 创建条形图
            y_pos = np.arange(len(comparison_modes))
            
            ax2.barh(y_pos, overall_diffs, align='center', label='Overall Quality Reduction')
            ax2.set_yticks(y_pos)
            ax2.set_yticklabels(comparison_modes)
            ax2.set_title('Performance Degradation vs Full Model')
            ax2.set_xlabel('Quality Reduction')
            
            # 显示改进百分比
            if 'improvement' in ablation_results:
                improvement = ablation_results['improvement']
                ax2.text(0.5, -0.15, f"RCA Improvement: {improvement:.1%}", 
                       transform=ax2.transAxes, ha='center', fontsize=12)
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_quality_metrics(self, concept_results: Dict, output_path: str):
        """可视化质量指标"""
        # 检查是否有质量指标
        if 'quality_metrics' not in concept_results:
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No quality metrics data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        quality_metrics = concept_results['quality_metrics']
        
        # 创建一个雷达图
        categories = ['Semantic Consistency', 'Spatial Continuity', 'PSNR', 'SSIM', 'LPIPS']
        
        # 提取指标值
        values = [
            quality_metrics.get('semantic_consistency', 0),
            quality_metrics.get('spatial_continuity', 0),
            quality_metrics.get('metrics', {}).get('psnr', 0) / 40,  # 标准化PSNR
            quality_metrics.get('metrics', {}).get('ssim', 0),
            1 - quality_metrics.get('metrics', {}).get('lpips', 0)  # 转换LPIPS (越低越好)
        ]
        
        # 标准化值到0-1之间
        values = [max(0, min(1, v)) for v in values]
        
        # 添加第一个点以闭合多边形
        values.append(values[0])
        categories.append(categories[0])
        
        # 创建雷达图
        fig = plt.figure(figsize=(8, 8))
        ax = fig.add_subplot(111, polar=True)
        
        # 计算角度
        angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # 闭合
        
        # 绘制雷达图
        ax.plot(angles, values, 'o-', linewidth=2)
        ax.fill(angles, values, alpha=0.25)
        ax.set_thetagrids(np.degrees(angles[:-1]), categories[:-1])
        
        # 设置y轴
        ax.set_ylim(0, 1)
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
        
        # 添加标题
        plt.title('Quality Metrics Overview', size=15, y=1.1)
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_weight_maps(self, spatial_results: Dict, output_path: str):
        """可视化权重图"""
        # 检查是否有权重图可视化结果
        if 'weight_visualization_results' not in spatial_results:
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No weight visualization data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        vis_results = spatial_results['weight_visualization_results']
        
        # 创建组合图像
        fig = plt.figure(figsize=(15, 10))
        
        # 1. 热力图
        if 'visualizations' in vis_results and 'heatmap' in vis_results['visualizations']:
            heatmap_paths = vis_results['visualizations']['heatmap']
            
            # 创建子图
            for i, (name, path) in enumerate(heatmap_paths.items()):
                if i < 4:  # 最多显示4个热力图
                    plt.subplot(2, 2, i+1)
                    
                    # 加载图像（如果路径有效）
                    try:
                        img = plt.imread(path)
                        plt.imshow(img)
                        plt.title(f"{name}")
                        plt.axis('off')
                    except:
                        # 如果无法加载，显示占位符
                        plt.text(0.5, 0.5, f"{name} (Image not available)", 
                                ha='center', va='center')
                        plt.axis('off')
        else:
            # 创建一个示例热力图
            plt.subplot(2, 2, 1)
            plt.text(0.5, 0.5, "Heatmap visualization not available", 
                    ha='center', va='center')
            plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_boundary_precision(self, spatial_results: Dict, output_path: str):
        """可视化边界精度"""
        # 检查是否有边界精度测试结果
        if 'boundary_precision_results' not in spatial_results:
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No boundary precision data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        boundary_results = spatial_results['boundary_precision_results']
        
        # 创建组合图像
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 1. 边界测试案例可视化
        if 'test_cases' in boundary_results:
            test_cases = boundary_results['test_cases']
            
            # 提取测试案例名称和指标
            case_names = [case['name'] for case in test_cases]
            sharpness = [case['metrics']['sharpness'] for case in test_cases]
            smoothness = [case['metrics']['smoothness'] for case in test_cases]
            consistency = [case['metrics']['geometric_consistency'] for case in test_cases]
            
            x = np.arange(len(case_names))
            width = 0.25
            
            ax1.bar(x - width, sharpness, width, label='Sharpness')
            ax1.bar(x, smoothness, width, label='Smoothness')
            ax1.bar(x + width, consistency, width, label='Geometric Consistency')
            
            ax1.set_title('Boundary Precision Metrics')
            ax1.set_xticks(x)
            ax1.set_xticklabels(case_names)
            ax1.set_ylim([0, 1.1])
            ax1.legend()
        
        # 2. 一个示例边界图像
        if 'visualizations' in boundary_results and boundary_results['visualizations']:
            first_key = list(boundary_results['visualizations'].keys())[0]
            test_image_path = boundary_results['visualizations'][first_key]
            
            try:
                img = plt.imread(test_image_path)
                ax2.imshow(img)
                ax2.set_title(f"Example: {first_key}")
                ax2.axis('off')
            except:
                ax2.text(0.5, 0.5, "Example image not available", 
                      ha='center', va='center')
                ax2.axis('off')
        else:
            ax2.text(0.5, 0.5, "Example image not available", 
                  ha='center', va='center')
            ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_control_precision(self, spatial_results: Dict, output_path: str):
        """可视化控制精度"""
        # 检查是否有控制精度测试结果
        if 'control_precision_results' not in spatial_results:
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No control precision data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        control_results = spatial_results['control_precision_results']
        
        # 创建组合图像
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 1. 控制测试案例指标
        if 'test_cases' in control_results:
            test_cases = control_results['test_cases']
            
            # 提取测试案例名称和指标
            case_names = [case['name'] for case in test_cases]
            precision = [case['metrics']['precision'] for case in test_cases]
            consistency = [case['metrics']['consistency'] for case in test_cases]
            
            x = np.arange(len(case_names))
            width = 0.35
            
            ax1.bar(x - width/2, precision, width, label='Precision')
            ax1.bar(x + width/2, consistency, width, label='Consistency')
            
            ax1.set_title('Control Precision Metrics')
            ax1.set_xticks(x)
            ax1.set_xticklabels(case_names)
            ax1.set_ylim([0, 1.1])
            ax1.legend()
        
        # 2. 一个示例控制图像
        if 'visualizations' in control_results and control_results['visualizations']:
            first_key = list(control_results['visualizations'].keys())[0]
            if isinstance(control_results['visualizations'][first_key], dict):
                test_image_path = control_results['visualizations'][first_key].get('control')
            else:
                test_image_path = control_results['visualizations'][first_key]
            
            try:
                img = plt.imread(test_image_path)
                ax2.imshow(img)
                ax2.set_title(f"Example: {first_key}")
                ax2.axis('off')
            except:
                ax2.text(0.5, 0.5, "Example image not available", 
                      ha='center', va='center')
                ax2.axis('off')
        else:
            ax2.text(0.5, 0.5, "Example image not available", 
                  ha='center', va='center')
            ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_compute_performance(self, performance_results: Dict, output_path: str):
        """可视化计算性能"""
        # 检查是否有计算性能结果
        if 'compute_performance' not in performance_results:
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No compute performance data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        compute_results = performance_results['compute_performance']
        
        # 创建组合图像
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 1. 推理时间 vs 批量大小
        if 'batch_sizes' in compute_results and 'inference_times' in compute_results:
            ax1.plot(compute_results['batch_sizes'], compute_results['inference_times'], 'o-')
            ax1.set_title('Inference Time vs Batch Size')
            ax1.set_xlabel('Batch Size')
            ax1.set_ylabel('Inference Time (ms)')
            ax1.grid(True)
        else:
            ax1.text(0.5, 0.5, "Inference time data not available", 
                  ha='center', va='center')
            ax1.axis('off')
        
        # 2. 吞吐量 vs 批量大小
        if 'batch_sizes' in compute_results and 'throughput' in compute_results:
            ax2.plot(compute_results['batch_sizes'], compute_results['throughput'], 'o-')
            ax2.set_title('Throughput vs Batch Size')
            ax2.set_xlabel('Batch Size')
            ax2.set_ylabel('Throughput (FPS)')
            ax2.grid(True)
        else:
            ax2.text(0.5, 0.5, "Throughput data not available", 
                  ha='center', va='center')
            ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_scalability(self, performance_results: Dict, output_path: str):
        """可视化可扩展性"""
        # 检查是否有可扩展性结果
        if 'scalability' not in performance_results:
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No scalability data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        scalability_results = performance_results['scalability']
        
        # 创建组合图像
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 1. 分辨率可扩展性
        if ('resolution_tests' in scalability_results and 
            'resolutions' in scalability_results['resolution_tests'] and 
            'quality_scores' in scalability_results['resolution_tests']):
            
            res_results = scalability_results['resolution_tests']
            
            ax1.plot(res_results['resolutions'], res_results['quality_scores'], 'o-')
            ax1.set_title('Quality vs Resolution')
            ax1.set_xlabel('Resolution')
            ax1.set_ylabel('Quality Score')
            ax1.set_ylim([0, 1.1])
            ax1.grid(True)
            
            # 标记最大支持分辨率
            if 'max_supported_resolution' in scalability_results:
                max_res = scalability_results['max_supported_resolution']
                ax1.axvline(x=max_res, color='r', linestyle='--', 
                          label=f"Max: {max_res}px")
                ax1.legend()
        else:
            ax1.text(0.5, 0.5, "Resolution data not available", 
                  ha='center', va='center')
            ax1.axis('off')
        
        # 2. 概念数量可扩展性
        if ('concept_tests' in scalability_results and 
            'concept_counts' in scalability_results['concept_tests'] and 
            'control_precision' in scalability_results['concept_tests']):
            
            concept_results = scalability_results['concept_tests']
            
            ax2.plot(concept_results['concept_counts'], concept_results['control_precision'], 'o-')
            ax2.set_title('Control Precision vs Concept Count')
            ax2.set_xlabel('Number of Concepts')
            ax2.set_ylabel('Control Precision')
            ax2.set_ylim([0, 1.1])
            ax2.grid(True)
            
            # 标记最大支持概念数
            if 'max_concepts' in scalability_results:
                max_concepts = scalability_results['max_concepts']
                ax2.axvline(x=max_concepts, color='r', linestyle='--', 
                          label=f"Max: {max_concepts}")
                ax2.legend()
        else:
            ax2.text(0.5, 0.5, "Concept count data not available", 
                  ha='center', va='center')
            ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_system_stability(self, performance_results: Dict, output_path: str):
        """可视化系统稳定性"""
        # 检查是否有系统稳定性结果
        if 'system_stability' not in performance_results:
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No system stability data available", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        stability_results = performance_results['system_stability']
        
        # 创建组合图像
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 1. 收敛稳定性
        if ('convergence_stability' in stability_results and 
            'epochs' in stability_results['convergence_stability'] and 
            'loss_curves' in stability_results['convergence_stability']):
            
            convergence_results = stability_results['convergence_stability']
            
            for i, curve in enumerate(convergence_results['loss_curves']):
                ax1.plot(convergence_results['epochs'], curve, 
                       label=f'Run {i+1}', alpha=0.7)
            
            ax1.set_title('Convergence Stability')
            ax1.set_xlabel('Epoch')
            ax1.set_ylabel('Loss')
            ax1.grid(True)
            ax1.legend()
        else:
            ax1.text(0.5, 0.5, "Convergence data not available", 
                  ha='center', va='center')
            ax1.axis('off')
        
        # 2. 长期稳定性
        if ('long_term_stability' in stability_results and 
            'hours' in stability_results['long_term_stability'] and 
            'inference_time' in stability_results['long_term_stability']):
            
            long_term_results = stability_results['long_term_stability']
            
            ax2.plot(long_term_results['hours'], long_term_results['inference_time'], 'o-')
            ax2.set_title('Long-term Stability (Inference Time)')
            ax2.set_xlabel('Hours')
            ax2.set_ylabel('Inference Time (ms)')
            ax2.grid(True)
            
            # 添加稳定性得分
            if 'stability_score' in long_term_results:
                stability_score = long_term_results['stability_score']
                ax2.text(0.5, 0.95, f"Stability Score: {stability_score:.2f}", 
                       transform=ax2.transAxes, ha='center')
        else:
            ax2.text(0.5, 0.5, "Long-term stability data not available", 
                  ha='center', va='center')
            ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _visualize_performance_summary(self, performance_results: Dict, output_path: str):
        """可视化性能总结"""
        # 提取总结指标
        summary = performance_results.get('system_performance', {}).get('summary', {})
        if not summary and 'compute_performance' in performance_results:
            # 尝试从计算性能结果中提取基本指标
            compute_results = performance_results['compute_performance']
            summary = {
                'inference_time_ms': compute_results.get('inference_time_ms', 0),
                'fps': compute_results.get('fps', 0),
                'peak_memory_gb': compute_results.get('peak_memory_gb', 0)
            }
        
        # 创建性能仪表盘
        plt.figure(figsize=(12, 8))
        
        # 如果没有数据，显示提示信息
        if not summary:
            plt.text(0.5, 0.5, "No performance summary data available", 
                   ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        # 指标名称和值
        metrics = [
            'Inference Time (ms)', 
            'FPS', 
            'Peak Memory (GB)'
        ]
        
        values = [
            summary.get('inference_time_ms', 0),
            summary.get('fps', 0),
            summary.get('peak_memory_gb', 0)
        ]
        
        # 创建一个简单的仪表盘
        plt.subplot(1, 1, 1)
        plt.bar(metrics, values, color=['skyblue', 'lightgreen', 'salmon'])
        plt.title('Performance Summary')
        plt.grid(axis='y')
        
        # 添加数值标签
        for i, v in enumerate(values):
            plt.text(i, v + max(values) * 0.02, f'{v:.2f}', ha='center')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _create_summary_report(self, 
                             concept_results: Dict, 
                             spatial_results: Dict,
                             performance_results: Dict,
                             output_path: str):
        """创建综合验证报告"""
        # 提取各方面的总结
        concept_summary = concept_results.get('summary', {})
        spatial_summary = spatial_results.get('summary', {})
        performance_summary = performance_results.get('summary', {})
        
        # 创建一个大型图像
        plt.figure(figsize=(15, 12))
        
        # 标题
        plt.suptitle("InFusion-Enhanced Validation Summary", fontsize=20, y=0.98)
        
        # 1. 概念分离总结
        plt.subplot(3, 1, 1)
        
        if concept_summary:
            # 提取指标
            metrics = [
                'Concept Separation',
                'Visual Quality',
                'Ablation Improvement'
            ]
            
            values = [
                concept_summary.get('concept_separation_score', 0),
                concept_summary.get('visual_quality', 0),
                concept_summary.get('ablation_improvement', 0)
            ]
            
            plt.bar(metrics, values, color='skyblue')
            plt.title('Concept Separation Validation')
            plt.ylim([0, 1.1])
            
            # 添加数值标签
            for i, v in enumerate(values):
                plt.text(i, v + 0.05, f'{v:.2f}', ha='center')
            
            # 添加总分
            overall = concept_summary.get('overall_score', 0)
            plt.text(0.95, 0.9, f"Overall: {overall:.2f}", 
                   transform=plt.gca().transAxes, ha='right',
                   bbox=dict(facecolor='white', alpha=0.8))
        else:
            plt.text(0.5, 0.5, "Concept separation summary not available", 
                   ha='center', va='center')
            plt.axis('off')
        
        # 2. 空间控制总结
        plt.subplot(3, 1, 2)
        
        if spatial_summary:
            # 提取指标
            metrics = [
                'Boundary Precision',
                'Transition Smoothness',
                'Control Precision',
                'Geometric Consistency'
            ]
            
            values = [
                spatial_summary.get('boundary_precision', 0),
                spatial_summary.get('transition_smoothness', 0),
                spatial_summary.get('control_precision', 0),
                spatial_summary.get('geometric_consistency', 0)
            ]
            
            plt.bar(metrics, values, color='lightgreen')
            plt.title('Spatial Control Validation')
            plt.ylim([0, 1.1])
            
            # 添加数值标签
            for i, v in enumerate(values):
                plt.text(i, v + 0.05, f'{v:.2f}', ha='center')
            
            # 添加总分
            overall = spatial_summary.get('overall_score', 0)
            plt.text(0.95, 0.9, f"Overall: {overall:.2f}", 
                   transform=plt.gca().transAxes, ha='right',
                   bbox=dict(facecolor='white', alpha=0.8))
        else:
            plt.text(0.5, 0.5, "Spatial control summary not available", 
                   ha='center', va='center')
            plt.axis('off')
        
        # 3. 系统性能总结
        plt.subplot(3, 1, 3)
        
        if performance_summary:
            # 提取指标
            metrics = [
                'Performance Score',
                'Scalability Score',
                'Stability Score'
            ]
            
            values = [
                performance_summary.get('performance_score', 0),
                performance_summary.get('scalability_score', 0),
                performance_summary.get('stability_score', 0)
            ]
            
            plt.bar(metrics, values, color='salmon')
            plt.title('System Performance Validation')
            plt.ylim([0, 1.1])
            
            # 添加数值标签
            for i, v in enumerate(values):
                plt.text(i, v + 0.05, f'{v:.2f}', ha='center')
            
            # 添加总分
            overall = performance_summary.get('overall_score', 0)
            plt.text(0.95, 0.9, f"Overall: {overall:.2f}", 
                   transform=plt.gca().transAxes, ha='right',
                   bbox=dict(facecolor='white', alpha=0.8))
        else:
            plt.text(0.5, 0.5, "System performance summary not available", 
                   ha='center', va='center')
            plt.axis('off')
        
        # 添加生成时间戳
        plt.figtext(0.5, 0.01, f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}", 
                  ha='center', fontsize=8)
        
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])
        plt.savefig(output_path)
        plt.close()
    
    def _create_metrics_dashboard(self, 
                                concept_results: Dict, 
                                spatial_results: Dict,
                                performance_results: Dict,
                                output_path: str):
        """创建关键指标仪表盘"""
        # 提取最重要的指标
        key_metrics = {}
        
        # 概念分离关键指标
        if 'quality_metrics' in concept_results:
            quality = concept_results['quality_metrics']
            key_metrics['Semantic Consistency'] = quality.get('semantic_consistency', 0)
            if 'metrics' in quality:
                key_metrics['PSNR'] = quality['metrics'].get('psnr', 0)
                key_metrics['SSIM'] = quality['metrics'].get('ssim', 0)
        
        # 空间控制关键指标
        if 'boundary_precision_results' in spatial_results:
            boundary = spatial_results['boundary_precision_results']
            key_metrics['Boundary Precision'] = boundary.get('sharpness_score', 0)
        
        if 'control_precision_results' in spatial_results:
            control = spatial_results['control_precision_results']
            key_metrics['Control Precision'] = control.get('precision_score', 0)
        
        # 系统性能关键指标
        if 'compute_performance' in performance_results:
            compute = performance_results['compute_performance']
            key_metrics['Inference Time (ms)'] = compute.get('inference_time_ms', 0)
            key_metrics['FPS'] = compute.get('fps', 0)
        
        if 'system_stability' in performance_results:
            stability = performance_results['system_stability']
            key_metrics['Stability Score'] = stability.get('stability_score', 0)
        
        # 创建仪表盘
        plt.figure(figsize=(12, 10))
        
        # 如果没有指标，显示提示信息
        if not key_metrics:
            plt.text(0.5, 0.5, "No key metrics available for dashboard", 
                   ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        # 对指标进行分类
        quality_metrics = {}
        performance_metrics = {}
        
        for name, value in key_metrics.items():
            if name in ['Inference Time (ms)', 'FPS', 'Memory Usage (GB)']:
                performance_metrics[name] = value
            else:
                quality_metrics[name] = value
        
        # 1. 质量指标
        plt.subplot(2, 1, 1)
        
        if quality_metrics:
            names = list(quality_metrics.keys())
            values = list(quality_metrics.values())
            
            # 标准化值以便在仪表盘上显示
            norm_values = []
            for name, value in zip(names, values):
                if name == 'PSNR':
                    # PSNR通常在20-40之间
                    norm_values.append(min(1.0, max(0.0, (value - 20) / 20)))
                else:
                    # 其他指标假定在0-1之间
                    norm_values.append(min(1.0, max(0.0, value)))
            
            plt.bar(names, norm_values, color='skyblue')
            plt.title('Quality Metrics')
            plt.ylim([0, 1.1])
            
            # 添加原始值标签
            for i, (v, orig) in enumerate(zip(norm_values, values)):
                if names[i] == 'PSNR':
                    plt.text(i, v + 0.05, f'{orig:.1f}dB', ha='center')
                else:
                    plt.text(i, v + 0.05, f'{orig:.2f}', ha='center')
        else:
            plt.text(0.5, 0.5, "Quality metrics not available", 
                   ha='center', va='center')
            plt.axis('off')
        
        # 2. 性能指标
        plt.subplot(2, 1, 2)
        
        if performance_metrics:
            names = list(performance_metrics.keys())
            values = list(performance_metrics.values())
            
            plt.bar(names, values, color='salmon')
            plt.title('Performance Metrics')
            
            # 添加值标签
            for i, v in enumerate(values):
                plt.text(i, v + max(values) * 0.05, f'{v:.2f}', ha='center')
        else:
            plt.text(0.5, 0.5, "Performance metrics not available", 
                   ha='center', va='center')
            plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    def _create_comparison_matrix(self, concept_results: Dict, output_path: str):
        """创建比较矩阵"""
        # 检查是否有消融实验结果
        if 'ablation_results' not in concept_results or not concept_results['ablation_results'].get('ablation_modes'):
            # 创建一个示例图像
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, "No comparison data available for matrix", 
                    ha='center', va='center', fontsize=14)
            plt.axis('off')
            plt.savefig(output_path)
            plt.close()
            return
        
        ablation_results = concept_results['ablation_results']
        
        # 创建比较矩阵
        plt.figure(figsize=(12, 8))
        
        # 提取模式和指标
        modes = [mode['name'] for mode in ablation_results['ablation_modes']]
        
        if 'metrics' in ablation_results:
            metrics = ablation_results['metrics']
            
            # 创建热力图数据
            metric_names = ['psnr', 'ssim', 'lpips']
            data = np.zeros((len(modes), len(metric_names)))
            
            for i, mode in enumerate(modes):
                if mode in metrics:
                    for j, metric in enumerate(metric_names):
                        if metric in metrics[mode]:
                            # 对LPIPS取反，因为越低越好
                            if metric == 'lpips':
                                data[i, j] = 1.0 - metrics[mode][metric]
                            # 归一化PSNR (假设在20-40之间)
                            elif metric == 'psnr':
                                data[i, j] = min(1.0, max(0.0, (metrics[mode][metric] - 20) / 20))
                            else:
                                data[i, j] = metrics[mode][metric]
            
            # 创建热力图
            plt.imshow(data, cmap='viridis')
            plt.colorbar(label='Normalized Score')
            
            # 添加标签
            plt.xticks(np.arange(len(metric_names)), metric_names)
            plt.yticks(np.arange(len(modes)), modes)
            
            # 添加数值标签
            for i in range(len(modes)):
                for j in range(len(metric_names)):
                    # 显示原始值
                    if j == 0:  # PSNR
                        orig_value = metrics[modes[i]].get('psnr', 0)
                        plt.text(j, i, f'{orig_value:.1f}', ha='center', va='center',
                               color='w' if data[i, j] < 0.7 else 'black')
                    elif j == 1:  # SSIM
                        orig_value = metrics[modes[i]].get('ssim', 0)
                        plt.text(j, i, f'{orig_value:.2f}', ha='center', va='center',
                               color='w' if data[i, j] < 0.7 else 'black')
                    elif j == 2:  # LPIPS
                        orig_value = metrics[modes[i]].get('lpips', 0)
                        plt.text(j, i, f'{orig_value:.3f}', ha='center', va='center',
                               color='w' if data[i, j] < 0.7 else 'black')
            
            plt.title('Model Comparison Matrix')
            plt.tight_layout()
            
            # 添加改进百分比
            if 'improvement' in ablation_results:
                improvement = ablation_results['improvement']
                plt.figtext(0.5, 0.01, f"RCA Improvement: {improvement:.1%}", 
                          ha='center', fontsize=12)
        else:
            plt.text(0.5, 0.5, "Metrics data not available for matrix", 
                   ha='center', va='center')
            plt.axis('off')
        
        plt.savefig(output_path)
        plt.close()
    
    def _create_html_report(self, 
                          concept_results: Dict, 
                          spatial_results: Dict,
                          performance_results: Dict,
                          output_path: str):
        """创建HTML报告"""
        # 创建HTML内容
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>InFusion-Enhanced Validation Report</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                }}
                h1, h2, h3 {{
                    color: #333;
                }}
                .section {{
                    background-color: white;
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }}
                .metric {{
                    display: inline-block;
                    width: 200px;
                    margin: 10px;
                    padding: 10px;
                    background-color: #e9f5ff;
                    border-radius: 5px;
                    text-align: center;
                }}
                .metric-value {{
                    font-size: 24px;
                    font-weight: bold;
                    margin: 5px 0;
                }}
                .image-container {{
                    margin: 15px 0;
                    text-align: center;
                }}
                img {{
                    max-width: 100%;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    color: #777;
                    font-size: 12px;
                }}
            </style>
        </head>
        <body>
            <h1>InFusion-Enhanced Validation Report</h1>
            <p>Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <div class="section">
                <h2>1. Concept Separation Validation</h2>
        """
        
        # 概念分离部分
        if 'summary' in concept_results:
            summary = concept_results['summary']
            html_content += f"""
                <div class="metric">
                    <p>Concept Separation</p>
                    <p class="metric-value">{summary.get('concept_separation_score', 0):.2f}</p>
                </div>
                <div class="metric">
                    <p>Visual Quality</p>
                    <p class="metric-value">{summary.get('visual_quality', 0):.2f}</p>
                </div>
                <div class="metric">
                    <p>Overall Score</p>
                    <p class="metric-value">{summary.get('overall_score', 0):.2f}</p>
                </div>
            """
        else:
            html_content += "<p>No concept separation summary available.</p>"
        
        html_content += """
            </div>
            
            <div class="section">
                <h2>2. Spatial Control Validation</h2>
        """
        
        # 空间控制部分
        if 'summary' in spatial_results:
            summary = spatial_results['summary']
            html_content += f"""
                <div class="metric">
                    <p>Boundary Precision</p>
                    <p class="metric-value">{summary.get('boundary_precision', 0):.2f}</p>
                </div>
                <div class="metric">
                    <p>Control Precision</p>
                    <p class="metric-value">{summary.get('control_precision', 0):.2f}</p>
                </div>
                <div class="metric">
                    <p>Overall Score</p>
                    <p class="metric-value">{summary.get('overall_score', 0):.2f}</p>
                </div>
            """
        else:
            html_content += "<p>No spatial control summary available.</p>"
        
        html_content += """
            </div>
            
            <div class="section">
                <h2>3. System Performance Validation</h2>
        """
        
        # 系统性能部分
        if 'compute_performance' in performance_results:
            compute = performance_results['compute_performance']
            html_content += f"""
                <div class="metric">
                    <p>Inference Time</p>
                    <p class="metric-value">{compute.get('inference_time_ms', 0):.1f} ms</p>
                </div>
                <div class="metric">
                    <p>Throughput</p>
                    <p class="metric-value">{compute.get('fps', 0):.1f} FPS</p>
                </div>
                <div class="metric">
                    <p>Memory Usage</p>
                    <p class="metric-value">{compute.get('peak_memory_gb', 0):.2f} GB</p>
                </div>
            """
        else:
            html_content += "<p>No performance data available.</p>"
        
        # 添加图片引用
        html_content += """
            </div>
            
            <div class="section">
                <h2>4. Validation Visualizations</h2>
                <h3>Summary Report</h3>
                <div class="image-container">
                    <img src="../summary/validation_summary.png" alt="Validation Summary">
                </div>
                
                <h3>Concept Separation</h3>
                <div class="image-container">
                    <img src="../concept_separation/single_concept_test.png" alt="Single Concept Test">
                </div>
                
                <h3>Spatial Control</h3>
                <div class="image-container">
                    <img src="../spatial_control/weight_visualization.png" alt="Weight Visualization">
                </div>
                
                <h3>Performance</h3>
                <div class="image-container">
                    <img src="../performance/performance_summary.png" alt="Performance Summary">
                </div>
            </div>
            
            <div class="footer">
                <p>InFusion-Enhanced CISM+RCA Validation System</p>
            </div>
        </body>
        </html>
        """
        
        # 保存HTML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)