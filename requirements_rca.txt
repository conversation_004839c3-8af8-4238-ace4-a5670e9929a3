# RCA阶段3依赖包
# 安装命令: pip install -r requirements_rca.txt

# 继承CISM阶段2的所有依赖
-r requirements_cism.txt

# RCA特定的额外依赖

# 深度学习框架增强
torch>=2.0.0
torchvision>=0.15.0

# 神经网络组件
torch-geometric>=2.3.0  # 图神经网络（可选，用于空间关系建模）

# 数值计算和优化
scipy>=1.9.0
scikit-learn>=1.2.0

# 可视化增强
matplotlib>=3.6.0
seaborn>=0.12.0
plotly>=5.15.0

# 图像处理增强
opencv-python>=4.7.0
scikit-image>=0.20.0

# 性能分析和监控
psutil>=5.9.0
GPUtil>=1.4.0
memory-profiler>=0.60.0

# 数据处理
pandas>=1.5.0
h5py>=3.7.0

# 配置管理
hydra-core>=1.3.0  # 高级配置管理（可选）

# 开发和测试工具
pytest>=7.2.0
pytest-cov>=4.0.0
black>=22.0.0
flake8>=6.0.0
mypy>=1.0.0

# 文档生成
sphinx>=5.0.0
sphinx-rtd-theme>=1.2.0

# 实验管理（可选）
wandb>=0.15.0
mlflow>=2.5.0

# 并行计算（可选）
joblib>=1.2.0
multiprocessing-logging>=0.3.0
