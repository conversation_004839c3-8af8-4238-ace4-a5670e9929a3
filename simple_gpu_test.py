#!/usr/bin/env python3
"""
简单GPU测试
"""

import os
os.environ['CUDA_VISIBLE_DEVICES'] = '4'

print("开始简单GPU测试...")

try:
    print("1. 导入torch...")
    import torch
    print("   ✅ torch导入成功")
    
    print("2. 检查CUDA...")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"   GPU数量: {torch.cuda.device_count()}")
        print(f"   当前GPU: {torch.cuda.current_device()}")
        print(f"   GPU名称: {torch.cuda.get_device_name()}")
        
        print("3. 测试GPU计算...")
        x = torch.randn(10, 10).cuda()
        y = x + 1
        print(f"   ✅ GPU计算成功: {y.shape}")
    
    print("4. 测试路径...")
    import sys
    current_dir = os.path.dirname(os.path.abspath(__file__))
    gaussian_splatting_dir = os.path.join(current_dir, "gaussian_splatting")
    sys.path.insert(0, gaussian_splatting_dir)
    print(f"   添加路径: {gaussian_splatting_dir}")
    
    print("5. 测试scene导入...")
    from scene import Scene, GaussianModel
    print("   ✅ scene导入成功")
    
    print("✅ 所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("测试完成。")
