<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: type_vec3.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_033f5edb0915b828d2c46ed4804e5503.html">detail</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">type_vec3.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00180.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;</div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#include &quot;qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#if GLM_CONFIG_SWIZZLE == GLM_SWIZZLE_OPERATOR</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#       include &quot;_swizzle.hpp&quot;</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#elif GLM_CONFIG_SWIZZLE == GLM_SWIZZLE_FUNCTION</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#       include &quot;_swizzle_func.hpp&quot;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#include &lt;cstddef&gt;</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;{</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;        <span class="keyword">struct </span>vec&lt;3, T, Q&gt;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;        {</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;                <span class="comment">// -- Implementation detail --</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;                <span class="keyword">typedef</span> T value_type;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;                <span class="keyword">typedef</span> vec&lt;3, T, Q&gt; type;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;                <span class="keyword">typedef</span> vec&lt;3, bool, Q&gt; bool_type;</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;                <span class="comment">// -- Data --</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#               if GLM_SILENT_WARNINGS == GLM_ENABLE</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#                       if GLM_COMPILER &amp; GLM_COMPILER_GCC</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#                               pragma GCC diagnostic push</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#                               pragma GCC diagnostic ignored &quot;-Wpedantic&quot;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#                       elif GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#                               pragma clang diagnostic push</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#                               pragma clang diagnostic ignored &quot;-Wgnu-anonymous-struct&quot;</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#                               pragma clang diagnostic ignored &quot;-Wnested-anon-types&quot;</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#                       elif GLM_COMPILER &amp; GLM_COMPILER_VC</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#                               pragma warning(push)</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#                               pragma warning(disable: 4201)  // nonstandard extension used : nameless struct/union</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#                               if GLM_CONFIG_ALIGNED_GENTYPES == GLM_ENABLE</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#                                       pragma warning(disable: 4324)  // structure was padded due to alignment specifier</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#                               endif</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#                       endif</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#               endif</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#               if GLM_CONFIG_XYZW_ONLY</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;                        T x, y, z;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#               elif GLM_CONFIG_ANONYMOUS_STRUCT == GLM_ENABLE</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                        <span class="keyword">union</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;                        {</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;                                <span class="keyword">struct</span>{ T x, y, z; };</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;                                <span class="keyword">struct</span>{ T r, g, b; };</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;                                <span class="keyword">struct</span>{ T s, t, p; };</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                                <span class="keyword">typename</span> detail::storage&lt;3, T, detail::is_aligned&lt;Q&gt;::value&gt;::type data;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="preprocessor">#                               if GLM_CONFIG_SWIZZLE == GLM_SWIZZLE_OPERATOR</span></div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;                                        GLM_SWIZZLE3_2_MEMBERS(T, Q, x, y, z)</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;                                        GLM_SWIZZLE3_2_MEMBERS(T, Q, r, g, b)</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;                                        GLM_SWIZZLE3_2_MEMBERS(T, Q, s, t, p)</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;                                        GLM_SWIZZLE3_3_MEMBERS(T, Q, x, y, z)</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;                                        GLM_SWIZZLE3_3_MEMBERS(T, Q, r, g, b)</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;                                        GLM_SWIZZLE3_3_MEMBERS(T, Q, s, t, p)</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;                                        GLM_SWIZZLE3_4_MEMBERS(T, Q, x, y, z)</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;                                        GLM_SWIZZLE3_4_MEMBERS(T, Q, r, g, b)</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                                        GLM_SWIZZLE3_4_MEMBERS(T, Q, s, t, p)</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="preprocessor">#                               endif</span></div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;                        };</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="preprocessor">#               else</span></div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                        <span class="keyword">union </span>{ T x, r, s; };</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;                        <span class="keyword">union </span>{ T y, g, t; };</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                        <span class="keyword">union </span>{ T z, b, p; };</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;<span class="preprocessor">#                       if GLM_CONFIG_SWIZZLE == GLM_SWIZZLE_FUNCTION</span></div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                                GLM_SWIZZLE_GEN_VEC_FROM_VEC3(T, Q)</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="preprocessor">#                       endif//GLM_CONFIG_SWIZZLE</span></div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="preprocessor">#               endif//GLM_LANG</span></div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;<span class="preprocessor">#               if GLM_SILENT_WARNINGS == GLM_ENABLE</span></div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;<span class="preprocessor">#                       if GLM_COMPILER &amp; GLM_COMPILER_CLANG</span></div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="preprocessor">#                               pragma clang diagnostic pop</span></div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;<span class="preprocessor">#                       elif GLM_COMPILER &amp; GLM_COMPILER_GCC</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;<span class="preprocessor">#                               pragma GCC diagnostic pop</span></div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="preprocessor">#                       elif GLM_COMPILER &amp; GLM_COMPILER_VC</span></div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="preprocessor">#                               pragma warning(pop)</span></div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="preprocessor">#                       endif</span></div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;<span class="preprocessor">#               endif</span></div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;                <span class="comment">// -- Component accesses --</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;                <span class="keyword">typedef</span> length_t length_type;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;                GLM_FUNC_DECL <span class="keyword">static</span> GLM_CONSTEXPR length_type <a class="code" href="a00254.html#gab703732449be6c7199369b3f9a91ed38">length</a>(){<span class="keywordflow">return</span> 3;}</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR T &amp; operator[](length_type i);</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR T <span class="keyword">const</span>&amp; operator[](length_type i) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                <span class="comment">// -- Implicit basic constructors --</span></div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec() GLM_DEFAULT;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(vec const&amp; v) GLM_DEFAULT;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;                template&lt;qualifier P&gt;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(vec&lt;3, T, P&gt; const&amp; v);</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;                <span class="comment">// -- Explicit basic constructors --</span></div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR explicit vec(T scalar);</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(T a, T b, T c);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;                <span class="comment">// -- Conversion scalar constructors --</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;                template&lt;typename U, qualifier P&gt;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR explicit vec(vec&lt;1, U, P&gt; const&amp; v);</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;                template&lt;typename X, typename Y, typename Z&gt;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(X x, Y y, Z z);</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;                template&lt;typename X, typename Y, typename Z&gt;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(vec&lt;1, X, Q&gt; const&amp; _x, Y _y, Z _z);</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                template&lt;typename X, typename Y, typename Z&gt;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(X _x, vec&lt;1, Y, Q&gt; const&amp; _y, Z _z);</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;                template&lt;typename X, typename Y, typename Z&gt;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(vec&lt;1, X, Q&gt; const&amp; _x, vec&lt;1, Y, Q&gt; const&amp; _y, Z _z);</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;                template&lt;typename X, typename Y, typename Z&gt;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(X _x, Y _y, vec&lt;1, Z, Q&gt; const&amp; _z);</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;                template&lt;typename X, typename Y, typename Z&gt;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(vec&lt;1, X, Q&gt; const&amp; _x, Y _y, vec&lt;1, Z, Q&gt; const&amp; _z);</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;                template&lt;typename X, typename Y, typename Z&gt;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(X _x, vec&lt;1, Y, Q&gt; const&amp; _y, vec&lt;1, Z, Q&gt; const&amp; _z);</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;                template&lt;typename X, typename Y, typename Z&gt;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(vec&lt;1, X, Q&gt; const&amp; _x, vec&lt;1, Y, Q&gt; const&amp; _y, vec&lt;1, Z, Q&gt; const&amp; _z);</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;                <span class="comment">// -- Conversion vector constructors --</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;                template&lt;typename A, typename B, qualifier P&gt;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(vec&lt;2, A, P&gt; const&amp; _xy, B _z);</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;                template&lt;typename A, typename B, qualifier P&gt;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(vec&lt;2, A, P&gt; const&amp; _xy, vec&lt;1, B, P&gt; const&amp; _z);</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;                template&lt;typename A, typename B, qualifier P&gt;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(A _x, vec&lt;2, B, P&gt; const&amp; _yz);</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;                template&lt;typename A, typename B, qualifier P&gt;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec(vec&lt;1, A, P&gt; const&amp; _x, vec&lt;2, B, P&gt; const&amp; _yz);</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;                template&lt;typename U, qualifier P&gt;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR GLM_EXPLICIT vec(vec&lt;4, U, P&gt; const&amp; v);</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;                template&lt;typename U, qualifier P&gt;</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR GLM_EXPLICIT vec(vec&lt;3, U, P&gt; const&amp; v);</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;                <span class="comment">// -- Swizzle constructors --</span></div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;<span class="preprocessor">#               if GLM_CONFIG_SWIZZLE == GLM_SWIZZLE_OPERATOR</span></div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;                        <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1, <span class="keywordtype">int</span> E2&gt;</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;                        GLM_FUNC_DECL GLM_CONSTEXPR vec(detail::_swizzle&lt;3, T, Q, E0, E1, E2, -1&gt; <span class="keyword">const</span>&amp; that)</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;                        {</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;                                *<span class="keyword">this</span> = that();</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;                        }</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;                        <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1&gt;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;                        GLM_FUNC_DECL GLM_CONSTEXPR vec(detail::_swizzle&lt;2, T, Q, E0, E1, -1, -2&gt; <span class="keyword">const</span>&amp; v, T <span class="keyword">const</span>&amp; scalar)</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;                        {</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;                                *<span class="keyword">this</span> = vec(v(), scalar);</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;                        }</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;                        <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> E0, <span class="keywordtype">int</span> E1&gt;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;                        GLM_FUNC_DECL GLM_CONSTEXPR vec(T <span class="keyword">const</span>&amp; scalar, detail::_swizzle&lt;2, T, Q, E0, E1, -1, -2&gt; <span class="keyword">const</span>&amp; v)</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;                        {</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;                                *<span class="keyword">this</span> = vec(scalar, v());</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;                        }</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;<span class="preprocessor">#               endif//GLM_CONFIG_SWIZZLE == GLM_SWIZZLE_OPERATOR</span></div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;                <span class="comment">// -- Unary arithmetic operators --</span></div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt;&amp; operator=(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v) GLM_DEFAULT;</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator+=(U scalar);</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator+=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator+=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator-=(U scalar);</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator-=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator-=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator*=(U scalar);</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator*=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator*=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator/=(U scalar);</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator/=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator/=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;                <span class="comment">// -- Increment and decrement operators --</span></div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator++();</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator--();</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator++(<span class="keywordtype">int</span>);</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator--(<span class="keywordtype">int</span>);</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;                <span class="comment">// -- Unary bit operators --</span></div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator%=(U scalar);</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator%=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator%=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator&amp;=(U scalar);</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator&amp;=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator&amp;=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator|=(U scalar);</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator|=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator|=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator^=(U scalar);</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator^=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator^=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator&lt;&lt;=(U scalar);</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator&lt;&lt;=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator&lt;&lt;=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator&gt;&gt;=(U scalar);</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator&gt;&gt;=(vec&lt;1, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;                <span class="keyword">template</span>&lt;<span class="keyword">typename</span> U&gt;</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; &amp; operator&gt;&gt;=(vec&lt;3, U, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;        };</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;        <span class="comment">// -- Unary operators --</span></div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator+(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator-(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;        <span class="comment">// -- Binary operators --</span></div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator+(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, T scalar);</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator+(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; scalar);</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator+(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator+(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator+(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator-(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, T scalar);</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator-(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator-(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator-(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator-(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator*(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, T scalar);</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator*(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator*(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator*(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator*(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator/(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, T scalar);</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator/(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator/(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator/(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator/(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator%(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, T scalar);</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator%(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator%(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator%(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator%(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&amp;(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, T scalar);</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&amp;(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&amp;(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;</div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&amp;(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&amp;(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator|(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, T scalar);</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator|(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator|(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator|(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator|(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator^(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, T scalar);</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;</div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator^(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator^(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator^(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;</div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator^(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&lt;&lt;(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, T scalar);</div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&lt;&lt;(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&lt;&lt;(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;</div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&lt;&lt;(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;</div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&lt;&lt;(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;</div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&gt;&gt;(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v, T scalar);</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;</div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&gt;&gt;(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&gt;&gt;(T scalar, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;</div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&gt;&gt;(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator&gt;&gt;(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, T, Q&gt; operator~(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;        <span class="comment">// -- Boolean operators --</span></div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR <span class="keywordtype">bool</span> operator==(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR <span class="keywordtype">bool</span> operator!=(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;        <span class="keyword">template</span>&lt;qualifier Q&gt;</div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, bool, Q&gt; operator&amp;&amp;(vec&lt;3, bool, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, bool, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;</div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;        <span class="keyword">template</span>&lt;qualifier Q&gt;</div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;3, bool, Q&gt; operator||(vec&lt;3, bool, Q&gt; <span class="keyword">const</span>&amp; v1, vec&lt;3, bool, Q&gt; <span class="keyword">const</span>&amp; v2);</div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;</div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;<span class="preprocessor">#ifndef GLM_EXTERNAL_TEMPLATE</span></div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;<span class="preprocessor">#include &quot;type_vec3.inl&quot;</span></div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;<span class="preprocessor">#endif//GLM_EXTERNAL_TEMPLATE</span></div>
<div class="ttc" id="a00254_html_gab703732449be6c7199369b3f9a91ed38"><div class="ttname"><a href="a00254.html#gab703732449be6c7199369b3f9a91ed38">glm::length</a></div><div class="ttdeci">GLM_FUNC_DECL T length(qua&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Returns the norm of a quaternions. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
