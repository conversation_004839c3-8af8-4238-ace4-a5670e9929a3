# 阶段3操作指南: RCA区域控制系统

**模型**: <PERSON>net 4 by Anthropic  
**开发者**: Augment Agent developed by Augment Code

## 🎯 阶段3操作目标
基于阶段2的CISM增强高斯点云，实现RCA (Region-aware Color Adjustment) 区域控制系统，提供空间精准的概念权重控制。

## 📋 前置条件检查

### 必需文件确认
```bash
# 检查阶段2输出
ls stage2_results/training_output/cism_enhanced_gaussians.ply
ls stage2_results/training_output/logs/training_stats.json

# 检查阶段1基础数据
ls stage1_results/concept_gaussians/gaussians_with_concept_id.ply
ls stage1_results/concept_masks/

# 检查原始数据
ls data/garden/images/
ls output/garden/c2w/  # 相机外参
ls output/garden/intri/  # 相机内参
```

### 环境准备
```bash
# 安装RCA依赖
pip install -r stage3_results/requirements_rca.txt

# 检查GPU和内存
python -c "
import torch
print(f'CUDA available: {torch.cuda.is_available()}')
print(f'GPU memory: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB')
print(f'GPU count: {torch.cuda.device_count()}')
"
```

## 🚀 阶段3执行步骤

### 步骤1: RCA配置准备
```bash
# 复制配置文件
cp stage3_results/rca_training_config.yaml ./

# 根据需要调整配置
vim rca_training_config.yaml
# 重点关注:
# - spatial_attention.hidden_dim: 网络复杂度
# - training.rca_interval: RCA应用频率
# - spatial_modulation.smoothing_sigma: 边界平滑度
```

### 步骤2: RCA网络训练
```bash
# 启动RCA训练
python stage3_results/train_rca.py \
    --config rca_training_config.yaml \
    --source_path data/garden \
    --model_path stage2_results/training_output/cism_enhanced_gaussians.ply \
    --output_dir stage3_results/training_output \
    --concept_masks_dir stage1_results/concept_masks/

# 监控训练进度
tail -f stage3_results/training_output/logs/rca_training.log
```

### 步骤3: 权重渲染测试
```bash
# 测试概念权重渲染
python stage3_results/rca_core/test_weight_rendering.py \
    --model_path stage3_results/training_output/rca_model_checkpoint.pth \
    --gaussians_path stage2_results/training_output/cism_enhanced_gaussians.ply \
    --cameras_dir output/garden/c2w/ \
    --output_dir stage3_results/weight_rendering_test/

# 检查权重图质量
ls stage3_results/weight_rendering_test/weight_heatmaps/
```

### 步骤4: 空间调制验证
```bash
# 验证空间调制效果
python stage3_results/rca_core/test_spatial_modulation.py \
    --rca_model stage3_results/training_output/rca_model_checkpoint.pth \
    --cism_guidance_samples stage2_results/training_output/guidance_samples/ \
    --output_dir stage3_results/spatial_modulation_test/
```

### 步骤5: RCA评估
```bash
# 完整RCA系统评估
python stage3_results/evaluate_rca.py \
    --model_path stage3_results/training_output/rca_enhanced_gaussians.ply \
    --source_path data/garden \
    --config rca_training_config.yaml \
    --output_dir stage3_results/evaluation_output
```

## 📊 预期输出结果

### 训练输出
```
stage3_results/training_output/
├── rca_enhanced_gaussians.ply       # RCA增强的高斯模型
├── rca_model_checkpoint.pth         # RCA网络权重
├── spatial_attention_model.pth      # 空间注意力网络
├── checkpoints/                     # 训练检查点
│   ├── iteration_2000.pth
│   ├── iteration_5000.pth
│   └── iteration_8000.pth
├── logs/                           # 训练日志
│   ├── rca_training.log
│   ├── loss_curves.png
│   └── weight_evolution.mp4
├── weight_visualizations/          # 权重可视化
│   ├── concept_0_weights/
│   ├── concept_1_weights/
│   ├── concept_2_weights/
│   └── combined_weights/
└── spatial_analysis/               # 空间分析结果
    ├── boundary_quality_maps/
    ├── spatial_consistency_metrics.json
    └── concept_separation_analysis.json
```

### 评估输出
```
stage3_results/evaluation_output/
├── rca_evaluation_report.json      # RCA评估报告
├── spatial_control_metrics.json    # 空间控制指标
├── weight_accuracy_analysis/       # 权重准确性分析
├── boundary_quality_assessment/    # 边界质量评估
├── comparison_with_baseline/       # 与基线对比
└── ablation_study_results/         # 消融研究结果
```

## 🎛️ 关键参数调整

### RCA网络参数
```yaml
# rca_training_config.yaml 核心参数
spatial_attention:
  input_dim: 13              # 输入特征维度 (位置3+颜色3+透明度1+尺度3+concept_id_onehot3)
  hidden_dim: 256            # 隐藏层维度 (128-512)
  dropout_rate: 0.1          # Dropout率 (0.0-0.3)
  use_residual: true         # 是否使用残差连接
  learning_rate: 0.001       # 学习率 (0.0001-0.01)
  weight_decay: 0.0001       # 权重衰减
  max_grad_norm: 1.0         # 梯度裁剪
```

### 训练调度参数
```yaml
training:
  rca_start_iter: 2000       # RCA开始迭代 (建议在CISM稳定后)
  rca_end_iter: 8000         # RCA结束迭代
  rca_interval: 5            # RCA应用间隔 (3-10)
  supervision_weight: 0.5    # 监督损失权重 (0.1-1.0)
  spatial_consistency_weight: 0.1  # 空间一致性权重
```

### 空间调制参数
```yaml
spatial_modulation:
  enable_boundary_smoothing: true    # 启用边界平滑
  smoothing_kernel_size: 5           # 平滑核大小 (3-7)
  smoothing_sigma: 1.0               # 高斯平滑标准差
  modulation_strength: 1.0           # 调制强度 (0.5-2.0)
```

## 🔧 故障排除

### 常见问题解决

#### 1. RCA网络不收敛
```bash
# 解决方案1: 调整学习率
learning_rate: 0.0005  # 降低学习率

# 解决方案2: 增加监督权重
supervision_weight: 0.8  # 提高监督信号

# 解决方案3: 检查输入特征
python stage3_results/rca_core/debug_input_features.py \
    --model_path stage2_results/training_output/cism_enhanced_gaussians.ply
```

#### 2. 权重渲染质量差
```bash
# 检查相机参数
python stage3_results/rca_core/validate_camera_params.py \
    --cameras_dir output/garden/c2w/ \
    --intrinsics_dir output/garden/intri/

# 调整渲染参数
weight_renderer:
  enable_sh: false           # 禁用球谐函数
  background_color: [0, 0, 0]  # 黑色背景
  render_resolution: 512     # 渲染分辨率
```

#### 3. 空间一致性差
```bash
# 增强空间约束
spatial_consistency_weight: 0.2  # 提高空间一致性权重

# 调整邻域半径
neighbor_radius: 0.1  # 空间邻域半径

# 启用更强的平滑
smoothing_sigma: 1.5  # 增加平滑强度
```

#### 4. 概念串扰严重
```bash
# 增强概念分离
concept_separation_weight: 0.1  # 添加概念分离损失

# 调整熵正则化
entropy_regularization: 0.02  # 增加熵正则化

# 检查concept_id质量
python stage1_results/validate_concept_id.py \
    --model_path stage1_results/concept_gaussians/gaussians_with_concept_id.ply
```

## 📈 质量评估指标

### RCA网络质量
```bash
# 检查网络收敛
python -c "
import torch
checkpoint = torch.load('stage3_results/training_output/rca_model_checkpoint.pth')
print(f'Final training loss: {checkpoint[\"final_loss\"]:.4f}')
print(f'Spatial consistency: {checkpoint[\"spatial_consistency\"]:.3f}')
print(f'Concept accuracy: {checkpoint[\"concept_accuracy\"]:.3f}')
"
```

### 空间控制精度
```bash
# 评估空间控制精度
python stage3_results/rca_core/evaluate_spatial_precision.py \
    --model_path stage3_results/training_output/rca_enhanced_gaussians.ply \
    --ground_truth_masks stage1_results/concept_masks/ \
    --output_metrics stage3_results/spatial_precision_metrics.json
```

### 边界质量评估
```bash
# 分析边界质量
python stage3_results/rca_core/analyze_boundary_quality.py \
    --weight_maps stage3_results/training_output/weight_visualizations/ \
    --output_analysis stage3_results/boundary_quality_analysis.json
```

## 🎯 成功标准

### 技术指标
- **空间一致性**: > 0.85
- **概念分离度**: > 0.80
- **权重渲染质量**: 无明显伪影
- **边界平滑度**: 平滑过渡，无硬边界

### 视觉指标
- **权重图质量**: 清晰的概念区域划分
- **空间连续性**: 邻域权重连续变化
- **边界处理**: 自然的概念边界过渡

## 🔗 下一步操作

### 准备阶段4
```bash
# 确认阶段3输出质量
python stage3_results/evaluate_rca.py --quick_check

# 准备阶段4输入
cp stage3_results/training_output/rca_enhanced_gaussians.ply stage4_input/
cp stage3_results/training_output/rca_model_checkpoint.pth stage4_input/
cp rca_training_config.yaml stage4_input/

# 验证RCA-CISM集成准备
python stage3_results/rca_core/test_cism_integration.py \
    --rca_model stage3_results/training_output/rca_model_checkpoint.pth \
    --cism_config stage2_results/cism_training_config.yaml
```

---

**操作完成**: Claude Sonnet 4 by Anthropic  
**下一阶段**: 准备进入阶段4 联合优化训练系统
