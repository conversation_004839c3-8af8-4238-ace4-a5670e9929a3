"""
RCA Core: 概念权重渲染器
阶段3.2: 将3D逐点概念权重渲染为2D权重图，用于空间化CISM引导

关键技术点：
- 扩展3DGS光栅化支持多通道权重渲染
- 保证权重渲染过程完全可微
- 确保2D权重图的空间连续性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
import logging

try:
    from gaussian_splatting.gaussian_renderer import GaussianRasterizationSettings, GaussianRasterizer
    from gaussian_splatting.utils.sh_utils import eval_sh
    GAUSSIAN_RENDERER_AVAILABLE = True
except ImportError:
    GAUSSIAN_RENDERER_AVAILABLE = False
    logging.warning("Gaussian renderer not available")

class ConceptWeightRenderer:
    """
    概念权重渲染器
    
    功能：
    1. 扩展3DGS光栅化支持多通道权重渲染
    2. 将3D概念权重渲染为2D权重图
    3. 保证渲染过程完全可微
    4. 提供权重归一化和平滑处理
    """
    
    def __init__(
        self,
        device: str = "cuda",
        enable_sh: bool = False,
        background_color: Optional[torch.Tensor] = None
    ):
        """
        初始化概念权重渲染器
        
        Args:
            device: 计算设备
            enable_sh: 是否启用球谐函数
            background_color: 背景颜色
        """
        self.device = device
        self.enable_sh = enable_sh
        
        if background_color is None:
            self.background_color = torch.tensor([0.0, 0.0, 0.0], dtype=torch.float32, device=device)
        else:
            self.background_color = background_color.to(device)
        
        if not GAUSSIAN_RENDERER_AVAILABLE:
            logging.warning("Gaussian renderer not available, using fallback implementation")
        
        logging.info("ConceptWeightRenderer initialized")
    
    def render_concept_weights(
        self,
        viewpoint_camera,
        pc,
        pipe,
        concept_weights: torch.Tensor,
        bg_color: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """
        渲染概念权重图
        
        Args:
            viewpoint_camera: 视点相机
            pc: 高斯点云
            pipe: 渲染管线参数
            concept_weights: 3D概念权重 [N, num_concepts]
            bg_color: 背景颜色
            
        Returns:
            render_results: 渲染结果字典
        """
        if bg_color is None:
            bg_color = self.background_color
        
        if not GAUSSIAN_RENDERER_AVAILABLE:
            return self._fallback_render(viewpoint_camera, pc, concept_weights, bg_color)
        
        # 设置光栅化参数
        tanfovx = torch.tan(viewpoint_camera.FoVx * 0.5)
        tanfovy = torch.tan(viewpoint_camera.FoVy * 0.5)
        
        raster_settings = GaussianRasterizationSettings(
            image_height=int(viewpoint_camera.image_height),
            image_width=int(viewpoint_camera.image_width),
            tanfovx=tanfovx,
            tanfovy=tanfovy,
            bg=bg_color,
            scale_modifier=pipe.scale_modifier,
            viewmatrix=viewpoint_camera.world_view_transform,
            projmatrix=viewpoint_camera.full_proj_transform,
            sh_degree=pc.active_sh_degree if self.enable_sh else 0,
            campos=viewpoint_camera.camera_center,
            prefiltered=False,
            debug=pipe.debug
        )
        
        rasterizer = GaussianRasterizer(raster_settings=raster_settings)
        
        # 获取3D高斯参数
        means3D = pc.get_xyz
        means2D = torch.zeros_like(means3D, dtype=torch.float32, device=self.device, requires_grad=True)
        
        try:
            means2D.retain_grad()
        except:
            pass
        
        # 获取其他参数
        opacity = pc.get_opacity
        scales = pc.get_scaling
        rotations = pc.get_rotation
        
        # 计算协方差
        cov3D_precomp = None
        
        # 使用概念权重作为颜色进行渲染
        num_concepts = concept_weights.shape[1]
        rendered_weights_list = []
        
        for concept_id in range(num_concepts):
            # 获取该概念的权重
            concept_weight = concept_weights[:, concept_id:concept_id+1]  # [N, 1]
            
            # 将权重扩展为RGB格式（所有通道使用相同权重）
            colors_precomp = concept_weight.repeat(1, 3)  # [N, 3]
            
            # 渲染该概念的权重图
            rendered_weight, radii = rasterizer(
                means3D=means3D,
                means2D=means2D,
                shs=None,
                colors_precomp=colors_precomp,
                opacities=opacity,
                scales=scales,
                rotations=rotations,
                cov3D_precomp=cov3D_precomp
            )
            
            # 只取第一个通道（因为三个通道都是相同的）
            rendered_weights_list.append(rendered_weight[0:1])  # [1, H, W]
        
        # 拼接所有概念的权重图
        rendered_weights = torch.cat(rendered_weights_list, dim=0)  # [num_concepts, H, W]
        rendered_weights = rendered_weights.permute(1, 2, 0)  # [H, W, num_concepts]
        
        # 归一化权重图
        normalized_weights = self.normalize_weight_maps(rendered_weights)
        
        render_results = {
            'rendered_weights': rendered_weights,
            'normalized_weights': normalized_weights,
            'radii': radii,
            'means2D': means2D
        }
        
        return render_results
    
    def _fallback_render(
        self,
        viewpoint_camera,
        pc,
        concept_weights: torch.Tensor,
        bg_color: torch.Tensor
    ) -> Dict[str, torch.Tensor]:
        """
        备用渲染实现（当Gaussian渲染器不可用时）
        
        Args:
            viewpoint_camera: 视点相机
            pc: 高斯点云
            concept_weights: 3D概念权重
            bg_color: 背景颜色
            
        Returns:
            render_results: 渲染结果字典
        """
        # 简单的投影渲染实现
        height = int(viewpoint_camera.image_height)
        width = int(viewpoint_camera.image_width)
        num_concepts = concept_weights.shape[1]
        
        # 创建空的权重图
        rendered_weights = torch.zeros((height, width, num_concepts), device=self.device)
        
        # 获取3D点
        xyz = pc.get_xyz  # [N, 3]
        
        # 简单的透视投影
        # 这里使用简化的投影，实际应用中需要使用正确的相机参数
        projected_x = (xyz[:, 0] / (xyz[:, 2] + 1e-6) + 1) * width * 0.5
        projected_y = (xyz[:, 1] / (xyz[:, 2] + 1e-6) + 1) * height * 0.5
        
        # 将投影坐标限制在图像范围内
        projected_x = torch.clamp(projected_x, 0, width - 1).long()
        projected_y = torch.clamp(projected_y, 0, height - 1).long()
        
        # 将权重分配到对应像素
        for i in range(xyz.shape[0]):
            x, y = projected_x[i], projected_y[i]
            if 0 <= x < width and 0 <= y < height:
                rendered_weights[y, x] += concept_weights[i]
        
        # 归一化
        normalized_weights = self.normalize_weight_maps(rendered_weights)
        
        render_results = {
            'rendered_weights': rendered_weights,
            'normalized_weights': normalized_weights,
            'radii': torch.zeros(xyz.shape[0], device=self.device),
            'means2D': torch.zeros_like(xyz[:, :2])
        }
        
        return render_results
    
    def normalize_weight_maps(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """
        归一化权重图，确保每像素权重和为1
        
        Args:
            weight_maps: 权重图 [H, W, num_concepts]
            
        Returns:
            normalized_weights: 归一化后的权重图
        """
        # 计算每像素的权重和
        weight_sum = torch.sum(weight_maps, dim=-1, keepdim=True)  # [H, W, 1]
        
        # 避免除零
        weight_sum = torch.clamp(weight_sum, min=1e-6)
        
        # 归一化
        normalized_weights = weight_maps / weight_sum
        
        return normalized_weights
    
    def smooth_weight_maps(
        self,
        weight_maps: torch.Tensor,
        kernel_size: int = 5,
        sigma: float = 1.0
    ) -> torch.Tensor:
        """
        平滑权重图，实现边界平滑处理
        
        Args:
            weight_maps: 权重图 [H, W, num_concepts]
            kernel_size: 高斯核大小
            sigma: 高斯核标准差
            
        Returns:
            smoothed_weights: 平滑后的权重图
        """
        # 创建高斯核
        gaussian_kernel = self._get_gaussian_kernel(kernel_size, sigma)
        gaussian_kernel = gaussian_kernel.to(weight_maps.device)
        
        # 转换维度用于卷积 [num_concepts, H, W]
        weight_maps_conv = weight_maps.permute(2, 0, 1).unsqueeze(0)  # [1, num_concepts, H, W]
        
        # 对每个概念通道分别进行卷积
        smoothed_weights_list = []
        for i in range(weight_maps.shape[2]):
            channel = weight_maps_conv[:, i:i+1, :, :]  # [1, 1, H, W]
            smoothed_channel = F.conv2d(
                channel,
                gaussian_kernel.unsqueeze(0).unsqueeze(0),  # [1, 1, kernel_size, kernel_size]
                padding=kernel_size // 2
            )
            smoothed_weights_list.append(smoothed_channel)
        
        # 拼接并转换回原始维度
        smoothed_weights_conv = torch.cat(smoothed_weights_list, dim=1)  # [1, num_concepts, H, W]
        smoothed_weights = smoothed_weights_conv.squeeze(0).permute(1, 2, 0)  # [H, W, num_concepts]
        
        # 重新归一化
        smoothed_weights = self.normalize_weight_maps(smoothed_weights)
        
        return smoothed_weights
    
    def _get_gaussian_kernel(self, kernel_size: int, sigma: float) -> torch.Tensor:
        """
        生成高斯卷积核
        
        Args:
            kernel_size: 核大小
            sigma: 标准差
            
        Returns:
            kernel: 高斯核
        """
        # 创建坐标网格
        coords = torch.arange(kernel_size, dtype=torch.float32)
        coords = coords - kernel_size // 2
        
        # 计算高斯权重
        gaussian_1d = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        gaussian_1d = gaussian_1d / gaussian_1d.sum()
        
        # 创建2D高斯核
        gaussian_2d = gaussian_1d[:, None] * gaussian_1d[None, :]
        
        return gaussian_2d
    
    def render_concept_masks(
        self,
        viewpoint_camera,
        pc,
        pipe,
        concept_weights: torch.Tensor,
        threshold: float = 0.5
    ) -> Dict[str, torch.Tensor]:
        """
        渲染概念掩码（二值化的权重图）
        
        Args:
            viewpoint_camera: 视点相机
            pc: 高斯点云
            pipe: 渲染管线参数
            concept_weights: 3D概念权重
            threshold: 二值化阈值
            
        Returns:
            mask_results: 掩码结果字典
        """
        # 首先渲染权重图
        weight_results = self.render_concept_weights(viewpoint_camera, pc, pipe, concept_weights)
        normalized_weights = weight_results['normalized_weights']
        
        # 二值化处理
        concept_masks = (normalized_weights > threshold).float()
        
        # 获取主导概念掩码
        dominant_concept = torch.argmax(normalized_weights, dim=-1)  # [H, W]
        
        mask_results = {
            'concept_masks': concept_masks,  # [H, W, num_concepts]
            'dominant_concept': dominant_concept,  # [H, W]
            'weight_maps': normalized_weights
        }
        
        return mask_results
    
    def compute_weight_consistency_loss(
        self,
        rendered_weights: torch.Tensor,
        target_weights: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        计算权重一致性损失
        
        Args:
            rendered_weights: 渲染的权重图 [H, W, num_concepts]
            target_weights: 目标权重图（可选）
            
        Returns:
            consistency_loss: 一致性损失
        """
        if target_weights is not None:
            # 与目标权重的L2损失
            consistency_loss = F.mse_loss(rendered_weights, target_weights)
        else:
            # 权重平滑性损失
            # 计算相邻像素权重的差异
            diff_x = torch.abs(rendered_weights[1:, :, :] - rendered_weights[:-1, :, :])
            diff_y = torch.abs(rendered_weights[:, 1:, :] - rendered_weights[:, :-1, :])
            
            consistency_loss = diff_x.mean() + diff_y.mean()
        
        return consistency_loss
