"""
RCA Core: 空间化CISM引导调制
阶段3.3: 使用RCA权重图调制CISM引导信号，实现精准空间控制

调制策略：
- 每个像素根据其概念权重选择对应的引导信号
- 边界区域实现多概念的平滑融合
- 避免不同概念间的突变和串扰
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
import logging

class SpatialModulation:
    """
    空间化CISM引导调制器
    
    功能：
    1. 使用RCA权重图调制CISM引导信号
    2. 实现精准的空间控制
    3. 处理多概念的平滑融合
    4. 避免概念间的突变和串扰
    """
    
    def __init__(
        self,
        device: str = "cuda",
        enable_boundary_smoothing: bool = True,
        smoothing_kernel_size: int = 5,
        smoothing_sigma: float = 1.0
    ):
        """
        初始化空间调制器
        
        Args:
            device: 计算设备
            enable_boundary_smoothing: 是否启用边界平滑
            smoothing_kernel_size: 平滑核大小
            smoothing_sigma: 平滑核标准差
        """
        self.device = device
        self.enable_boundary_smoothing = enable_boundary_smoothing
        self.smoothing_kernel_size = smoothing_kernel_size
        self.smoothing_sigma = smoothing_sigma
        
        # 预计算高斯核
        if enable_boundary_smoothing:
            self.gaussian_kernel = self._create_gaussian_kernel(
                smoothing_kernel_size, smoothing_sigma
            ).to(device)
        
        logging.info("SpatialModulation initialized")
    
    def spatially_modulated_guidance(
        self,
        delta_epsilons: Dict[int, torch.Tensor],
        weight_maps: torch.Tensor,
        modulation_strength: float = 1.0,
        use_adaptive_blending: bool = True
    ) -> torch.Tensor:
        """
        空间调制CISM引导信号
        
        Args:
            delta_epsilons: 概念引导差异字典 {concept_id: delta_epsilon [C, H, W]}
            weight_maps: 权重图 [H, W, num_concepts]
            modulation_strength: 调制强度
            use_adaptive_blending: 是否使用自适应混合
            
        Returns:
            modulated_guidance: 调制后的引导信号 [C, H, W]
        """
        # 获取图像尺寸和通道数
        first_delta = list(delta_epsilons.values())[0]
        C, H, W = first_delta.shape
        num_concepts = weight_maps.shape[2]
        
        # 初始化调制后的引导信号
        modulated_guidance = torch.zeros_like(first_delta)
        
        # 对每个概念进行空间调制
        for concept_id, delta_eps in delta_epsilons.items():
            if concept_id < num_concepts:
                # 获取该概念的权重图
                concept_weight = weight_maps[:, :, concept_id]  # [H, W]
                
                # 扩展权重图到通道维度
                concept_weight_expanded = concept_weight.unsqueeze(0).expand(C, -1, -1)  # [C, H, W]
                
                # 应用调制强度
                modulated_weight = concept_weight_expanded * modulation_strength
                
                # 空间调制
                modulated_eps = delta_eps * modulated_weight
                
                # 累加到总的引导信号
                modulated_guidance += modulated_eps
        
        # 自适应混合（可选）
        if use_adaptive_blending:
            modulated_guidance = self._adaptive_blending(
                modulated_guidance, weight_maps, delta_epsilons
            )
        
        # 边界平滑处理
        if self.enable_boundary_smoothing:
            modulated_guidance = self._smooth_boundaries(modulated_guidance)
        
        return modulated_guidance
    
    def _adaptive_blending(
        self,
        modulated_guidance: torch.Tensor,
        weight_maps: torch.Tensor,
        delta_epsilons: Dict[int, torch.Tensor]
    ) -> torch.Tensor:
        """
        自适应混合，根据权重分布调整混合策略
        
        Args:
            modulated_guidance: 当前调制的引导信号
            weight_maps: 权重图
            delta_epsilons: 原始引导差异
            
        Returns:
            blended_guidance: 自适应混合后的引导信号
        """
        # 计算权重熵，衡量权重分布的均匀性
        weight_entropy = self._compute_weight_entropy(weight_maps)  # [H, W]
        
        # 高熵区域（权重分布均匀）使用更强的混合
        # 低熵区域（权重集中）使用较弱的混合
        entropy_normalized = (weight_entropy - weight_entropy.min()) / (weight_entropy.max() - weight_entropy.min() + 1e-8)
        
        # 创建自适应混合权重
        adaptive_weight = 0.5 + 0.5 * entropy_normalized  # [H, W]
        adaptive_weight = adaptive_weight.unsqueeze(0).expand(modulated_guidance.shape[0], -1, -1)  # [C, H, W]
        
        # 计算全局平均引导信号
        global_guidance = torch.zeros_like(modulated_guidance)
        total_weight = 0
        
        for concept_id, delta_eps in delta_epsilons.items():
            concept_weight = weight_maps[:, :, concept_id].sum()
            global_guidance += delta_eps * concept_weight
            total_weight += concept_weight
        
        if total_weight > 0:
            global_guidance = global_guidance / total_weight
        
        # 自适应混合
        blended_guidance = (1 - adaptive_weight) * modulated_guidance + adaptive_weight * global_guidance
        
        return blended_guidance
    
    def _compute_weight_entropy(self, weight_maps: torch.Tensor) -> torch.Tensor:
        """
        计算权重分布的熵
        
        Args:
            weight_maps: 权重图 [H, W, num_concepts]
            
        Returns:
            entropy: 权重熵 [H, W]
        """
        # 避免log(0)
        weight_maps_safe = weight_maps + 1e-8
        
        # 计算熵
        entropy = -torch.sum(weight_maps_safe * torch.log(weight_maps_safe), dim=-1)
        
        return entropy
    
    def _smooth_boundaries(self, guidance: torch.Tensor) -> torch.Tensor:
        """
        边界平滑处理
        
        Args:
            guidance: 引导信号 [C, H, W]
            
        Returns:
            smoothed_guidance: 平滑后的引导信号
        """
        # 对每个通道分别进行高斯平滑
        smoothed_channels = []
        
        for c in range(guidance.shape[0]):
            channel = guidance[c:c+1].unsqueeze(0)  # [1, 1, H, W]
            
            smoothed_channel = F.conv2d(
                channel,
                self.gaussian_kernel.unsqueeze(0).unsqueeze(0),
                padding=self.smoothing_kernel_size // 2
            )
            
            smoothed_channels.append(smoothed_channel.squeeze(0))
        
        smoothed_guidance = torch.cat(smoothed_channels, dim=0)
        
        return smoothed_guidance
    
    def _create_gaussian_kernel(self, kernel_size: int, sigma: float) -> torch.Tensor:
        """
        创建高斯卷积核
        
        Args:
            kernel_size: 核大小
            sigma: 标准差
            
        Returns:
            kernel: 高斯核
        """
        # 创建坐标网格
        coords = torch.arange(kernel_size, dtype=torch.float32)
        coords = coords - kernel_size // 2
        
        # 计算高斯权重
        gaussian_1d = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        gaussian_1d = gaussian_1d / gaussian_1d.sum()
        
        # 创建2D高斯核
        gaussian_2d = gaussian_1d[:, None] * gaussian_1d[None, :]
        
        return gaussian_2d
    
    def compute_modulation_quality_metrics(
        self,
        original_guidance: torch.Tensor,
        modulated_guidance: torch.Tensor,
        weight_maps: torch.Tensor
    ) -> Dict[str, float]:
        """
        计算调制质量指标
        
        Args:
            original_guidance: 原始引导信号
            modulated_guidance: 调制后的引导信号
            weight_maps: 权重图
            
        Returns:
            metrics: 质量指标字典
        """
        with torch.no_grad():
            # 1. 信号保真度（与原始信号的相似性）
            fidelity = F.cosine_similarity(
                original_guidance.flatten(),
                modulated_guidance.flatten(),
                dim=0
            ).item()
            
            # 2. 空间一致性（相邻像素的平滑性）
            diff_x = torch.abs(modulated_guidance[:, 1:, :] - modulated_guidance[:, :-1, :])
            diff_y = torch.abs(modulated_guidance[:, :, 1:] - modulated_guidance[:, :, :-1])
            spatial_consistency = 1.0 / (1.0 + diff_x.mean().item() + diff_y.mean().item())
            
            # 3. 权重利用率（权重分布的有效性）
            weight_entropy = self._compute_weight_entropy(weight_maps)
            weight_utilization = weight_entropy.mean().item()
            
            # 4. 概念分离度（不同概念区域的区分度）
            concept_separation = self._compute_concept_separation(weight_maps)
            
            # 5. 边界质量（边界区域的平滑性）
            boundary_quality = self._compute_boundary_quality(weight_maps)
            
            metrics = {
                'fidelity': fidelity,
                'spatial_consistency': spatial_consistency,
                'weight_utilization': weight_utilization,
                'concept_separation': concept_separation,
                'boundary_quality': boundary_quality,
                'overall_quality': (fidelity + spatial_consistency + weight_utilization + 
                                  concept_separation + boundary_quality) / 5.0
            }
        
        return metrics
    
    def _compute_concept_separation(self, weight_maps: torch.Tensor) -> float:
        """
        计算概念分离度
        
        Args:
            weight_maps: 权重图 [H, W, num_concepts]
            
        Returns:
            separation: 概念分离度
        """
        # 计算主导概念
        dominant_concept = torch.argmax(weight_maps, dim=-1)  # [H, W]
        
        # 计算每个概念的纯度
        total_purity = 0.0
        num_concepts = weight_maps.shape[2]
        
        for concept_id in range(num_concepts):
            concept_mask = (dominant_concept == concept_id)
            if concept_mask.sum() > 0:
                concept_weights = weight_maps[concept_mask, concept_id]
                purity = concept_weights.mean().item()
                total_purity += purity
        
        separation = total_purity / num_concepts
        return separation
    
    def _compute_boundary_quality(self, weight_maps: torch.Tensor) -> float:
        """
        计算边界质量
        
        Args:
            weight_maps: 权重图 [H, W, num_concepts]
            
        Returns:
            boundary_quality: 边界质量
        """
        # 计算权重梯度
        grad_x = torch.abs(weight_maps[1:, :, :] - weight_maps[:-1, :, :])
        grad_y = torch.abs(weight_maps[:, 1:, :] - weight_maps[:, :-1, :])
        
        # 边界区域是梯度较大的区域
        gradient_magnitude = torch.sqrt(grad_x[:-1, :, :] ** 2 + grad_y[:, :-1, :] ** 2)
        
        # 计算边界区域的平滑性
        boundary_threshold = gradient_magnitude.mean() + gradient_magnitude.std()
        boundary_mask = gradient_magnitude > boundary_threshold
        
        if boundary_mask.sum() > 0:
            # 计算边界区域的局部方差（越小越平滑）
            boundary_variance = gradient_magnitude[boundary_mask].var().item()
            boundary_quality = 1.0 / (1.0 + boundary_variance)
        else:
            boundary_quality = 1.0
        
        return boundary_quality
    
    def visualize_modulation(
        self,
        delta_epsilons: Dict[int, torch.Tensor],
        weight_maps: torch.Tensor,
        modulated_guidance: torch.Tensor
    ) -> Dict[str, torch.Tensor]:
        """
        可视化调制过程
        
        Args:
            delta_epsilons: 原始引导差异
            weight_maps: 权重图
            modulated_guidance: 调制后的引导信号
            
        Returns:
            visualizations: 可视化结果字典
        """
        with torch.no_grad():
            # 1. 权重图可视化
            weight_vis = weight_maps / weight_maps.max()  # 归一化到[0,1]
            
            # 2. 主导概念图
            dominant_concept = torch.argmax(weight_maps, dim=-1).float()
            dominant_concept_vis = dominant_concept / (weight_maps.shape[2] - 1)
            
            # 3. 权重熵图
            entropy_map = self._compute_weight_entropy(weight_maps)
            entropy_vis = (entropy_map - entropy_map.min()) / (entropy_map.max() - entropy_map.min() + 1e-8)
            
            # 4. 调制强度图
            modulation_strength = torch.norm(modulated_guidance, dim=0)
            modulation_vis = (modulation_strength - modulation_strength.min()) / (modulation_strength.max() - modulation_strength.min() + 1e-8)
            
            visualizations = {
                'weight_maps': weight_vis,  # [H, W, num_concepts]
                'dominant_concept': dominant_concept_vis,  # [H, W]
                'entropy_map': entropy_vis,  # [H, W]
                'modulation_strength': modulation_vis  # [H, W]
            }
        
        return visualizations
