#!/usr/bin/env python3
"""
🔥 Concept PLY I/O 测试脚本

测试concept_id的PLY保存和加载功能
验证数据一致性和向后兼容性
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# 添加gaussian_splatting路径
sys.path.append('./gaussian_splatting')
from scene import GaussianModel
from utils.graphics_utils import BasicPointCloud

def test_concept_ply_io():
    """测试concept_id的PLY保存和加载功能"""
    
    print("🔥 开始测试Concept PLY I/O功能...")
    
    # 创建测试目录
    test_dir = Path("test_concept_ply")
    test_dir.mkdir(exist_ok=True)
    
    # 步骤1：创建带有concept_id的GaussianModel
    print("\n📝 步骤1：创建测试高斯模型")
    
    # 创建简单的点云数据
    num_points = 100
    points = np.random.randn(num_points, 3) * 2.0
    colors = np.random.rand(num_points, 3)
    
    pcd = BasicPointCloud(points=points, colors=colors, normals=np.zeros_like(points))
    
    # 创建高斯模型
    gaussians = GaussianModel(sh_degree=3)
    gaussians.create_from_pcd(pcd, spatial_lr_scale=1.0)
    
    # 手动设置一些concept_id进行测试
    test_concept_ids = torch.randint(0, 3, (num_points,), device="cuda")
    test_concept_confidences = torch.rand(num_points, device="cuda")
    
    gaussians._concept_id = test_concept_ids
    gaussians._concept_confidence = torch.nn.Parameter(test_concept_confidences.requires_grad_(True))
    
    print(f"   ✅ 创建高斯模型：{num_points} 个点")
    print(f"   📊 Concept分布：{torch.bincount(test_concept_ids)}")
    
    # 步骤2：保存PLY文件
    print("\n💾 步骤2：保存PLY文件")
    ply_path = test_dir / "test_concept_gaussians.ply"
    
    gaussians.save_ply(str(ply_path))
    print(f"   ✅ PLY文件已保存：{ply_path}")
    
    # 验证文件存在
    assert ply_path.exists(), "PLY文件保存失败"
    
    # 步骤3：加载PLY文件并验证数据一致性
    print("\n📂 步骤3：加载PLY文件并验证数据")
    
    # 创建新的高斯模型用于加载
    gaussians_loaded = GaussianModel(sh_degree=3)
    gaussians_loaded.load_ply(str(ply_path))
    
    print(f"   ✅ PLY文件已加载：{gaussians_loaded.get_xyz.shape[0]} 个点")
    
    # 验证数据一致性
    print("\n🔍 步骤4：验证数据一致性")
    
    # 验证concept_id
    concept_ids_match = torch.equal(gaussians._concept_id, gaussians_loaded._concept_id)
    print(f"   Concept IDs 匹配：{concept_ids_match}")
    
    # 验证concept_confidence（允许小的浮点误差）
    confidence_diff = torch.abs(gaussians._concept_confidence - gaussians_loaded._concept_confidence).max()
    confidence_match = confidence_diff < 1e-6
    print(f"   Concept Confidences 匹配：{confidence_match} (最大差异: {confidence_diff:.8f})")
    
    # 验证其他属性（xyz, opacity等）
    xyz_diff = torch.abs(gaussians.get_xyz - gaussians_loaded.get_xyz).max()
    xyz_match = xyz_diff < 1e-6
    print(f"   XYZ 匹配：{xyz_match} (最大差异: {xyz_diff:.8f})")
    
    # 步骤5：测试向后兼容性
    print("\n🔄 步骤5：测试向后兼容性")
    
    # 创建不包含concept信息的PLY文件（模拟原始3DGS文件）
    legacy_ply_path = test_dir / "legacy_gaussians.ply"
    
    # 手动创建legacy PLY（不包含concept属性）
    create_legacy_ply(gaussians, str(legacy_ply_path))
    
    # 尝试加载legacy PLY文件
    gaussians_legacy = GaussianModel(sh_degree=3)
    gaussians_legacy.load_ply(str(legacy_ply_path))
    
    print(f"   ✅ Legacy PLY文件加载成功：{gaussians_legacy.get_xyz.shape[0]} 个点")
    
    # 验证默认concept初始化
    default_concept_ids = (gaussians_legacy._concept_id == 0).all()
    default_confidences = torch.allclose(gaussians_legacy._concept_confidence, torch.tensor(0.1))
    
    print(f"   默认concept_id（全为0）：{default_concept_ids}")
    print(f"   默认confidence（全为0.1）：{default_confidences}")
    
    # 生成测试报告
    print("\n📋 测试报告")
    print("=" * 50)
    
    all_tests_passed = all([
        concept_ids_match,
        confidence_match,
        xyz_match,
        default_concept_ids,
        default_confidences
    ])
    
    if all_tests_passed:
        print("🎉 所有测试通过！Concept PLY I/O功能正常工作")
        print("   ✅ 数据保存/加载一致性：通过")
        print("   ✅ 向后兼容性：通过")
        print("   ✅ 默认初始化：通过")
    else:
        print("❌ 部分测试失败，需要检查实现")
    
    # 清理测试文件
    print(f"\n🧹 清理测试文件：{test_dir}")
    import shutil
    shutil.rmtree(test_dir)
    
    return all_tests_passed

def create_legacy_ply(gaussians, path):
    """创建不包含concept信息的legacy PLY文件"""
    from utils.system_utils import mkdir_p
    from plyfile import PlyData, PlyElement
    
    mkdir_p(os.path.dirname(path))
    
    xyz = gaussians._xyz.detach().cpu().numpy()
    normals = np.zeros_like(xyz)
    f_dc = gaussians._features_dc.detach().transpose(1, 2).flatten(start_dim=1).contiguous().cpu().numpy()
    f_rest = gaussians._features_rest.detach().transpose(1, 2).flatten(start_dim=1).contiguous().cpu().numpy()
    opacities = gaussians._opacity.detach().cpu().numpy()
    scale = gaussians._scaling.detach().cpu().numpy()
    rotation = gaussians._rotation.detach().cpu().numpy()
    
    # 创建legacy属性列表（不包含concept属性）
    l = ['x', 'y', 'z', 'nx', 'ny', 'nz']
    for i in range(gaussians._features_dc.shape[1]*gaussians._features_dc.shape[2]):
        l.append('f_dc_{}'.format(i))
    for i in range(gaussians._features_rest.shape[1]*gaussians._features_rest.shape[2]):
        l.append('f_rest_{}'.format(i))
    l.append('opacity')
    for i in range(gaussians._scaling.shape[1]):
        l.append('scale_{}'.format(i))
    for i in range(gaussians._rotation.shape[1]):
        l.append('rot_{}'.format(i))
    
    dtype_full = [(attribute, 'f4') for attribute in l]
    elements = np.empty(xyz.shape[0], dtype=dtype_full)
    
    # 拼接数据（不包含concept数据）
    attributes = np.concatenate((xyz, normals, f_dc, f_rest, opacities, scale, rotation), axis=1)
    elements[:] = list(map(tuple, attributes))
    el = PlyElement.describe(elements, 'vertex')
    PlyData([el]).write(path)

if __name__ == "__main__":
    test_concept_ply_io() 