#!/usr/bin/env python3
"""
阶段3简化测试脚本：RCA核心功能验证
"""

import torch
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_rca_data_preparator():
    """测试RCA数据准备器"""
    print("🧪 测试RCA数据准备器")
    
    try:
        from rca_core import RCADataPreparator
        
        # 创建数据准备器
        preparator = RCADataPreparator(debug_mode=True)
        print("✅ RCADataPreparator创建成功")
        
        # 创建模拟数据
        rendered_weights = torch.randn(3, 64, 64)
        rendered_weights = torch.softmax(rendered_weights, dim=0)
        
        concept_embeddings = {
            "bg": torch.randn(77, 768),
            "concept0": torch.randn(77, 768),
            "concept1": torch.randn(77, 768)
        }
        
        # 测试数据准备
        cross_attention_kwargs = preparator.prepare_rca_data(
            rendered_weights, concept_embeddings, batch_size=1, cfg_enabled=True
        )
        
        print("✅ RCA数据准备成功")
        print(f"📊 掩码数量: {len(cross_attention_kwargs['mask'])}")
        print(f"📊 文本嵌入数量: {len(cross_attention_kwargs['text_embeddings'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 阶段3简化测试：RCA核心功能验证")
    print("=" * 50)
    
    success = test_rca_data_preparator()
    
    if success:
        print("\n🏆 阶段3简化测试：通过！")
    else:
        print("\n❌ 阶段3简化测试：失败")
    
    return success

if __name__ == "__main__":
    main()
