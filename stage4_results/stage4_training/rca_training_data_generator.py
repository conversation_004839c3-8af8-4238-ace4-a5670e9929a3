# -*- coding: utf-8 -*-
"""
InFusion 阶段4: RCA训练数据生成器
构建RCA网络的训练数据集和监督信号
"""

import torch
import torch.nn as nn
import numpy as np
import os
import json
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import time
from sklearn.neighbors import NearestNeighbors
import cv2
from pathlib import Path

@dataclass
class TrainingDataConfig:
    """训练数据配置"""
    # 数据采样配置
    points_per_batch: int = 8192
    concept_balance_strategy: str = "balanced"  # "balanced", "weighted", "focal"
    hard_mining_ratio: float = 0.3
    
    # 数据增强配置
    geometric_augmentation: bool = True
    noise_augmentation: bool = True
    occlusion_augmentation: bool = True
    
    # 几何变换参数
    rotation_range: float = 0.1  # 弧度
    translation_range: float = 0.05  # 相对范围
    scale_range: float = 0.1
    
    # 噪声注入参数
    feature_noise_std: float = 0.01
    position_noise_std: float = 0.001
    
    # 遮挡模拟参数
    occlusion_ratio: float = 0.15
    occlusion_patch_size: int = 32
    
    # 邻域上下文配置
    knn_neighbors: int = 16
    context_radius: float = 0.1
    
    # 缓存配置
    enable_caching: bool = True
    cache_dir: str = "./cache/training_data"
    max_cache_size_gb: float = 4.0

class RCATrainingDataGenerator:
    """
    🔥 RCA网络训练数据生成器
    
    功能:
    - 从3D高斯场提取训练特征
    - 构建concept_id监督标签
    - 实现数据增强策略
    - 生成平衡的训练批次
    """
    
    def __init__(self, config: TrainingDataConfig = None):
        self.config = config or TrainingDataConfig()
        
        # 数据状态
        self.gaussian_data = None
        self.concept_ids = None
        self.camera_params = None
        
        # 缓存系统
        self.feature_cache = {}
        self.augmentation_cache = {}
        
        # 统计信息
        self.data_stats = {
            'total_points': 0,
            'concept_distribution': {},
            'feature_dimensions': {},
            'augmentation_count': 0,
            'hard_samples_mined': 0
        }
        
        # 邻域搜索器
        self.knn_searcher = None
        
        print(f"🎯 RCA训练数据生成器初始化完成")
        
        # 创建缓存目录
        if self.config.enable_caching:
            os.makedirs(self.config.cache_dir, exist_ok=True)
    
    def load_stage3_training_data(self, stage3_output_dir: str) -> bool:
        """
        🔥 加载阶段3的训练数据
        
        Args:
            stage3_output_dir: 阶段3输出目录
            
        Returns:
            bool: 加载是否成功
        """
        try:
            print("📂 加载阶段3训练数据...")
            
            # 1. 加载带concept_id的3D高斯数据
            gaussians_path = os.path.join(stage3_output_dir, "stage3_final_gaussians.ply")
            if not os.path.exists(gaussians_path):
                print(f"错误: 找不到高斯数据文件 {gaussians_path}")
                return False
            
            self.gaussian_data = self._load_gaussians_with_concept_id(gaussians_path)
            
            # 2. 加载相机参数
            camera_params_path = os.path.join(stage3_output_dir, "camera_params.pt")
            if os.path.exists(camera_params_path):
                self.camera_params = torch.load(camera_params_path)
            else:
                print("警告: 未找到相机参数文件，将使用默认设置")
            
            # 3. 提取concept_id标签
            if 'concept_ids' in self.gaussian_data:
                self.concept_ids = self.gaussian_data['concept_ids']
            else:
                print("错误: 高斯数据中缺少concept_id信息")
                return False
            
            # 4. 更新统计信息
            self._update_data_statistics()
            
            # 5. 初始化邻域搜索器
            self._initialize_knn_searcher()
            
            print(f"✅ 阶段3数据加载完成")
            print(f"   总点数: {self.data_stats['total_points']}")
            print(f"   概念分布: {self.data_stats['concept_distribution']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载阶段3数据失败: {str(e)}")
            return False
    
    def _load_gaussians_with_concept_id(self, ply_path: str) -> Dict[str, torch.Tensor]:
        """加载带concept_id的PLY文件"""
        # 这里需要实现PLY文件读取逻辑
        # 简化实现，实际需要解析PLY格式
        print(f"   加载PLY文件: {ply_path}")
        
        # 模拟数据结构 - 实际实现需要真实的PLY解析
        gaussian_data = {
            'xyz': torch.randn(50000, 3),  # 位置
            'rgb': torch.rand(50000, 3),   # 颜色
            'opacity': torch.rand(50000, 1),  # 透明度
            'scales': torch.rand(50000, 3),   # 尺度
            'rotations': torch.randn(50000, 4),  # 旋转
            'concept_ids': torch.randint(0, 3, (50000,))  # concept_id
        }
        
        return gaussian_data
    
    def _update_data_statistics(self):
        """更新数据统计信息"""
        self.data_stats['total_points'] = self.gaussian_data['xyz'].shape[0]
        
        # 概念分布统计
        concept_counts = torch.bincount(self.concept_ids, minlength=3)
        self.data_stats['concept_distribution'] = {
            f'concept_{i}': count.item() 
            for i, count in enumerate(concept_counts)
        }
        
        # 特征维度统计
        self.data_stats['feature_dimensions'] = {
            key: list(tensor.shape) 
            for key, tensor in self.gaussian_data.items()
        }
    
    def _initialize_knn_searcher(self):
        """初始化KNN邻域搜索器"""
        print("   初始化KNN邻域搜索器...")
        positions = self.gaussian_data['xyz'].cpu().numpy()
        self.knn_searcher = NearestNeighbors(
            n_neighbors=self.config.knn_neighbors + 1,  # +1因为包含自己
            algorithm='ball_tree'
        )
        self.knn_searcher.fit(positions)
        print(f"   ✅ KNN搜索器初始化完成 (K={self.config.knn_neighbors})")
    
    def generate_training_batch(self, 
                               batch_size: int = None,
                               concept_weights: Dict[int, float] = None) -> Dict[str, torch.Tensor]:
        """
        🔥 生成训练批次
        
        Args:
            batch_size: 批次大小
            concept_weights: 概念权重分布
            
        Returns:
            batch_data: 训练批次数据
        """
        batch_size = batch_size or self.config.points_per_batch
        
        # 1. 采样点索引
        sampled_indices = self._sample_balanced_indices(batch_size, concept_weights)
        
        # 2. 提取基础特征
        batch_features = self._extract_batch_features(sampled_indices)
        
        # 3. 计算邻域上下文特征
        batch_features = self._add_context_features(batch_features, sampled_indices)
        
        # 4. 应用数据增强
        if self.config.geometric_augmentation or self.config.noise_augmentation:
            batch_features = self._apply_data_augmentation(batch_features)
        
        # 5. 准备监督标签
        batch_labels = self._prepare_supervision_labels(sampled_indices)
        
        # 6. 组装批次数据
        batch_data = {
            'features': batch_features,
            'labels': batch_labels,
            'indices': sampled_indices,
            'batch_size': batch_size
        }
        
        return batch_data
    
    def _sample_balanced_indices(self, 
                                batch_size: int,
                                concept_weights: Dict[int, float] = None) -> torch.Tensor:
        """
        🔥 平衡采样点索引
        
        Args:
            batch_size: 批次大小
            concept_weights: 概念权重
            
        Returns:
            sampled_indices: 采样的点索引
        """
        total_points = self.data_stats['total_points']
        
        if self.config.concept_balance_strategy == "balanced":
            # 均衡采样：每个概念采样相同数量
            num_concepts = len(self.data_stats['concept_distribution'])
            points_per_concept = batch_size // num_concepts
            
            sampled_indices = []
            for concept_id in range(num_concepts):
                concept_mask = (self.concept_ids == concept_id)
                concept_indices = torch.where(concept_mask)[0]
                
                if len(concept_indices) > 0:
                    # 随机采样
                    sampled_count = min(points_per_concept, len(concept_indices))
                    selected = torch.randperm(len(concept_indices))[:sampled_count]
                    sampled_indices.append(concept_indices[selected])
            
            sampled_indices = torch.cat(sampled_indices)
            
        elif self.config.concept_balance_strategy == "weighted":
            # 加权采样：根据concept_weights分布采样
            if concept_weights is None:
                concept_weights = {0: 0.3, 1: 0.5, 2: 0.2}  # 默认权重
            
            sampled_indices = []
            for concept_id, weight in concept_weights.items():
                concept_mask = (self.concept_ids == concept_id)
                concept_indices = torch.where(concept_mask)[0]
                
                if len(concept_indices) > 0:
                    points_to_sample = int(batch_size * weight)
                    if points_to_sample > 0:
                        sampled_count = min(points_to_sample, len(concept_indices))
                        selected = torch.randperm(len(concept_indices))[:sampled_count]
                        sampled_indices.append(concept_indices[selected])
            
            sampled_indices = torch.cat(sampled_indices)
            
        else:
            # 随机采样
            sampled_indices = torch.randperm(total_points)[:batch_size]
        
        # 困难样本挖掘
        if self.config.hard_mining_ratio > 0:
            sampled_indices = self._add_hard_samples(sampled_indices, batch_size)
        
        return sampled_indices
    
    def _extract_batch_features(self, indices: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        🔥 提取批次基础特征
        
        Args:
            indices: 点索引
            
        Returns:
            features: 特征字典
        """
        features = {}
        
        # 1. 基础几何特征
        features['positions'] = self.gaussian_data['xyz'][indices]  # [N, 3]
        features['colors'] = self.gaussian_data['rgb'][indices]     # [N, 3]
        features['opacity'] = self.gaussian_data['opacity'][indices]  # [N, 1]
        features['scales'] = self.gaussian_data['scales'][indices]  # [N, 3]
        features['rotations'] = self.gaussian_data['rotations'][indices]  # [N, 4]
        
        # 2. concept_id one-hot编码
        concept_ids = self.concept_ids[indices]  # [N]
        num_concepts = len(self.data_stats['concept_distribution'])
        features['concept_id_onehot'] = torch.nn.functional.one_hot(
            concept_ids, num_classes=num_concepts
        ).float()  # [N, num_concepts]
        
        # 3. 位置编码 (可选)
        if hasattr(self.config, 'use_positional_encoding') and self.config.use_positional_encoding:
            features['positional_encoding'] = self._compute_positional_encoding(
                features['positions']
            )
        
        # 4. 几何特征 (可选)
        if hasattr(self.config, 'use_geometric_features') and self.config.use_geometric_features:
            features['geometric_features'] = self._compute_geometric_features(
                features['positions'], features['scales'], features['rotations']
            )
        
        # 5. 外观特征 (可选)
        if hasattr(self.config, 'use_appearance_features') and self.config.use_appearance_features:
            features['appearance_features'] = self._compute_appearance_features(
                features['colors'], features['opacity']
            )
        
        return features
    
    def _add_context_features(self, 
                             features: Dict[str, torch.Tensor],
                             indices: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        🔥 添加邻域上下文特征
        
        Args:
            features: 当前特征
            indices: 点索引
            
        Returns:
            enhanced_features: 增强后的特征
        """
        if self.knn_searcher is None:
            return features
        
        positions = features['positions'].cpu().numpy()
        batch_size = positions.shape[0]
        
        # 查找每个点的邻域
        distances, neighbor_indices = self.knn_searcher.kneighbors(positions)
        
        # 聚合邻域特征
        context_features = []
        for i in range(batch_size):
            neighbors = neighbor_indices[i][1:]  # 排除自己
            neighbor_distances = distances[i][1:]
            
            # 权重衰减 (基于距离)
            weights = np.exp(-neighbor_distances / self.config.context_radius)
            weights = weights / (weights.sum() + 1e-8)
            
            # 聚合邻域RGB特征
            neighbor_colors = self.gaussian_data['rgb'][neighbors].cpu().numpy()
            aggregated_color = np.average(neighbor_colors, axis=0, weights=weights)
            
            # 聚合邻域concept_id分布
            neighbor_concepts = self.concept_ids[neighbors].cpu().numpy()
            concept_hist = np.bincount(neighbor_concepts, minlength=3)
            concept_hist = concept_hist / (concept_hist.sum() + 1e-8)
            
            # 合并上下文特征
            context_feature = np.concatenate([
                aggregated_color,           # [3] 邻域平均颜色
                concept_hist,               # [3] 邻域概念分布
                [weights.std()],           # [1] 邻域密度指标
                [neighbor_distances.min()], # [1] 最近邻距离
            ])  # 总计 [8] 维
            
            context_features.append(context_feature)
        
        features['context_features'] = torch.tensor(
            np.array(context_features), dtype=torch.float32
        )  # [N, 8]
        
        return features
    
    def _apply_data_augmentation(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        🔥 应用数据增强
        
        Args:
            features: 原始特征
            
        Returns:
            augmented_features: 增强后的特征
        """
        augmented_features = features.copy()
        
        # 1. 几何变换增强
        if self.config.geometric_augmentation:
            augmented_features = self._apply_geometric_augmentation(augmented_features)
        
        # 2. 噪声注入增强
        if self.config.noise_augmentation:
            augmented_features = self._apply_noise_augmentation(augmented_features)
        
        # 3. 遮挡模拟增强
        if self.config.occlusion_augmentation:
            augmented_features = self._apply_occlusion_augmentation(augmented_features)
        
        self.data_stats['augmentation_count'] += 1
        
        return augmented_features
    
    def _apply_geometric_augmentation(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """应用几何变换增强"""
        # 随机旋转
        if np.random.random() < 0.5:
            angle = np.random.uniform(-self.config.rotation_range, self.config.rotation_range)
            rotation_matrix = self._create_rotation_matrix(angle)
            features['positions'] = torch.matmul(features['positions'], rotation_matrix.T)
        
        # 随机平移
        if np.random.random() < 0.5:
            translation = torch.randn(3) * self.config.translation_range
            features['positions'] = features['positions'] + translation
        
        # 随机缩放
        if np.random.random() < 0.3:
            scale_factor = 1.0 + np.random.uniform(-self.config.scale_range, self.config.scale_range)
            features['positions'] = features['positions'] * scale_factor
            features['scales'] = features['scales'] * scale_factor
        
        return features
    
    def _apply_noise_augmentation(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """应用噪声注入增强"""
        # 位置噪声
        position_noise = torch.randn_like(features['positions']) * self.config.position_noise_std
        features['positions'] = features['positions'] + position_noise
        
        # 特征噪声
        color_noise = torch.randn_like(features['colors']) * self.config.feature_noise_std
        features['colors'] = torch.clamp(features['colors'] + color_noise, 0.0, 1.0)
        
        opacity_noise = torch.randn_like(features['opacity']) * self.config.feature_noise_std
        features['opacity'] = torch.clamp(features['opacity'] + opacity_noise, 0.0, 1.0)
        
        return features
    
    def _apply_occlusion_augmentation(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """应用遮挡模拟增强"""
        batch_size = features['positions'].shape[0]
        num_occluded = int(batch_size * self.config.occlusion_ratio)
        
        if num_occluded > 0:
            # 随机选择要遮挡的点
            occluded_indices = torch.randperm(batch_size)[:num_occluded]
            
            # 将这些点的某些特征设为零或默认值
            features['colors'][occluded_indices] = 0.5  # 设为灰色
            features['opacity'][occluded_indices] = 0.1  # 降低透明度
        
        return features
    
    def _prepare_supervision_labels(self, indices: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        🔥 准备监督标签
        
        Args:
            indices: 点索引
            
        Returns:
            labels: 监督标签字典
        """
        # 主要监督标签：concept_id分类
        concept_ids = self.concept_ids[indices]  # [N]
        
        # 辅助监督信息
        labels = {
            'concept_ids': concept_ids,  # [N] 主要分类标签
            'concept_ids_onehot': torch.nn.functional.one_hot(
                concept_ids, num_classes=len(self.data_stats['concept_distribution'])
            ).float(),  # [N, num_concepts] one-hot标签
        }
        
        # 可选：邻域一致性标签
        if hasattr(self.config, 'use_consistency_labels') and self.config.use_consistency_labels:
            labels['neighborhood_consistency'] = self._compute_neighborhood_consistency_labels(indices)
        
        return labels
    
    def _add_hard_samples(self, current_indices: torch.Tensor, target_batch_size: int) -> torch.Tensor:
        """添加困难样本"""
        num_hard_samples = int(target_batch_size * self.config.hard_mining_ratio)
        
        # 简化实现：随机选择边界区域的点作为困难样本
        boundary_mask = (self.concept_ids == 2)  # concept_id=2是边界
        boundary_indices = torch.where(boundary_mask)[0]
        
        if len(boundary_indices) > 0:
            selected_hard = boundary_indices[torch.randperm(len(boundary_indices))[:num_hard_samples]]
            combined_indices = torch.cat([current_indices, selected_hard])
            
            # 保持批次大小
            if len(combined_indices) > target_batch_size:
                combined_indices = combined_indices[torch.randperm(len(combined_indices))[:target_batch_size]]
            
            self.data_stats['hard_samples_mined'] += len(selected_hard)
            return combined_indices
        
        return current_indices
    
    def _create_rotation_matrix(self, angle: float) -> torch.Tensor:
        """创建绕Z轴旋转矩阵"""
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation_matrix = torch.tensor([
            [cos_a, -sin_a, 0],
            [sin_a,  cos_a, 0],
            [0,      0,     1]
        ], dtype=torch.float32)
        return rotation_matrix
    
    def _compute_positional_encoding(self, positions: torch.Tensor) -> torch.Tensor:
        """计算位置编码"""
        # 简化的正弦/余弦位置编码
        pe_dim = 64
        encoded = []
        
        for i in range(pe_dim // 6):  # 每个坐标轴使用pe_dim//6个频率
            freq = 2.0 ** i
            for axis in range(3):  # x, y, z
                encoded.append(torch.sin(positions[:, axis] * freq))
                encoded.append(torch.cos(positions[:, axis] * freq))
        
        # 补齐到pe_dim维度
        while len(encoded) < pe_dim:
            encoded.append(torch.zeros_like(positions[:, 0]))
        
        return torch.stack(encoded[:pe_dim], dim=1)  # [N, pe_dim]
    
    def _compute_geometric_features(self, 
                                   positions: torch.Tensor,
                                   scales: torch.Tensor,
                                   rotations: torch.Tensor) -> torch.Tensor:
        """计算几何特征"""
        # 体积特征
        volumes = scales.prod(dim=1, keepdim=True)  # [N, 1]
        
        # 形状特征（长宽高比）
        sorted_scales, _ = torch.sort(scales, dim=1)
        aspect_ratios = sorted_scales[:, 1:] / (sorted_scales[:, :-1] + 1e-8)  # [N, 2]
        
        # 旋转特征（简化为旋转幅度）
        rotation_magnitude = torch.norm(rotations[:, 1:], dim=1, keepdim=True)  # [N, 1]
        
        # 合并几何特征
        geometric_features = torch.cat([
            volumes,          # [1] 体积
            aspect_ratios,    # [2] 长宽比
            rotation_magnitude, # [1] 旋转幅度
            scales,           # [3] 原始尺度
        ], dim=1)  # [N, 7]
        
        return geometric_features
    
    def _compute_appearance_features(self, 
                                    colors: torch.Tensor,
                                    opacity: torch.Tensor) -> torch.Tensor:
        """计算外观特征"""
        # 颜色统计特征
        color_mean = colors.mean(dim=1, keepdim=True)     # [N, 1]
        color_std = colors.std(dim=1, keepdim=True)       # [N, 1]
        color_max = colors.max(dim=1, keepdim=True)[0]    # [N, 1]
        color_min = colors.min(dim=1, keepdim=True)[0]    # [N, 1]
        
        # 颜色亮度和饱和度
        brightness = colors.mean(dim=1, keepdim=True)     # [N, 1]
        saturation = colors.std(dim=1, keepdim=True)      # [N, 1]
        
        # 合并外观特征
        appearance_features = torch.cat([
            colors,           # [3] 原始RGB
            opacity,          # [1] 透明度
            color_mean,       # [1] 颜色均值
            color_std,        # [1] 颜色标准差
            color_max,        # [1] 颜色最大值
            color_min,        # [1] 颜色最小值
            brightness,       # [1] 亮度
            saturation,       # [1] 饱和度
        ], dim=1)  # [N, 11]
        
        return appearance_features
    
    def get_training_statistics(self) -> Dict[str, Any]:
        """获取训练数据统计信息"""
        return {
            'data_stats': self.data_stats,
            'config': self.config.__dict__,
            'cache_info': {
                'feature_cache_size': len(self.feature_cache),
                'augmentation_cache_size': len(self.augmentation_cache),
            }
        }
    
    def save_training_data_cache(self, cache_path: str):
        """保存训练数据缓存"""
        try:
            cache_data = {
                'feature_cache': self.feature_cache,
                'augmentation_cache': self.augmentation_cache,
                'data_stats': self.data_stats
            }
            
            torch.save(cache_data, cache_path)
            print(f"✅ 训练数据缓存已保存: {cache_path}")
            
        except Exception as e:
            print(f"❌ 保存缓存失败: {str(e)}")
    
    def load_training_data_cache(self, cache_path: str) -> bool:
        """加载训练数据缓存"""
        try:
            if not os.path.exists(cache_path):
                return False
            
            cache_data = torch.load(cache_path)
            self.feature_cache = cache_data.get('feature_cache', {})
            self.augmentation_cache = cache_data.get('augmentation_cache', {})
            self.data_stats.update(cache_data.get('data_stats', {}))
            
            print(f"✅ 训练数据缓存已加载: {cache_path}")
            return True
            
        except Exception as e:
            print(f"❌ 加载缓存失败: {str(e)}")
            return False

# 使用示例
if __name__ == "__main__":
    # 配置训练数据生成器
    config = TrainingDataConfig(
        points_per_batch=4096,
        concept_balance_strategy="balanced",
        geometric_augmentation=True,
        noise_augmentation=True
    )
    
    # 创建数据生成器
    data_generator = RCATrainingDataGenerator(config)
    
    # 加载阶段3数据
    if data_generator.load_stage3_training_data("./output/stage3"):
        # 生成训练批次
        for epoch in range(5):
            batch_data = data_generator.generate_training_batch(batch_size=2048)
            print(f"Epoch {epoch}: 批次大小 {batch_data['batch_size']}")
            print(f"特征维度: {batch_data['features']['positions'].shape}")
            print(f"标签形状: {batch_data['labels']['concept_ids'].shape}")