#!/bin/bash
# InFusion-Enhanced Project Environment Setup Script

if [ -n "$CONDA_PREFIX" ]; then
    MAMBA_ENV_PATH="$CONDA_PREFIX"
elif [ -n "$MAMBA_PREFIX" ]; then
    MAMBA_ENV_PATH="$MAMBA_PREFIX"
else
    echo "Error: CONDA_PREFIX or MAMBA_PREFIX not set. Activate the environment first."
    return 1
fi

if [ -d "$MAMBA_ENV_PATH" ]; then
    export CUDA_HOME=$MAMBA_ENV_PATH
    
    # Ensure env's bin is at the front of PATH
    CLEAN_PATH=$(echo "$PATH" | awk -v RS=: -v ORS=: '$0 != ENV_BIN { print $0 }' ENV_BIN="$MAMBA_ENV_PATH/bin" | sed 's/:$//')
    export PATH="$MAMBA_ENV_PATH/bin:$CLEAN_PATH"
    
    # Ensure env's lib is at the front of LD_LIBRARY_PATH
    PYTHON_SITE_PACKAGES=$(python -c "import site; print(site.getsitepackages()[0])" 2>/dev/null)
    TORCH_LIB_PATH="$PYTHON_SITE_PACKAGES/torch/lib"

    NEW_LD_LIBRARY_PATH="$MAMBA_ENV_PATH/lib64:$MAMBA_ENV_PATH/lib"
    if [ -d "$TORCH_LIB_PATH" ]; then
        NEW_LD_LIBRARY_PATH="$TORCH_LIB_PATH:$NEW_LD_LIBRARY_PATH"
    fi
    if [ -n "$LD_LIBRARY_PATH" ]; then
         CLEAN_LD_PATH=$(echo "$LD_LIBRARY_PATH" | awk -v RS=: -v ORS=: '$0 != T_LIB && $0 != M_LIB && $0 != M_LIB64 { print $0 }' T_LIB="$TORCH_LIB_PATH" M_LIB="$MAMBA_ENV_PATH/lib" M_LIB64="$MAMBA_ENV_PATH/lib64" | sed 's/:$//')
         if [ -n "$CLEAN_LD_PATH" ]; then
            export LD_LIBRARY_PATH="$NEW_LD_LIBRARY_PATH:$CLEAN_LD_PATH"
         else
            export LD_LIBRARY_PATH="$NEW_LD_LIBRARY_PATH"
         fi
    else
        export LD_LIBRARY_PATH="$NEW_LD_LIBRARY_PATH"
    fi

    # Set compiler environment variables
    ENV_GCC_NAME="x86_64-conda-linux-gnu-gcc"
    ENV_GPP_NAME="x86_64-conda-linux-gnu-g++"

    if [ -f "$MAMBA_ENV_PATH/bin/$ENV_GCC_NAME" ]; then
        export CC=$MAMBA_ENV_PATH/bin/$ENV_GCC_NAME
    else
        echo "Warning: Environment GCC ($ENV_GCC_NAME) not found. Falling back to system gcc."
        export CC=gcc
    fi
    if [ -f "$MAMBA_ENV_PATH/bin/$ENV_GPP_NAME" ]; then
        export CXX=$MAMBA_ENV_PATH/bin/$ENV_GPP_NAME
    else
        echo "Warning: Environment G++ ($ENV_GPP_NAME) not found. Falling back to system g++."
        export CXX=g++
    fi
else
    echo "Error: Micromamba environment path does not exist: $MAMBA_ENV_PATH"
    return 1
fi

echo "=== InFusion-Enhanced Environment (infusion_pro) Setup Complete ==="
echo "Active Environment: $MAMBA_ENV_PATH"
echo "CUDA_HOME: $CUDA_HOME"
echo "PATH (first 3 entries): $(echo $PATH | tr ':' '\n' | head -n 3 | paste -sd: -)"
echo "LD_LIBRARY_PATH (first 3 entries): $(echo $LD_LIBRARY_PATH | tr ':' '\n' | head -n 3 | paste -sd: -)"

if command -v nvcc &> /dev/null; then NVCC_PATH_WHICH=$(which nvcc); else NVCC_PATH_WHICH="Not in PATH"; fi
echo "nvcc (from PATH): $NVCC_PATH_WHICH $([[ -x "$NVCC_PATH_WHICH" ]] && $NVCC_PATH_WHICH --version | grep release || echo '')"

echo "CC (env var): $CC $([[ -x "$CC" ]] && $CC --version | head -n1 || echo '')"
echo "CXX (env var): $CXX $([[ -x "$CXX" ]] && $CXX --version | head -n1 || echo '')"

echo "Python: $(python --version)"
echo "PyTorch linked CUDA: $(python -c 'import torch; print(torch.version.cuda if torch.cuda.is_available() else "Not Available")' 2>/dev/null || echo 'PyTorch not installed')"
echo "==================================================================="
