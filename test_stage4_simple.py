#!/usr/bin/env python3
"""
阶段4简化测试脚本
测试基本的导入和配置功能
"""

import os
import sys

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        import torch
        print("✅ torch导入成功")
    except Exception as e:
        print(f"❌ torch导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ numpy导入成功")
    except Exception as e:
        print(f"❌ numpy导入失败: {e}")
        return False
    
    return True

def test_project_imports():
    """测试项目模块导入"""
    print("\n🔍 测试项目模块导入...")
    
    # 添加路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    gaussian_splatting_dir = os.path.join(current_dir, "gaussian_splatting")
    if gaussian_splatting_dir not in sys.path:
        sys.path.insert(0, gaussian_splatting_dir)
        print(f"添加路径: {gaussian_splatting_dir}")
    
    try:
        from scene import Scene, GaussianModel
        print("✅ scene模块导入成功")
    except Exception as e:
        print(f"❌ scene模块导入失败: {e}")
        return False
    
    try:
        from cism_core import DiffusionEngine
        print("✅ cism_core导入成功")
    except Exception as e:
        print(f"❌ cism_core导入失败: {e}")
        return False
    
    try:
        from rca_core import RCAIntegrator
        print("✅ rca_core导入成功")
    except Exception as e:
        print(f"❌ rca_core导入失败: {e}")
        return False
    
    return True

def test_stage4_config():
    """测试Stage4Config"""
    print("\n🔍 测试Stage4Config...")
    
    try:
        # 简化的配置类
        from dataclasses import dataclass
        
        @dataclass
        class Stage4Config:
            """阶段4训练配置"""
            # 基本路径
            model_path: str = "./output/point_cloud/iteration_30000/point_cloud.ply"
            concept_masks_path: str = "./concept_masks"
            output_path: str = "./output/stage4"
            
            # 训练参数
            iterations: int = 1000
            device: str = "cuda"
            
            # RCA参数
            enable_rca: bool = True
            
            # CISM参数
            cism_start_iter: int = 100
            cism_end_iter: int = 900
            cism_interval: int = 10
            
            # 学习率
            position_lr_init: float = 0.00016
            feature_lr: float = 0.0025
            opacity_lr: float = 0.05
            scaling_lr: float = 0.005
            rotation_lr: float = 0.001
            
            # 调试
            debug_mode: bool = False
        
        config = Stage4Config()
        print("✅ Stage4Config创建成功")
        print(f"   模型路径: {config.model_path}")
        print(f"   迭代次数: {config.iterations}")
        print(f"   RCA启用: {config.enable_rca}")
        return True
        
    except Exception as e:
        print(f"❌ Stage4Config测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始阶段4简化测试...")
    
    # 测试基本导入
    if not test_basic_imports():
        print("❌ 基本导入测试失败")
        return False
    
    # 测试项目导入
    if not test_project_imports():
        print("❌ 项目导入测试失败")
        return False
    
    # 测试配置
    if not test_stage4_config():
        print("❌ 配置测试失败")
        return False
    
    print("\n🎉 阶段4简化测试全部通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
