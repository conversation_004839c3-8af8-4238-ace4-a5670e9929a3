<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: trigonometric.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">trigonometric.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00160.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#include &quot;detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;{</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga6e1db4862c5e25afd553930e2fdd6a68">radians</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00373.html#ga8faec9e303538065911ba8b3caf7326b">degrees</a>);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga8faec9e303538065911ba8b3caf7326b">degrees</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00373.html#ga6e1db4862c5e25afd553930e2fdd6a68">radians</a>);</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga29747fd108cb7292ae5a284f69691a69">sin</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga6a41efc740e3b3c937447d3a6284130e">cos</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga293a34cfb9f0115cc606b4a97c84f11f">tan</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga0552d2df4865fa8c3d7cfc3ec2caac73">asin</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#gacc9b092df8257c68f19c9053703e2563">acos</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga5229f087eaccbc466f1c609ce3107b95">atan</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga5229f087eaccbc466f1c609ce3107b95">atan</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y_over_x);</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#gac7c39ff21809e281552b4dbe46f4a39d">sinh</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga4e260e372742c5f517aca196cf1e62b3">cosh</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1">tanh</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; <a class="code" href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">angle</a>);</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga3ef16b501ee859fddde88e22192a5950">asinh</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#ga858f35dc66fd2688f20c52b5f25be76a">acosh</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;        GLM_FUNC_DECL vec&lt;L, T, Q&gt; <a class="code" href="a00373.html#gabc925650e618357d07da255531658b87">atanh</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x);</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;<span class="preprocessor">#include &quot;detail/func_trigonometric.inl&quot;</span></div>
<div class="ttc" id="a00373_html_ga8faec9e303538065911ba8b3caf7326b"><div class="ttname"><a href="a00373.html#ga8faec9e303538065911ba8b3caf7326b">glm::degrees</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt; degrees(vec&lt; L, T, Q &gt; const &amp;radians)</div><div class="ttdoc">Converts radians to degrees and returns the result. </div></div>
<div class="ttc" id="a00373_html_ga4e260e372742c5f517aca196cf1e62b3"><div class="ttname"><a href="a00373.html#ga4e260e372742c5f517aca196cf1e62b3">glm::cosh</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; cosh(vec&lt; L, T, Q &gt; const &amp;angle)</div><div class="ttdoc">Returns the hyperbolic cosine function, (exp(x) + exp(-x)) / 2. </div></div>
<div class="ttc" id="a00373_html_gacc9b092df8257c68f19c9053703e2563"><div class="ttname"><a href="a00373.html#gacc9b092df8257c68f19c9053703e2563">glm::acos</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; acos(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Arc cosine. </div></div>
<div class="ttc" id="a00373_html_ga29747fd108cb7292ae5a284f69691a69"><div class="ttname"><a href="a00373.html#ga29747fd108cb7292ae5a284f69691a69">glm::sin</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; sin(vec&lt; L, T, Q &gt; const &amp;angle)</div><div class="ttdoc">The standard trigonometric sine function. </div></div>
<div class="ttc" id="a00373_html_ga6e1db4862c5e25afd553930e2fdd6a68"><div class="ttname"><a href="a00373.html#ga6e1db4862c5e25afd553930e2fdd6a68">glm::radians</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt; radians(vec&lt; L, T, Q &gt; const &amp;degrees)</div><div class="ttdoc">Converts degrees to radians and returns the result. </div></div>
<div class="ttc" id="a00257_html_ga8aa248b31d5ade470c87304df5eb7bd8"><div class="ttname"><a href="a00257.html#ga8aa248b31d5ade470c87304df5eb7bd8">glm::angle</a></div><div class="ttdeci">GLM_FUNC_DECL T angle(qua&lt; T, Q &gt; const &amp;x)</div><div class="ttdoc">Returns the quaternion rotation angle. </div></div>
<div class="ttc" id="a00373_html_ga0552d2df4865fa8c3d7cfc3ec2caac73"><div class="ttname"><a href="a00373.html#ga0552d2df4865fa8c3d7cfc3ec2caac73">glm::asin</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; asin(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Arc sine. </div></div>
<div class="ttc" id="a00373_html_gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1"><div class="ttname"><a href="a00373.html#gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1">glm::tanh</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; tanh(vec&lt; L, T, Q &gt; const &amp;angle)</div><div class="ttdoc">Returns the hyperbolic tangent function, sinh(angle) / cosh(angle) </div></div>
<div class="ttc" id="a00373_html_gac7c39ff21809e281552b4dbe46f4a39d"><div class="ttname"><a href="a00373.html#gac7c39ff21809e281552b4dbe46f4a39d">glm::sinh</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; sinh(vec&lt; L, T, Q &gt; const &amp;angle)</div><div class="ttdoc">Returns the hyperbolic sine function, (exp(x) - exp(-x)) / 2. </div></div>
<div class="ttc" id="a00373_html_ga3ef16b501ee859fddde88e22192a5950"><div class="ttname"><a href="a00373.html#ga3ef16b501ee859fddde88e22192a5950">glm::asinh</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; asinh(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Arc hyperbolic sine; returns the inverse of sinh. </div></div>
<div class="ttc" id="a00373_html_gabc925650e618357d07da255531658b87"><div class="ttname"><a href="a00373.html#gabc925650e618357d07da255531658b87">glm::atanh</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; atanh(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Arc hyperbolic tangent; returns the inverse of tanh. </div></div>
<div class="ttc" id="a00373_html_ga6a41efc740e3b3c937447d3a6284130e"><div class="ttname"><a href="a00373.html#ga6a41efc740e3b3c937447d3a6284130e">glm::cos</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; cos(vec&lt; L, T, Q &gt; const &amp;angle)</div><div class="ttdoc">The standard trigonometric cosine function. </div></div>
<div class="ttc" id="a00373_html_ga5229f087eaccbc466f1c609ce3107b95"><div class="ttname"><a href="a00373.html#ga5229f087eaccbc466f1c609ce3107b95">glm::atan</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; atan(vec&lt; L, T, Q &gt; const &amp;y_over_x)</div><div class="ttdoc">Arc tangent. </div></div>
<div class="ttc" id="a00373_html_ga858f35dc66fd2688f20c52b5f25be76a"><div class="ttname"><a href="a00373.html#ga858f35dc66fd2688f20c52b5f25be76a">glm::acosh</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; acosh(vec&lt; L, T, Q &gt; const &amp;x)</div><div class="ttdoc">Arc hyperbolic cosine; returns the non-negative inverse of cosh. </div></div>
<div class="ttc" id="a00373_html_ga293a34cfb9f0115cc606b4a97c84f11f"><div class="ttname"><a href="a00373.html#ga293a34cfb9f0115cc606b4a97c84f11f">glm::tan</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; L, T, Q &gt; tan(vec&lt; L, T, Q &gt; const &amp;angle)</div><div class="ttdoc">The standard trigonometric tangent function. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
