<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_quaternion</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_quaternion<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00125.html" title="GLM_GTC_quaternion ">glm/gtc/quaternion.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaf4dd967dead22dd932fc7460ceecb03f"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf4dd967dead22dd932fc7460ceecb03f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#gaf4dd967dead22dd932fc7460ceecb03f">eulerAngles</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gaf4dd967dead22dd932fc7460ceecb03f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns euler angles, pitch as x, yaw as y, roll as z.  <a href="a00299.html#gaf4dd967dead22dd932fc7460ceecb03f">More...</a><br /></td></tr>
<tr class="separator:gaf4dd967dead22dd932fc7460ceecb03f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8f7fa76e06c417b757ddfd438f3f677b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8f7fa76e06c417b757ddfd438f3f677b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga8f7fa76e06c417b757ddfd438f3f677b">greaterThan</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga8f7fa76e06c417b757ddfd438f3f677b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x &gt; y.  <a href="a00299.html#ga8f7fa76e06c417b757ddfd438f3f677b">More...</a><br /></td></tr>
<tr class="separator:ga8f7fa76e06c417b757ddfd438f3f677b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga388cbeba987dae7b5937f742efa49a5a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga388cbeba987dae7b5937f742efa49a5a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga388cbeba987dae7b5937f742efa49a5a">greaterThanEqual</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga388cbeba987dae7b5937f742efa49a5a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x &gt;= y.  <a href="a00299.html#ga388cbeba987dae7b5937f742efa49a5a">More...</a><br /></td></tr>
<tr class="separator:ga388cbeba987dae7b5937f742efa49a5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad091a2d22c8acfebfa92bcfca1dfe9c4"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad091a2d22c8acfebfa92bcfca1dfe9c4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#gad091a2d22c8acfebfa92bcfca1dfe9c4">lessThan</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gad091a2d22c8acfebfa92bcfca1dfe9c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison result of x &lt; y.  <a href="a00299.html#gad091a2d22c8acfebfa92bcfca1dfe9c4">More...</a><br /></td></tr>
<tr class="separator:gad091a2d22c8acfebfa92bcfca1dfe9c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac00012eea281800d2403f4ea8443134d"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac00012eea281800d2403f4ea8443134d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#gac00012eea281800d2403f4ea8443134d">lessThanEqual</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gac00012eea281800d2403f4ea8443134d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of result x &lt;= y.  <a href="a00299.html#gac00012eea281800d2403f4ea8443134d">More...</a><br /></td></tr>
<tr class="separator:gac00012eea281800d2403f4ea8443134d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga333ab70047fbe4132406100c292dbc89"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga333ab70047fbe4132406100c292dbc89"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga333ab70047fbe4132406100c292dbc89">mat3_cast</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga333ab70047fbe4132406100c292dbc89"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a quaternion to a 3 * 3 matrix.  <a href="a00299.html#ga333ab70047fbe4132406100c292dbc89">More...</a><br /></td></tr>
<tr class="separator:ga333ab70047fbe4132406100c292dbc89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1113212d9bdefc2e31ad40e5bbb506f3"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1113212d9bdefc2e31ad40e5bbb506f3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga1113212d9bdefc2e31ad40e5bbb506f3">mat4_cast</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga1113212d9bdefc2e31ad40e5bbb506f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a quaternion to a 4 * 4 matrix.  <a href="a00299.html#ga1113212d9bdefc2e31ad40e5bbb506f3">More...</a><br /></td></tr>
<tr class="separator:ga1113212d9bdefc2e31ad40e5bbb506f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7603e81477b46ddb448896909bc04928"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7603e81477b46ddb448896909bc04928"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga7603e81477b46ddb448896909bc04928">pitch</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga7603e81477b46ddb448896909bc04928"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns pitch value of euler angles expressed in radians.  <a href="a00299.html#ga7603e81477b46ddb448896909bc04928">More...</a><br /></td></tr>
<tr class="separator:ga7603e81477b46ddb448896909bc04928"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1108a4ab88ca87bac321454eea7702f8"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1108a4ab88ca87bac321454eea7702f8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga1108a4ab88ca87bac321454eea7702f8">quat_cast</a> (mat&lt; 3, 3, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga1108a4ab88ca87bac321454eea7702f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a pure rotation 3 * 3 matrix to a quaternion.  <a href="a00299.html#ga1108a4ab88ca87bac321454eea7702f8">More...</a><br /></td></tr>
<tr class="separator:ga1108a4ab88ca87bac321454eea7702f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4524810f07f72e8c7bdc7764fa11cb58"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4524810f07f72e8c7bdc7764fa11cb58"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga4524810f07f72e8c7bdc7764fa11cb58">quat_cast</a> (mat&lt; 4, 4, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga4524810f07f72e8c7bdc7764fa11cb58"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a pure rotation 4 * 4 matrix to a quaternion.  <a href="a00299.html#ga4524810f07f72e8c7bdc7764fa11cb58">More...</a><br /></td></tr>
<tr class="separator:ga4524810f07f72e8c7bdc7764fa11cb58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabe7fc5ec5feb41ab234d5d2b6254697f"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabe7fc5ec5feb41ab234d5d2b6254697f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#gabe7fc5ec5feb41ab234d5d2b6254697f">quatLookAt</a> (vec&lt; 3, T, Q &gt; const &amp;direction, vec&lt; 3, T, Q &gt; const &amp;up)</td></tr>
<tr class="memdesc:gabe7fc5ec5feb41ab234d5d2b6254697f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a look at quaternion based on the default handedness.  <a href="a00299.html#gabe7fc5ec5feb41ab234d5d2b6254697f">More...</a><br /></td></tr>
<tr class="separator:gabe7fc5ec5feb41ab234d5d2b6254697f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2da350c73411be3bb19441b226b81a74"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2da350c73411be3bb19441b226b81a74"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga2da350c73411be3bb19441b226b81a74">quatLookAtLH</a> (vec&lt; 3, T, Q &gt; const &amp;direction, vec&lt; 3, T, Q &gt; const &amp;up)</td></tr>
<tr class="memdesc:ga2da350c73411be3bb19441b226b81a74"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a left-handed look at quaternion.  <a href="a00299.html#ga2da350c73411be3bb19441b226b81a74">More...</a><br /></td></tr>
<tr class="separator:ga2da350c73411be3bb19441b226b81a74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf6529ac8c04a57fcc35865b5c9437cc8"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf6529ac8c04a57fcc35865b5c9437cc8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#gaf6529ac8c04a57fcc35865b5c9437cc8">quatLookAtRH</a> (vec&lt; 3, T, Q &gt; const &amp;direction, vec&lt; 3, T, Q &gt; const &amp;up)</td></tr>
<tr class="memdesc:gaf6529ac8c04a57fcc35865b5c9437cc8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a right-handed look at quaternion.  <a href="a00299.html#gaf6529ac8c04a57fcc35865b5c9437cc8">More...</a><br /></td></tr>
<tr class="separator:gaf6529ac8c04a57fcc35865b5c9437cc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0cc5ad970d0b00829b139fe0fe5a1e13"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0cc5ad970d0b00829b139fe0fe5a1e13"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga0cc5ad970d0b00829b139fe0fe5a1e13">roll</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga0cc5ad970d0b00829b139fe0fe5a1e13"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns roll value of euler angles expressed in radians.  <a href="a00299.html#ga0cc5ad970d0b00829b139fe0fe5a1e13">More...</a><br /></td></tr>
<tr class="separator:ga0cc5ad970d0b00829b139fe0fe5a1e13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8da38cdfdc452dafa660c2f46506bad5"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8da38cdfdc452dafa660c2f46506bad5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00299.html#ga8da38cdfdc452dafa660c2f46506bad5">yaw</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga8da38cdfdc452dafa660c2f46506bad5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns yaw value of euler angles expressed in radians.  <a href="a00299.html#ga8da38cdfdc452dafa660c2f46506bad5">More...</a><br /></td></tr>
<tr class="separator:ga8da38cdfdc452dafa660c2f46506bad5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00125.html" title="GLM_GTC_quaternion ">glm/gtc/quaternion.hpp</a>&gt; to use the features of this extension. </p>
<p>Defines a templated quaternion type and several quaternion operations. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaf4dd967dead22dd932fc7460ceecb03f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::eulerAngles </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns euler angles, pitch as x, yaw as y, roll as z. </p>
<p>The result is expressed in radians.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8f7fa76e06c417b757ddfd438f3f677b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::greaterThan </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of result x &gt; y. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00255.html" title="Exposes comparison functions for quaternion types that take a user defined epsilon values...">GLM_EXT_quaternion_relational</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga388cbeba987dae7b5937f742efa49a5a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::greaterThanEqual </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of result x &gt;= y. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00255.html" title="Exposes comparison functions for quaternion types that take a user defined epsilon values...">GLM_EXT_quaternion_relational</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad091a2d22c8acfebfa92bcfca1dfe9c4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::lessThan </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison result of x &lt; y. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00255.html" title="Exposes comparison functions for quaternion types that take a user defined epsilon values...">GLM_EXT_quaternion_relational</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac00012eea281800d2403f4ea8443134d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::lessThanEqual </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of result x &lt;= y. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00255.html" title="Exposes comparison functions for quaternion types that take a user defined epsilon values...">GLM_EXT_quaternion_relational</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga333ab70047fbe4132406100c292dbc89"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; glm::mat3_cast </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a quaternion to a 3 * 3 matrix. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> </dd></dl>

<p>Referenced by <a class="el" href="a00126_source.html#l00113">glm::toMat3()</a>.</p>

</div>
</div>
<a class="anchor" id="ga1113212d9bdefc2e31ad40e5bbb506f3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::mat4_cast </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a quaternion to a 4 * 4 matrix. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> </dd></dl>

<p>Referenced by <a class="el" href="a00126_source.html#l00120">glm::toMat4()</a>.</p>

</div>
</div>
<a class="anchor" id="ga7603e81477b46ddb448896909bc04928"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::pitch </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns pitch value of euler angles expressed in radians. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1108a4ab88ca87bac321454eea7702f8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::quat_cast </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a pure rotation 3 * 3 matrix to a quaternion. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> </dd></dl>

<p>Referenced by <a class="el" href="a00126_source.html#l00127">glm::toQuat()</a>.</p>

</div>
</div>
<a class="anchor" id="ga4524810f07f72e8c7bdc7764fa11cb58"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::quat_cast </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a pure rotation 4 * 4 matrix to a quaternion. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabe7fc5ec5feb41ab234d5d2b6254697f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::quatLookAt </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>direction</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>up</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a look at quaternion based on the default handedness. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td>Desired forward direction. Needs to be normalized. </td></tr>
    <tr><td class="paramname">up</td><td>Up vector, how the camera is oriented. Typically (0, 1, 0). </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga2da350c73411be3bb19441b226b81a74"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::quatLookAtLH </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>direction</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>up</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a left-handed look at quaternion. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td>Desired forward direction onto which the +z-axis gets mapped. Needs to be normalized. </td></tr>
    <tr><td class="paramname">up</td><td>Up vector, how the camera is oriented. Typically (0, 1, 0). </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaf6529ac8c04a57fcc35865b5c9437cc8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::quatLookAtRH </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>direction</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>up</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a right-handed look at quaternion. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td>Desired forward direction onto which the -z-axis gets mapped. Needs to be normalized. </td></tr>
    <tr><td class="paramname">up</td><td>Up vector, how the camera is oriented. Typically (0, 1, 0). </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga0cc5ad970d0b00829b139fe0fe5a1e13"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::roll </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns roll value of euler angles expressed in radians. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8da38cdfdc452dafa660c2f46506bad5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::yaw </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns yaw value of euler angles expressed in radians. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00299.html" title="Include <glm/gtc/quaternion.hpp> to use the features of this extension. ">GLM_GTC_quaternion</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
