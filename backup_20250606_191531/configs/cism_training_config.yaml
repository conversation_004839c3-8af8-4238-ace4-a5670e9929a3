# CISM训练配置文件
# 阶段2: CISM语义引导系统训练参数

# 基本配置
experiment_name: "cism_enhanced_garden"
output_dir: "output/cism_experiments"
device: "cuda"

# Stable Diffusion模型配置
model_id: "runwayml/stable-diffusion-v1-5"
half_precision: true
attention_slicing: true
memory_efficient_attention: true

# 概念配置
concept_config_path: "configs/concept_prompts.yaml"
active_concepts: [0, 1, 2]  # 背景、修复、边界
main_concept_id: 1          # 主要修复概念

# 用户自定义概念（示例）
user_concept: "modern minimalist garden with geometric patterns, clean lines, contemporary landscape design, high quality, photorealistic"

# 概念权重
concept_weights: [0.3, 1.0, 0.5]  # [背景, 修复, 边界]

# CISM训练调度
training_schedule:
  cism_start_iter: 1000      # CISM开始迭代
  cism_end_iter: 15000       # CISM结束迭代
  cism_interval: 10          # CISM应用间隔
  
  # SDS损失权重调度
  sds_start_weight: 0.1      # 起始权重
  sds_end_weight: 1.0        # 结束权重
  sds_warmup_iters: 500      # 预热迭代数
  weight_schedule_type: "linear"  # linear, cosine, constant

# SDS损失配置
guidance_scale: 7.5        # CFG引导强度
min_step: 20               # 最小时间步
max_step: 980              # 最大时间步
weight_type: "dreamfusion" # 时间步权重类型
use_multi_concept: true    # 是否使用多概念SDS

# 训练优化
optimization:
  # 3DGS基础学习率（保持原有设置）
  position_lr_init: 0.00016
  position_lr_final: 0.0000016
  feature_lr: 0.0025
  opacity_lr: 0.05
  scaling_lr: 0.005
  rotation_lr: 0.001
  
  # concept_confidence学习率
  concept_confidence_lr: 0.005  # concept_confidence的学习率

# 渲染配置
rendering:
  image_resolution: 512      # 渲染分辨率
  batch_size: 1             # 批次大小
  random_background: true    # 随机背景
  
# 监控和日志
monitoring:
  log_interval: 100          # 日志输出间隔
  save_interval: 1000        # 模型保存间隔
  eval_interval: 500         # 评估间隔
  
  # 可视化
  save_training_images: true
  save_concept_visualizations: true
  
# 内存管理
memory:
  gradient_accumulation_steps: 1
  max_memory_usage: 0.9      # 最大GPU内存使用率
  clear_cache_interval: 100  # 清理缓存间隔

# 高级设置
advanced:
  # 概念掩码渲染
  render_concept_masks: false
  concept_mask_resolution: 256
  
  # 多视角一致性
  multi_view_consistency: false
  consistency_weight: 0.1
  
  # 边界平滑
  boundary_smoothing: true
  smoothing_kernel_size: 5
  
  # 自适应引导强度
  adaptive_guidance: false
  guidance_scale_range: [5.0, 10.0]

# 实验变体配置
experiment_variants:
  # 基础CISM
  basic:
    use_multi_concept: false
    main_concept_id: 1
    guidance_scale: 7.5
  
  # 多概念CISM
  multi_concept:
    use_multi_concept: true
    concept_weights: [0.3, 1.0, 0.5]
    guidance_scale: 7.5
  
  # 高强度引导
  high_guidance:
    use_multi_concept: true
    concept_weights: [0.2, 1.2, 0.4]
    guidance_scale: 10.0
  
  # 渐进式训练
  progressive:
    cism_start_iter: 500
    cism_end_iter: 20000
    sds_start_weight: 0.05
    sds_end_weight: 1.5

# 数据路径
data_paths:
  # 输入数据（基于您的30000次训练结果）
  gaussians_path: "output/garden/point_cloud.ply"
  cameras_dir: "output/garden/"
  concept_masks_dir: "data/garden/concept_masks/"
  
  # 输出路径
  output_gaussians: "output/cism_experiments/enhanced_gaussians.ply"
  training_logs: "output/cism_experiments/logs/"
  visualizations: "output/cism_experiments/visualizations/"

# 评估配置
evaluation:
  # 评估指标
  metrics: ["psnr", "ssim", "lpips"]
  
  # 评估视角
  eval_cameras: "test"  # train, test, all
  
  # 概念质量评估
  concept_evaluation:
    enabled: true
    clip_score_threshold: 0.8
    concept_consistency_check: true

# 调试和开发
debug:
  enable_debug_mode: false
  save_intermediate_results: false
  profile_performance: false
  verbose_logging: false

# 使用说明
usage_instructions: |
  1. 确保已完成30000次基础训练
  2. 准备概念掩码数据
  3. 设置用户自定义概念
  4. 运行CISM增强训练：
     python train_cism.py --config configs/cism_training_config.yaml
  
  5. 监控训练进度：
     tensorboard --logdir output/cism_experiments/logs/
  
  6. 评估结果：
     python evaluate_cism.py --model_path output/cism_experiments/enhanced_gaussians.ply
