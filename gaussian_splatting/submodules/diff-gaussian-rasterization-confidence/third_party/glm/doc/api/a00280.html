<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Core features</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Modules</a> &#124;
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">Core features</div>  </div>
</div><!--header-->
<div class="contents">

<p>Features that implement in C++ the GLSL specification as closely as possible.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Modules</h2></td></tr>
<tr class="memitem:a00241"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00241.html">Common functions</a></td></tr>
<tr class="memdesc:a00241"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides GLSL common functions. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00242"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00242.html">Exponential functions</a></td></tr>
<tr class="memdesc:a00242"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides GLSL exponential functions. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00279"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00279.html">Geometric functions</a></td></tr>
<tr class="memdesc:a00279"><td class="mdescLeft">&#160;</td><td class="mdescRight">These operate on vectors as vectors, not component-wise. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00281"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00281.html">Vector types</a></td></tr>
<tr class="memdesc:a00281"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector types of two to four components with an exhaustive set of operators. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00282"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html">Vector types with precision qualifiers</a></td></tr>
<tr class="memdesc:a00282"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector types with precision qualifiers which may result in various precision in term of ULPs. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00283"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html">Matrix types</a></td></tr>
<tr class="memdesc:a00283"><td class="mdescLeft">&#160;</td><td class="mdescRight">Matrix types of with C columns and R rows where C and R are values between 2 to 4 included. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00284"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00284.html">Matrix types with precision qualifiers</a></td></tr>
<tr class="memdesc:a00284"><td class="mdescLeft">&#160;</td><td class="mdescRight">Matrix types with precision qualifiers which may result in various precision in term of ULPs. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00370"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00370.html">Integer functions</a></td></tr>
<tr class="memdesc:a00370"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides GLSL functions on integer types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00371"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00371.html">Matrix functions</a></td></tr>
<tr class="memdesc:a00371"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides GLSL matrix functions. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00372"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html">Floating-Point Pack and Unpack Functions</a></td></tr>
<tr class="memdesc:a00372"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides GLSL functions to pack and unpack half, single and double-precision floating point values into more compact integer types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00373"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00373.html">Angle and Trigonometry Functions</a></td></tr>
<tr class="memdesc:a00373"><td class="mdescLeft">&#160;</td><td class="mdescRight">Function parameters specified as angle are assumed to be in units of radians. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00374"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00374.html">Vector Relational Functions</a></td></tr>
<tr class="memdesc:a00374"><td class="mdescLeft">&#160;</td><td class="mdescRight">Relational and equality operators (&lt;, &lt;=, &gt;, &gt;=, ==, !=) are defined to operate on scalars and produce scalar Boolean results. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga2c27aea32de57d58aec8e92d5d2181e2"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00280.html#ga2c27aea32de57d58aec8e92d5d2181e2">mat3x2</a></td></tr>
<tr class="memdesc:ga2c27aea32de57d58aec8e92d5d2181e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 2 components matrix of single-precision floating-point numbers.  <a href="a00280.html#ga2c27aea32de57d58aec8e92d5d2181e2">More...</a><br /></td></tr>
<tr class="separator:ga2c27aea32de57d58aec8e92d5d2181e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Features that implement in C++ the GLSL specification as closely as possible. </p>
<p>The GLM core consists of C++ types that mirror GLSL types and C++ functions that mirror the GLSL functions.</p>
<p>The best documentation for GLM Core is the current GLSL specification, <a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.clean.pdf">version 4.2 (pdf file)</a>.</p>
<p>GLM core functionalities require &lt;<a class="el" href="a00037.html" title="Core features ">glm/glm.hpp</a>&gt; to be included to be used. </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="ga2c27aea32de57d58aec8e92d5d2181e2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 2, f32, defaultp &gt; mat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 2 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00088_source.html#l00015">15</a> of file <a class="el" href="a00088_source.html">matrix_float3x2.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
