# -*- coding: utf-8 -*-
"""
InFusion 阶段4: RCA端到端优化策略
实现RCA与CISM的联合优化确保收敛稳定性
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR, StepLR, ReduceLROnPlateau
import numpy as np
import os
import time
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
import gc
from collections import defaultdict

@dataclass
class EndToEndOptimizerConfig:
    """端到端优化器配置"""
    # 学习率配置
    rca_learning_rate: float = 0.001
    gaussians_learning_rate: float = 0.0001
    cism_learning_rate: float = 0.0005
    
    # 优化器配置
    optimizer_type: str = "adamw"  # "adam", "adamw", "sgd"
    weight_decay: float = 0.01
    momentum: float = 0.9  # 仅SGD使用
    beta1: float = 0.9
    beta2: float = 0.999
    eps: float = 1e-8
    
    # 学习率调度配置
    scheduler_type: str = "cosine"  # "cosine", "step", "plateau"
    step_size: int = 1000
    gamma: float = 0.5
    patience: int = 100  # plateau调度器使用
    
    # 梯度控制配置
    gradient_clip_norm: float = 1.0
    gradient_accumulation_steps: int = 1
    
    # 训练稳定性配置
    warmup_steps: int = 500
    warmup_factor: float = 0.1
    
    # 权重初始化配置
    init_method: str = "xavier_uniform"  # "xavier_uniform", "kaiming_normal", "normal"
    init_gain: float = 1.0
    
    # 早停配置
    early_stopping_patience: int = 200
    early_stopping_min_delta: float = 1e-6
    
    # 混合精度配置
    use_mixed_precision: bool = True
    loss_scale: float = 2.0 ** 16
    
    # 检查点配置
    save_checkpoint_every: int = 500
    keep_n_checkpoints: int = 5

class GradientManager:
    """
    🔥 梯度管理器
    处理梯度裁剪、累积、混合精度等
    """
    
    def __init__(self, config: EndToEndOptimizerConfig):
        self.config = config
        self.scaler = None
        
        if config.use_mixed_precision:
            self.scaler = torch.cuda.amp.GradScaler(init_scale=config.loss_scale)
        
        # 梯度统计
        self.gradient_stats = {
            'norm_history': [],
            'clip_count': 0,
            'overflow_count': 0
        }
    
    def backward_and_step(self, 
                         loss: torch.Tensor,
                         optimizers: Dict[str, optim.Optimizer],
                         step: int) -> Dict[str, float]:
        """
        🔥 反向传播和参数更新
        
        Args:
            loss: 总损失
            optimizers: 优化器字典
            step: 当前步数
            
        Returns:
            grad_info: 梯度信息
        """
        grad_info = {}
        
        # 清零梯度
        for optimizer in optimizers.values():
            optimizer.zero_grad()
        
        # 反向传播
        if self.config.use_mixed_precision and self.scaler is not None:
            # 混合精度反向传播
            self.scaler.scale(loss).backward()
            
            # 梯度裁剪和更新
            for name, optimizer in optimizers.items():
                # 反缩放梯度用于裁剪
                self.scaler.unscale_(optimizer)
                
                # 计算梯度范数
                grad_norm = self._compute_gradient_norm(optimizer)
                grad_info[f'{name}_grad_norm'] = grad_norm
                
                # 梯度裁剪
                if self.config.gradient_clip_norm > 0:
                    torch.nn.utils.clip_grad_norm_(
                        [p for group in optimizer.param_groups for p in group['params']],
                        self.config.gradient_clip_norm
                    )
                    
                    # 重新计算裁剪后的梯度范数
                    clipped_grad_norm = self._compute_gradient_norm(optimizer)
                    grad_info[f'{name}_clipped_grad_norm'] = clipped_grad_norm
                    
                    if clipped_grad_norm < grad_norm:
                        self.gradient_stats['clip_count'] += 1
                
                # 参数更新
                self.scaler.step(optimizer)
            
            # 更新scaler
            self.scaler.update()
            
        else:
            # 标准精度反向传播
            loss.backward()
            
            # 梯度裁剪和更新
            for name, optimizer in optimizers.items():
                # 计算梯度范数
                grad_norm = self._compute_gradient_norm(optimizer)
                grad_info[f'{name}_grad_norm'] = grad_norm
                
                # 梯度裁剪
                if self.config.gradient_clip_norm > 0:
                    torch.nn.utils.clip_grad_norm_(
                        [p for group in optimizer.param_groups for p in group['params']],
                        self.config.gradient_clip_norm
                    )
                
                # 参数更新
                optimizer.step()
        
        # 更新梯度统计
        total_grad_norm = sum(grad_info.get(f'{name}_grad_norm', 0) 
                             for name in optimizers.keys())
        self.gradient_stats['norm_history'].append(total_grad_norm)
        
        return grad_info
    
    def _compute_gradient_norm(self, optimizer: optim.Optimizer) -> float:
        """计算梯度范数"""
        total_norm = 0.0
        param_count = 0
        
        for group in optimizer.param_groups:
            for p in group['params']:
                if p.grad is not None:
                    param_norm = p.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
                    param_count += 1
        
        if param_count > 0:
            total_norm = (total_norm ** 0.5)
        
        return total_norm

class LearningRateScheduler:
    """
    🔥 学习率调度器
    支持多种调度策略和预热
    """
    
    def __init__(self, 
                 optimizers: Dict[str, optim.Optimizer],
                 config: EndToEndOptimizerConfig):
        self.optimizers = optimizers
        self.config = config
        self.schedulers = {}
        self.warmup_steps = config.warmup_steps
        self.current_step = 0
        
        # 创建调度器
        self._create_schedulers()
        
        # 保存初始学习率
        self.base_lrs = {}
        for name, optimizer in optimizers.items():
            self.base_lrs[name] = [group['lr'] for group in optimizer.param_groups]
    
    def _create_schedulers(self):
        """创建学习率调度器"""
        for name, optimizer in self.optimizers.items():
            if self.config.scheduler_type == "cosine":
                self.schedulers[name] = CosineAnnealingLR(
                    optimizer, T_max=10000, eta_min=1e-7
                )
            elif self.config.scheduler_type == "step":
                self.schedulers[name] = StepLR(
                    optimizer, 
                    step_size=self.config.step_size,
                    gamma=self.config.gamma
                )
            elif self.config.scheduler_type == "plateau":
                self.schedulers[name] = ReduceLROnPlateau(
                    optimizer,
                    mode='min',
                    patience=self.config.patience,
                    factor=self.config.gamma,
                    verbose=True
                )
    
    def step(self, loss: float = None):
        """学习率调度步进"""
        self.current_step += 1
        
        # 预热阶段
        if self.current_step <= self.warmup_steps:
            self._apply_warmup()
        else:
            # 正常调度
            for name, scheduler in self.schedulers.items():
                if self.config.scheduler_type == "plateau":
                    if loss is not None:
                        scheduler.step(loss)
                else:
                    scheduler.step()
    
    def _apply_warmup(self):
        """应用学习率预热"""
        warmup_factor = self.config.warmup_factor + (1 - self.config.warmup_factor) * (
            self.current_step / self.warmup_steps
        )
        
        for name, optimizer in self.optimizers.items():
            base_lrs = self.base_lrs[name]
            for group, base_lr in zip(optimizer.param_groups, base_lrs):
                group['lr'] = base_lr * warmup_factor
    
    def get_current_lrs(self) -> Dict[str, List[float]]:
        """获取当前学习率"""
        current_lrs = {}
        for name, optimizer in self.optimizers.items():
            current_lrs[name] = [group['lr'] for group in optimizer.param_groups]
        return current_lrs

class EarlyStoppingMonitor:
    """
    🔥 早停监控器
    基于验证损失实现早停
    """
    
    def __init__(self, config: EndToEndOptimizerConfig):
        self.patience = config.early_stopping_patience
        self.min_delta = config.early_stopping_min_delta
        
        self.best_loss = float('inf')
        self.patience_count = 0
        self.should_stop = False
        
        self.loss_history = []
    
    def check(self, current_loss: float) -> bool:
        """
        检查是否应该早停
        
        Args:
            current_loss: 当前验证损失
            
        Returns:
            bool: 是否应该早停
        """
        self.loss_history.append(current_loss)
        
        # 检查是否有改善
        if current_loss < self.best_loss - self.min_delta:
            self.best_loss = current_loss
            self.patience_count = 0
        else:
            self.patience_count += 1
        
        # 判断是否早停
        if self.patience_count >= self.patience:
            self.should_stop = True
            print(f"🛑 早停触发: {self.patience_count} 步无改善")
        
        return self.should_stop

class RCAEndToEndOptimizer:
    """
    🔥 RCA端到端优化器主控制器
    
    功能:
    - 管理RCA网络、3D高斯、CISM组件的联合优化
    - 实现梯度流控制和训练稳定性
    - 提供检查点管理和早停机制
    """
    
    def __init__(self, config: EndToEndOptimizerConfig = None):
        self.config = config or EndToEndOptimizerConfig()
        
        # 核心组件
        self.rca_network = None
        self.gaussians_3d = None
        self.cism_controller = None
        self.loss_function = None
        
        # 优化器和调度器
        self.optimizers = {}
        self.lr_scheduler = None
        
        # 管理器
        self.gradient_manager = GradientManager(self.config)
        self.early_stopping = EarlyStoppingMonitor(self.config)
        
        # 训练状态
        self.current_epoch = 0
        self.current_step = 0
        self.training_stats = defaultdict(list)
        
        print(f"🎯 RCA端到端优化器初始化完成")
    
    def setup_components(self,
                        rca_network: nn.Module,
                        gaussians_3d: nn.Module,
                        cism_controller: nn.Module,
                        loss_function: nn.Module):
        """
        🔥 设置优化组件
        
        Args:
            rca_network: RCA网络
            gaussians_3d: 3D高斯点云
            cism_controller: CISM控制器
            loss_function: 损失函数
        """
        self.rca_network = rca_network
        self.gaussians_3d = gaussians_3d
        self.cism_controller = cism_controller
        self.loss_function = loss_function
        
        # 初始化网络权重
        self._initialize_network_weights()
        
        # 创建优化器
        self._create_optimizers()
        
        # 创建学习率调度器
        self.lr_scheduler = LearningRateScheduler(self.optimizers, self.config)
        
        print("✅ 优化组件设置完成")
    
    def _initialize_network_weights(self):
        """初始化网络权重"""
        def init_weights(module):
            if isinstance(module, nn.Linear):
                if self.config.init_method == "xavier_uniform":
                    nn.init.xavier_uniform_(module.weight, gain=self.config.init_gain)
                elif self.config.init_method == "kaiming_normal":
                    nn.init.kaiming_normal_(module.weight, nonlinearity='relu')
                elif self.config.init_method == "normal":
                    nn.init.normal_(module.weight, std=0.02)
                
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            
            elif isinstance(module, nn.Conv2d):
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
        # 初始化RCA网络
        if self.rca_network is not None:
            self.rca_network.apply(init_weights)
            print("   ✅ RCA网络权重初始化完成")
    
    def _create_optimizers(self):
        """创建优化器"""
        # RCA网络优化器
        if self.rca_network is not None:
            self.optimizers['rca'] = self._create_single_optimizer(
                self.rca_network.parameters(),
                lr=self.config.rca_learning_rate
            )
        
        # 3D高斯优化器
        if self.gaussians_3d is not None:
            self.optimizers['gaussians'] = self._create_single_optimizer(
                self.gaussians_3d.parameters(),
                lr=self.config.gaussians_learning_rate
            )
        
        # CISM优化器
        if self.cism_controller is not None and hasattr(self.cism_controller, 'parameters'):
            self.optimizers['cism'] = self._create_single_optimizer(
                self.cism_controller.parameters(),
                lr=self.config.cism_learning_rate
            )
        
        print(f"   ✅ 创建 {len(self.optimizers)} 个优化器")
    
    def _create_single_optimizer(self, parameters, lr: float) -> optim.Optimizer:
        """创建单个优化器"""
        if self.config.optimizer_type == "adam":
            return optim.Adam(
                parameters,
                lr=lr,
                betas=(self.config.beta1, self.config.beta2),
                eps=self.config.eps,
                weight_decay=self.config.weight_decay
            )
        elif self.config.optimizer_type == "adamw":
            return optim.AdamW(
                parameters,
                lr=lr,
                betas=(self.config.beta1, self.config.beta2),
                eps=self.config.eps,
                weight_decay=self.config.weight_decay
            )
        elif self.config.optimizer_type == "sgd":
            return optim.SGD(
                parameters,
                lr=lr,
                momentum=self.config.momentum,
                weight_decay=self.config.weight_decay
            )
        else:
            raise ValueError(f"不支持的优化器类型: {self.config.optimizer_type}")
    
    def optimize_step(self, 
                     batch_data: Dict[str, torch.Tensor],
                     validation_data: Optional[Dict[str, torch.Tensor]] = None) -> Dict[str, Any]:
        """
        🔥 执行一步优化
        
        Args:
            batch_data: 训练批次数据
            validation_data: 验证数据（可选）
            
        Returns:
            step_results: 步骤结果
        """
        self.current_step += 1
        step_start_time = time.time()
        
        # 设置训练模式
        if self.rca_network is not None:
            self.rca_network.train()
        
        # 前向传播上下文（混合精度）
        if self.config.use_mixed_precision:
            autocast_context = torch.cuda.amp.autocast()
        else:
            autocast_context = torch.no_grad()  # 占位符
        
        # 前向传播
        with autocast_context if self.config.use_mixed_precision else torch.enable_grad():
            # 1. RCA网络前向传播
            rca_predictions = self._rca_forward_pass(batch_data)
            
            # 2. 权重渲染
            weight_maps = self._render_concept_weights(rca_predictions, batch_data)
            
            # 3. CISM引导计算
            cism_guidance = self._compute_cism_guidance(weight_maps, batch_data)
            
            # 4. 损失计算
            loss_dict = self.loss_function(
                predictions=rca_predictions,
                targets=batch_data['labels'],
                weight_maps=weight_maps,
                positions_3d=batch_data['features']['positions'],
                network_parameters=list(self.rca_network.parameters()) if self.rca_network else None
            )
        
        # 反向传播和参数更新
        grad_info = self.gradient_manager.backward_and_step(
            loss_dict['total'], self.optimizers, self.current_step
        )
        
        # 学习率调度
        self.lr_scheduler.step(loss_dict['total'].item())
        
        # 验证（如果提供验证数据）
        validation_loss = None
        if validation_data is not None and self.current_step % 100 == 0:
            validation_loss = self._validate_step(validation_data)
            
            # 早停检查
            if validation_loss is not None:
                self.early_stopping.check(validation_loss)
        
        # 收集步骤结果
        step_time = time.time() - step_start_time
        step_results = {
            'step': self.current_step,
            'losses': {key: loss.item() for key, loss in loss_dict.items()},
            'gradients': grad_info,
            'learning_rates': self.lr_scheduler.get_current_lrs(),
            'validation_loss': validation_loss,
            'step_time': step_time,
            'early_stopping': self.early_stopping.should_stop
        }
        
        # 更新统计
        self._update_training_stats(step_results)
        
        # 检查点保存
        if self.current_step % self.config.save_checkpoint_every == 0:
            self._save_checkpoint()
        
        return step_results
    
    def _rca_forward_pass(self, batch_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """RCA网络前向传播"""
        if self.rca_network is None:
            raise RuntimeError("RCA网络未设置")
        
        # 组合特征
        features = batch_data['features']
        combined_features = []
        
        # 基础特征
        for key in ['positions', 'colors', 'opacity', 'scales', 'concept_id_onehot']:
            if key in features:
                feat = features[key]
                if feat.dim() == 1:
                    feat = feat.unsqueeze(1)
                combined_features.append(feat)
        
        # 可选特征
        for key in ['positional_encoding', 'geometric_features', 'appearance_features', 'context_features']:
            if key in features:
                combined_features.append(features[key])
        
        # 组合所有特征
        input_features = torch.cat(combined_features, dim=1)
        
        # RCA网络预测
        predictions = self.rca_network(input_features)
        return predictions
    
    def _render_concept_weights(self, 
                               rca_predictions: torch.Tensor,
                               batch_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """渲染概念权重图"""
        # 这里需要集成权重渲染器
        # 简化实现 - 实际需要调用RCA权重渲染器
        batch_size, num_concepts = rca_predictions.shape
        H, W = 128, 128  # 假设渲染分辨率
        
        # 模拟权重图
        weight_maps = torch.rand(1, num_concepts, H, W, device=rca_predictions.device)
        weight_maps = F.softmax(weight_maps, dim=1)
        
        return weight_maps
    
    def _compute_cism_guidance(self, 
                              weight_maps: torch.Tensor,
                              batch_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算CISM引导"""
        # 这里需要集成CISM控制器
        # 简化实现 - 实际需要调用CISM引导计算
        guidance = torch.randn_like(weight_maps)
        return guidance
    
    def _validate_step(self, validation_data: Dict[str, torch.Tensor]) -> float:
        """验证步骤"""
        if self.rca_network is not None:
            self.rca_network.eval()
        
        with torch.no_grad():
            # 验证前向传播
            rca_predictions = self._rca_forward_pass(validation_data)
            weight_maps = self._render_concept_weights(rca_predictions, validation_data)
            
            # 计算验证损失
            loss_dict = self.loss_function(
                predictions=rca_predictions,
                targets=validation_data['labels'],
                weight_maps=weight_maps,
                positions_3d=validation_data['features']['positions']
            )
        
        return loss_dict['total'].item()
    
    def _update_training_stats(self, step_results: Dict[str, Any]):
        """更新训练统计"""
        for key, value in step_results['losses'].items():
            self.training_stats[f'loss_{key}'].append(value)
        
        for key, value in step_results['gradients'].items():
            self.training_stats[f'grad_{key}'].append(value)
        
        self.training_stats['step_time'].append(step_results['step_time'])
        
        if step_results['validation_loss'] is not None:
            self.training_stats['validation_loss'].append(step_results['validation_loss'])
    
    def _save_checkpoint(self):
        """保存检查点"""
        checkpoint = {
            'step': self.current_step,
            'epoch': self.current_epoch,
            'config': self.config,
            'training_stats': dict(self.training_stats)
        }
        
        # 保存模型状态
        if self.rca_network is not None:
            checkpoint['rca_network_state_dict'] = self.rca_network.state_dict()
        
        if self.gaussians_3d is not None:
            checkpoint['gaussians_state_dict'] = self.gaussians_3d.state_dict()
        
        # 保存优化器状态
        checkpoint['optimizers_state_dict'] = {
            name: optimizer.state_dict() 
            for name, optimizer in self.optimizers.items()
        }
        
        # 保存路径
        checkpoint_path = f"checkpoint_step_{self.current_step}.pth"
        torch.save(checkpoint, checkpoint_path)
        
        print(f"✅ 检查点已保存: {checkpoint_path}")
        
        # 清理旧检查点
        self._cleanup_old_checkpoints()
    
    def _cleanup_old_checkpoints(self):
        """清理旧检查点"""
        # 简化实现 - 实际需要管理检查点数量
        pass
    
        def get_training_summary(self) -> Dict[str, Any]:
        """获取训练摘要"""
        summary = {
            'current_step': self.current_step,
            'current_epoch': self.current_epoch,
            'early_stopping_triggered': self.early_stopping.should_stop,
            'best_validation_loss': self.early_stopping.best_loss,
            'patience_count': self.early_stopping.patience_count
        }
        
        # 损失统计
        for key, values in self.training_stats.items():
            if len(values) > 0:
                summary[f'{key}_current'] = values[-1]
                summary[f'{key}_mean'] = np.mean(values[-100:])  # 最近100步的平均值
                summary[f'{key}_std'] = np.std(values[-100:])
        
        # 梯度统计
        gradient_stats = self.gradient_manager.gradient_stats
        summary['gradient_clip_count'] = gradient_stats['clip_count']
        summary['gradient_overflow_count'] = gradient_stats['overflow_count']
        if len(gradient_stats['norm_history']) > 0:
            summary['gradient_norm_mean'] = np.mean(gradient_stats['norm_history'][-100:])
        
        return summary
    
    def should_stop_training(self) -> bool:
        """判断是否应该停止训练"""
        return self.early_stopping.should_stop
    
    def save_training_state(self, save_path: str):
        """保存完整训练状态"""
        try:
            training_state = {
                'optimizer_config': self.config,
                'current_step': self.current_step,
                'current_epoch': self.current_epoch,
                'training_stats': dict(self.training_stats),
                'gradient_stats': self.gradient_manager.gradient_stats,
                'early_stopping_state': {
                    'best_loss': self.early_stopping.best_loss,
                    'patience_count': self.early_stopping.patience_count,
                    'should_stop': self.early_stopping.should_stop,
                    'loss_history': self.early_stopping.loss_history
                }
            }
            
            torch.save(training_state, save_path)
            print(f"✅ 训练状态已保存: {save_path}")
            
        except Exception as e:
            print(f"❌ 保存训练状态失败: {str(e)}")
    
    def load_training_state(self, load_path: str) -> bool:
        """加载训练状态"""
        try:
            if not os.path.exists(load_path):
                print(f"训练状态文件不存在: {load_path}")
                return False
            
            training_state = torch.load(load_path)
            
            self.current_step = training_state['current_step']
            self.current_epoch = training_state['current_epoch']
            self.training_stats = defaultdict(list, training_state['training_stats'])
            self.gradient_manager.gradient_stats = training_state['gradient_stats']
            
            # 恢复早停状态
            early_stopping_state = training_state['early_stopping_state']
            self.early_stopping.best_loss = early_stopping_state['best_loss']
            self.early_stopping.patience_count = early_stopping_state['patience_count']
            self.early_stopping.should_stop = early_stopping_state['should_stop']
            self.early_stopping.loss_history = early_stopping_state['loss_history']
            
            print(f"✅ 训练状态已加载: {load_path}")
            return True
            
        except Exception as e:
            print(f"❌ 加载训练状态失败: {str(e)}")
            return False

# 使用示例
if __name__ == "__main__":
    # 配置优化器
    config = EndToEndOptimizerConfig(
        rca_learning_rate=0.001,
        gaussians_learning_rate=0.0001,
        optimizer_type="adamw",
        scheduler_type="cosine",
        use_mixed_precision=True,
        early_stopping_patience=200
    )
    
    # 创建优化器
    optimizer = RCAEndToEndOptimizer(config)
    
    # 模拟网络组件
    rca_network = nn.Sequential(
        nn.Linear(64, 128),
        nn.ReLU(),
        nn.Linear(128, 3)
    )
    
    gaussians_3d = nn.Parameter(torch.randn(1000, 10))
    
    from rca_loss_functions import RCALossFunction
    loss_function = RCALossFunction()
    
    # 设置组件
    optimizer.setup_components(
        rca_network=rca_network,
        gaussians_3d=gaussians_3d,
        cism_controller=None,
        loss_function=loss_function
    )
    
    print("✅ 端到端优化器测试完成")