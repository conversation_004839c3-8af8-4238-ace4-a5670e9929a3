#!/usr/bin/env python3
"""
CISM增强结果评估脚本
阶段2: 评估CISM语义引导的效果

使用方法:
python evaluate_cism.py --model_path output/cism_experiments/enhanced_gaussians.ply --source_path data/garden --config configs/cism_training_config.yaml
"""

import os
import sys
import argparse
import yaml
import logging
from pathlib import Path
import torch
import numpy as np
from typing import Dict, List, Tuple
import json

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入3DGS组件
try:
    from gaussian_splatting.scene import Scene, GaussianModel
    from gaussian_splatting.utils.general_utils import safe_state
    from gaussian_splatting.utils.loss_utils import l1_loss, ssim
    from gaussian_splatting.gaussian_renderer import render
    from gaussian_splatting.arguments import ModelParams, PipelineParams
    from gaussian_splatting.utils.image_utils import psnr
    GAUSSIAN_SPLATTING_AVAILABLE = True
except ImportError:
    GAUSSIAN_SPLATTING_AVAILABLE = False
    logging.warning("Gaussian Splatting modules not available")

# 导入CISM组件
from cism_core import DiffusionEngine, ConceptGuidance

def setup_logging(verbose: bool = False):
    """设置日志系统"""
    log_level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def load_config(config_path: str) -> Dict:
    """加载配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def load_enhanced_gaussians(model_path: str) -> GaussianModel:
    """加载CISM增强后的高斯模型"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    gaussians = GaussianModel(sh_degree=3)
    gaussians.load_ply(model_path)
    
    logging.info(f"Loaded enhanced Gaussian model from {model_path}")
    logging.info(f"Number of Gaussian points: {gaussians.get_xyz.shape[0]}")
    
    return gaussians

def setup_evaluation_scene(source_path: str, gaussians: GaussianModel, config: Dict):
    """设置评估场景"""
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        raise ImportError("Gaussian Splatting modules not available")
    
    # 创建模型参数
    model_params = ModelParams()
    model_params.source_path = source_path
    model_params.resolution = config['rendering']['image_resolution']
    
    # 创建场景
    scene = Scene(model_params, gaussians, load_iteration=None, shuffle=False)
    
    return scene

def compute_rendering_metrics(
    gaussians: GaussianModel,
    scene: Scene,
    camera_set: str = "test"
) -> Dict:
    """计算渲染质量指标"""
    logging.info(f"Computing rendering metrics on {camera_set} set...")
    
    # 获取相机
    if camera_set == "test":
        cameras = scene.getTestCameras()
    elif camera_set == "train":
        cameras = scene.getTrainCameras()
    else:
        cameras = scene.getTrainCameras() + scene.getTestCameras()
    
    metrics = {
        'psnr_values': [],
        'ssim_values': [],
        'l1_values': [],
        'lpips_values': []
    }
    
    pipeline_params = PipelineParams()
    background = torch.tensor([1.0, 1.0, 1.0], device="cuda")
    
    for camera in cameras:
        # 渲染
        render_pkg = render(camera, gaussians, pipeline_params, background)
        rendered_image = render_pkg["render"]
        
        # 真实图像
        gt_image = camera.original_image.cuda()
        
        # 计算指标
        psnr_val = psnr(rendered_image, gt_image).mean().item()
        ssim_val = ssim(rendered_image, gt_image).mean().item()
        l1_val = l1_loss(rendered_image, gt_image).mean().item()
        
        metrics['psnr_values'].append(psnr_val)
        metrics['ssim_values'].append(ssim_val)
        metrics['l1_values'].append(l1_val)
    
    # 计算平均值
    avg_metrics = {
        'avg_psnr': np.mean(metrics['psnr_values']),
        'avg_ssim': np.mean(metrics['ssim_values']),
        'avg_l1': np.mean(metrics['l1_values']),
        'std_psnr': np.std(metrics['psnr_values']),
        'std_ssim': np.std(metrics['ssim_values']),
        'std_l1': np.std(metrics['l1_values']),
        'num_views': len(cameras)
    }
    
    logging.info(f"Rendering metrics ({camera_set}):")
    logging.info(f"  PSNR: {avg_metrics['avg_psnr']:.2f} ± {avg_metrics['std_psnr']:.2f}")
    logging.info(f"  SSIM: {avg_metrics['avg_ssim']:.4f} ± {avg_metrics['std_ssim']:.4f}")
    logging.info(f"  L1: {avg_metrics['avg_l1']:.4f} ± {avg_metrics['std_l1']:.4f}")
    
    return avg_metrics

def evaluate_concept_quality(
    gaussians: GaussianModel,
    concept_guidance: ConceptGuidance,
    config: Dict
) -> Dict:
    """评估概念质量"""
    logging.info("Evaluating concept quality...")
    
    concept_stats = {
        'total_points': gaussians.get_xyz.shape[0],
        'concept_distribution': {},
        'concept_confidence_stats': {}
    }
    
    # 检查是否有concept_id属性
    if hasattr(gaussians, '_concept_id') and hasattr(gaussians, '_concept_confidence'):
        concept_ids = gaussians.get_concept_id
        concept_confidences = gaussians.get_concept_confidence
        
        # 概念分布统计
        unique_concepts, counts = torch.unique(concept_ids, return_counts=True)
        for concept_id, count in zip(unique_concepts.cpu().numpy(), counts.cpu().numpy()):
            concept_stats['concept_distribution'][int(concept_id)] = {
                'count': int(count),
                'percentage': float(count / concept_stats['total_points'] * 100)
            }
        
        # 概念置信度统计
        for concept_id in unique_concepts.cpu().numpy():
            mask = (concept_ids == concept_id)
            confidences = concept_confidences[mask]
            
            concept_stats['concept_confidence_stats'][int(concept_id)] = {
                'mean_confidence': float(confidences.mean().item()),
                'std_confidence': float(confidences.std().item()),
                'min_confidence': float(confidences.min().item()),
                'max_confidence': float(confidences.max().item())
            }
        
        logging.info("Concept distribution:")
        for concept_id, stats in concept_stats['concept_distribution'].items():
            logging.info(f"  Concept {concept_id}: {stats['count']} points ({stats['percentage']:.1f}%)")
        
        logging.info("Concept confidence statistics:")
        for concept_id, stats in concept_stats['concept_confidence_stats'].items():
            logging.info(f"  Concept {concept_id}: {stats['mean_confidence']:.3f} ± {stats['std_confidence']:.3f}")
    
    else:
        logging.warning("No concept_id information found in the model")
        concept_stats['has_concept_info'] = False
    
    return concept_stats

def generate_evaluation_report(
    rendering_metrics: Dict,
    concept_stats: Dict,
    config: Dict,
    output_path: str
):
    """生成评估报告"""
    report = {
        'evaluation_summary': {
            'model_type': 'CISM Enhanced 3D Gaussian Splatting',
            'evaluation_date': str(torch.datetime.now()),
            'total_gaussian_points': concept_stats['total_points']
        },
        'rendering_quality': rendering_metrics,
        'concept_analysis': concept_stats,
        'configuration': {
            'user_concept': config['concept_config'].get('user_concept', 'Not specified'),
            'concept_weights': config['concept_config'].get('concept_weights', []),
            'guidance_scale': config['sds_loss'].get('guidance_scale', 7.5),
            'training_iterations': f"{config['training_schedule']['cism_start_iter']}-{config['training_schedule']['cism_end_iter']}"
        }
    }
    
    # 保存报告
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logging.info(f"Evaluation report saved to {output_path}")
    
    return report

def compare_with_baseline(
    enhanced_metrics: Dict,
    baseline_model_path: Optional[str] = None
) -> Dict:
    """与基线模型比较"""
    if not baseline_model_path or not Path(baseline_model_path).exists():
        logging.warning("Baseline model not provided or not found, skipping comparison")
        return {}
    
    logging.info("Comparing with baseline model...")
    
    # 这里可以加载基线模型并计算指标
    # 为简化，这里返回空字典
    comparison = {
        'baseline_available': False,
        'improvements': {}
    }
    
    return comparison

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CISM Enhanced Model Evaluation")
    parser.add_argument("--model_path", type=str, required=True, help="Enhanced Gaussian model path")
    parser.add_argument("--source_path", type=str, required=True, help="Source data path")
    parser.add_argument("--config", type=str, required=True, help="Training configuration file")
    parser.add_argument("--baseline_model", type=str, help="Baseline model for comparison")
    parser.add_argument("--output_dir", type=str, default="output/evaluation", help="Output directory")
    parser.add_argument("--camera_set", type=str, default="test", choices=["train", "test", "all"], help="Camera set for evaluation")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # 检查依赖
    if not GAUSSIAN_SPLATTING_AVAILABLE:
        print("Error: Gaussian Splatting modules not available")
        sys.exit(1)
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logging.info("Starting CISM Enhanced Model Evaluation")
    logging.info(f"Model: {args.model_path}")
    logging.info(f"Source: {args.source_path}")
    logging.info(f"Config: {args.config}")
    
    try:
        # 加载配置
        config = load_config(args.config)
        
        # 加载增强模型
        gaussians = load_enhanced_gaussians(args.model_path)
        
        # 设置评估场景
        scene = setup_evaluation_scene(args.source_path, gaussians, config)
        
        # 计算渲染质量指标
        rendering_metrics = compute_rendering_metrics(gaussians, scene, args.camera_set)
        
        # 初始化概念引导系统（用于概念评估）
        try:
            diffusion_engine = DiffusionEngine(device="cuda")
            concept_guidance = ConceptGuidance(diffusion_engine, config['concept_config']['concept_config_path'])
            
            # 评估概念质量
            concept_stats = evaluate_concept_quality(gaussians, concept_guidance, config)
            
            # 清理
            diffusion_engine.cleanup()
            concept_guidance.cleanup()
            
        except Exception as e:
            logging.warning(f"Concept evaluation failed: {e}")
            concept_stats = {'error': str(e)}
        
        # 与基线比较
        comparison = compare_with_baseline(rendering_metrics, args.baseline_model)
        
        # 生成评估报告
        report_path = output_dir / "evaluation_report.json"
        report = generate_evaluation_report(rendering_metrics, concept_stats, config, str(report_path))
        
        # 输出总结
        logging.info("\n" + "="*50)
        logging.info("EVALUATION SUMMARY")
        logging.info("="*50)
        logging.info(f"Model: CISM Enhanced 3D Gaussian Splatting")
        logging.info(f"Total Points: {concept_stats['total_points']:,}")
        logging.info(f"PSNR: {rendering_metrics['avg_psnr']:.2f} dB")
        logging.info(f"SSIM: {rendering_metrics['avg_ssim']:.4f}")
        logging.info(f"L1 Loss: {rendering_metrics['avg_l1']:.4f}")
        
        if 'concept_distribution' in concept_stats:
            logging.info("\nConcept Distribution:")
            for concept_id, stats in concept_stats['concept_distribution'].items():
                logging.info(f"  Concept {concept_id}: {stats['percentage']:.1f}%")
        
        logging.info(f"\nDetailed report saved to: {report_path}")
        logging.info("Evaluation completed successfully")
        
    except Exception as e:
        logging.error(f"Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
